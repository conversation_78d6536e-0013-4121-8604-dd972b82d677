package com.ict.ycwl.guestbook.config;

import com.ict.ycwl.common.web.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(Exception.class)
    public AjaxResult exceptionHandler(Exception e, HttpServletRequest request) {
        log.info("Request URL : {}，Exception : " + e, request.getRequestURL());

        if (e instanceof BindException) {
            BindException be = (BindException) e;
            return AjaxResult.error(403, be.getBindingResult().getAllErrors().get(0).getDefaultMessage());
        }
        return AjaxResult.error("操作错误，请重试");
    }

}
