import{g as he,_ as ke,k as Re,E as ce,l as Ie,m as $e,n as Pe,t as De,p as Se,o as Le}from"./base-kpSIrADU.js";import{E as Oe}from"./empty-DmGQucfw.js";import{E as W}from"./button-IGKrEYb9.js";import{a as se,E as ue}from"./form-item-Bd-FvCZ5.js";import{C as be,U as ye,i as Ne,E as te,d as Fe}from"./input-DqmydyK4.js";import{a as de,E as ie}from"./select-BOcQ2ynX.js";import"./scrollbar-BNeK4Yi-.js";import{E as pe}from"./overlay-D06mCCGK.js";import{E as fe,a as Ge}from"./checkbox-DWZ5xHlw.js";import{f as Ce,h as G,a0 as Z,k as D,a1 as Be,m as q,d as H,a2 as Ve,H as Ae,j as Ee,P as X,a3 as Y,a4 as je,o as y,c as R,a as w,b as a,w as d,R as j,S as O,u as e,Q,t as L,p as P,K as ee,X as z,a5 as me,N,T as F,s as ae,r as U,a6 as ge,a7 as _e,v as Me,_ as S,Y as qe,W as He}from"./index-C0QCllTd.js";import{_ as Ye,a as xe,g as Ke,b as ze,s as Qe,u as We,c as Xe,d as Je,e as Ze,f as et}from"./dynamic-import-helper-BqvKthby.js";import{m as tt,a as B}from"./index-m25zEilF.js";import{_ as le}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{E as we}from"./date-picker-C-6M_J1A.js";import"./castArray-CSO3s-vM.js";import"./_initCloneObject-BmTtMqsv.js";import"./_commonjsHelpers-BbMlrU8H.js";import"./index-DOdSMika.js";import"./flatten-BP0fiJV-.js";import"./index-1tmHbbca.js";const Ue="left-check-change",Te="right-check-change",K=Ce({data:{type:G(Array),default:()=>[]},titles:{type:G(Array),default:()=>[]},buttonTexts:{type:G(Array),default:()=>[]},filterPlaceholder:String,filterMethod:{type:G(Function)},leftDefaultChecked:{type:G(Array),default:()=>[]},rightDefaultChecked:{type:G(Array),default:()=>[]},renderContent:{type:G(Function)},modelValue:{type:G(Array),default:()=>[]},format:{type:G(Object),default:()=>({})},filterable:Boolean,props:{type:G(Object),default:()=>tt({label:"label",key:"key",disabled:"disabled"})},targetOrder:{type:String,values:["original","push","unshift"],default:"original"},validateEvent:{type:Boolean,default:!0}}),re=(i,r)=>[i,r].every(Z)||Z(i)&&Ne(r),lt={[be]:(i,r,p)=>[i,p].every(Z)&&["left","right"].includes(r),[ye]:i=>Z(i),[Ue]:re,[Te]:re},ne="checked-change",ot=Ce({data:K.data,optionRender:{type:G(Function)},placeholder:String,title:String,filterable:Boolean,format:K.format,filterMethod:K.filterMethod,defaultChecked:K.leftDefaultChecked,props:K.props}),at={[ne]:re},J=i=>{const r={label:"label",key:"key",disabled:"disabled"};return D(()=>({...r,...i.props}))},rt=(i,r,p)=>{const c=J(i),k=D(()=>i.data.filter(m=>Be(i.filterMethod)?i.filterMethod(r.query,m):String(m[c.value.label]||m[c.value.key]).toLowerCase().includes(r.query.toLowerCase()))),g=D(()=>k.value.filter(m=>!m[c.value.disabled])),n=D(()=>{const m=r.checked.length,A=i.data.length,{noChecked:_,hasChecked:t}=i.format;return _&&t?m>0?t.replace(/\${checked}/g,m.toString()).replace(/\${total}/g,A.toString()):_.replace(/\${total}/g,A.toString()):`${m}/${A}`}),l=D(()=>{const m=r.checked.length;return m>0&&m<g.value.length}),b=()=>{const m=g.value.map(A=>A[c.value.key]);r.allChecked=m.length>0&&m.every(A=>r.checked.includes(A))},C=m=>{r.checked=m?g.value.map(A=>A[c.value.key]):[]};return q(()=>r.checked,(m,A)=>{if(b(),r.checkChangeByUser){const _=m.concat(A).filter(t=>!m.includes(t)||!A.includes(t));p(ne,m,_)}else p(ne,m),r.checkChangeByUser=!0}),q(g,()=>{b()}),q(()=>i.data,()=>{const m=[],A=k.value.map(_=>_[c.value.key]);r.checked.forEach(_=>{A.includes(_)&&m.push(_)}),r.checkChangeByUser=!1,r.checked=m}),q(()=>i.defaultChecked,(m,A)=>{if(A&&m.length===A.length&&m.every(u=>A.includes(u)))return;const _=[],t=g.value.map(u=>u[c.value.key]);m.forEach(u=>{t.includes(u)&&_.push(u)}),r.checkChangeByUser=!1,r.checked=_},{immediate:!0}),{filteredData:k,checkableData:g,checkedSummary:n,isIndeterminate:l,updateAllChecked:b,handleAllCheckedChange:C}},nt=(i,r)=>({onSourceCheckedChange:(k,g)=>{i.leftChecked=k,g&&r(Ue,k,g)},onTargetCheckedChange:(k,g)=>{i.rightChecked=k,g&&r(Te,k,g)}}),st=i=>{const r=J(i),p=D(()=>i.data.reduce((g,n)=>(g[n[r.value.key]]=n)&&g,{})),c=D(()=>i.data.filter(g=>!i.modelValue.includes(g[r.value.key]))),k=D(()=>i.targetOrder==="original"?i.data.filter(g=>i.modelValue.includes(g[r.value.key])):i.modelValue.reduce((g,n)=>{const l=p.value[n];return l&&g.push(l),g},[]));return{sourceData:c,targetData:k}},ut=(i,r,p)=>{const c=J(i),k=(l,b,C)=>{p(ye,l),p(be,l,b,C)};return{addToLeft:()=>{const l=i.modelValue.slice();r.rightChecked.forEach(b=>{const C=l.indexOf(b);C>-1&&l.splice(C,1)}),k(l,"left",r.rightChecked)},addToRight:()=>{let l=i.modelValue.slice();const b=i.data.filter(C=>{const m=C[c.value.key];return r.leftChecked.includes(m)&&!i.modelValue.includes(m)}).map(C=>C[c.value.key]);l=i.targetOrder==="unshift"?b.concat(l):l.concat(b),i.targetOrder==="original"&&(l=i.data.filter(C=>l.includes(C[c.value.key])).map(C=>C[c.value.key])),k(l,"right",r.leftChecked)}}},dt=H({name:"ElTransferPanel"}),it=H({...dt,props:ot,emits:at,setup(i,{expose:r,emit:p}){const c=i,k=Ve(),g=({option:h})=>h,{t:n}=Ae(),l=Ee("transfer"),b=X({checked:[],allChecked:!1,query:"",checkChangeByUser:!0}),C=J(c),{filteredData:m,checkedSummary:A,isIndeterminate:_,handleAllCheckedChange:t}=rt(c,b,p),u=D(()=>!Y(b.query)&&Y(m.value)),v=D(()=>!Y(k.default()[0].children)),{checked:V,allChecked:s,query:f}=je(b);return r({query:f}),(h,I)=>(y(),R("div",{class:L(e(l).b("panel"))},[w("p",{class:L(e(l).be("panel","header"))},[a(e(fe),{modelValue:e(s),"onUpdate:modelValue":I[0]||(I[0]=$=>Q(s)?s.value=$:null),indeterminate:e(_),"validate-event":!1,onChange:e(t)},{default:d(()=>[j(O(h.title)+" ",1),w("span",null,O(e(A)),1)]),_:1},8,["modelValue","indeterminate","onChange"])],2),w("div",{class:L([e(l).be("panel","body"),e(l).is("with-footer",e(v))])},[h.filterable?(y(),P(e(te),{key:0,modelValue:e(f),"onUpdate:modelValue":I[1]||(I[1]=$=>Q(f)?f.value=$:null),class:L(e(l).be("panel","filter")),size:"default",placeholder:h.placeholder,"prefix-icon":e(he),clearable:"","validate-event":!1},null,8,["modelValue","class","placeholder","prefix-icon"])):ee("v-if",!0),z(a(e(Ge),{modelValue:e(V),"onUpdate:modelValue":I[2]||(I[2]=$=>Q(V)?V.value=$:null),"validate-event":!1,class:L([e(l).is("filterable",h.filterable),e(l).be("panel","list")])},{default:d(()=>[(y(!0),R(N,null,F(e(m),$=>(y(),P(e(fe),{key:$[e(C).key],class:L(e(l).be("panel","item")),label:$[e(C).key],disabled:$[e(C).disabled],"validate-event":!1},{default:d(()=>{var o;return[a(g,{option:(o=h.optionRender)==null?void 0:o.call(h,$)},null,8,["option"])]}),_:2},1032,["class","label","disabled"]))),128))]),_:1},8,["modelValue","class"]),[[me,!e(u)&&!e(Y)(h.data)]]),z(w("p",{class:L(e(l).be("panel","empty"))},O(e(u)?e(n)("el.transfer.noMatch"):e(n)("el.transfer.noData")),3),[[me,e(u)||e(Y)(h.data)]])],2),e(v)?(y(),R("p",{key:0,class:L(e(l).be("panel","footer"))},[ae(h.$slots,"default")],2)):ee("v-if",!0)],2))}});var ve=ke(it,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/transfer/src/transfer-panel.vue"]]);const pt={key:0},ct={key:0},ft=H({name:"ElTransfer"}),mt=H({...ft,props:K,emits:lt,setup(i,{expose:r,emit:p}){const c=i,k=Ve(),{t:g}=Ae(),n=Ee("transfer"),{formItem:l}=Re(),b=X({leftChecked:[],rightChecked:[]}),C=J(c),{sourceData:m,targetData:A}=st(c),{onSourceCheckedChange:_,onTargetCheckedChange:t}=nt(b,p),{addToLeft:u,addToRight:v}=ut(c,b,p),V=U(),s=U(),f=T=>{switch(T){case"left":V.value.query="";break;case"right":s.value.query="";break}},h=D(()=>c.buttonTexts.length===2),I=D(()=>c.titles[0]||g("el.transfer.titles.0")),$=D(()=>c.titles[1]||g("el.transfer.titles.1")),o=D(()=>c.filterPlaceholder||g("el.transfer.filterPlaceholder"));q(()=>c.modelValue,()=>{var T;c.validateEvent&&((T=l==null?void 0:l.validate)==null||T.call(l,"change").catch(x=>Fe()))});const M=D(()=>T=>c.renderContent?c.renderContent(ge,T):k.default?k.default({option:T}):ge("span",T[C.value.label]||T[C.value.key]));return r({clearQuery:f,leftPanel:V,rightPanel:s}),(T,x)=>(y(),R("div",{class:L(e(n).b())},[a(ve,{ref_key:"leftPanel",ref:V,data:e(m),"option-render":e(M),placeholder:e(o),title:e(I),filterable:T.filterable,format:T.format,"filter-method":T.filterMethod,"default-checked":T.leftDefaultChecked,props:c.props,onCheckedChange:e(_)},{default:d(()=>[ae(T.$slots,"left-footer")]),_:3},8,["data","option-render","placeholder","title","filterable","format","filter-method","default-checked","props","onCheckedChange"]),w("div",{class:L(e(n).e("buttons"))},[a(e(W),{type:"primary",class:L([e(n).e("button"),e(n).is("with-texts",e(h))]),disabled:e(Y)(b.rightChecked),onClick:e(u)},{default:d(()=>[a(e(ce),null,{default:d(()=>[a(e(Ie))]),_:1}),e(_e)(T.buttonTexts[0])?ee("v-if",!0):(y(),R("span",pt,O(T.buttonTexts[0]),1))]),_:1},8,["class","disabled","onClick"]),a(e(W),{type:"primary",class:L([e(n).e("button"),e(n).is("with-texts",e(h))]),disabled:e(Y)(b.leftChecked),onClick:e(v)},{default:d(()=>[e(_e)(T.buttonTexts[1])?ee("v-if",!0):(y(),R("span",ct,O(T.buttonTexts[1]),1)),a(e(ce),null,{default:d(()=>[a(e($e))]),_:1})]),_:1},8,["class","disabled","onClick"])],2),a(ve,{ref_key:"rightPanel",ref:s,data:e(A),"option-render":e(M),placeholder:e(o),filterable:T.filterable,format:T.format,"filter-method":T.filterMethod,title:e($),"default-checked":T.rightDefaultChecked,props:c.props,onCheckedChange:e(t)},{default:d(()=>[ae(T.$slots,"right-footer")]),_:3},8,["data","option-render","placeholder","filterable","format","filter-method","title","default-checked","props","onCheckedChange"])],2))}});var gt=ke(mt,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/transfer/src/transfer.vue"]]);const _t=Me(gt);function vt(i){return i=i===11?13:i,Ye(Object.assign({"../assets/images/avatar/1.jpg":()=>S(()=>import("./1-2Djgt5lP.js"),[]),"../assets/images/avatar/10.jpg":()=>S(()=>import("./10-TkdyvCNi.js"),[]),"../assets/images/avatar/12.jpg":()=>S(()=>import("./12-BcA11hp_.js"),[]),"../assets/images/avatar/13.jpg":()=>S(()=>import("./13-iJd-ijt8.js"),[]),"../assets/images/avatar/14.jpg":()=>S(()=>import("./14-BNf6Omv-.js"),[]),"../assets/images/avatar/2.jpg":()=>S(()=>import("./2-Bvl-tPQy.js"),[]),"../assets/images/avatar/3.jpg":()=>S(()=>import("./3-DnsbFUaC.js"),[]),"../assets/images/avatar/4.jpg":()=>S(()=>import("./4-B6-4LUk3.js"),[]),"../assets/images/avatar/5.jpg":()=>S(()=>import("./5-BRnz0CRK.js"),[]),"../assets/images/avatar/6.jpg":()=>S(()=>import("./6-WT7oubfy.js"),[]),"../assets/images/avatar/7.jpg":()=>S(()=>import("./7-De9B_OxM.js"),[]),"../assets/images/avatar/8.jpg":()=>S(()=>import("./8-ZpeXJSx9.js"),[]),"../assets/images/avatar/9.jpg":()=>S(()=>import("./9-BCAwrtzz.js"),[])}),`../assets/images/avatar/${i}.jpg`,5).then(r=>r.default).catch(r=>{throw console.error("Failed to load image:",r),r})}const oe=qe("group",()=>{async function i(s){const f=await xe(s);return f.code===200&&B.success(f.msg),f}const r=U([]);async function p(){const s=await Ke();r.value=s.data}const c=U([]);async function k(){const s=await ze();c.value=s.data}async function g(s){const f=await Qe(s);f.code===200&&B.success(f.msg)}async function n(s){const f=await We(s);return f.code===200&&B.success(f.msg),f}const l=U([]);async function b(s){const f=await Xe(s);l.value=await C(f.data),console.log(l.value)}async function C(s){return Promise.all(s.map(async f=>{try{const h=await vt(Number(f.role_id));return{...f,avatar_path:h}}catch(h){return console.error("Failed to load avatar for user:",f.work_number,h),f}}))}const m=U([]);async function A(s){const f=await Je(s);m.value=f.data}const _=U([]);async function t(s){const f=await Ze(s);_.value=f.data}async function u(s){const f=await et(s);return f.code===200&&B.success(f.msg),f}const v=U(["营销部","物流部","专卖部","内管部"]),V=U(["班组一","班组二","班组三","班组四","班组五","班组六"]);return{postAddUserAction:i,setUserAuthorityAction:g,roles:c,getAllAuthorityAction:p,getRoleAction:k,AllAuthority:r,userAvatarAction:n,getAllUserAction:b,roleAuthority:m,getRoleOperationsAction:A,editUserInfo:_,userInfoArr:l,getEditUserInfoAction:t,updateUserInfoAction:u,groupList:v,gList:V}}),ht={class:"groupSettingDialog"},kt={class:"groupSettingContent"},bt={class:"groupSettingLeft"},yt={class:"groupSettingRight"},Ct={class:"groupSettingRightBtn"},Vt=H({__name:"GroupSetting",setup(i,{expose:r}){const p=oe(),c=U([]),k=U(),g=U([]),n=U(1),l=U([]),b=U(!1);q(b,u=>{u&&(n.value=1,p.getAllAuthorityAction().then(()=>{k.value=p.AllAuthority,g.value=p.AllAuthority.map(v=>({key:v.operation_id,label:v.operation_state,value:v.operation_name}))}),p.getRoleAction().then(()=>{l.value=p.roles}),A(n.value))});const C=()=>{b.value=!1,n.value=1};r({groupSettingOpen:b});const m=u=>{_.role_id=u,n.value=u,A(u)};function A(u){p.getRoleOperationsAction(u).then(()=>{c.value=p.roleAuthority.map(v=>v.operationId)})}const _=X({idList:"",role_id:0}),t=()=>{const u=Array.from(c.value);_.idList=u.toString(),_.idList==""?B({type:"warning",message:"该角色权限点不能为空"}):p.setUserAuthorityAction({..._}).then(()=>{C()})};return(u,v)=>{const V=W,s=_t,f=pe;return y(),R("div",ht,[a(f,{title:"设置权限",modelValue:b.value,"onUpdate:modelValue":v[3]||(v[3]=h=>b.value=h),width:"69%",onClose:C},{default:d(()=>[v[6]||(v[6]=w("div",{class:"groupSettingTopText"},"设置角色权限点：",-1)),w("div",kt,[w("div",bt,[(y(!0),R(N,null,F(l.value,h=>(y(),P(V,{key:h.role_id,class:L(["groupSettingLeftButton",{active:n.value===h.role_id}]),onClick:I=>m(h.role_id)},{default:d(()=>[j(O(h.role_name),1)]),_:2},1032,["onClick","class"]))),128))]),w("div",yt,[a(s,{class:"groupSettingTransfer",modelValue:c.value,"onUpdate:modelValue":v[0]||(v[0]=h=>c.value=h),data:g.value,titles:["权限点","该角色权限点"]},null,8,["modelValue","data"]),w("div",Ct,[a(V,{class:"groupSettingRightCancel",onClick:v[1]||(v[1]=h=>C())},{default:d(()=>v[4]||(v[4]=[j("取消")])),_:1}),a(V,{class:"groupSettingRightConfirm",onClick:v[2]||(v[2]=h=>t())},{default:d(()=>v[5]||(v[5]=[j("确认")])),_:1})])])])]),_:1},8,["modelValue"])])}}}),At=le(Vt,[["__scopeId","data-v-bd2d3261"]]),Et={class:"groupAddDialog"},wt={class:"groupAddInfo"},Ut={class:"groupAddTwo"},Tt={class:"groupAddSetting"},Rt=H({__name:"GroupAdd",emits:["renewUser"],setup(i,{expose:r,emit:p}){const c=oe(),k=U(!1);r({groupAddOpen:k});const g=U(),n=X({user_name:[{required:!0,message:"请输入用户名",trigger:"blur"}],phone:[{required:!0,message:"请输入电话",trigger:"blur"}],sign_time:[{required:!0,message:"请选择入职时间",trigger:"blur"}],work_number:[{required:!0,message:"请输入工号",trigger:"blur"}],role_id:[{required:!0,message:"请选择角色",trigger:"blur"}]}),l=U({user_name:"",phone:"",email:"",department:"",group:"",role_id:void 0,work_number:"",sign_time:"",avatarPath:""}),b=_=>{if(_==0)l.value.department!=="物流部"&&l.value.group&&l.value.department&&(B.warning({message:"只能选择一个下拉框！",duration:1e3}),l.value.group="",l.value.department="");else{if(l.value.department==="物流部")return;l.value.group&&l.value.department&&(B.warning({message:"只能选择一个下拉框！",duration:1e3}),l.value.group="",l.value.department="")}},C=_=>{_&&(k.value=!1,_.resetFields())},m=p,A=async _=>{_&&await _.validate((t,u)=>{t?l.value.role_id!=0?(l.value.department==="物流部"&&l.value.group&&(l.value.department=""),c.postAddUserAction(l.value).then(v=>{v.code==200&&(m("renewUser"),C(g.value))})):B.error("请选择角色"):console.log("error submit!",u)})};return(_,t)=>{const u=te,v=se,V=de,s=ie,f=we,h=ue,I=W,$=pe;return y(),R("div",Et,[a($,{title:"添加用户",modelValue:e(k),"onUpdate:modelValue":t[12]||(t[12]=o=>Q(k)?k.value=o:null),width:"80%",onClose:t[13]||(t[13]=o=>C(e(g)))},{default:d(()=>[w("div",wt,[w("div",Ut,[a(h,{inline:!0,style:{height:"100%"},"label-width":"110",rules:e(n),ref_key:"userFormRef",ref:g,model:e(l)},{default:d(()=>[a(v,{label:"工号",prop:"work_number"},{default:d(()=>[a(u,{modelValue:e(l).work_number,"onUpdate:modelValue":t[0]||(t[0]=o=>e(l).work_number=o),placeholder:"点击输入"},null,8,["modelValue"])]),_:1}),a(v,{label:"姓名",prop:"user_name"},{default:d(()=>[a(u,{modelValue:e(l).user_name,"onUpdate:modelValue":t[1]||(t[1]=o=>e(l).user_name=o),placeholder:"点击输入"},null,8,["modelValue"])]),_:1}),a(v,{label:"电话",prop:"phone"},{default:d(()=>[a(u,{modelValue:e(l).phone,"onUpdate:modelValue":t[2]||(t[2]=o=>e(l).phone=o),placeholder:"点击输入"},null,8,["modelValue"])]),_:1}),a(v,{label:"邮箱",prop:"email"},{default:d(()=>[a(u,{modelValue:e(l).email,"onUpdate:modelValue":t[3]||(t[3]=o=>e(l).email=o),placeholder:"点击输入"},null,8,["modelValue"])]),_:1}),a(v,{label:"部门",prop:"department"},{default:d(()=>[a(s,{modelValue:e(l).department,"onUpdate:modelValue":t[4]||(t[4]=o=>e(l).department=o),onChange:t[5]||(t[5]=o=>b(0))},{default:d(()=>[(y(!0),R(N,null,F(e(c).groupList,o=>(y(),P(V,{key:o,label:o,value:o},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(v,{label:"班组",prop:"group"},{default:d(()=>[a(s,{modelValue:e(l).group,"onUpdate:modelValue":t[6]||(t[6]=o=>e(l).group=o),onChange:t[7]||(t[7]=o=>b(2))},{default:d(()=>[(y(!0),R(N,null,F(e(c).gList,o=>(y(),P(V,{key:o,label:o,value:o},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(v,{label:"入职时间",prop:"sign_time"},{default:d(()=>[a(f,{modelValue:e(l).sign_time,"onUpdate:modelValue":t[8]||(t[8]=o=>e(l).sign_time=o),"value-format":"YYYY-MM-DD",type:"date",placeholder:"点击输入"},null,8,["modelValue"])]),_:1}),a(v,{label:"角色",prop:"role_id",class:"groupAddRadioGroup"},{default:d(()=>[a(s,{modelValue:e(l).role_id,"onUpdate:modelValue":t[9]||(t[9]=o=>e(l).role_id=o)},{default:d(()=>[(y(!0),R(N,null,F(e(c).roles,o=>(y(),P(V,{key:o.role_id,label:o.role_name,value:o.role_id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["rules","model"])]),w("div",Tt,[a(I,{class:"groupAddCancel",onClick:t[10]||(t[10]=o=>C(e(g)))},{default:d(()=>t[14]||(t[14]=[j("取消")])),_:1}),a(I,{class:"groupAddConfirm",onClick:t[11]||(t[11]=o=>A(e(g)))},{default:d(()=>t[15]||(t[15]=[j("确认")])),_:1})])])]),_:1},8,["modelValue"])])}}}),It=le(Rt,[["__scopeId","data-v-6a45ff2f"]]),$t={class:"groupUserEditDialog"},Pt={class:"groupUserEdit"},Dt={class:"groupUserEditInfo"},St={style:{color:"#cbfefe"}},Lt={style:{color:"#cbfefe"}},Ot={class:"groupUserEditSetting"},Nt=H({__name:"GroupUserEdit",emits:["renewUser"],setup(i,{expose:r,emit:p}){const c=oe(),k=U(!1),g=p,n=U({user_name:"",work_number:"",department:"",phone:"",role_id:void 0,signTime:"",email:"",newPassword:"",group:""});r({groupUserEditIsOpen:k,EditData:n});const l=U(),b=X({phone:[{required:!0,message:"请输入电话",trigger:"blur"}],department:[{required:!0,message:"请选择部门",trigger:"blur"}],sign_time:[{required:!0,message:"请选择入职时间",trigger:"blur"}],role_id:[{required:!0,trigger:"blur"}]}),C=_=>{if(_==0)n.value.department!=="物流部"&&n.value.group&&n.value.department&&(B.warning({message:"只能选择一个下拉框！",duration:1e3}),n.value.group="",n.value.department="");else{if(n.value.department==="物流部")return;n.value.group&&n.value.department&&(B.warning({message:"只能选择一个下拉框！",duration:1e3}),n.value.group="",n.value.department="")}},m=_=>{_&&(k.value=!1,_.resetFields())},A=(_,t)=>{if(!_)return;const u=new FormData;u.append("user_name",t.user_name),u.append("work_number",t.work_number),t.department!=="物流部"?u.append("department",t.department):t.department==="物流部"&&t.group&&u.append("group",t.group),u.append("phone",t.phone),u.append("role_id",t.role_id),console.log(t.signTime),u.append("signTime",t.signTime),t.newPassword?u.append("newPassword",t.newPassword):u.append("newPassword",""),t.email?u.append("email",t.email):u.append("email",""),c.updateUserInfoAction(u).then(v=>{v.code==200&&(g("renewUser"),k.value=!1,_.resetFields())})};return(_,t)=>{const u=se,v=te,V=de,s=ie,f=we,h=ue,I=W,$=pe;return y(),R("div",$t,[a($,{title:"编辑信息",modelValue:e(k),"onUpdate:modelValue":t[11]||(t[11]=o=>Q(k)?k.value=o:null),width:"80%",onClose:t[12]||(t[12]=o=>m(e(l)))},{default:d(()=>[w("div",Pt,[w("div",Dt,[a(h,{inline:!0,style:{height:"100%"},"label-width":"110",model:e(n),rules:e(b),ref_key:"userEditFormRef",ref:l},{default:d(()=>[a(u,{label:"工号"},{default:d(()=>[t[13]||(t[13]=w("span",{style:{color:"#cbfefe"}},":",-1)),w("span",St,O(e(n).work_number),1)]),_:1}),a(u,{label:"姓名"},{default:d(()=>[t[14]||(t[14]=w("span",{style:{color:"#cbfefe"}},":",-1)),w("span",Lt,O(e(n).user_name),1)]),_:1}),a(u,{label:"电话",prop:"phone"},{default:d(()=>[a(v,{modelValue:e(n).phone,"onUpdate:modelValue":t[0]||(t[0]=o=>e(n).phone=o),placeholder:"点击输入"},null,8,["modelValue"])]),_:1}),a(u,{label:"邮箱",prop:"email"},{default:d(()=>[a(v,{modelValue:e(n).email,"onUpdate:modelValue":t[1]||(t[1]=o=>e(n).email=o),placeholder:"点击输入"},null,8,["modelValue"])]),_:1}),a(u,{label:"部门",prop:"department"},{default:d(()=>[a(s,{modelValue:e(n).department,"onUpdate:modelValue":t[2]||(t[2]=o=>e(n).department=o),onChange:t[3]||(t[3]=o=>C(0))},{default:d(()=>[(y(!0),R(N,null,F(e(c).groupList,o=>(y(),P(V,{key:o,label:o,value:o},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(u,{label:"班组",prop:"group"},{default:d(()=>[a(s,{modelValue:e(n).group,"onUpdate:modelValue":t[4]||(t[4]=o=>e(n).group=o),onChange:t[5]||(t[5]=o=>C(2))},{default:d(()=>[(y(!0),R(N,null,F(e(c).gList,o=>(y(),P(V,{key:o,label:o,value:o},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(u,{label:"入职时间",prop:"sign_time"},{default:d(()=>[a(f,{modelValue:e(n).signTime,"onUpdate:modelValue":t[6]||(t[6]=o=>e(n).signTime=o),"value-format":"YYYY-MM-DD",type:"date",placeholder:"点击输入"},null,8,["modelValue"])]),_:1}),a(u,{label:"角色",prop:"role_id",class:"groupUserEditRadioGroup"},{default:d(()=>[a(s,{modelValue:e(n).role_id,"onUpdate:modelValue":t[7]||(t[7]=o=>e(n).role_id=o)},{default:d(()=>[(y(!0),R(N,null,F(e(c).roles,o=>(y(),P(V,{key:o.role_id,label:o.role_name,value:o.role_id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(u,{label:"新密码",prop:"newPassword"},{default:d(()=>[a(v,{modelValue:e(n).newPassword,"onUpdate:modelValue":t[8]||(t[8]=o=>e(n).newPassword=o)},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),w("div",Ot,[a(I,{class:"groupUserEditCancel",onClick:t[9]||(t[9]=o=>m(e(l)))},{default:d(()=>t[15]||(t[15]=[j("取消")])),_:1}),a(I,{class:"groupUserEditConfirm",onClick:t[10]||(t[10]=o=>A(e(l),e(n)))},{default:d(()=>t[16]||(t[16]=[j("确认")])),_:1})])])]),_:1},8,["modelValue"])])}}}),Ft=le(Nt,[["__scopeId","data-v-fb8d6ab8"]]),Gt={class:"Group"},Bt={class:"groupControl"},jt={class:"groupSearch"},Mt={class:"useInfo"},qt={class:"usePhoto"},Ht=["src"],Yt={class:"groupDialog"},xt=H({__name:"Group",setup(i){const r=oe(),p=U({department:"",userName:"",workNumber:"",group:"",role:""}),c=U(["领导","主管","部门负责人","经办人员","系统管理员"]);q(()=>p.value.department,V=>{V!=="物流部"&&(p.value.group="")}),q(()=>p.value.group,V=>{V&&p.value.department!=="物流部"&&(p.value.department="物流部")});const k=U(r.userInfoArr);r.getRoleAction();function g(){r.getAllUserAction({...p.value}).then(()=>{k.value=r.userInfoArr,k.value.length==0?_.value=!0:_.value=!1})}g();const n=U(),l=U(),b=U();function C(){var V;typeof((V=l.value)==null?void 0:V.groupAddOpen)=="boolean"&&(l.value.groupAddOpen=!0)}function m(){var V;typeof((V=n.value)==null?void 0:V.groupSettingOpen)=="boolean"&&(n.value.groupSettingOpen=!0)}function A(V){var s;typeof((s=b.value)==null?void 0:s.groupUserEditIsOpen)=="boolean"&&(b.value.groupUserEditIsOpen=!0),r.getEditUserInfoAction(V).then(()=>{if(b.value){const f={...r.editUserInfo[0],signTime:v(r.editUserInfo[0].sign_time)};f.department.includes("部")?f.group="":(f.group=f.department,f.department="物流部"),b.value.EditData=f}})}const _=U(!1);function t(){g()}function u(){p.value={department:"",userName:"",workNumber:"",group:""},g()}function v(V){const s=new Date(V),f=s.getFullYear(),h=String(s.getMonth()+1).padStart(2,"0"),I=String(s.getDate()).padStart(2,"0");return`${f}-${h}-${I}`}return(V,s)=>{const f=te,h=se,I=de,$=ie,o=ue,M=W,T=Oe,x=He("op");return y(),R("div",Gt,[w("div",Bt,[z((y(),R("div",jt,[a(o,{class:"groupSform",inline:!0,style:{height:"100%","margin-top":"7px"},modelValue:e(p),"onUpdate:modelValue":s[5]||(s[5]=E=>Q(p)?p.value=E:null)},{default:d(()=>[a(h,{label:"姓名"},{default:d(()=>[a(f,{modelValue:e(p).userName,"onUpdate:modelValue":s[0]||(s[0]=E=>e(p).userName=E),placeholder:"点击输入"},null,8,["modelValue"])]),_:1}),a(h,{label:"工号"},{default:d(()=>[a(f,{modelValue:e(p).workNumber,"onUpdate:modelValue":s[1]||(s[1]=E=>e(p).workNumber=E),placeholder:"点击输入"},null,8,["modelValue"])]),_:1}),a(h,{label:"部门"},{default:d(()=>[a($,{modelValue:e(p).department,"onUpdate:modelValue":s[2]||(s[2]=E=>e(p).department=E)},{default:d(()=>[(y(!0),R(N,null,F(e(r).groupList,E=>(y(),P(I,{key:E,value:E},null,8,["value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(h,{label:"班组"},{default:d(()=>[a($,{modelValue:e(p).group,"onUpdate:modelValue":s[3]||(s[3]=E=>e(p).group=E),disabled:e(p).department!=="物流部"},{default:d(()=>[(y(!0),R(N,null,F(e(r).gList,E=>(y(),P(I,{key:E,value:E},null,8,["value"]))),128))]),_:1},8,["modelValue","disabled"])]),_:1}),a(h,{label:"角色"},{default:d(()=>[a($,{modelValue:e(p).role,"onUpdate:modelValue":s[4]||(s[4]=E=>e(p).role=E)},{default:d(()=>[(y(!0),R(N,null,F(e(c),E=>(y(),P(I,{key:E,value:E},null,8,["value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["modelValue"])])),[[x,"user-service:view"]]),a(M,{class:"groupSearchButton",icon:e(he),onClick:t},null,8,["icon"]),a(M,{class:"groupSearchButton",icon:e(Pe),onClick:u},null,8,["icon"]),z((y(),P(M,{class:"groupSet",icon:e(De),onClick:m},{default:d(()=>s[6]||(s[6]=[j("设置权限")])),_:1},8,["icon"])),[[x,"user-service:set"]]),z((y(),P(M,{class:"groupAdd",icon:e(Se),onClick:C},{default:d(()=>s[7]||(s[7]=[j("添加用户")])),_:1},8,["icon"])),[[x,"user-service:add"]])]),w("div",Mt,[e(_)?(y(),P(T,{key:0,class:"empty",description:"查无此人"})):(y(!0),R(N,{key:1},F(e(k),E=>(y(),R("div",{class:"useInfoItem",key:E.work_number},[w("div",qt,[w("img",{alt:"",src:E.avatar_path},null,8,Ht),z(a(M,{class:"groupInfoItemButton",icon:e(Le),onClick:Kt=>A(E.work_number)},null,8,["icon","onClick"]),[[x,"user-service:update"]])]),w("div",null,O(E.user_name),1),w("div",null,O(E.department.includes("组")?"物流部  ":"")+O(E.department)+"  "+O(E.position),1),w("div",null,"工号："+O(E.work_number),1)]))),128))]),w("div",Yt,[a(At,{ref_key:"groupSettingRef",ref:n},null,512),a(It,{ref_key:"groupAddRef",ref:l,onRenewUser:t},null,512),a(Ft,{ref_key:"groupUserEditRef",ref:b,onRenewUser:t},null,512)])])}}}),ml=le(xt,[["__scopeId","data-v-15011c0f"]]);export{ml as default};
