package com.ict.datamanagement.domain.dto.store;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("商铺更新行政区表单")
public class UpdateAreaRequest {

    @ApiModelProperty(value = "商铺Id集合",dataType = "List<Long>",required = true)
    private List<Long> storeIdList;

    @ApiModelProperty(value = "行政区Id",dataType = "Long",required = true)
    private Long areaId;

}
