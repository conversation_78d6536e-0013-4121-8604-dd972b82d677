var jt=Object.defineProperty;var Mt=(e,t,n)=>t in e?jt(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var ve=(e,t,n)=>Mt(e,typeof t!="symbol"?t+"":t,n);import{m as G,r as K,k,L as Ht,f as zt,h as Le,bf as $t,d as ct,aj as Vt,z as Jt,o as D,p as J,w as pe,X as Wt,a as Be,t as M,u as A,n as Kt,K as Y,q as Gt,s as Qt,c as ke,S as Xt,N as Zt,M as Yt,b as ut,a5 as en,ad as tn,l as Ue,ak as lt,a1 as ge,i as nn,bg as De,ao as sn,am as Ie,bh as rn}from"./index-C0QCllTd.js";import{an as on,ao as ft,ap as dt,aj as pt,aq as an,ar as cn,N as Ce,ai as un,as as ln,at as fn,i as dn,M as qe,f as pn,E as je,_ as mn,T as hn,ag as yn}from"./base-kpSIrADU.js";function H(e){var t;const n=dt(e);return(t=n==null?void 0:n.$el)!=null?t:n}const _e=Ce?window:void 0;function ee(...e){let t,n,s,r;if(on(e[0])||Array.isArray(e[0])?([n,s,r]=e,t=_e):[t,n,s,r]=e,!t)return ft;Array.isArray(n)||(n=[n]),Array.isArray(s)||(s=[s]);const i=[],o=()=>{i.forEach(u=>u()),i.length=0},c=(u,p,m,b)=>(u.addEventListener(p,m,b),()=>u.removeEventListener(p,m,b)),f=G(()=>[H(t),dt(r)],([u,p])=>{o(),u&&i.push(...n.flatMap(m=>s.map(b=>c(u,m,b,p))))},{immediate:!0,flush:"post"}),l=()=>{f(),o()};return pt(l),l}let Me=!1;function Ar(e,t,n={}){const{window:s=_e,ignore:r=[],capture:i=!0,detectIframe:o=!1}=n;if(!s)return;un&&!Me&&(Me=!0,Array.from(s.document.body.children).forEach(m=>m.addEventListener("click",ft)));let c=!0;const f=m=>r.some(b=>{if(typeof b=="string")return Array.from(s.document.querySelectorAll(b)).some(d=>d===m.target||m.composedPath().includes(d));{const d=H(b);return d&&(m.target===d||m.composedPath().includes(d))}}),u=[ee(s,"click",m=>{const b=H(e);if(!(!b||b===m.target||m.composedPath().includes(b))){if(m.detail===0&&(c=!f(m)),!c){c=!0;return}t(m)}},{passive:!0,capture:i}),ee(s,"pointerdown",m=>{const b=H(e);b&&(c=!m.composedPath().includes(b)&&!f(m))},{passive:!0}),o&&ee(s,"blur",m=>{var b;const d=H(e);((b=s.document.activeElement)==null?void 0:b.tagName)==="IFRAME"&&!(d!=null&&d.contains(s.document.activeElement))&&t(m)})].filter(Boolean);return()=>u.forEach(m=>m())}function bn(e,t=!1){const n=K(),s=()=>n.value=!!e();return s(),an(s,t),n}function gn(e){return JSON.parse(JSON.stringify(e))}const He=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},ze="__vueuse_ssr_handlers__";He[ze]=He[ze]||{};var $e=Object.getOwnPropertySymbols,wn=Object.prototype.hasOwnProperty,En=Object.prototype.propertyIsEnumerable,Sn=(e,t)=>{var n={};for(var s in e)wn.call(e,s)&&t.indexOf(s)<0&&(n[s]=e[s]);if(e!=null&&$e)for(var s of $e(e))t.indexOf(s)<0&&En.call(e,s)&&(n[s]=e[s]);return n};function On(e,t,n={}){const s=n,{window:r=_e}=s,i=Sn(s,["window"]);let o;const c=bn(()=>r&&"ResizeObserver"in r),f=()=>{o&&(o.disconnect(),o=void 0)},l=G(()=>H(e),p=>{f(),c.value&&r&&p&&(o=new ResizeObserver(t),o.observe(p,i))},{immediate:!0,flush:"post"}),u=()=>{f(),l()};return pt(u),{isSupported:c,stop:u}}var Ve;(function(e){e.UP="UP",e.RIGHT="RIGHT",e.DOWN="DOWN",e.LEFT="LEFT",e.NONE="NONE"})(Ve||(Ve={}));var Rn=Object.defineProperty,Je=Object.getOwnPropertySymbols,Tn=Object.prototype.hasOwnProperty,An=Object.prototype.propertyIsEnumerable,We=(e,t,n)=>t in e?Rn(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,Cn=(e,t)=>{for(var n in t)Tn.call(t,n)&&We(e,n,t[n]);if(Je)for(var n of Je(t))An.call(t,n)&&We(e,n,t[n]);return e};const _n={easeInSine:[.12,0,.39,0],easeOutSine:[.61,1,.88,1],easeInOutSine:[.37,0,.63,1],easeInQuad:[.11,0,.5,0],easeOutQuad:[.5,1,.89,1],easeInOutQuad:[.45,0,.55,1],easeInCubic:[.32,0,.67,0],easeOutCubic:[.33,1,.68,1],easeInOutCubic:[.65,0,.35,1],easeInQuart:[.5,0,.75,0],easeOutQuart:[.25,1,.5,1],easeInOutQuart:[.76,0,.24,1],easeInQuint:[.64,0,.78,0],easeOutQuint:[.22,1,.36,1],easeInOutQuint:[.83,0,.17,1],easeInExpo:[.7,0,.84,0],easeOutExpo:[.16,1,.3,1],easeInOutExpo:[.87,0,.13,1],easeInCirc:[.55,0,1,.45],easeOutCirc:[0,.55,.45,1],easeInOutCirc:[.85,0,.15,1],easeInBack:[.36,0,.66,-.56],easeOutBack:[.34,1.56,.64,1],easeInOutBack:[.68,-.6,.32,1.6]};Cn({linear:cn},_n);function Cr(e,t,n,s={}){var r,i,o;const{clone:c=!1,passive:f=!1,eventName:l,deep:u=!1,defaultValue:p}=s,m=Ht(),b=(m==null?void 0:m.emit)||((r=m==null?void 0:m.$emit)==null?void 0:r.bind(m))||((o=(i=m==null?void 0:m.proxy)==null?void 0:i.$emit)==null?void 0:o.bind(m==null?void 0:m.proxy));let d=l;d=l||d||`update:${t.toString()}`;const y=w=>c?ln(c)?c(w):gn(w):w,h=()=>fn(e[t])?y(e[t]):p;if(f){const w=h(),O=K(w);return G(()=>e[t],S=>O.value=y(S)),G(O,S=>{(S!==e[t]||u)&&b(d,S)},{deep:u}),O}else return k({get(){return h()},set(w){b(d,w)}})}const Nn={tab:"Tab",enter:"Enter",space:"Space",left:"ArrowLeft",up:"ArrowUp",right:"ArrowRight",down:"ArrowDown",esc:"Escape",delete:"Delete",backspace:"Backspace",numpadEnter:"NumpadEnter",pageUp:"PageUp",pageDown:"PageDown",home:"Home",end:"End"},xn=e=>e,mt=["success","info","warning","error"],_=xn({customClass:"",center:!1,dangerouslyUseHTMLString:!1,duration:3e3,icon:void 0,id:"",message:"",onClose:void 0,showClose:!1,type:"info",offset:16,zIndex:0,grouping:!1,repeatNum:1,appendTo:Ce?document.body:void 0}),Pn=zt({customClass:{type:String,default:_.customClass},center:{type:Boolean,default:_.center},dangerouslyUseHTMLString:{type:Boolean,default:_.dangerouslyUseHTMLString},duration:{type:Number,default:_.duration},icon:{type:dn,default:_.icon},id:{type:String,default:_.id},message:{type:Le([String,Object,Function]),default:_.message},onClose:{type:Le(Function),required:!1},showClose:{type:Boolean,default:_.showClose},type:{type:String,values:mt,default:_.type},offset:{type:Number,default:_.offset},zIndex:{type:Number,default:_.zIndex},grouping:{type:Boolean,default:_.grouping},repeatNum:{type:Number,default:_.repeatNum}}),Fn={destroy:()=>!0},F=$t([]),vn=e=>{const t=F.findIndex(r=>r.id===e),n=F[t];let s;return t>0&&(s=F[t-1]),{current:n,prev:s}},Ln=e=>{const{prev:t}=vn(e);return t?t.vm.exposed.bottom.value:0},Bn=(e,t)=>F.findIndex(s=>s.id===e)>0?20:t,kn=["id"],Un=["innerHTML"],Dn=ct({name:"ElMessage"}),In=ct({...Dn,props:Pn,emits:Fn,setup(e,{expose:t}){const n=e,{Close:s}=hn,{ns:r,zIndex:i}=Vt("message"),{currentZIndex:o,nextZIndex:c}=i,f=K(),l=K(!1),u=K(0);let p;const m=k(()=>n.type?n.type==="error"?"danger":n.type:"info"),b=k(()=>{const E=n.type;return{[r.bm("icon",E)]:E&&qe[E]}}),d=k(()=>n.icon||qe[n.type]||""),y=k(()=>Ln(n.id)),h=k(()=>Bn(n.id,n.offset)+y.value),w=k(()=>u.value+h.value),O=k(()=>({top:`${h.value}px`,zIndex:o.value}));function S(){n.duration!==0&&({stop:p}=yn(()=>{T()},n.duration))}function P(){p==null||p()}function T(){l.value=!1}function L({code:E}){E===Nn.esc&&T()}return Jt(()=>{S(),c(),l.value=!0}),G(()=>n.repeatNum,()=>{P(),S()}),ee(document,"keydown",L),On(f,()=>{u.value=f.value.getBoundingClientRect().height}),t({visible:l,bottom:w,close:T}),(E,U)=>(D(),J(tn,{name:A(r).b("fade"),onBeforeLeave:E.onClose,onAfterLeave:U[0]||(U[0]=de=>E.$emit("destroy")),persisted:""},{default:pe(()=>[Wt(Be("div",{id:E.id,ref_key:"messageRef",ref:f,class:M([A(r).b(),{[A(r).m(E.type)]:E.type&&!E.icon},A(r).is("center",E.center),A(r).is("closable",E.showClose),E.customClass]),style:Kt(A(O)),role:"alert",onMouseenter:P,onMouseleave:S},[E.repeatNum>1?(D(),J(A(pn),{key:0,value:E.repeatNum,type:A(m),class:M(A(r).e("badge"))},null,8,["value","type","class"])):Y("v-if",!0),A(d)?(D(),J(A(je),{key:1,class:M([A(r).e("icon"),A(b)])},{default:pe(()=>[(D(),J(Gt(A(d))))]),_:1},8,["class"])):Y("v-if",!0),Qt(E.$slots,"default",{},()=>[E.dangerouslyUseHTMLString?(D(),ke(Zt,{key:1},[Y(" Caution here, message could've been compromised, never use user's input as message "),Be("p",{class:M(A(r).e("content")),innerHTML:E.message},null,10,Un)],2112)):(D(),ke("p",{key:0,class:M(A(r).e("content"))},Xt(E.message),3))]),E.showClose?(D(),J(A(je),{key:2,class:M(A(r).e("closeBtn")),onClick:Yt(T,["stop"])},{default:pe(()=>[ut(A(s))]),_:1},8,["class","onClick"])):Y("v-if",!0)],46,kn),[[en,l.value]])]),_:3},8,["name","onBeforeLeave"]))}});var qn=mn(In,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/message/src/message.vue"]]);let jn=1;const ht=e=>{const t=!e||Ue(e)||lt(e)||ge(e)?{message:e}:e,n={..._,...t};if(!n.appendTo)n.appendTo=document.body;else if(Ue(n.appendTo)){let s=document.querySelector(n.appendTo);sn(s)||(s=document.body),n.appendTo=s}return n},Mn=e=>{const t=F.indexOf(e);if(t===-1)return;F.splice(t,1);const{handler:n}=e;n.close()},Hn=({appendTo:e,...t},n)=>{const s=`message_${jn++}`,r=t.onClose,i=document.createElement("div"),o={...t,id:s,onClose:()=>{r==null||r(),Mn(u)},onDestroy:()=>{Ie(null,i)}},c=ut(qn,o,ge(o.message)||lt(o.message)?{default:ge(o.message)?o.message:()=>o.message}:null);c.appContext=n||z._context,Ie(c,i),e.appendChild(i.firstElementChild);const f=c.component,u={id:s,vnode:c,vm:f,handler:{close:()=>{f.exposed.visible.value=!1}},props:c.component.props};return u},z=(e={},t)=>{if(!Ce)return{close:()=>{}};if(nn(De.max)&&F.length>=De.max)return{close:()=>{}};const n=ht(e);if(n.grouping&&F.length){const r=F.find(({vnode:i})=>{var o;return((o=i.props)==null?void 0:o.message)===n.message});if(r)return r.props.repeatNum+=1,r.props.type=n.type,r.handler}const s=Hn(n,t);return F.push(s),s.handler};mt.forEach(e=>{z[e]=(t={},n)=>{const s=ht(t);return z({...s,type:e},n)}});function zn(e){for(const t of F)(!e||e===t.props.type)&&t.handler.close()}z.closeAll=zn;z._context=null;const we=rn(z,"$message"),$n="http://127.0.0.1:8080",Vn=1e4;function yt(e,t){return function(){return e.apply(t,arguments)}}const{toString:Jn}=Object.prototype,{getPrototypeOf:Ne}=Object,ie=(e=>t=>{const n=Jn.call(t);return e[n]||(e[n]=n.slice(8,-1).toLowerCase())})(Object.create(null)),v=e=>(e=e.toLowerCase(),t=>ie(t)===e),ae=e=>t=>typeof t===e,{isArray:$}=Array,Q=ae("undefined");function Wn(e){return e!==null&&!Q(e)&&e.constructor!==null&&!Q(e.constructor)&&x(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const bt=v("ArrayBuffer");function Kn(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&bt(e.buffer),t}const Gn=ae("string"),x=ae("function"),gt=ae("number"),ce=e=>e!==null&&typeof e=="object",Qn=e=>e===!0||e===!1,te=e=>{if(ie(e)!=="object")return!1;const t=Ne(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},Xn=v("Date"),Zn=v("File"),Yn=v("Blob"),es=v("FileList"),ts=e=>ce(e)&&x(e.pipe),ns=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||x(e.append)&&((t=ie(e))==="formdata"||t==="object"&&x(e.toString)&&e.toString()==="[object FormData]"))},ss=v("URLSearchParams"),[rs,os,is,as]=["ReadableStream","Request","Response","Headers"].map(v),cs=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function X(e,t,{allOwnKeys:n=!1}={}){if(e===null||typeof e>"u")return;let s,r;if(typeof e!="object"&&(e=[e]),$(e))for(s=0,r=e.length;s<r;s++)t.call(null,e[s],s,e);else{const i=n?Object.getOwnPropertyNames(e):Object.keys(e),o=i.length;let c;for(s=0;s<o;s++)c=i[s],t.call(null,e[c],c,e)}}function wt(e,t){t=t.toLowerCase();const n=Object.keys(e);let s=n.length,r;for(;s-- >0;)if(r=n[s],t===r.toLowerCase())return r;return null}const I=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Et=e=>!Q(e)&&e!==I;function Ee(){const{caseless:e}=Et(this)&&this||{},t={},n=(s,r)=>{const i=e&&wt(t,r)||r;te(t[i])&&te(s)?t[i]=Ee(t[i],s):te(s)?t[i]=Ee({},s):$(s)?t[i]=s.slice():t[i]=s};for(let s=0,r=arguments.length;s<r;s++)arguments[s]&&X(arguments[s],n);return t}const us=(e,t,n,{allOwnKeys:s}={})=>(X(t,(r,i)=>{n&&x(r)?e[i]=yt(r,n):e[i]=r},{allOwnKeys:s}),e),ls=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),fs=(e,t,n,s)=>{e.prototype=Object.create(t.prototype,s),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},ds=(e,t,n,s)=>{let r,i,o;const c={};if(t=t||{},e==null)return t;do{for(r=Object.getOwnPropertyNames(e),i=r.length;i-- >0;)o=r[i],(!s||s(o,e,t))&&!c[o]&&(t[o]=e[o],c[o]=!0);e=n!==!1&&Ne(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},ps=(e,t,n)=>{e=String(e),(n===void 0||n>e.length)&&(n=e.length),n-=t.length;const s=e.indexOf(t,n);return s!==-1&&s===n},ms=e=>{if(!e)return null;if($(e))return e;let t=e.length;if(!gt(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},hs=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&Ne(Uint8Array)),ys=(e,t)=>{const s=(e&&e[Symbol.iterator]).call(e);let r;for(;(r=s.next())&&!r.done;){const i=r.value;t.call(e,i[0],i[1])}},bs=(e,t)=>{let n;const s=[];for(;(n=e.exec(t))!==null;)s.push(n);return s},gs=v("HTMLFormElement"),ws=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(n,s,r){return s.toUpperCase()+r}),Ke=(({hasOwnProperty:e})=>(t,n)=>e.call(t,n))(Object.prototype),Es=v("RegExp"),St=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),s={};X(n,(r,i)=>{let o;(o=t(r,i,e))!==!1&&(s[i]=o||r)}),Object.defineProperties(e,s)},Ss=e=>{St(e,(t,n)=>{if(x(e)&&["arguments","caller","callee"].indexOf(n)!==-1)return!1;const s=e[n];if(x(s)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")})}})},Os=(e,t)=>{const n={},s=r=>{r.forEach(i=>{n[i]=!0})};return $(e)?s(e):s(String(e).split(t)),n},Rs=()=>{},Ts=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t,me="abcdefghijklmnopqrstuvwxyz",Ge="0123456789",Ot={DIGIT:Ge,ALPHA:me,ALPHA_DIGIT:me+me.toUpperCase()+Ge},As=(e=16,t=Ot.ALPHA_DIGIT)=>{let n="";const{length:s}=t;for(;e--;)n+=t[Math.random()*s|0];return n};function Cs(e){return!!(e&&x(e.append)&&e[Symbol.toStringTag]==="FormData"&&e[Symbol.iterator])}const _s=e=>{const t=new Array(10),n=(s,r)=>{if(ce(s)){if(t.indexOf(s)>=0)return;if(!("toJSON"in s)){t[r]=s;const i=$(s)?[]:{};return X(s,(o,c)=>{const f=n(o,r+1);!Q(f)&&(i[c]=f)}),t[r]=void 0,i}}return s};return n(e,0)},Ns=v("AsyncFunction"),xs=e=>e&&(ce(e)||x(e))&&x(e.then)&&x(e.catch),Rt=((e,t)=>e?setImmediate:t?((n,s)=>(I.addEventListener("message",({source:r,data:i})=>{r===I&&i===n&&s.length&&s.shift()()},!1),r=>{s.push(r),I.postMessage(n,"*")}))(`axios@${Math.random()}`,[]):n=>setTimeout(n))(typeof setImmediate=="function",x(I.postMessage)),Ps=typeof queueMicrotask<"u"?queueMicrotask.bind(I):typeof process<"u"&&process.nextTick||Rt,a={isArray:$,isArrayBuffer:bt,isBuffer:Wn,isFormData:ns,isArrayBufferView:Kn,isString:Gn,isNumber:gt,isBoolean:Qn,isObject:ce,isPlainObject:te,isReadableStream:rs,isRequest:os,isResponse:is,isHeaders:as,isUndefined:Q,isDate:Xn,isFile:Zn,isBlob:Yn,isRegExp:Es,isFunction:x,isStream:ts,isURLSearchParams:ss,isTypedArray:hs,isFileList:es,forEach:X,merge:Ee,extend:us,trim:cs,stripBOM:ls,inherits:fs,toFlatObject:ds,kindOf:ie,kindOfTest:v,endsWith:ps,toArray:ms,forEachEntry:ys,matchAll:bs,isHTMLForm:gs,hasOwnProperty:Ke,hasOwnProp:Ke,reduceDescriptors:St,freezeMethods:Ss,toObjectSet:Os,toCamelCase:ws,noop:Rs,toFiniteNumber:Ts,findKey:wt,global:I,isContextDefined:Et,ALPHABET:Ot,generateString:As,isSpecCompliantForm:Cs,toJSONObject:_s,isAsyncFn:Ns,isThenable:xs,setImmediate:Rt,asap:Ps};function g(e,t,n,s,r){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),s&&(this.request=s),r&&(this.response=r,this.status=r.status?r.status:null)}a.inherits(g,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:a.toJSONObject(this.config),code:this.code,status:this.status}}});const Tt=g.prototype,At={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{At[e]={value:e}});Object.defineProperties(g,At);Object.defineProperty(Tt,"isAxiosError",{value:!0});g.from=(e,t,n,s,r,i)=>{const o=Object.create(Tt);return a.toFlatObject(e,o,function(f){return f!==Error.prototype},c=>c!=="isAxiosError"),g.call(o,e.message,t,n,s,r),o.cause=e,o.name=e.name,i&&Object.assign(o,i),o};const Fs=null;function Se(e){return a.isPlainObject(e)||a.isArray(e)}function Ct(e){return a.endsWith(e,"[]")?e.slice(0,-2):e}function Qe(e,t,n){return e?e.concat(t).map(function(r,i){return r=Ct(r),!n&&i?"["+r+"]":r}).join(n?".":""):t}function vs(e){return a.isArray(e)&&!e.some(Se)}const Ls=a.toFlatObject(a,{},null,function(t){return/^is[A-Z]/.test(t)});function ue(e,t,n){if(!a.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,n=a.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,function(y,h){return!a.isUndefined(h[y])});const s=n.metaTokens,r=n.visitor||u,i=n.dots,o=n.indexes,f=(n.Blob||typeof Blob<"u"&&Blob)&&a.isSpecCompliantForm(t);if(!a.isFunction(r))throw new TypeError("visitor must be a function");function l(d){if(d===null)return"";if(a.isDate(d))return d.toISOString();if(!f&&a.isBlob(d))throw new g("Blob is not supported. Use a Buffer instead.");return a.isArrayBuffer(d)||a.isTypedArray(d)?f&&typeof Blob=="function"?new Blob([d]):Buffer.from(d):d}function u(d,y,h){let w=d;if(d&&!h&&typeof d=="object"){if(a.endsWith(y,"{}"))y=s?y:y.slice(0,-2),d=JSON.stringify(d);else if(a.isArray(d)&&vs(d)||(a.isFileList(d)||a.endsWith(y,"[]"))&&(w=a.toArray(d)))return y=Ct(y),w.forEach(function(S,P){!(a.isUndefined(S)||S===null)&&t.append(o===!0?Qe([y],P,i):o===null?y:y+"[]",l(S))}),!1}return Se(d)?!0:(t.append(Qe(h,y,i),l(d)),!1)}const p=[],m=Object.assign(Ls,{defaultVisitor:u,convertValue:l,isVisitable:Se});function b(d,y){if(!a.isUndefined(d)){if(p.indexOf(d)!==-1)throw Error("Circular reference detected in "+y.join("."));p.push(d),a.forEach(d,function(w,O){(!(a.isUndefined(w)||w===null)&&r.call(t,w,a.isString(O)?O.trim():O,y,m))===!0&&b(w,y?y.concat(O):[O])}),p.pop()}}if(!a.isObject(e))throw new TypeError("data must be an object");return b(e),t}function Xe(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(s){return t[s]})}function xe(e,t){this._pairs=[],e&&ue(e,this,t)}const _t=xe.prototype;_t.append=function(t,n){this._pairs.push([t,n])};_t.toString=function(t){const n=t?function(s){return t.call(this,s,Xe)}:Xe;return this._pairs.map(function(r){return n(r[0])+"="+n(r[1])},"").join("&")};function Bs(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Nt(e,t,n){if(!t)return e;const s=n&&n.encode||Bs;a.isFunction(n)&&(n={serialize:n});const r=n&&n.serialize;let i;if(r?i=r(t,n):i=a.isURLSearchParams(t)?t.toString():new xe(t,n).toString(s),i){const o=e.indexOf("#");o!==-1&&(e=e.slice(0,o)),e+=(e.indexOf("?")===-1?"?":"&")+i}return e}class Ze{constructor(){this.handlers=[]}use(t,n,s){return this.handlers.push({fulfilled:t,rejected:n,synchronous:s?s.synchronous:!1,runWhen:s?s.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){a.forEach(this.handlers,function(s){s!==null&&t(s)})}}const xt={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ks=typeof URLSearchParams<"u"?URLSearchParams:xe,Us=typeof FormData<"u"?FormData:null,Ds=typeof Blob<"u"?Blob:null,Is={isBrowser:!0,classes:{URLSearchParams:ks,FormData:Us,Blob:Ds},protocols:["http","https","file","blob","url","data"]},Pe=typeof window<"u"&&typeof document<"u",Oe=typeof navigator=="object"&&navigator||void 0,qs=Pe&&(!Oe||["ReactNative","NativeScript","NS"].indexOf(Oe.product)<0),js=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Ms=Pe&&window.location.href||"http://localhost",Hs=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:Pe,hasStandardBrowserEnv:qs,hasStandardBrowserWebWorkerEnv:js,navigator:Oe,origin:Ms},Symbol.toStringTag,{value:"Module"})),C={...Hs,...Is};function zs(e,t){return ue(e,new C.classes.URLSearchParams,Object.assign({visitor:function(n,s,r,i){return C.isNode&&a.isBuffer(n)?(this.append(s,n.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)}},t))}function $s(e){return a.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Vs(e){const t={},n=Object.keys(e);let s;const r=n.length;let i;for(s=0;s<r;s++)i=n[s],t[i]=e[i];return t}function Pt(e){function t(n,s,r,i){let o=n[i++];if(o==="__proto__")return!0;const c=Number.isFinite(+o),f=i>=n.length;return o=!o&&a.isArray(r)?r.length:o,f?(a.hasOwnProp(r,o)?r[o]=[r[o],s]:r[o]=s,!c):((!r[o]||!a.isObject(r[o]))&&(r[o]=[]),t(n,s,r[o],i)&&a.isArray(r[o])&&(r[o]=Vs(r[o])),!c)}if(a.isFormData(e)&&a.isFunction(e.entries)){const n={};return a.forEachEntry(e,(s,r)=>{t($s(s),r,n,0)}),n}return null}function Js(e,t,n){if(a.isString(e))try{return(t||JSON.parse)(e),a.trim(e)}catch(s){if(s.name!=="SyntaxError")throw s}return(n||JSON.stringify)(e)}const Z={transitional:xt,adapter:["xhr","http","fetch"],transformRequest:[function(t,n){const s=n.getContentType()||"",r=s.indexOf("application/json")>-1,i=a.isObject(t);if(i&&a.isHTMLForm(t)&&(t=new FormData(t)),a.isFormData(t))return r?JSON.stringify(Pt(t)):t;if(a.isArrayBuffer(t)||a.isBuffer(t)||a.isStream(t)||a.isFile(t)||a.isBlob(t)||a.isReadableStream(t))return t;if(a.isArrayBufferView(t))return t.buffer;if(a.isURLSearchParams(t))return n.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let c;if(i){if(s.indexOf("application/x-www-form-urlencoded")>-1)return zs(t,this.formSerializer).toString();if((c=a.isFileList(t))||s.indexOf("multipart/form-data")>-1){const f=this.env&&this.env.FormData;return ue(c?{"files[]":t}:t,f&&new f,this.formSerializer)}}return i||r?(n.setContentType("application/json",!1),Js(t)):t}],transformResponse:[function(t){const n=this.transitional||Z.transitional,s=n&&n.forcedJSONParsing,r=this.responseType==="json";if(a.isResponse(t)||a.isReadableStream(t))return t;if(t&&a.isString(t)&&(s&&!this.responseType||r)){const o=!(n&&n.silentJSONParsing)&&r;try{return JSON.parse(t)}catch(c){if(o)throw c.name==="SyntaxError"?g.from(c,g.ERR_BAD_RESPONSE,this,null,this.response):c}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:C.classes.FormData,Blob:C.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};a.forEach(["delete","get","head","post","put","patch"],e=>{Z.headers[e]={}});const Ws=a.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Ks=e=>{const t={};let n,s,r;return e&&e.split(`
`).forEach(function(o){r=o.indexOf(":"),n=o.substring(0,r).trim().toLowerCase(),s=o.substring(r+1).trim(),!(!n||t[n]&&Ws[n])&&(n==="set-cookie"?t[n]?t[n].push(s):t[n]=[s]:t[n]=t[n]?t[n]+", "+s:s)}),t},Ye=Symbol("internals");function W(e){return e&&String(e).trim().toLowerCase()}function ne(e){return e===!1||e==null?e:a.isArray(e)?e.map(ne):String(e)}function Gs(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let s;for(;s=n.exec(e);)t[s[1]]=s[2];return t}const Qs=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function he(e,t,n,s,r){if(a.isFunction(s))return s.call(this,t,n);if(r&&(t=n),!!a.isString(t)){if(a.isString(s))return t.indexOf(s)!==-1;if(a.isRegExp(s))return s.test(t)}}function Xs(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,n,s)=>n.toUpperCase()+s)}function Zs(e,t){const n=a.toCamelCase(" "+t);["get","set","has"].forEach(s=>{Object.defineProperty(e,s+n,{value:function(r,i,o){return this[s].call(this,t,r,i,o)},configurable:!0})})}class N{constructor(t){t&&this.set(t)}set(t,n,s){const r=this;function i(c,f,l){const u=W(f);if(!u)throw new Error("header name must be a non-empty string");const p=a.findKey(r,u);(!p||r[p]===void 0||l===!0||l===void 0&&r[p]!==!1)&&(r[p||f]=ne(c))}const o=(c,f)=>a.forEach(c,(l,u)=>i(l,u,f));if(a.isPlainObject(t)||t instanceof this.constructor)o(t,n);else if(a.isString(t)&&(t=t.trim())&&!Qs(t))o(Ks(t),n);else if(a.isHeaders(t))for(const[c,f]of t.entries())i(f,c,s);else t!=null&&i(n,t,s);return this}get(t,n){if(t=W(t),t){const s=a.findKey(this,t);if(s){const r=this[s];if(!n)return r;if(n===!0)return Gs(r);if(a.isFunction(n))return n.call(this,r,s);if(a.isRegExp(n))return n.exec(r);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,n){if(t=W(t),t){const s=a.findKey(this,t);return!!(s&&this[s]!==void 0&&(!n||he(this,this[s],s,n)))}return!1}delete(t,n){const s=this;let r=!1;function i(o){if(o=W(o),o){const c=a.findKey(s,o);c&&(!n||he(s,s[c],c,n))&&(delete s[c],r=!0)}}return a.isArray(t)?t.forEach(i):i(t),r}clear(t){const n=Object.keys(this);let s=n.length,r=!1;for(;s--;){const i=n[s];(!t||he(this,this[i],i,t,!0))&&(delete this[i],r=!0)}return r}normalize(t){const n=this,s={};return a.forEach(this,(r,i)=>{const o=a.findKey(s,i);if(o){n[o]=ne(r),delete n[i];return}const c=t?Xs(i):String(i).trim();c!==i&&delete n[i],n[c]=ne(r),s[c]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const n=Object.create(null);return a.forEach(this,(s,r)=>{s!=null&&s!==!1&&(n[r]=t&&a.isArray(s)?s.join(", "):s)}),n}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,n])=>t+": "+n).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...n){const s=new this(t);return n.forEach(r=>s.set(r)),s}static accessor(t){const s=(this[Ye]=this[Ye]={accessors:{}}).accessors,r=this.prototype;function i(o){const c=W(o);s[c]||(Zs(r,o),s[c]=!0)}return a.isArray(t)?t.forEach(i):i(t),this}}N.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);a.reduceDescriptors(N.prototype,({value:e},t)=>{let n=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(s){this[n]=s}}});a.freezeMethods(N);function ye(e,t){const n=this||Z,s=t||n,r=N.from(s.headers);let i=s.data;return a.forEach(e,function(c){i=c.call(n,i,r.normalize(),t?t.status:void 0)}),r.normalize(),i}function Ft(e){return!!(e&&e.__CANCEL__)}function V(e,t,n){g.call(this,e??"canceled",g.ERR_CANCELED,t,n),this.name="CanceledError"}a.inherits(V,g,{__CANCEL__:!0});function vt(e,t,n){const s=n.config.validateStatus;!n.status||!s||s(n.status)?e(n):t(new g("Request failed with status code "+n.status,[g.ERR_BAD_REQUEST,g.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n))}function Ys(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function er(e,t){e=e||10;const n=new Array(e),s=new Array(e);let r=0,i=0,o;return t=t!==void 0?t:1e3,function(f){const l=Date.now(),u=s[i];o||(o=l),n[r]=f,s[r]=l;let p=i,m=0;for(;p!==r;)m+=n[p++],p=p%e;if(r=(r+1)%e,r===i&&(i=(i+1)%e),l-o<t)return;const b=u&&l-u;return b?Math.round(m*1e3/b):void 0}}function tr(e,t){let n=0,s=1e3/t,r,i;const o=(l,u=Date.now())=>{n=u,r=null,i&&(clearTimeout(i),i=null),e.apply(null,l)};return[(...l)=>{const u=Date.now(),p=u-n;p>=s?o(l,u):(r=l,i||(i=setTimeout(()=>{i=null,o(r)},s-p)))},()=>r&&o(r)]}const re=(e,t,n=3)=>{let s=0;const r=er(50,250);return tr(i=>{const o=i.loaded,c=i.lengthComputable?i.total:void 0,f=o-s,l=r(f),u=o<=c;s=o;const p={loaded:o,total:c,progress:c?o/c:void 0,bytes:f,rate:l||void 0,estimated:l&&c&&u?(c-o)/l:void 0,event:i,lengthComputable:c!=null,[t?"download":"upload"]:!0};e(p)},n)},et=(e,t)=>{const n=e!=null;return[s=>t[0]({lengthComputable:n,total:e,loaded:s}),t[1]]},tt=e=>(...t)=>a.asap(()=>e(...t)),nr=C.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,C.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(C.origin),C.navigator&&/(msie|trident)/i.test(C.navigator.userAgent)):()=>!0,sr=C.hasStandardBrowserEnv?{write(e,t,n,s,r,i){const o=[e+"="+encodeURIComponent(t)];a.isNumber(n)&&o.push("expires="+new Date(n).toGMTString()),a.isString(s)&&o.push("path="+s),a.isString(r)&&o.push("domain="+r),i===!0&&o.push("secure"),document.cookie=o.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function rr(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function or(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Lt(e,t){return e&&!rr(t)?or(e,t):t}const nt=e=>e instanceof N?{...e}:e;function j(e,t){t=t||{};const n={};function s(l,u,p,m){return a.isPlainObject(l)&&a.isPlainObject(u)?a.merge.call({caseless:m},l,u):a.isPlainObject(u)?a.merge({},u):a.isArray(u)?u.slice():u}function r(l,u,p,m){if(a.isUndefined(u)){if(!a.isUndefined(l))return s(void 0,l,p,m)}else return s(l,u,p,m)}function i(l,u){if(!a.isUndefined(u))return s(void 0,u)}function o(l,u){if(a.isUndefined(u)){if(!a.isUndefined(l))return s(void 0,l)}else return s(void 0,u)}function c(l,u,p){if(p in t)return s(l,u);if(p in e)return s(void 0,l)}const f={url:i,method:i,data:i,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:c,headers:(l,u,p)=>r(nt(l),nt(u),p,!0)};return a.forEach(Object.keys(Object.assign({},e,t)),function(u){const p=f[u]||r,m=p(e[u],t[u],u);a.isUndefined(m)&&p!==c||(n[u]=m)}),n}const Bt=e=>{const t=j({},e);let{data:n,withXSRFToken:s,xsrfHeaderName:r,xsrfCookieName:i,headers:o,auth:c}=t;t.headers=o=N.from(o),t.url=Nt(Lt(t.baseURL,t.url),e.params,e.paramsSerializer),c&&o.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):"")));let f;if(a.isFormData(n)){if(C.hasStandardBrowserEnv||C.hasStandardBrowserWebWorkerEnv)o.setContentType(void 0);else if((f=o.getContentType())!==!1){const[l,...u]=f?f.split(";").map(p=>p.trim()).filter(Boolean):[];o.setContentType([l||"multipart/form-data",...u].join("; "))}}if(C.hasStandardBrowserEnv&&(s&&a.isFunction(s)&&(s=s(t)),s||s!==!1&&nr(t.url))){const l=r&&i&&sr.read(i);l&&o.set(r,l)}return t},ir=typeof XMLHttpRequest<"u",ar=ir&&function(e){return new Promise(function(n,s){const r=Bt(e);let i=r.data;const o=N.from(r.headers).normalize();let{responseType:c,onUploadProgress:f,onDownloadProgress:l}=r,u,p,m,b,d;function y(){b&&b(),d&&d(),r.cancelToken&&r.cancelToken.unsubscribe(u),r.signal&&r.signal.removeEventListener("abort",u)}let h=new XMLHttpRequest;h.open(r.method.toUpperCase(),r.url,!0),h.timeout=r.timeout;function w(){if(!h)return;const S=N.from("getAllResponseHeaders"in h&&h.getAllResponseHeaders()),T={data:!c||c==="text"||c==="json"?h.responseText:h.response,status:h.status,statusText:h.statusText,headers:S,config:e,request:h};vt(function(E){n(E),y()},function(E){s(E),y()},T),h=null}"onloadend"in h?h.onloadend=w:h.onreadystatechange=function(){!h||h.readyState!==4||h.status===0&&!(h.responseURL&&h.responseURL.indexOf("file:")===0)||setTimeout(w)},h.onabort=function(){h&&(s(new g("Request aborted",g.ECONNABORTED,e,h)),h=null)},h.onerror=function(){s(new g("Network Error",g.ERR_NETWORK,e,h)),h=null},h.ontimeout=function(){let P=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const T=r.transitional||xt;r.timeoutErrorMessage&&(P=r.timeoutErrorMessage),s(new g(P,T.clarifyTimeoutError?g.ETIMEDOUT:g.ECONNABORTED,e,h)),h=null},i===void 0&&o.setContentType(null),"setRequestHeader"in h&&a.forEach(o.toJSON(),function(P,T){h.setRequestHeader(T,P)}),a.isUndefined(r.withCredentials)||(h.withCredentials=!!r.withCredentials),c&&c!=="json"&&(h.responseType=r.responseType),l&&([m,d]=re(l,!0),h.addEventListener("progress",m)),f&&h.upload&&([p,b]=re(f),h.upload.addEventListener("progress",p),h.upload.addEventListener("loadend",b)),(r.cancelToken||r.signal)&&(u=S=>{h&&(s(!S||S.type?new V(null,e,h):S),h.abort(),h=null)},r.cancelToken&&r.cancelToken.subscribe(u),r.signal&&(r.signal.aborted?u():r.signal.addEventListener("abort",u)));const O=Ys(r.url);if(O&&C.protocols.indexOf(O)===-1){s(new g("Unsupported protocol "+O+":",g.ERR_BAD_REQUEST,e));return}h.send(i||null)})},cr=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let s=new AbortController,r;const i=function(l){if(!r){r=!0,c();const u=l instanceof Error?l:this.reason;s.abort(u instanceof g?u:new V(u instanceof Error?u.message:u))}};let o=t&&setTimeout(()=>{o=null,i(new g(`timeout ${t} of ms exceeded`,g.ETIMEDOUT))},t);const c=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(l=>{l.unsubscribe?l.unsubscribe(i):l.removeEventListener("abort",i)}),e=null)};e.forEach(l=>l.addEventListener("abort",i));const{signal:f}=s;return f.unsubscribe=()=>a.asap(c),f}},ur=function*(e,t){let n=e.byteLength;if(n<t){yield e;return}let s=0,r;for(;s<n;)r=s+t,yield e.slice(s,r),s=r},lr=async function*(e,t){for await(const n of fr(e))yield*ur(n,t)},fr=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:n,value:s}=await t.read();if(n)break;yield s}}finally{await t.cancel()}},st=(e,t,n,s)=>{const r=lr(e,t);let i=0,o,c=f=>{o||(o=!0,s&&s(f))};return new ReadableStream({async pull(f){try{const{done:l,value:u}=await r.next();if(l){c(),f.close();return}let p=u.byteLength;if(n){let m=i+=p;n(m)}f.enqueue(new Uint8Array(u))}catch(l){throw c(l),l}},cancel(f){return c(f),r.return()}},{highWaterMark:2})},le=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",kt=le&&typeof ReadableStream=="function",dr=le&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Ut=(e,...t)=>{try{return!!e(...t)}catch{return!1}},pr=kt&&Ut(()=>{let e=!1;const t=new Request(C.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),rt=64*1024,Re=kt&&Ut(()=>a.isReadableStream(new Response("").body)),oe={stream:Re&&(e=>e.body)};le&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!oe[t]&&(oe[t]=a.isFunction(e[t])?n=>n[t]():(n,s)=>{throw new g(`Response type '${t}' is not supported`,g.ERR_NOT_SUPPORT,s)})})})(new Response);const mr=async e=>{if(e==null)return 0;if(a.isBlob(e))return e.size;if(a.isSpecCompliantForm(e))return(await new Request(C.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(a.isArrayBufferView(e)||a.isArrayBuffer(e))return e.byteLength;if(a.isURLSearchParams(e)&&(e=e+""),a.isString(e))return(await dr(e)).byteLength},hr=async(e,t)=>{const n=a.toFiniteNumber(e.getContentLength());return n??mr(t)},yr=le&&(async e=>{let{url:t,method:n,data:s,signal:r,cancelToken:i,timeout:o,onDownloadProgress:c,onUploadProgress:f,responseType:l,headers:u,withCredentials:p="same-origin",fetchOptions:m}=Bt(e);l=l?(l+"").toLowerCase():"text";let b=cr([r,i&&i.toAbortSignal()],o),d;const y=b&&b.unsubscribe&&(()=>{b.unsubscribe()});let h;try{if(f&&pr&&n!=="get"&&n!=="head"&&(h=await hr(u,s))!==0){let T=new Request(t,{method:"POST",body:s,duplex:"half"}),L;if(a.isFormData(s)&&(L=T.headers.get("content-type"))&&u.setContentType(L),T.body){const[E,U]=et(h,re(tt(f)));s=st(T.body,rt,E,U)}}a.isString(p)||(p=p?"include":"omit");const w="credentials"in Request.prototype;d=new Request(t,{...m,signal:b,method:n.toUpperCase(),headers:u.normalize().toJSON(),body:s,duplex:"half",credentials:w?p:void 0});let O=await fetch(d);const S=Re&&(l==="stream"||l==="response");if(Re&&(c||S&&y)){const T={};["status","statusText","headers"].forEach(de=>{T[de]=O[de]});const L=a.toFiniteNumber(O.headers.get("content-length")),[E,U]=c&&et(L,re(tt(c),!0))||[];O=new Response(st(O.body,rt,E,()=>{U&&U(),y&&y()}),T)}l=l||"text";let P=await oe[a.findKey(oe,l)||"text"](O,e);return!S&&y&&y(),await new Promise((T,L)=>{vt(T,L,{data:P,headers:N.from(O.headers),status:O.status,statusText:O.statusText,config:e,request:d})})}catch(w){throw y&&y(),w&&w.name==="TypeError"&&/fetch/i.test(w.message)?Object.assign(new g("Network Error",g.ERR_NETWORK,e,d),{cause:w.cause||w}):g.from(w,w&&w.code,e,d)}}),Te={http:Fs,xhr:ar,fetch:yr};a.forEach(Te,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const ot=e=>`- ${e}`,br=e=>a.isFunction(e)||e===null||e===!1,Dt={getAdapter:e=>{e=a.isArray(e)?e:[e];const{length:t}=e;let n,s;const r={};for(let i=0;i<t;i++){n=e[i];let o;if(s=n,!br(n)&&(s=Te[(o=String(n)).toLowerCase()],s===void 0))throw new g(`Unknown adapter '${o}'`);if(s)break;r[o||"#"+i]=s}if(!s){const i=Object.entries(r).map(([c,f])=>`adapter ${c} `+(f===!1?"is not supported by the environment":"is not available in the build"));let o=t?i.length>1?`since :
`+i.map(ot).join(`
`):" "+ot(i[0]):"as no adapter specified";throw new g("There is no suitable adapter to dispatch the request "+o,"ERR_NOT_SUPPORT")}return s},adapters:Te};function be(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new V(null,e)}function it(e){return be(e),e.headers=N.from(e.headers),e.data=ye.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Dt.getAdapter(e.adapter||Z.adapter)(e).then(function(s){return be(e),s.data=ye.call(e,e.transformResponse,s),s.headers=N.from(s.headers),s},function(s){return Ft(s)||(be(e),s&&s.response&&(s.response.data=ye.call(e,e.transformResponse,s.response),s.response.headers=N.from(s.response.headers))),Promise.reject(s)})}const It="1.7.9",fe={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{fe[e]=function(s){return typeof s===e||"a"+(t<1?"n ":" ")+e}});const at={};fe.transitional=function(t,n,s){function r(i,o){return"[Axios v"+It+"] Transitional option '"+i+"'"+o+(s?". "+s:"")}return(i,o,c)=>{if(t===!1)throw new g(r(o," has been removed"+(n?" in "+n:"")),g.ERR_DEPRECATED);return n&&!at[o]&&(at[o]=!0,console.warn(r(o," has been deprecated since v"+n+" and will be removed in the near future"))),t?t(i,o,c):!0}};fe.spelling=function(t){return(n,s)=>(console.warn(`${s} is likely a misspelling of ${t}`),!0)};function gr(e,t,n){if(typeof e!="object")throw new g("options must be an object",g.ERR_BAD_OPTION_VALUE);const s=Object.keys(e);let r=s.length;for(;r-- >0;){const i=s[r],o=t[i];if(o){const c=e[i],f=c===void 0||o(c,i,e);if(f!==!0)throw new g("option "+i+" must be "+f,g.ERR_BAD_OPTION_VALUE);continue}if(n!==!0)throw new g("Unknown option "+i,g.ERR_BAD_OPTION)}}const se={assertOptions:gr,validators:fe},B=se.validators;class q{constructor(t){this.defaults=t,this.interceptors={request:new Ze,response:new Ze}}async request(t,n){try{return await this._request(t,n)}catch(s){if(s instanceof Error){let r={};Error.captureStackTrace?Error.captureStackTrace(r):r=new Error;const i=r.stack?r.stack.replace(/^.+\n/,""):"";try{s.stack?i&&!String(s.stack).endsWith(i.replace(/^.+\n.+\n/,""))&&(s.stack+=`
`+i):s.stack=i}catch{}}throw s}}_request(t,n){typeof t=="string"?(n=n||{},n.url=t):n=t||{},n=j(this.defaults,n);const{transitional:s,paramsSerializer:r,headers:i}=n;s!==void 0&&se.assertOptions(s,{silentJSONParsing:B.transitional(B.boolean),forcedJSONParsing:B.transitional(B.boolean),clarifyTimeoutError:B.transitional(B.boolean)},!1),r!=null&&(a.isFunction(r)?n.paramsSerializer={serialize:r}:se.assertOptions(r,{encode:B.function,serialize:B.function},!0)),se.assertOptions(n,{baseUrl:B.spelling("baseURL"),withXsrfToken:B.spelling("withXSRFToken")},!0),n.method=(n.method||this.defaults.method||"get").toLowerCase();let o=i&&a.merge(i.common,i[n.method]);i&&a.forEach(["delete","get","head","post","put","patch","common"],d=>{delete i[d]}),n.headers=N.concat(o,i);const c=[];let f=!0;this.interceptors.request.forEach(function(y){typeof y.runWhen=="function"&&y.runWhen(n)===!1||(f=f&&y.synchronous,c.unshift(y.fulfilled,y.rejected))});const l=[];this.interceptors.response.forEach(function(y){l.push(y.fulfilled,y.rejected)});let u,p=0,m;if(!f){const d=[it.bind(this),void 0];for(d.unshift.apply(d,c),d.push.apply(d,l),m=d.length,u=Promise.resolve(n);p<m;)u=u.then(d[p++],d[p++]);return u}m=c.length;let b=n;for(p=0;p<m;){const d=c[p++],y=c[p++];try{b=d(b)}catch(h){y.call(this,h);break}}try{u=it.call(this,b)}catch(d){return Promise.reject(d)}for(p=0,m=l.length;p<m;)u=u.then(l[p++],l[p++]);return u}getUri(t){t=j(this.defaults,t);const n=Lt(t.baseURL,t.url);return Nt(n,t.params,t.paramsSerializer)}}a.forEach(["delete","get","head","options"],function(t){q.prototype[t]=function(n,s){return this.request(j(s||{},{method:t,url:n,data:(s||{}).data}))}});a.forEach(["post","put","patch"],function(t){function n(s){return function(i,o,c){return this.request(j(c||{},{method:t,headers:s?{"Content-Type":"multipart/form-data"}:{},url:i,data:o}))}}q.prototype[t]=n(),q.prototype[t+"Form"]=n(!0)});class Fe{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let n;this.promise=new Promise(function(i){n=i});const s=this;this.promise.then(r=>{if(!s._listeners)return;let i=s._listeners.length;for(;i-- >0;)s._listeners[i](r);s._listeners=null}),this.promise.then=r=>{let i;const o=new Promise(c=>{s.subscribe(c),i=c}).then(r);return o.cancel=function(){s.unsubscribe(i)},o},t(function(i,o,c){s.reason||(s.reason=new V(i,o,c),n(s.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const n=this._listeners.indexOf(t);n!==-1&&this._listeners.splice(n,1)}toAbortSignal(){const t=new AbortController,n=s=>{t.abort(s)};return this.subscribe(n),t.signal.unsubscribe=()=>this.unsubscribe(n),t.signal}static source(){let t;return{token:new Fe(function(r){t=r}),cancel:t}}}function wr(e){return function(n){return e.apply(null,n)}}function Er(e){return a.isObject(e)&&e.isAxiosError===!0}const Ae={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Ae).forEach(([e,t])=>{Ae[t]=e});function qt(e){const t=new q(e),n=yt(q.prototype.request,t);return a.extend(n,q.prototype,t,{allOwnKeys:!0}),a.extend(n,t,null,{allOwnKeys:!0}),n.create=function(r){return qt(j(e,r))},n}const R=qt(Z);R.Axios=q;R.CanceledError=V;R.CancelToken=Fe;R.isCancel=Ft;R.VERSION=It;R.toFormData=ue;R.AxiosError=g;R.Cancel=R.CanceledError;R.all=function(t){return Promise.all(t)};R.spread=wr;R.isAxiosError=Er;R.mergeConfig=j;R.AxiosHeaders=N;R.formToJSON=e=>Pt(a.isHTMLForm(e)?new FormData(e):e);R.getAdapter=Dt.getAdapter;R.HttpStatusCode=Ae;R.default=R;class Sr{constructor(t){ve(this,"instance");var n,s,r,i;this.instance=R.create(t),this.instance.defaults.timeout=1e4,this.instance.interceptors.request.use(o=>o,o=>o),this.instance.interceptors.response.use(o=>o.headers.captcha?o:o.data,o=>(o.message.includes("timeout")?we({message:"请求页面超时，请刷新重试",duration:5*1e3,type:"error"}):o.message.includes("Network Error")&&we({message:"网络错误，请检查网络连接",duration:5*1e3,type:"error"}),o)),this.instance.interceptors.request.use((n=t.interceptors)==null?void 0:n.requestSuccessFn,(s=t.interceptors)==null?void 0:s.requestFailureFn),this.instance.interceptors.response.use((r=t.interceptors)==null?void 0:r.responseSuccessFn,(i=t.interceptors)==null?void 0:i.responseFailureFn)}request(t){var n;return(n=t.interceptors)!=null&&n.requestSuccessFn&&(t=t.interceptors.requestSuccessFn(t)),new Promise((s,r)=>{this.instance.request(t).then(i=>{var o;(o=t.interceptors)!=null&&o.responseSuccessFn&&(i=t.interceptors.responseSuccessFn(i)),s(i)}).catch(i=>{r(i)})})}get(t){return this.request({...t,method:"GET"})}post(t){return this.request({...t,method:"POST"})}delete(t){return this.request({...t,method:"DELETE"})}patch(t){return this.request({...t,method:"PATCH"})}put(t){return this.request({...t,method:"PUT"})}}const _r=new Sr({baseURL:$n,timeout:Vn,interceptors:{requestSuccessFn:e=>{const t=localStorage.getItem("token");return e.headers&&t&&(e.headers.Authorization=t),e},responseSuccessFn:e=>(e.code===500&&we.error(e.msg),e)}});export{Nn as E,we as a,On as b,Cr as c,H as d,xn as m,Ar as o,_r as r,ee as u};
