package com.ict.ycwl.pathcalculate.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("`group`")
public class Group {
    @TableId(type = IdType.ASSIGN_ID)
    @TableField("`group_id`")
    private Long groupId;

    private String groupName;

}
