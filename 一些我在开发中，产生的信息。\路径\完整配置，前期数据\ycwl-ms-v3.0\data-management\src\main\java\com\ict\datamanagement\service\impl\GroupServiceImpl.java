package com.ict.datamanagement.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.ict.datamanagement.domain.entity.Group;
import com.ict.datamanagement.domain.entity.Group;
import com.ict.datamanagement.domain.vo.GroupVO;
import com.ict.datamanagement.domain.vo.GroupVO;
import com.ict.datamanagement.mapper.GroupMapper;
import com.ict.datamanagement.service.GroupService;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【group】的数据库操作Service实现
* @createDate 2024-04-22 13:45:36
*/
@Service
public class GroupServiceImpl extends ServiceImpl<GroupMapper, Group>
    implements GroupService {

    @Autowired
    private GroupMapper groupMapper;

    @Override
    public List<GroupVO> getOptionalData() {
        List<Group> groupList = groupMapper.selectGroupList();
        List<GroupVO> groupVOList = new ArrayList<>();
        for (Group group : groupList) {
            GroupVO groupVO = getGroupVO(group);
            groupVOList.add(groupVO);
        }
        return groupVOList;
    }

    @Override
    public GroupVO getGroupVO(Group group) {
        GroupVO groupVO = new GroupVO();
        BeanUtils.copyProperties(group,groupVO);
        return groupVO;
    }
}




