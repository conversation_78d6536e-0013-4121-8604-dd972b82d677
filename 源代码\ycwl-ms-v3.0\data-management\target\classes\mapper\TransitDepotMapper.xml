<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.datamanagement.mapper.TransitDepotMapper">
    <update id="myDeleteById">
        delete from transit_depot where transit_depot_id=#{id} and status=0
    </update>
    <update id="updateTransitDepot">
        update transit_depot
        <set>
            <if test="teamId != null and teamId!=0">
                group_id=#{teamId},
            </if>
            <if test="status != null and status.trim() != ''">
                status=#{status},
            </if>
            <if test="longitude != null and longitude.trim() != '' ">
                longitude=#{longitude},
            </if>
            <if test="latitude != null and latitude.trim() != '' ">
                latitude=#{latitude},
            </if>
        </set>
        where transit_depot_id=#{transitDepotId}
    </update>
    <update id="updateStatusToZeroById">
        update transit_depot set status=0 where transit_depot_id=#{oldTransitDepotId}
    </update>
    <select id="selectById" resultType="com.ict.datamanagement.domain.entity.TransitDepot">
        select * from transit_depot where transit_depot_id=#{id} and is_delete=0
    </select>
    <select id="selectByName" resultType="com.ict.datamanagement.domain.entity.TransitDepot">
        select * from transit_depot where transit_depot_name = #{transitDepotName} and is_delete=0
    </select>
    <select id="selectList" resultType="com.ict.datamanagement.domain.vo.transitDepotV0.TransitDepotVO">
        SELECT
        t1.transit_depot_id AS transitDepotId,
        t1.transit_depot_name AS transitDepotName,
        t4.group_name AS groupName,
        t1.longitude,
        t1.latitude,
        t1.`status`,
        GROUP_CONCAT(DISTINCT t3.delivery_name SEPARATOR ',') AS deliveryType,
        GROUP_CONCAT(DISTINCT t2.delivery_area_name SEPARATOR ',') AS deliveryName
        FROM
        transit_depot AS t1
        left JOIN
        transit_delivery as t5 on t1.transit_depot_id = t5.transit_depot_id
        left join
        delivery_area AS t2 ON t5.delivery_area_id = t2.delivery_area_id AND t2.transit_depot_id != 0
        left JOIN
        delivery_type AS t3 ON t2.delivery_type_id = t3.delivery_type_id
        left JOIN
        `group` AS t4 ON t1.group_id=t4.group_id
        <where>
            and t1.is_delete = 0
            <if test="transitDepotName != null and transitDepotName.trim() != ''">
                and t1.transit_depot_name like concat('%', #{transitDepotName}, '%')
            </if>
            <if test="teamName != null and teamName.trim() != ''">
                and t4.group_name=#{teamName}
            </if>
            <if test="status != null and status.trim() != ''">
                and t1.status=#{status}
            </if>
            <if test="deliveryType != null and deliveryType.trim() != ''">
                and t3.delivery_name=#{deliveryType}
            </if>
            <if test="deliveryName != null and deliveryName.trim() != ''">
                and t2.delivery_area_name=#{deliveryName}
            </if>
        </where>

        GROUP BY
        t1.transit_depot_id, t1.transit_depot_name, t1.longitude, t1.latitude, t1.`status`;
    </select>
    <select id="mySelectList" resultType="com.ict.datamanagement.domain.entity.TransitDepot">
        select * from transit_depot where status=1 and is_delete=0
    </select>
    <select id="selectGroupById" resultType="java.lang.String">
        select group_name from transit_depot as t1 join `group` as t2 on t1.group_id=t2.group_id where transit_depot_id=#{transitDepotId} and t1.is_delete=0
    </select>
    <select id="myselectTransitDepotNameS" resultType="java.lang.String">
        select transit_depot_name from transit_depot where group_id=#{teamId} and status=1 and is_delete=0
    </select>
    <select id="selectTransitByTeamId" resultType="com.ict.datamanagement.domain.entity.TransitDepot">
        select * from transit_depot where group_id=#{teamId} and is_delete=0
    </select>

</mapper>