import{Y as w,r as y}from"./index-C0QCllTd.js";import{r}from"./index-m25zEilF.js";function L(){return r.get({timeout:18e4,url:"/pickup/pickupUser/pickupUserExport",responseType:"blob"})}function P(e,a){return r.post({url:"/pickup/pickupUser/pickupUserImport",data:e,headers:{accept:"*/*","Content-Type":"multipart/form-data"},onUploadProgress:a.onUploadProgress,signal:a.signal})}function b(e){return r.get({url:"/datamanagement/getImportLogs",params:e})}function h(e){return r.post({url:"/pickup/pickupUser/downloadNullFrom",responseType:"blob",params:e})}function S(e){return r.get({url:"/datamanagement/downloadLogs",headers:{"Content-Type":"application/x-download"},responseType:"blob",params:e})}function T(e){return r.get({url:"/pickup/pickupUser/pickupUserList",params:e})}function x(e){return r.patch({url:"/pickup/pickupUser/pickupUserUpdate",params:e})}function v(e){return r.get({url:"/pickup/pickupUser/pickupUserRecalculate",params:e,timeout:18e4})}function C(e){return r.delete({url:"/datamanagement/deleteImportLogs",params:e})}function I(){return r.get({url:"/pickup/pickupUser/searchDownBox"})}function M(e){return r.patch({url:"/pickup/pickupUser/toBeAssigned",headers:{"Content-Type":"application/json"},params:e,paramsSerializer:a=>new URLSearchParams(a).toString()})}function A(){return r.post({url:"/pickup/pickupUser/mapMarkers",timeout:0})}function B(){return r.get({url:"/pickup/pickupUser/parameters"})}function D(e){return r.get({url:"/pickup/pickupUser/updateParameters",params:e})}const j=w("pick",()=>{const e=y(!1);async function a(t,n){return await P(t,n)}async function s(){return await L()}async function u(t){e.value=!0;const n=await T(t);return e.value=!1,n.data}async function o(t){return await x(t)}async function c(t){e.value=!0;const n=await v(t);return e.value=!1,n}async function p(t){return(await b(t)).data}async function i(t){return await h(t)}async function l(t){return await S(t)}async function d(t){return await C(t)}async function g(){return(await I()).data}async function k(t){return await M(t)}async function f(){return(await A()).data}async function m(t){return await D(t)}async function U(){return(await B()).data}return{importUser:a,exportUser:s,getUserList:u,updateUser:o,calculateUser:c,importUserLog:p,downloadNullForm:i,updateParamsList:m,downloadUserLog:l,deleteCarLog:d,getSelectList:g,beAssigned:k,getMap:f,getParams:U,loading:e}});export{j as u};
