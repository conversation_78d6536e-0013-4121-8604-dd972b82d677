package com.ict.ycwl.pathcalculate.pojo;

import lombok.Data;

/**
 * 车辆类
 */
@Data
public class Car {

    /**
     * 车辆id
     */
    Long carId;

    /**
     *车牌号(七位)
     */
    String licensePlateNumber;

    /**
     *最大载重（吨）
     */
    String maxLoad;

    /**
     *最大行驶距离（米）
     */
    String maxDistance;

    /**
     *积分
     */
    String integral;

    /**
     *状态（0：异常；1：正常）
     */
    String status;

    /**
     *最长可工作时长（单位：米）[ 暂定字段 ]
     */
    String deliveryTime;

    /**
     *所属大区id
     */
    Long areaId;

    /**
     *所属中转站id
     */
    Long transitDepotId;

}
