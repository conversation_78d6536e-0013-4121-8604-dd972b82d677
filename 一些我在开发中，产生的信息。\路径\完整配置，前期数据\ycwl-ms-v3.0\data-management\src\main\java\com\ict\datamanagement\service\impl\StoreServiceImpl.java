package com.ict.datamanagement.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ict.datamanagement.domain.dto.store.StoreListRequest;
import com.ict.datamanagement.domain.entity.Store;
import com.ict.datamanagement.domain.vo.StoreVO;
import com.ict.datamanagement.mapper.StoreMapper;
import com.ict.datamanagement.service.StoreService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【store】的数据库操作Service实现
* @createDate 2024-04-16 23:16:01
*/
@Service
public class StoreServiceImpl extends ServiceImpl<StoreMapper, Store>
    implements StoreService{



    /**
     * 获取Store列表的QueryWrapper
     * @param request StoreListRequest
     * @return QueryWrapper<Store>
     */
    @Override
    public QueryWrapper<Store> getQueryWrapper(StoreListRequest request) {
        QueryWrapper<Store> queryWrapper = new QueryWrapper<>();
        if (request==null){
            return queryWrapper;
        }

        String customerCode = request.getCustomerCode();
        String contactName = request.getContactName();
        String storeAddress = request.getStoreAddress();
        Long areaId = request.getAreaId();
        Long routeId = request.getRouteId();
        Long groupId = request.getGroupId();

        queryWrapper.like(StrUtil.isNotEmpty(customerCode),"customer_code",customerCode);
        queryWrapper.like(StrUtil.isNotEmpty(contactName),"contact_name",contactName);
        queryWrapper.like(StrUtil.isNotEmpty(storeAddress),"store_address",storeAddress);
        queryWrapper.eq(areaId!=null,"area_id",areaId);
        queryWrapper.eq(routeId!=null,"route_id",routeId);
        queryWrapper.eq(groupId!=null,"group_id",groupId);


        return queryWrapper;
    }

    /**
     * 获取实体类VO
     * @param store
     * @return
     */
    @Override
    public StoreVO getStoreVO(Store store) {
        StoreVO storeVO = new StoreVO();
        BeanUtils.copyProperties(store,storeVO);
        if (store.getLocationType().equals("1")){
            storeVO.setLocationType("乡镇");
        }else {
            storeVO.setLocationType("城区");
        }
        return storeVO;
    }
}




