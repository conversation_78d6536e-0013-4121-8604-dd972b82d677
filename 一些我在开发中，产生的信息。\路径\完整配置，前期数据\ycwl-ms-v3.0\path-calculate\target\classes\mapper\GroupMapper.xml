<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.pathcalculate.mapper.GroupMapper">

    <select id="getGroupsByLikeName" resultType="com.ict.ycwl.pathcalculate.pojo.Group">
        SELECT * FROM `group` where group_name like '班组%';
    </select>

    <select id="getGroupsByName" resultType="com.ict.ycwl.pathcalculate.pojo.Group">
        SELECT * FROM `group` where group_name = #{name};
    </select>
</mapper>