package com.ict.ycwl.guestbook.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ict.ycwl.guestbook.api.form.FeedbackListForm;
import com.ict.ycwl.guestbook.api.vo.FeedbackListVo;
import com.ict.ycwl.guestbook.domain.Feedback;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface FeedbackMapper extends BaseMapper<Feedback> {

    List<FeedbackListVo> selectFeedbackList(FeedbackListForm form);

    Long insertFeedback(Feedback feedback);

    int updateFeedbackStatus(@Param("feedbackStatus") Integer feedbackStatus, @Param("feedbackId") Long feedbackId, @Param("completeTime") LocalDateTime completeTime);

    String selectTypeById(Long feedbackId);

    int deleteFeedbackByIds(@Param("feedbackIdList") List<Long> feedbackIdList);

    int updateUpdateData(@Param("updateTime") LocalDateTime updateTime,@Param("updateBy")Long updateBy,@Param("feedbackId")Long feedbackId);

    Integer selectCountByType(String feedbackType);
}
