<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.clustercalculate.mapper.AccumulationMapper">
    <update id="updateIsDelete">
        UPDATE accumulation
        SET is_delete=1
        WHERE is_delete = 0
          and area_name = #{keyword};
    </update>

    <select id="selectIdByLonAndLat" resultType="java.lang.Long">
        SELECT accumulation_id
        from accumulation
        where longitude = #{longitude}
          and latitude = #{latitude}
          and is_delete = 0;
    </select>

    <select id="selectAll" resultType="java.lang.Integer">
        select COUNT(*)
        FROM accumulation
        where area_name = #{keyword};
    </select>

    <select id="selectAllByAreaName" resultType="com.ict.ycwl.clustercalculate.pojo.LngAndLat">
        SELECT longitude, latitude
        FROM accumulation
        WHERE area_name = #{keyword}
          and is_delete = 0;
    </select>


</mapper>