package com.ict.ycwl.guestbook.api.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Data
@ApiModel("异常反馈信息添加表单")
@AllArgsConstructor
@NoArgsConstructor
public class FeedbackAddForm {

    @NotBlank(message = "反馈类型不能为空")
    @ApiModelProperty(value = "反馈类型（1：物流反馈；2：营销反馈）",dataType = "String",required = true,example = "1")
    private String feedbackType;

    @NotBlank(message = "客户编码不能为空")
    @ApiModelProperty(value = "客户编码",dataType = "String",required = true)
    private String customerCode;

    @NotBlank(message = "大区名称不能为空")
    @ApiModelProperty(value = "大区名称", dataType = "String", required = true)
    private String areaName;

    @Min(value = 1,message = "路线id数值有误")
    @NotNull(message = "路线id不能为空")
    @ApiModelProperty(value = "路线id",dataType = "Long",required = true)
    private Long routeId;

    @NotBlank(message = "路线名称不能为空")
    @ApiModelProperty(value = "路线名称",dataType = "String",required = true)
    private String routeName;

    @NotNull(message = "订单日期不能为空")
    @ApiModelProperty(value = "订单日期",dataType = "LocalDateTime",required = true,example = "2023-10-23 11:21:59")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime orderDate;

    @NotNull(message = "送货员工号不能为空")
    @ApiModelProperty(value = "送货员工号",dataType = "String",required = true)
    private String deliveryWorkNumber;

    @NotBlank(message = "送货员名称不能为空")
    @ApiModelProperty(value = "送货员名称",dataType = "String",required = true)
    private String deliveryName;

    @NotBlank(message = "反馈异常信息内容不能为空")
    @ApiModelProperty(value = "反馈异常信息内容",dataType = "String",required = true)
    private String feedbackInformation;

    @ApiModelProperty(value = "反馈异常信息附带文件集合",dataType = "List<MultipartFile>")
    private List<MultipartFile> fileList;

    @ApiModelProperty(value = "客户专员名称",dataType = "String")
    private String customerManagerName;

    @ApiModelProperty(value = "客户专员工号",dataType = "String")
    private String ManagerWorkNumber;
}
