package com.ict.ycwl.pathcalculate.pojo;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("point_distance")
public class PointDistance {

    @TableId(type = IdType.ASSIGN_ID)
    private Long pointDistanceId;

    private String distance;

    private String polyline;

    private String origin;

    private String destination;

    private Timestamp createTime;

    private Timestamp updateTime;

    private boolean isDelete;

    private Long transitDepotId;

    private String type;
}
