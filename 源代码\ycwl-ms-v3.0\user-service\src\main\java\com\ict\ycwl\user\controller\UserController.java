package com.ict.ycwl.user.controller;


import com.ict.ycwl.common.web.AjaxResult;
import com.ict.ycwl.user.pojo.request.AddUserRequest;
import com.ict.ycwl.user.pojo.request.UpdateUserRequest;
import com.ict.ycwl.user.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@Api(tags = "用户管理API")
@RestController
@Controller
@RequestMapping("/user")
public class UserController {

    @Autowired
    private UserService userService;


    @ApiOperation("添加用户接口")
    @PostMapping("/add")
    public AjaxResult AddUser(@RequestHeader("Authorization") String authorization,AddUserRequest addUserRequest) {
        System.out.println(addUserRequest);
        return AjaxResult.success(userService.userAdd(authorization, addUserRequest.getPhone(), addUserRequest.getEmail(), addUserRequest.getDepartment(), String.valueOf(addUserRequest.getSign_time()), addUserRequest.getWork_number(), addUserRequest.getUser_name(), addUserRequest.getRole_id(),addUserRequest.getGroup()));
        //return userService.userAdd(authorization, phone, email, department, signTime, workNumber, userName, roleId);
    }

    @ApiOperation("用户修改密码接口")
    @PostMapping("/update/password")
    public AjaxResult updatePassword(@RequestHeader("Authorization") String authorization,
                                     @ApiParam(value = "用户id", required = true)
                                     @RequestParam("userId") Long userId,
                                     @ApiParam(value = "旧密码", required = true)
                                     @RequestParam("currentPassword") String currentPassword,
                                     @ApiParam(value = "新密码", required = true)
                                     @RequestParam("newPassword") String newPassword,
                                     @ApiParam(value = "确认新密码", required = true)
                                     @RequestParam("confirmPassword") String confirmPassword) {
        return userService.userUpdatePassword(authorization, userId, currentPassword, newPassword, confirmPassword);
    }

    @ApiOperation("获取编辑的用户信息接口")
    @GetMapping("/get")
    public AjaxResult getUserInfo(@ApiParam(value = "用户工号", required = true)
                                  @RequestParam("workNumber") String workNumber) {
        return userService.getUserInfo(workNumber);
    }

    @ApiOperation("修改用户信息接口")
    @PostMapping("/update")
    public AjaxResult updateUser(@RequestHeader("Authorization") String authorization, UpdateUserRequest user) {
        return userService.updateUser(authorization, user.getPhone(), user.getEmail(), user.getDepartment(), user.getSignTime(), user.getWork_number(), user.getUser_name(), user.getRole_id(), user.getNewPassword(),user.getGroup());
    }


}
