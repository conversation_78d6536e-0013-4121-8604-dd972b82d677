package com.ict.ycwl.guestbook.service;

import com.ict.ycwl.common.web.Paging;
import com.ict.ycwl.guestbook.api.form.FeedbackAddForm;
import com.ict.ycwl.guestbook.api.form.FeedbackListForm;
import com.ict.ycwl.guestbook.api.vo.ConditionsDataVo;
import com.ict.ycwl.guestbook.api.vo.FeedbackListVo;
import com.ict.ycwl.guestbook.api.vo.UnhandledFeedbackVo;

import java.util.List;


public interface FeedbackService {

    Paging<FeedbackListVo> getFeedbackList(FeedbackListForm form);

    void addFeedback(FeedbackAddForm form, Long userId) throws Exception;

    void removeFeedbacks(List<Long> feedbackIdList);

    ConditionsDataVo getConditionsData();

    UnhandledFeedbackVo getUnhandledMount();
}
