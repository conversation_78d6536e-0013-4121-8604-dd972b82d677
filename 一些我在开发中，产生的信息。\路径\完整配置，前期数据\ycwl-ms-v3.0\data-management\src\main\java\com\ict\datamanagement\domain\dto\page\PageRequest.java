package com.ict.datamanagement.domain.dto.page;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import java.io.Serializable;

@ApiModel("分页表单")
@Data
public class PageRequest implements Serializable {

    /**
     * 当前页码
     */
    @ApiModelProperty(value = "当前页码",dataType = "Integer",required = true,example = "1")
    private Integer pageNum;


    /**
     * 总页码
     */
    @ApiModelProperty(value = "总页码",dataType = "Integer",required = true,example = "10")
    private Integer pageSize;

}
