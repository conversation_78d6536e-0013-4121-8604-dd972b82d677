package com.ict.ycwl.guestbook.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ict.ycwl.guestbook.domain.FeedbackFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface FeedbackFileMapper extends BaseMapper<FeedbackFile> {

    List<String> selectFilePath(Long feedbackId);

    int insertFeedbackFile(@Param("fileList") List<FeedbackFile> fileList);

    int deleteFeedbackFile(@Param("feedbackIdList") List<Long> feedbackIdList);

    List<String> selectFileRealPath(@Param("feedbackIdList") List<Long> feedbackIdList);
}
