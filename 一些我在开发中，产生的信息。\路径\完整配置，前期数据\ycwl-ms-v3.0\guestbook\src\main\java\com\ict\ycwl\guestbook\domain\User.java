package com.ict.ycwl.guestbook.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.time.LocalDateTime;


@Data
public class User {

    @TableId(type = IdType.ASSIGN_ID)
    private Long userId;

    private String loginName;

    private String userName;

    private String workNumber;

    private String sex;

    private String position;

    private String password;

    private String department;

    private String phone;

    private String email;

    private String status;

    private String avatarPath;

    private Long createBy;

    private LocalDateTime createTime;

    private Long updateBy;

    private LocalDateTime updateTime;

}
