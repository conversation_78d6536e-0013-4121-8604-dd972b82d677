package com.ict.ycwl.guestbook.api.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@ApiModel("处理信息添加表单")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class ReplyAddForm {

    @NotBlank(message = "处理信息内容不能为空")
    @ApiModelProperty(value = "处理信息内容(文字)",dataType = "String",required = true,example = "客户未开门")
    private String replyContent;

    @NotBlank(message = "处理类型不能为空")
    @ApiModelProperty(value = "处理类型（1：送货部；2：营销部）",dataType = "String",required = true,example = "1")
    private String replyType;

    @Min(value = -1,message = "反馈信息id数值错误")
    @NotNull(message = "所属反馈信息id不能为空")
    @ApiModelProperty(value = "所属反馈信息id",dataType = "Long",required = true)
    private Long feedbackId;

    @NotNull(message = "反馈信息处理状态不能为空")
    @ApiModelProperty(value = "反馈信息处理状态（0：未处理；1：处理中；2：已处理；3：无需处理）", dataType = "Integer", example = "1")
    private Integer feedbackStatus;

    @ApiModelProperty(value = "附带文件列表",dataType = "List<MultipartFile>")
    private List<MultipartFile> fileList;

}
