package com.ict.ycwl.pathcalculate.vo.details;

import lombok.Data;

/**
 * 聚集区返回类
 */
@Data
public class AccumulationDetailsVO {

    /**
     * 聚集区id
     */
    Long accumulationId;

    /**
     * 聚集区名称
     */
    String accumulationName;

    /**
     * 聚集区经度
     */
    double longitude;

    /**
     * 聚集区纬度
     */
    double latitude;

    /**
     * 聚集区地址
     */
    String accumulationAddress;
}
