package com.ict.datamanagement.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.ict.datamanagement.domain.entity.Route;
import com.ict.datamanagement.domain.vo.RouteVO;
import com.ict.datamanagement.mapper.RouteMapper;
import com.ict.datamanagement.service.RouteService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【route】的数据库操作Service实现
* @createDate 2024-04-22 13:45:36
*/
@Service
public class RouteServiceImpl extends ServiceImpl<RouteMapper, Route>
    implements RouteService {

    @Override
    public List<RouteVO> getOptionalData() {
        List<Route> routeList = this.list();
        List<RouteVO> routeVOList = new ArrayList<>();
        for (Route route : routeList) {
            RouteVO routeVO = getRouteVO(route);
            routeVOList.add(routeVO);
        }
        return routeVOList;
    }

    @Override
    public RouteVO getRouteVO(Route route) {
        RouteVO routeVO = new RouteVO();
        BeanUtils.copyProperties(route,routeVO);
        return routeVO;
    }
}




