package com.ict.ycwl.guestbook.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class Feedback {

    @TableId(type = IdType.ASSIGN_ID)
    private Long feedbackId;

    private String customerCode;

    private Long routeId;

    private String routeName;

    private LocalDateTime orderDate;

    private String deliveryWorkNumber;

    private String deliveryName;

    private String customerManagerName;

    private String ManagerWorkNumber;

    private Long createBy;

    private String feedbackInformation;

    private String feedbackType;

    private Integer feedbackStatus;

    private LocalDateTime createTime;

    private LocalDateTime completeTime;

    private String areaName;

    private LocalDateTime updateTime;

    private Long updateBy;

}
