package com.ict.ycwl.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ict.ycwl.common.web.AjaxResult;
import com.ict.ycwl.user.pojo.User;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;

public interface UserService extends IService<User> {
    //登录接口
    AjaxResult loginByName(String loginName, String password, String captcha, HttpServletRequest httpServletRequest);

    //添加用户接口
    AjaxResult userAdd(String authorization,
                    String phone,
                   String email,
                   String department,
                   String signTime,
                   String workNumber,
                   String userName,
                   Long roleId,String group);

    //头像上传接口
    Object uploadAvatar( MultipartFile photo) throws Exception;

    //查询用户接口
    AjaxResult userSearch(String department,String userName,String workNumber,String group,String role);

    //用户修改密码接口
    AjaxResult userUpdatePassword(String authorization, Long userId,String currentPassword, String newPassword,String confirmPassword);

    //获取用户信息接口
    AjaxResult getUserInfo(String workNumber);

    //更新用户信息接口
    AjaxResult updateUser(String authorization,
                          String phone,
                          String email,
                          String department,
                          String signTime,
                          String workNumber,
                          String userName,
                          Long roleId,
                          String newPassword,String group);

}
