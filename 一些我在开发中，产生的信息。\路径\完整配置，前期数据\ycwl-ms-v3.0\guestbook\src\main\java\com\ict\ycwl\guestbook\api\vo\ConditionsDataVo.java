package com.ict.ycwl.guestbook.api.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ConditionsDataVo implements Serializable {

    //private List<ConditionStoreVo> storeList;

    //private List<ConditionRouteVo> routeList;

    private List<ConditionAreaVo> areaList;

    private List<ConditionUserVo> deliveryUserList;

    private List<ConditionUserVo> customerManagerList;

}
