import{a as w,N as W,a1 as A,a0 as z,a9 as G,aa as U}from"./base-kpSIrADU.js";import{a0 as v,ak as N,z as K,aE as V,A as h,Q as $,j as x,aV as j,m as Q,aW as q,ay as E,f as J,h as Y,d as Z,b as g,s as O,a6 as P}from"./index-C0QCllTd.js";import{t as F}from"./input-DqmydyK4.js";import{f as ee}from"./scrollbar-BNeK4Yi-.js";var m=(e=>(e[e.TEXT=1]="TEXT",e[e.CLASS=2]="CLASS",e[e.STYLE=4]="STYLE",e[e.PROPS=8]="PROPS",e[e.FULL_PROPS=16]="FULL_PROPS",e[e.HYDRATE_EVENTS=32]="HYDRATE_EVENTS",e[e.STABLE_FRAGMENT=64]="STABLE_FRAGMENT",e[e.KEYED_FRAGMENT=128]="KEYED_FRAGMENT",e[e.UNKEYED_FRAGMENT=256]="UNKEYED_FRAGMENT",e[e.NEED_PATCH=512]="NEED_PATCH",e[e.DYNAMIC_SLOTS=1024]="DYNAMIC_SLOTS",e[e.HOISTED=-1]="HOISTED",e[e.BAIL=-2]="BAIL",e))(m||{});const p=e=>{const n=v(e)?e:[e],t=[];return n.forEach(o=>{var u;v(o)?t.push(...p(o)):N(o)&&v(o.children)?t.push(...p(o.children)):(t.push(o),N(o)&&((u=o.component)!=null&&u.subTree)&&t.push(...p(o.component.subTree)))}),t},ce=(e,n,t)=>{let o={offsetX:0,offsetY:0};const u=r=>{const d=r.clientX,l=r.clientY,{offsetX:i,offsetY:f}=o,c=e.value.getBoundingClientRect(),T=c.left,y=c.top,D=c.width,k=c.height,_=document.documentElement.clientWidth,H=document.documentElement.clientHeight,B=-T+i,I=-y+f,R=_-T-D+i,X=H-y-k+f,S=L=>{const b=Math.min(Math.max(i+L.clientX-d,B),R),C=Math.min(Math.max(f+L.clientY-l,I),X);o={offsetX:b,offsetY:C},e.value.style.transform=`translate(${w(b)}, ${w(C)})`},M=()=>{document.removeEventListener("mousemove",S),document.removeEventListener("mouseup",M)};document.addEventListener("mousemove",S),document.addEventListener("mouseup",M)},a=()=>{n.value&&e.value&&n.value.addEventListener("mousedown",u)},s=()=>{n.value&&e.value&&n.value.removeEventListener("mousedown",u)};K(()=>{V(()=>{t.value?a():s()})}),h(()=>{s()})},me=(e,n={})=>{$(e)||F("[useLockscreen]","You need to pass a ref param to this function");const t=n.ns||x("popup"),o=j(()=>t.bm("parent","hidden"));if(!W||A(document.body,o.value))return;let u=0,a=!1,s="0";const r=()=>{setTimeout(()=>{U(document==null?void 0:document.body,o.value),a&&document&&(document.body.style.width=s)},200)};Q(e,d=>{if(!d){r();return}a=!A(document.body,o.value),a&&(s=document.body.style.width),u=ee(t.namespace.value);const l=document.documentElement.clientHeight<document.body.scrollHeight,i=z(document.body,"overflowY");u>0&&(l||i==="scroll")&&a&&(document.body.style.width=`calc(100% - ${u}px)`),G(document.body,o.value)}),q(()=>r())},oe=e=>{if(!e)return{onClick:E,onMousedown:E,onMouseup:E};let n=!1,t=!1;return{onClick:s=>{n&&t&&e(s),n=t=!1},onMousedown:s=>{n=s.target===s.currentTarget},onMouseup:s=>{t=s.target===s.currentTarget}}},te=J({mask:{type:Boolean,default:!0},customMaskEvent:{type:Boolean,default:!1},overlayClass:{type:Y([String,Array,Object])},zIndex:{type:Y([String,Number])}}),ne={click:e=>e instanceof MouseEvent},se="overlay";var ue=Z({name:"ElOverlay",props:te,emits:ne,setup(e,{slots:n,emit:t}){const o=x(se),u=d=>{t("click",d)},{onClick:a,onMousedown:s,onMouseup:r}=oe(e.customMaskEvent?void 0:u);return()=>e.mask?g("div",{class:[o.b(),e.overlayClass],style:{zIndex:e.zIndex},onClick:a,onMousedown:s,onMouseup:r},[O(n,"default")],m.STYLE|m.CLASS|m.PROPS,["onClick","onMouseup","onMousedown"]):P("div",{class:e.overlayClass,style:{zIndex:e.zIndex,position:"fixed",top:"0px",right:"0px",bottom:"0px",left:"0px"}},[O(n,"default")])}});const le=ue;export{le as E,me as a,oe as b,p as f,ce as u};
