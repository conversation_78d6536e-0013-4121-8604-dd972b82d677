package com.ict.datamanagement.domain.vo;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class StoreVO implements Serializable {

    /**
     * 店铺id
     */
    private Long storeId;

    /**
     * 客户编码
     */
    private String customerCode;

    /**
     * 店铺名称
     */
    private String storeName;

    /**
     * 店铺经营地址
     */
    private String storeAddress;

    /**
     * 店铺经度
     */
    private Double longitude;

    /**
     * 店铺纬度
     */
    private Double latitude;

    /**
     * 商圈类型
     */
    private String type;

    /**
     * 订货周期
     */
    private String orderCycle;

    /**
     * 店铺所属行政区
     */
    private String district;

    /**
     * 店铺所属大区
     */
    private String areaName;

    /**
     * 店铺联系人名称
     */
    private String contactName;

    /**
     * 店铺联系人电话号码
     */
    private String contactPhone;

    /**
     * 状态（0：异常；1：正常）
     */
    private String status;

    /**
     * 客户专员id
     */
    private Long customerManagerId;

    /**
     * 聚集区id
     */
    private Long accumulationId;

    /**
     * 大区id
     */
    private Long areaId;

    /**
     * 路线id
     */
    private Long routeId;

    /**
     * 路线名称
     */
    private String routeName;

    /**
     * 客户专员名称
     */
    private String customerManagerName;

    /**
     * 是否软删除（0：不是；1：是）
     */
    @TableLogic
    private Integer isDelete;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 客户档位
     */
    private String gear;

    /**
     * 创建者
     */
    private Long createBy;

    /**
     * 更新者
     */
    private Long updateBy;

    /**
     * 班组Id
     */
    private Long groupId;

    /**
     * 商铺类型归属
     */
    private String locationType;


}
