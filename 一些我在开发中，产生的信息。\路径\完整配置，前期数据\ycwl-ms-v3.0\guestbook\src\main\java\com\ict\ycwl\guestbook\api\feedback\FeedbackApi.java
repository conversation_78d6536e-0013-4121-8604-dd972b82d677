package com.ict.ycwl.guestbook.api.feedback;

import com.auth0.jwt.JWT;
import com.ict.ycwl.common.web.AjaxResult;
import com.ict.ycwl.common.web.Paging;
import com.ict.ycwl.guestbook.api.form.FeedbackAddForm;
import com.ict.ycwl.guestbook.api.form.FeedbackListForm;
import com.ict.ycwl.guestbook.api.form.ReplyAddForm;
import com.ict.ycwl.guestbook.api.vo.ConditionSingleDataVo;
import com.ict.ycwl.guestbook.api.vo.ConditionsDataVo;
import com.ict.ycwl.guestbook.api.vo.FeedbackListVo;
import com.ict.ycwl.guestbook.api.vo.FeedbackReplyVo;
import com.ict.ycwl.guestbook.service.ConditionDataService;
import com.ict.ycwl.guestbook.service.FeedbackReplyService;
import com.ict.ycwl.guestbook.service.FeedbackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Api(tags = "留言板API")
@Slf4j
@Validated
@RestController
@RequestMapping("/feedback")
public class FeedbackApi {

    @Autowired
    private FeedbackService feedbackService;

    @Autowired
    private FeedbackReplyService replyService;

    @Autowired
    private ConditionDataService conditionDataService;


    @ApiOperation("反馈信息列表接口")
    @GetMapping("/list")
    public AjaxResult list(@RequestHeader("Authorization")String authorization, @Valid FeedbackListForm form){
        Paging<FeedbackListVo> feedbackPaging = feedbackService.getFeedbackList(form);
        return AjaxResult.success(feedbackPaging);
    }

    @ApiOperation("处理信息详情接口")
    @GetMapping("/details/{feedbackId}")
    public AjaxResult details(@RequestHeader("Authorization")String authorization,
                              @NotNull(message = "异常反馈信息id不能为空") @Min(value = 1,message = "异常反馈信息id格式错误")
                              @ApiParam(value = "异常反馈信息id",required = true,example = "1")
                              @PathVariable("feedbackId")Long feedbackId){
        List<FeedbackReplyVo> replyList = replyService.getReplyAndFile(feedbackId);
        return AjaxResult.success(replyList);
    }

    @ApiOperation("反馈信息添加接口")
    @PostMapping("/add")
    public AjaxResult add(@RequestHeader("Authorization")String authorization,
                          @Valid FeedbackAddForm form) throws Exception {
        //token解析数据
        Long userId = Long.valueOf(JWT.decode(authorization).getClaim("userId").asString());
        feedbackService.addFeedback(form, userId);

        return AjaxResult.success("添加成功");
    }

    @ApiOperation("处理信息添加接口")
    @PostMapping("/addReply")
    public AjaxResult addReply(@RequestHeader("Authorization") String authorization, @Valid ReplyAddForm form) throws Exception {
        //token解析数据
        Long userId = Long.valueOf(JWT.decode(authorization).getClaim("userId").asString());
        replyService.addReplyAndFile(form, userId);

        return AjaxResult.success("添加成功");
    }

    @ApiOperation("删除反馈信息接口")
    @DeleteMapping("/delete/{feedbackIdList}")
    public AjaxResult delete(@RequestHeader("Authorization") String authorization,
                             @Size(min = 1, message = "反馈信息id集合有误") @NotNull(message = "信息id不能为空")
                             @ApiParam(value = "反馈信息id集合", required = true)
                             @PathVariable("feedbackIdList") List<Long> feedbackIdList) {
        feedbackService.removeFeedbacks(feedbackIdList);
        return AjaxResult.success("删除成功");
    }

    @ApiOperation("获取下拉框数据")
    @GetMapping("/getConditionsData")
    public AjaxResult getConditionsData(@RequestHeader("Authorization") String authorization) {
        ConditionsDataVo conditionsData = feedbackService.getConditionsData();
        System.out.println(conditionsData.toString());
        return AjaxResult.success(conditionsData);
    }

    @ApiOperation("获取客户下拉框数据")
    @GetMapping("/getSingleConditionsData/{customerCode}")
    public AjaxResult getSingleData(@RequestHeader("Authorization") String authorization,@PathVariable("customerCode") String customerCode){
        ConditionSingleDataVo ConditionsData = conditionDataService.getDataByStore(customerCode);
        return AjaxResult.success(ConditionsData);
    }

    @ApiOperation("获取未处理反馈信息数目")
    @GetMapping("/getUnhandledAmount")
    public AjaxResult getUnhandledAmount(@RequestHeader("Authorization") String authorization){
        return AjaxResult.success(feedbackService.getUnhandledMount());
    }

}
