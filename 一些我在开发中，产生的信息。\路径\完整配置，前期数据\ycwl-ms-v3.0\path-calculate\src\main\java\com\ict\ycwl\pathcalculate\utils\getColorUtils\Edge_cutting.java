package com.ict.ycwl.pathcalculate.utils.getColorUtils;

import org.locationtech.jts.geom.*;
import org.locationtech.jts.io.ParseException;

import java.io.*;
import java.security.PrivateKey;
import java.util.ArrayList;
import java.util.List;


public class Edge_cutting {
    private Polygon boundary;
    private Geometry polygons;

    private static GeometryFactory geometryFactory = new GeometryFactory();

    public Edge_cutting (Polygon boundary, Geometry polygons){
        this.boundary = boundary;
        this.polygons = polygons;
    }

    public Geometry get_cutting(Polygon boundary, Geometry polygons){
        return boundary.intersection(polygons);
    }

    public Geometry get_fulling(Polygon boundary, Geometry polygons){
        List<Polygon> complementary = new ArrayList<>();
        List<Geometry> new_polygons = new ArrayList<>();
        for(int i = 0; i < polygons.getNumGeometries(); i++){
            Geometry triangular = polygons.getGeometryN(i);
        }
        return geometryFactory.createGeometryCollection(new_polygons.toArray(new Geometry[0]));
    }
}
