package com.ict.datamanagement.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ict.datamanagement.domain.entity.Area;
import com.ict.datamanagement.domain.vo.AreaVO;
import com.ict.datamanagement.mapper.AreaMapper;
import com.ict.datamanagement.service.AreaService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


/**
* <AUTHOR>
* @description 针对表【area】的数据库操作Service实现
* @createDate 2024-04-22 13:45:36
*/
@Service
public class AreaServiceImpl extends ServiceImpl<AreaMapper, Area>
    implements AreaService {


    @Override
    public List<AreaVO> getOptionalData() {
        List<Area> areaList = this.list();
        List<AreaVO> areaVOList = new ArrayList<>();
        for (Area area : areaList) {
            AreaVO areaVO = getAreaVO(area);
            areaVOList.add(areaVO);
        }
        return areaVOList;
    }

    @Override
    public AreaVO getAreaVO(Area area) {
        AreaVO areaVO = new AreaVO();
        BeanUtils.copyProperties(area,areaVO);
        return areaVO;
    }
}




