import org.locationtech.jts.geom.*;
import org.locationtech.jts.geom.util.GeometryFixer;
import org.locationtech.jts.io.ParseException;
import org.locationtech.jts.precision.GeometryPrecisionReducer;
import org.locationtech.jts.simplify.TopologyPreservingSimplifier;
import org.locationtech.jts.triangulate.DelaunayTriangulationBuilder;
import org.locationtech.jts.triangulate.quadedge.QuadEdgeSubdivision;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Triangulation {
    private static GeometryFactory geometryFactory = new GeometryFactory();

    public static void main(String[] args) throws IOException, ParseException {
        List<Polygon> polygons = new ArrayList<>();
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.727166, 25.304861),
                new Coordinate(113.733395, 25.25601),
                new Coordinate(113.865906, 25.201329),
                new Coordinate(113.937188, 25.303314),
                new Coordinate(113.955875, 25.330793),
                new Coordinate(113.836659, 25.831624),
                new Coordinate(113.727166, 25.304861)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.793132, 25.152474),
                new Coordinate(113.838499, 25.139183),
                new Coordinate(113.943643, 25.133792),
                new Coordinate(113.799577, 25.162533),
                new Coordinate(113.793132, 25.152474)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.774136, 24.915802),
                new Coordinate(113.900526, 24.960427),
                new Coordinate(113.814666, 24.976133),
                new Coordinate(113.774136, 24.915802)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.744233, 25.042364),
                new Coordinate(113.745712, 25.03193),
                new Coordinate(113.780645, 25.027158),
                new Coordinate(113.830642, 25.056242),
                new Coordinate(113.830316, 25.064812),
                new Coordinate(113.825693, 25.071896),
                new Coordinate(113.757961, 25.059644),
                new Coordinate(113.706815, 25.066009),
                new Coordinate(113.744233, 25.042364)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.658321, 25.080264),

                new Coordinate(113.717595, 25.088101),
                new Coordinate(113.68599, 25.091865),
                new Coordinate(113.658321, 25.080264)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.726711, 25.088577),
                new Coordinate(113.726711, 25.088577),
                new Coordinate(113.739468, 25.081346),
                new Coordinate(113.744636, 25.083087),
                new Coordinate(113.745773, 25.086166),
                new Coordinate(113.745773, 25.086166),
                new Coordinate(113.745322, 25.089879),
                new Coordinate(113.733613, 25.089755),
                new Coordinate(113.726711, 25.088577)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.746279, 25.088949),
                new Coordinate(113.754657, 25.074007),
                new Coordinate(113.765271, 25.101996),
                new Coordinate(113.746753, 25.094568),
                new Coordinate(113.746279, 25.088949)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.560588, 25.088858),
                new Coordinate(113.560588, 25.088858),
                new Coordinate(113.624317, 24.965148),
                new Coordinate(113.644577, 25.052965),
                new Coordinate(113.6549, 25.109814),
                new Coordinate(113.638226, 25.1129),
                new Coordinate(113.627673, 25.111031),
                new Coordinate(113.560588, 25.088858)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.438228, 25.222742),
                new Coordinate(113.599192, 25.254163),
                new Coordinate(113.598679, 25.258883),
                new Coordinate(113.438228, 25.222742)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(114.097266, 24.010266),
                new Coordinate(114.097266, 24.010266),
                new Coordinate(114.117483, 23.961694),
                new Coordinate(114.14478, 23.974555),
                new Coordinate(114.17876, 24.028903),
                new Coordinate(114.16795, 24.044339),
                new Coordinate(114.133139, 24.05045),
                new Coordinate(114.097266, 24.010266)
        }));

        List<Polygon> De_polygons = new ArrayList<>();
        De_polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.521913, 24.788187),
                new Coordinate(113.523381, 24.779982),
                new Coordinate(113.523381, 24.779982),
                new Coordinate(113.526114, 24.778843),
                new Coordinate(113.556485, 24.779326),
                new Coordinate(113.556485, 24.779326),
                new Coordinate(113.533929, 24.824828),
                new Coordinate(113.521913, 24.788187),


        }));
        De_polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.551172, 24.793437),
                new Coordinate(113.560325, 24.794646),
                new Coordinate(113.562777, 24.7982),
                new Coordinate(113.561867, 24.798907),
                new Coordinate(113.553801, 24.803802),
                new Coordinate(113.551172, 24.793437),
        }));
        /*for(Polygon p : De_polygons) System.out.println(p);
        System.out.println("\n\n"+arePolygonsAdjacent(De_polygons.get(0), De_polygons.get(1)));
        System.out.println("\n\n\n\nStep0: 获得所有凸包");
        for(Polygon p : polygons) System.out.println(p);

        Geometry triangulatedNetwork = buildTriangulatedNetwork(polygons);
        System.out.println("\n\n\n");
        System.out.println(triangulatedNetwork);
        triangulatedNetwork = mergeOverlappingTriangles(triangulatedNetwork, polygons);
        System.out.println("\n\n\n\nStep6: 输出正式结果");
        System.out.println(triangulatedNetwork);*/
        System.out.println("\n\nStep: 打印结果\n");
        System.out.println(solve(polygons));
    }

    public static Geometry solve(List<Polygon> polygons) throws IOException {
        System.out.println("\n\nStep: 打印凸包");
        for(Polygon p : polygons) System.out.println(p);
        System.out.println("\n\n");
        Geometry triangulatedNetwork = buildTriangulatedNetwork(polygons);
        BufferedWriter triangulatedWriter = new BufferedWriter(new FileWriter("original_triangulatedNetwork.txt"));
        for(int i = 0; i < triangulatedNetwork.getNumGeometries(); i++){
            Geometry g = triangulatedNetwork.getGeometryN(i);
            triangulatedWriter.write(g.toText() + "\n");
        }
        //triangulatedWriter.write(triangulatedNetwork.toText());
        System.out.println("\n\nStep: 打印三角网格\n" + triangulatedNetwork + "\n\n");
        triangulatedNetwork = mergeOverlappingTriangles(triangulatedNetwork, polygons);
        return triangulatedNetwork;
    }

    /**
     * 根据一系列多边形的顶点生成三角网
     * @param polygons 多边形列表
     * @return 生成的三角网几何图形
     */
    public static Geometry buildTriangulatedNetwork(List<Polygon> polygons) {
        /*List<Coordinate> coordinates = new ArrayList<>();

        // 提取所有多边形的顶点
        for (Polygon polygon : polygons) {
            for (Coordinate coord : polygon.getCoordinates()) {
                coordinates.add(coord);
            }
        }

        // 生成Delaunay三角剖分
        DelaunayTriangulationBuilder builder = new DelaunayTriangulationBuilder();
        builder.setSites(coordinates);
        QuadEdgeSubdivision subdivision = builder.getSubdivision();

        return subdivision.getTriangles(geometryFactory);*/
        VoronoiSplit voronoiSplit = new VoronoiSplit();
        return VoronoiSplit.VT_gird(polygons);
    }

    /**
     * 合并与多边形重叠的三角网部分，但保留其他不重叠的三角形
     * @param triangulatedNetwork 生成的三角网几何图形
     * @param polygons 多边形列表
     * @return 合并后的几何图形
     */
    public static Geometry mergeOverlappingTriangles(Geometry triangulatedNetwork, List<Polygon> polygons) {
        List<Geometry> overlappingTriangles = new ArrayList<>();
        List<Geometry> triangles = new ArrayList<>();
        List<Geometry> new_triangulatedNetwork = new ArrayList<>();
        for (int i = 0; i < triangulatedNetwork.getNumGeometries(); i++) {
            Geometry triangle = triangulatedNetwork.getGeometryN(i);
            triangle = makeValid(triangle);
            new_triangulatedNetwork.add(triangle);
        }
        triangulatedNetwork = geometryFactory.createGeometryCollection(new_triangulatedNetwork.toArray(new Geometry[0]));
        for (int i = 0; i < triangulatedNetwork.getNumGeometries(); i++) {
            Geometry triangle = triangulatedNetwork.getGeometryN(i);
            triangles.add(triangle);
        }


        // 将三角网格中的所有三角形存入triangles
        for (int i = 0; i < triangulatedNetwork.getNumGeometries(); i++) {
            Geometry triangle = triangulatedNetwork.getGeometryN(i);
            triangles.add(triangle);
        }

        // 初始化is_used, 表示所有三角形是否被使用过
        Map<Geometry, Boolean> is_used = new HashMap<>();
        for (int i = 0; i < triangulatedNetwork.getNumGeometries(); i++) {
            Geometry triangle = triangulatedNetwork.getGeometryN(i);
            is_used.put(triangle, false);
        }

        // 初始化to_has, 表示所有三角形重合的那个凸包
        Map<Geometry, Polygon> to_has = new HashMap<>();
        for (int i = 0; i < triangulatedNetwork.getNumGeometries(); i++) {
            Geometry triangle = triangulatedNetwork.getGeometryN(i);
            to_has.put(triangle, null);
        }
        for (Polygon p : polygons) {
            for (int i = 0; i < triangulatedNetwork.getNumGeometries(); i++) {
                Geometry triangle = triangulatedNetwork.getGeometryN(i);
                Geometry tmp = p.intersection(triangle);
                if(tmp instanceof Polygon && !tmp.isEmpty()){
                    to_has.put(triangle, p);
                }
            }
        }
        List<Geometry> new_overlappingTriangles = new ArrayList<>();
        new_overlappingTriangles = new ArrayList<>();
        for (Geometry p : overlappingTriangles) {
            Geometry updatedPolygon = p;
            for (int j = 0; j < updatedPolygon.getNumGeometries(); j++) {
                if(updatedPolygon.getGeometryN(j) instanceof Polygon) {
                    new_overlappingTriangles.add(updatedPolygon.getGeometryN(j));
                }
            }
        }

        overlappingTriangles = new ArrayList<>(new_overlappingTriangles);
        new_overlappingTriangles.clear();

        // 临边合并
        for (Polygon p : polygons) {
            Geometry updatedPolygon = p;

            for (int i = 0; i < triangulatedNetwork.getNumGeometries(); i++) {
                Geometry triangle = triangulatedNetwork.getGeometryN(i);
                // 判断共线情况并合并或重合的情况
                if ( ( ( ( !p.intersection(triangle).isEmpty() && p.intersection(triangle) instanceof LineString) && to_has.get(triangle) == null ) || to_has.get(triangle) == p ) && !is_used.get(triangle) ) {
                    //Geometry new_triangle = makeValid(triangle);
                    if(updatedPolygon.union(triangle) instanceof GeometryCollection) {
                        System.out.println(updatedPolygon);
                        System.out.println(triangle);
                        System.out.println(updatedPolygon.union(triangle));
                    }
                    updatedPolygon = updatedPolygon.union(triangle);
                    updatedPolygon = get_shell(updatedPolygon);
                    triangles.remove(triangle);
                    //used.put(triangle, true);
                    is_used.put(triangle, true);
                }
            }
            // 将合并后的多边形添加到结果列表中
            overlappingTriangles.add(updatedPolygon);
        }


        /*for (Polygon p : polygons) {
            Geometry updatedPolygon = p;

            for (int i = 0; i < triangulatedNetwork.getNumGeometries(); i++) {

                Geometry triangle = triangulatedNetwork.getGeometryN(i);
            }
        }*/
        System.out.println("\n\nStep: 打印第一轮合并: 包含关系与临边关系合并");
        for (Geometry G : overlappingTriangles){
            System.out.println(G);
        }
        System.out.println("\n\n");

        // 合并对角形
        new_overlappingTriangles = new ArrayList<>();
        for (Geometry p : overlappingTriangles) {
            Geometry updatedPolygon = p;

            for (int i = 0; i < triangulatedNetwork.getNumGeometries(); i++) {

                Geometry triangle = triangulatedNetwork.getGeometryN(i);

                    // 判断共线情况并合并
                    if (!p.intersection(triangle).isEmpty() &&  !is_used.get(triangle)) {
                        if(p.intersection(triangle) instanceof Point) continue;
                        updatedPolygon = updatedPolygon.union(triangle);
                        triangles.remove(triangle);

                        is_used.put(triangle, true);
                }
            }

            // 将合并后的多边形添加到结果列表中
            new_overlappingTriangles.add(updatedPolygon);
        }
        overlappingTriangles = new_overlappingTriangles;

        System.out.println("\n\nStep: 打印第二轮合并: 对角关系合并");
        for (Geometry G : overlappingTriangles){
            System.out.println(G);
        }
        System.out.println("\n\n");

        new_overlappingTriangles = new ArrayList<>();
        for (Geometry p : overlappingTriangles) {
            Geometry updatedPolygon = p;
            for (int j = 0; j < updatedPolygon.getNumGeometries(); j++) {
                if(updatedPolygon.getGeometryN(j) instanceof Polygon) {
                    new_overlappingTriangles.add(updatedPolygon.getGeometryN(j));
                }
            }
        }

        overlappingTriangles = new ArrayList<>(new_overlappingTriangles);
        new_overlappingTriangles.clear();
        //if(overlappingTriangles.isEmpty()) System.out.println("NOOOOOOOOOO");
        for (Geometry p : overlappingTriangles) {
            Geometry updatedPolygon = p;

            for (int i = 0; i < triangulatedNetwork.getNumGeometries(); i++) {

                Geometry triangle = triangulatedNetwork.getGeometryN(i);

                // 判断共线情况并合并
                //System.out.println("triangle " +triangle);
                //System.out.println("updatedPolygon " +updatedPolygon + "\n");
                updatedPolygon = get_shell(updatedPolygon);
                if (!updatedPolygon.intersection(triangle).isEmpty() && !is_used.get(triangle)) {
                    if(updatedPolygon.intersection(triangle) instanceof Point) continue;
                    updatedPolygon = updatedPolygon.union(triangle);
                    triangles.remove(triangle);

                    is_used.put(triangle, true);
                }
            }

            // 将合并后的多边形添加到结果列表中
            new_overlappingTriangles.add(updatedPolygon);
        }
        overlappingTriangles = new_overlappingTriangles;


        System.out.println("\n\nStep: 打印第三轮合并");
        for (Geometry G : overlappingTriangles){
            System.out.println(G);
        }
        System.out.println("\n\n");

        /*List<Geometry> to_remove = new ArrayList<>();
        for(Geometry g1:overlappingTriangles){
            for(Geometry g2:overlappingTriangles){
                if(g1.contains(g2) && !g1.equals(g2)){
                    to_remove.add(g2);
                }
            }
        }
        for(Geometry g : to_remove){
            overlappingTriangles.remove(g);
        }
        List<Geometry> NewTriangles = new ArrayList<>();
        for(Geometry triangle: triangles){
            for(Geometry geometry: overlappingTriangles){
                if(arePolygonsAdjacent((Polygon) triangle, geometry)){
                    overlappingTriangles.set(overlappingTriangles.indexOf(geometry), geometry.union(triangle));
                    break;
                }
            }
            NewTriangles.add(triangle);
        }*/

        // 将 overlappingTriangles 转换为 GeometryCollection 然后返回
        int cnt = 0;
        List<Geometry> toRemove = new ArrayList<>();
        while(!triangles.isEmpty() && cnt < 3){
            toRemove.clear();
            System.out.println("\n\n\n\n\n存在未被囊括的三角形\n\n");
            new_overlappingTriangles = new ArrayList<>();
            for (Geometry p : overlappingTriangles) {
                Geometry updatedPolygon = p;

                for (Geometry triangle : triangles) {

                    // 判断共线情况并合并
                    if (!p.intersection(triangle).isEmpty() && !(p.intersection(triangle) instanceof Point) && !is_used.get(triangle)) {
                        //if(p.intersection(triangle) instanceof Point) continue;
                        updatedPolygon = updatedPolygon.union(triangle);
                        toRemove.add(triangle);

                        is_used.put(triangle, true);
                    }
                }

                // 将合并后的多边形添加到结果列表中
                new_overlappingTriangles.add(updatedPolygon);
            }
            overlappingTriangles = new_overlappingTriangles;
            triangles.removeAll(toRemove);
            cnt++;
        }

        System.out.println("\n\nStep: 打印其他轮合并");
        for (Geometry G : overlappingTriangles){
            System.out.println(G);
        }
        System.out.println("\n\n");

        if(!triangles.isEmpty()){
            System.out.println("\n\n仍然存在存在未被囊括的三角形\n\n\n\n");
            for(Geometry g : triangles) System.out.println(g);
        }
        return geometryFactory.createGeometryCollection(overlappingTriangles.toArray(new Geometry[0]));
    }
    public static Geometry makeValid(Geometry geometry) {
        Geometry fixedGeometry = GeometryFixer.fix(geometry);

        // 简化几何体
        geometry = TopologyPreservingSimplifier.simplify(fixedGeometry, 0.0000001);
        // 检查几何图形是否有效
        if (!geometry.isValid()) {
            System.out.println("Invalid geometry, applying buffer(0) to fix...");
            geometry = geometry.buffer(0);
        }

        // 使用 GeometryPrecisionReducer 来降低几何图形的精度
        PrecisionModel precisionModel = new PrecisionModel(1e10);
        GeometryPrecisionReducer reducer = new GeometryPrecisionReducer(precisionModel);
        geometry = reducer.reduce(geometry);

        return geometry;
    }


    public static Geometry get_shell(Geometry updatedPolygon){
        //updatedPolygon = makeValid(updatedPolygon);
        if(!updatedPolygon.getInteriorPoint().isEmpty()){
            // 将updatedPolygon赋值为updatedPolygon与它的内轮廓形成的多边形的union
            GeometryFactory geometryFactory = new GeometryFactory();
            //System.out.println(updatedPolygon);
            for (int j = 0; j < ((Polygon) updatedPolygon).getNumInteriorRing(); j++) {
                LinearRing interiorRing = ((Polygon) updatedPolygon).getInteriorRingN(j);
                Polygon interiorPolygon = geometryFactory.createPolygon(interiorRing, null);
                updatedPolygon = updatedPolygon.union(interiorPolygon);
            }
        }
        return updatedPolygon;
    }

    public static boolean arePolygonsAdjacent(Polygon poly1, Geometry poly2) {
        // 获取两个多边形的边界
        Geometry boundary1 = poly1.getBoundary();
        Geometry boundary2 = poly2.getBoundary();
        int cnt = 0;
        // 遍历第一个多边形的每一个顶点
        for (int i = 0; i < boundary1.getNumGeometries(); i++) {
            Coordinate[] coords1 = ((LineString) boundary1.getGeometryN(i)).getCoordinates();

            // 遍历第二个多边形的每一个顶点
            for (int j = 0; j < boundary2.getNumGeometries(); j++) {
                Coordinate[] coords2 = ((LineString) boundary2.getGeometryN(j)).getCoordinates();

                // 检查是否有两个连续的点重合
                for (int k = 0; k < coords1.length - 1; k++) {
                    for (int l = 0; l < coords2.length - 1; l++) {
                        if (coords1[k].equals2D(coords2[l])) {
                            cnt++;
                        }
                    }
                }
            }
        }
        if(cnt >= 2) return true;
        return false;
    }
}
