import{r}from"./index-m25zEilF.js";function i(e){return r.post({url:"/userservice/user/add",headers:{"Content-Type":"application/x-www-form-urlencoded"},data:e})}function l(){return r.get({url:"/userservice/user/get/operations"})}function c(){return r.get({url:"/userservice/user/get/roles"})}function p(e){return r.post({url:"/userservice/user/set",headers:{"Content-Type":"application/x-www-form-urlencoded"},data:e})}function d(e){return r.post({url:"/userservice/user/avatar",data:e})}function f(e){return r.get({url:"/userservice/user/search",params:e})}function v(e){return r.get({url:`/userservice/user/get/role/operations?role_id=${e}`})}function g(e){return r.get({url:`/userservice/user/get?workNumber=${e}`})}function m(e){return r.post({headers:{"Content-Type":"application/x-www-form-urlencoded"},url:"/userservice/user/update",data:e})}function w(e){return r.post({url:"/userservice/user/update/password",data:e})}const y=(e,s,u)=>{const t=e[s];return t?typeof t=="function"?t():Promise.resolve(t):new Promise((o,n)=>{(typeof queueMicrotask=="function"?queueMicrotask:setTimeout)(n.bind(null,new Error("Unknown variable dynamic import: "+s+(s.split("/").length!==u?". Note that variables only represent file names one level deep.":""))))})};export{y as _,i as a,c as b,f as c,v as d,g as e,m as f,l as g,w as p,p as s,d as u};
