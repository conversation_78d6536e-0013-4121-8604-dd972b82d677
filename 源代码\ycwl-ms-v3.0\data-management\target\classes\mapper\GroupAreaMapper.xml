<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.datamanagement.mapper.GroupAreaMapper">
    <update id="updateTeamByareaId">
        update group_areas set group_id=#{teamId} where area_id=#{areaId}
    </update>
    <update id="updateTeamIdByTeamId">
        update group_areas SET group_id =0 WHERE group_id=#{teamId}
    </update>

    <select id="selectTeamId" resultType="java.lang.Integer">
        select group_id from group_areas where area_id=#{areaId}
    </select>
    <select id="selectAreaIdCount" resultType="java.lang.Integer">
        select COUNT(group_id) from group_areas where area_id =#{areaId}
    </select>
</mapper>