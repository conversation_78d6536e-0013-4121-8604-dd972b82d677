package com.ict.ycwl.pathcalculate.pojo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Comparator;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LngAndLat {
    private double longitude;
    private double latitude;

    static class LatitudeComparator implements Comparator<LngAndLat> {
        @Override
        public int compare(LngAndLat p1, LngAndLat p2) {
            return Double.compare(p1.getLatitude(), p2.getLatitude());
        }
    }

    public static LngAndLat[] sortPointsByLatitude(LngAndLat[] points) {
        Arrays.sort(points, new LatitudeComparator());
        return points;
    }

    static class LongitudeComparator implements Comparator<LngAndLat> {
        @Override
        public int compare(LngAndLat p1, LngAndLat p2) {
            return Double.compare(p1.getLongitude(), p2.getLongitude());
        }
    }

    public static LngAndLat[] sortPointsByLongitude(LngAndLat[] points) {
        Arrays.sort(points, new LongitudeComparator());
        return points;
    }
}
