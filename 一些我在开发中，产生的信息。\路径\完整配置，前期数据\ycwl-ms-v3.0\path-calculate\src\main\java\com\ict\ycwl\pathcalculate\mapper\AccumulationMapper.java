package com.ict.ycwl.pathcalculate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ict.ycwl.pathcalculate.pojo.Accumulation;
import com.ict.ycwl.pathcalculate.pojo.LngAndLat;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Repository
public interface AccumulationMapper extends BaseMapper<Accumulation> {

    /**
     * 查询数据的经度和纬度
     *
     * @return 返回所有聚集区的经纬度的一个数组
     */
    List<LngAndLat> selectCoordinates();

    /**
     * 根据经纬度找到聚集区，并将聚集区的path_accumulation_id字段改成accumulationId值
     *
     * @param accumulationId 路径聚集区id
     * @param longitude      经度
     * @param latitude       纬度
     */
    void updateAccumulationIdByLonAndLat(Long accumulationId, double longitude, double latitude);
}
