package com.ict.ycwl.user.controller;


import com.ict.ycwl.common.web.AjaxResult;
import com.ict.ycwl.user.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags ="用户管理API")
@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    private UserService userService;


    @ApiOperation("添加用户接口")
    @PostMapping("/add")
    public AjaxResult AddUser(@RequestHeader("Authorization") String authorization,
                              @ApiParam(value = "电话号码",required = true)
                                  @RequestParam("phone")    String phone,
                              @ApiParam(name = "email",value = "电子邮箱")
                              @RequestParam(required = false)   String email,
                              @ApiParam(value = "部门",required = true)
                              @RequestParam("department")   String department,
                              @ApiParam(value = "入职时间",required = true)
                              @RequestParam("sign_time")    String signTime,
                              @ApiParam(value = "工号",required = true)
                              @RequestParam("work_number")  String workNumber,
                              @ApiParam(value = "姓名",required = true)
                              @RequestParam("user_name")    String userName,
                              @ApiParam(value = "角色对应的id",required = true)
                              @RequestParam("role_id")      Long roleId)
    {
        return userService.userAdd(authorization,phone,email,department,signTime,workNumber,userName,roleId);

    }

    @ApiOperation("用户修改密码接口")
    @PostMapping("/update/password")
    public AjaxResult updatePassword(@ApiParam(value = "用户id",required = true)
                                     @RequestParam("userId") Long userId,
                                     @ApiParam(value = "旧密码",required = true)
                                     @RequestParam("currentPassword") String currentPassword,
                                     @ApiParam(value = "新密码",required = true)
                                     @RequestParam("newPassword") String newPassword,
                                     @ApiParam(value = "确认新密码",required = true)
                                     @RequestParam("confirmPassword") String confirmPassword){
        return userService.userUpdatePassword(userId,currentPassword,newPassword,confirmPassword);
    }

    @ApiOperation("获取编辑的用户信息接口")
    @GetMapping("/get")
    public AjaxResult getUserInfo(@ApiParam(value = "用户工号",required = true)
                                  @RequestParam("workNumber") String workNumber){
        return userService.getUserInfo(workNumber);
    }

    @ApiOperation("修改用户信息接口")
    @PostMapping("/update")
    public AjaxResult updateUser(@RequestHeader("Authorization") String authorization,
                                 @ApiParam(value = "电话号码",required = true)
                                     @RequestParam("phone")    String phone,
                                 @ApiParam(name = "email",value = "电子邮箱")
                                     @RequestParam(required = false)   String email,
                                 @ApiParam(value = "部门",required = true)
                                     @RequestParam("department")   String department,
                                 @ApiParam(value = "入职时间",name = "signTime")
                                     @RequestParam(required = false)    String signTime,
                                 @ApiParam(value = "工号",required = true)
                                     @RequestParam("work_number")  String workNumber,
                                 @ApiParam(value = "姓名",required = true)
                                     @RequestParam("user_name")    String userName,
                                 @ApiParam(value = "角色对应的id",required = true)
                                     @RequestParam("role_id")      Long roleId,
                                 @ApiParam(value = "新密码",name = "newPassword")
                                     @RequestParam(required = false) String newPassword)
    {
        return userService.updateUser(authorization,phone,email,department,signTime,workNumber,userName,roleId,newPassword);
    }


}
