package com.ict.ycwl.pathcalculate.utils.getColorUtils;

import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Polygon;
import org.locationtech.jts.geom.PrecisionModel;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.GeometryCollection;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import java.util.ArrayList;
import java.util.List;

public class Test {

    private GeometryFactory geometryFactory = new GeometryFactory(new PrecisionModel(), 4326);

    public Polygon removePointsInsidePolygon(Polygon polygon1, Polygon polygon2) {
        Coordinate[] coordinates1 = polygon1.getCoordinates();
        List<Coordinate> newCoords = new ArrayList<>();

        // 遍历 polygon1 的所有点
        for (Coordinate coord : coordinates1) {
            // 如果该点不在 polygon2 内，则添加到新的坐标列表中
            if (!polygon2.contains(geometryFactory.createPoint(coord))) {
                newCoords.add(coord);
            }
        }

        // 确保新的坐标列表形成一个闭合的多边形
        if (!newCoords.isEmpty() && !newCoords.get(0).equals2D(newCoords.get(newCoords.size() - 1))) {
            newCoords.add(newCoords.get(0));
        }

        // 构建新的多边形
        return geometryFactory.createPolygon(newCoords.toArray(new Coordinate[0]));
    }

    public static void main(String[] args) {
        GeometryFactory geometryFactory = new GeometryFactory(new PrecisionModel(), 4326);

        // 示例多边形1
        Polygon polygon1 = geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.727166, 25.304861),
                new Coordinate(113.733395, 25.25601),
                new Coordinate(113.865906, 25.201329),
                new Coordinate(113.937188, 25.303314),
                new Coordinate(113.955875, 25.330793),
                new Coordinate(113.836659, 25.831624),
                new Coordinate(113.727166, 25.304861)
        });

        // 示例多边形2
        Polygon polygon2 = geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.733395, 25.25601),
                new Coordinate(113.800248, 25.902548),
                new Coordinate(113.900526, 25.960427),
                new Coordinate(113.733395, 25.25601)
        });

        Test remover = new Test();
        Polygon newPolygon = remover.removePointsInsidePolygon(polygon1, polygon2);

        System.out.println("Original Polygon: " + polygon1 + "\n" + polygon2 + "\n\n");
        System.out.println("New Polygon: " + newPolygon);
    }
}
