import com.ict.ycwl.clustercalculate.pojo.DoublePoint;
import com.ict.ycwl.clustercalculate.service.CalculateService;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import static org.junit.jupiter.api.Assertions.assertNotNull;

public class calculateTest {

    @Test
    public void abc() {
        DoublePoint doublePoint1 = new DoublePoint(new double[]{11.11, 22.22});
        DoublePoint doublePoint2 = new DoublePoint(new double[]{11.11, 22.22});
        System.out.println(doublePoint1.equals(doublePoint2));
    }

}

