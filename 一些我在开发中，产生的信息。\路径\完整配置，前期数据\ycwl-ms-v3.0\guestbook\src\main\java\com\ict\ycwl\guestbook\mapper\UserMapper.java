package com.ict.ycwl.guestbook.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ict.ycwl.guestbook.api.vo.ConditionUserVo;
import com.ict.ycwl.guestbook.api.vo.UserVo;
import com.ict.ycwl.guestbook.domain.User;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface UserMapper extends BaseMapper<User> {

    List<UserVo> selectUserByGroupId(Long groupId);

    List<ConditionUserVo> selectUserForCondition(String position);

}
