function f(n){var t=n;function r(){return t}function e(i){t=i}return{get:r,set:e}}function d(n,t,r,e){let i,c;if(n&&t in n&&r in n[t]){typeof e>"u"&&(e=n[t][r]),i=f(e);try{Object.defineProperty(n[t],r,i)}catch{c={},c[r]=i;try{n[t]=Object.create(n[t],c)}catch(u){console.error(u)}}}}function l(){let n=window.navigator.userAgent;(n.includes("UOS")||n.includes("Linux"))&&(n=n.replace(/\(.*?\)/,"(Windows NT 10.0; Win64;x64)"),d(window,"navigator","userAgent",n),console.log(window.navigator.userAgent))}export{l as m};
