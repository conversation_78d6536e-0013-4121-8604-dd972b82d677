package com.ict.datamanagement.controller;

import com.ict.datamanagement.common.BaseResponse;
import com.ict.datamanagement.common.ResultUtils;
import com.ict.datamanagement.constant.OptionalDataType;
import com.ict.datamanagement.service.AreaService;
import com.ict.datamanagement.service.GroupService;
import com.ict.datamanagement.service.RouteService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@ApiOperation("公共接口")
@RestController
public class CommonController {

    @Resource
    private RouteService routeService;

    @Resource
    private GroupService groupService;

    @Resource
    private AreaService areaService;


    @ApiOperation("获取下拉框数据")
    @GetMapping("/getOptionalData/{dataType}")
    public BaseResponse<?> getOptionalData(@PathVariable("dataType") String dataType){
        if (OptionalDataType.GROUP_TYPE.equals(dataType)){
            return ResultUtils.success(groupService.getOptionalData());
        } else if (OptionalDataType.ROUTE_TYPE.equals(dataType)) {
            return ResultUtils.success(routeService.getOptionalData());
        }else if (OptionalDataType.AREA_TYPE.equals(dataType)) {
            return ResultUtils.success(areaService.getOptionalData());
        }

        return null;
    }
}
