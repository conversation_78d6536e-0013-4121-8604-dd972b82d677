package com.ict.ycwl.pathcalculate.pojo;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("gear")
public class Gear {
    @TableId(type = IdType.ASSIGN_ID)
    private Long gearId;

    private String gearName;

    private String cargoWeight;

}
