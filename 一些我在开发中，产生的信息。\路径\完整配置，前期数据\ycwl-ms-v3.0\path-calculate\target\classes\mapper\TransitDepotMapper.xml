<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.pathcalculate.mapper.TransitDepotMapper">
    <select id="selectByName" resultType="com.ict.ycwl.pathcalculate.pojo.TransitDepot">
        SELECT *
        FROM transit_depot
        WHERE transit_depot_name LIKE CONCAT('%', #{name}, '%');
    </select>


</mapper>