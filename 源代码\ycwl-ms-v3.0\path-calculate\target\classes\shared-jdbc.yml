jjking:
  dbPath: E:\\www\\wwwroot\\ycwl\\masterDatasource.txt
spring:
  datasource:
    druid:
      # 所有数据源的默认 Druid 配置
      default-config:
        initial-size: 5
        max-active: 20
        min-idle: 5
        max-wait: 60000
        pool-prepared-statements: true
        max-pooled-prepared-statements: 20
        time-between-eviction-runs-millis: 60000
        min-evictable-idle-time-millis: 300000
        validation-query: SELECT 1
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
        filters: stat,wall,log4j2
      # 数据源配置
      master:
        url: **************************************************************************************************
        username: root
        password: 123
        driver-class-name: com.mysql.jdbc.Driver
      slave1:
        url: *********************************************************************************************************
        username: root
        password: 123
        driver-class-name: com.mysql.jdbc.Driver
      slave2:
        url: *********************************************************************************************************
        username: root
        password: 123
        driver-class-name: com.mysql.jdbc.Driver
      slave3:
        url: *********************************************************************************************************
        username: root
        password: 123
        driver-class-name: com.mysql.jdbc.Driver