import{bs as Te,az as G,Q as pe,bt as Ur,bu as hp,bv as Os,bw as wt,af as vp,N as hh,aq as D,ay as tt,bx as fp,by as pp,ar as F,bz as vh,as as Yt,bA as fh,bB as jt,bC as dp,bD as gp,bE as ie,bF as ph,bG as yp,bH as mp,bI as Mt,bJ as Sp,bK as vi,bL as xp,bM as Yr,bN as bp,an as it,aw as st,bO as _p,bP as wp,bQ as Ap,bR as Tp,bS as Dp,bT as Cp,bU as Ip,bV as dh,bW as Io,bX as Lp,bY as Mp,bZ as Pp,b_ as Rp,av as U,ax as Nt,b$ as Bs,c0 as Ep,c1 as Pe,c2 as gh,w as at,l as Rt,c3 as O,c4 as Dt,c5 as Za,c6 as kp,c7 as Vp,c8 as Np,c9 as E,Z as ut,ca as cr,a6 as Gt,ac as dt,cb as xe,cc as Ht,cd as xt,ce as Wt,cf as zt,V as le,cg as yh,ch as xr,ci as mh,_ as Ye,cj as Sh,aF as bt,ck as fi,aH as Ft,am as W,ao as q,cl as Lo,cm as zp,cn as oa,aE as Ct,co as xh,c as br,K as Pt,P as X,h as Et,k as ht,cp as pi,cq as di,bd as K,cr as Op,cs as Bp,U as se,ap as ot,ct as _r,cu as sa,cv as Gp,cw as Zt,aA as ft,M as Ut,m as ue,A as gi,aC as kt,cx as Fp,cy as $e,L as la,Y as bh,a9 as ne,aD as Ot,aG as de,cz as Bn,cA as Hp,aY as yi,cB as Gs,cC as Xe,cD as _h,X as pt,cE as B,S as oe,O as Wp,cF as Zp,T as Mo,W as Up,cG as Yp,cH as Fs,cI as mi,cJ as wh,cK as Ke,cL as wr,cM as et,cN as $p,cO as Xp,aX as Kp,cP as Ah,cQ as dr,cR as gr,cS as Si,cT as xi,cU as qp,cV as ua,cW as Th,cX as ya,cY as Gn,cZ as jp,c_ as Jp,c$ as Vt,d0 as fe,d1 as Po,d2 as qt,d3 as Qp,d4 as td,aB as Dh,d5 as Ch,d6 as Ih,d7 as Lh,d8 as Ua,d9 as ed,J as Mh,da as Ya,db as ye,at as mt,dc as Fn,dd as be,de as bi,df as rd,d as yt,dg as Ph,dh as Ar,di as Rh,dj as Hs,dk as Nr,dl as Ws,dm as ad,dn as Ni,dp as Oa,dq as Eh,dr as Wr,ds as $a,o as Zs,dt as Ie,du as ca,dv as id,dw as nd,dx as hr,dy as Us,dz as od,dA as Hn,dB as Wn,G as Ro,dC as $r,dD as Xr,dE as ha,dF as _i,dG as sd,dH as kh,dI as ld,dJ as Vh,dK as ud,dL as Ys,dM as cd,dN as hd,dO as $s,y as vd,dP as fd,dQ as wi,dR as Nh,b as Eo,n as Xa,dS as Le,p as re,dT as ko,a5 as vr,$ as pd,dU as Vo,dV as Ka,dW as Zn,dX as Un,dY as dd,B as zi,dZ as gd,d_ as zh,d$ as No,e0 as Ai,e1 as Oi,e2 as yd,e3 as md,e4 as zr,e5 as Sd,e6 as Xs,e7 as xd,e8 as Ks,e9 as Ti,u as bd,ea as Yn,eb as Je,ec as qs,ed as _d,ee as wd,ef as Ad,eg as ma,eh as Td,v as Dd,ei as Cd,ej as Kr,ek as Id,el as Ld,em as Oh,en as js,eo as Md,ep as Bh,a8 as Pd,a4 as Gh,eq as Fh,er as Rd,es as Hh,et as Wh,eu as Js,ev as Zh,ew as Qs,ex as va,a as Uh,ey as zo,ez as Ed,eA as kd,eB as Vd,eC as Nd,eD as zd,eE as Od,eF as Oo,eG as Bd,eH as Bo,eI as Gd,eJ as tl,z as Fd,eK as Hd,eL as Wd,eM as Zd,eN as Ud,r as Yd,eO as $d,eP as Xd,eQ as Ze,aj as Yh,a1 as Go,aQ as $h,eR as Kd,eS as el,eT as Xh,eU as qd,eV as jd,eW as Kh,eX as qh,eY as Dr,eZ as Sa,e_ as Jd,e$ as Fo,j as fr,f0 as $n,f1 as Qd,f2 as tg,s as eg,f3 as qa,f4 as rg,f5 as ag,f6 as Di,g as ig,f7 as ng,f8 as og,f9 as sg,E as rl,br as lg,bo as ug,bn as cg,bk as hg,bj as vg,bi as fg,bm as pg,bl as dg,bq as gg,bp as yg}from"./universalTransition-CVj2Okbb.js";var Bi=Math.sin,Gi=Math.cos,jh=Math.PI,Ve=Math.PI*2,mg=180/jh,Jh=function(){function a(){}return a.prototype.reset=function(e){this._start=!0,this._d=[],this._str="",this._p=Math.pow(10,e||4)},a.prototype.moveTo=function(e,t){this._add("M",e,t)},a.prototype.lineTo=function(e,t){this._add("L",e,t)},a.prototype.bezierCurveTo=function(e,t,r,i,n,o){this._add("C",e,t,r,i,n,o)},a.prototype.quadraticCurveTo=function(e,t,r,i){this._add("Q",e,t,r,i)},a.prototype.arc=function(e,t,r,i,n,o){this.ellipse(e,t,r,r,0,i,n,o)},a.prototype.ellipse=function(e,t,r,i,n,o,s,l){var u=s-o,c=!l,h=Math.abs(u),v=Te(h-Ve)||(c?u>=Ve:-u>=Ve),f=u>0?u%Ve:u%Ve+Ve,p=!1;v?p=!0:Te(h)?p=!1:p=f>=jh==!!c;var d=e+r*Gi(o),g=t+i*Bi(o);this._start&&this._add("M",d,g);var y=Math.round(n*mg);if(v){var m=1/this._p,S=(c?1:-1)*(Ve-m);this._add("A",r,i,y,1,+c,e+r*Gi(o+S),t+i*Bi(o+S)),m>.01&&this._add("A",r,i,y,0,+c,d,g)}else{var x=e+r*Gi(s),b=t+i*Bi(s);this._add("A",r,i,y,+p,+c,x,b)}},a.prototype.rect=function(e,t,r,i){this._add("M",e,t),this._add("l",r,0),this._add("l",0,i),this._add("l",-r,0),this._add("Z")},a.prototype.closePath=function(){this._d.length>0&&this._add("Z")},a.prototype._add=function(e,t,r,i,n,o,s,l,u){for(var c=[],h=this._p,v=1;v<arguments.length;v++){var f=arguments[v];if(isNaN(f)){this._invalid=!0;return}c.push(Math.round(f*h)/h)}this._d.push(e+c.join(" ")),this._start=e==="Z"},a.prototype.generateStr=function(){this._str=this._invalid?"":this._d.join(""),this._d=[]},a.prototype.getStr=function(){return this._str},a}(),Ho="none",Sg=Math.round;function xg(a){var e=a.fill;return e!=null&&e!==Ho}function bg(a){var e=a.stroke;return e!=null&&e!==Ho}var Xn=["lineCap","miterLimit","lineJoin"],_g=G(Xn,function(a){return"stroke-"+a.toLowerCase()});function wg(a,e,t,r){var i=e.opacity==null?1:e.opacity;if(t instanceof pe){a("opacity",i);return}if(xg(e)){var n=Ur(e.fill);a("fill",n.color);var o=e.fillOpacity!=null?e.fillOpacity*n.opacity*i:n.opacity*i;o<1&&a("fill-opacity",o)}else a("fill",Ho);if(bg(e)){var s=Ur(e.stroke);a("stroke",s.color);var l=e.strokeNoScale?t.getLineScale():1,u=l?(e.lineWidth||0)/l:0,c=e.strokeOpacity!=null?e.strokeOpacity*s.opacity*i:s.opacity*i,h=e.strokeFirst;if(u!==1&&a("stroke-width",u),h&&a("paint-order",h?"stroke":"fill"),c<1&&a("stroke-opacity",c),e.lineDash){var v=hp(t),f=v[0],p=v[1];f&&(p=Sg(p||0),a("stroke-dasharray",f.join(",")),(p||r)&&a("stroke-dashoffset",p))}for(var d=0;d<Xn.length;d++){var g=Xn[d];if(e[g]!==Os[g]){var y=e[g]||Os[g];y&&a(_g[d],y)}}}}var Qh="http://www.w3.org/2000/svg",tv="http://www.w3.org/1999/xlink",Ag="http://www.w3.org/2000/xmlns/",Tg="http://www.w3.org/XML/1998/namespace",al="ecmeta_";function ev(a){return document.createElementNS(Qh,a)}function At(a,e,t,r,i){return{tag:a,attrs:t||{},children:r,text:i,key:e}}function Dg(a,e){var t=[];if(e)for(var r in e){var i=e[r],n=r;i!==!1&&(i!==!0&&i!=null&&(n+='="'+i+'"'),t.push(n))}return"<"+a+" "+t.join(" ")+">"}function Cg(a){return"</"+a+">"}function Wo(a,e){e=e||{};var t=e.newline?`
`:"";function r(i){var n=i.children,o=i.tag,s=i.attrs,l=i.text;return Dg(o,s)+(o!=="style"?vp(l):l||"")+(n?""+t+G(n,function(u){return r(u)}).join(t)+t:"")+Cg(o)}return r(a)}function Ig(a,e,t){t=t||{};var r=t.newline?`
`:"",i=" {"+r,n=r+"}",o=G(wt(a),function(l){return l+i+G(wt(a[l]),function(u){return u+":"+a[l][u]+";"}).join(r)+n}).join(r),s=G(wt(e),function(l){return"@keyframes "+l+i+G(wt(e[l]),function(u){return u+i+G(wt(e[l][u]),function(c){var h=e[l][u][c];return c==="d"&&(h='path("'+h+'")'),c+":"+h+";"}).join(r)+n}).join(r)+n}).join(r);return!o&&!s?"":["<![CDATA[",o,s,"]]>"].join(r)}function Kn(a){return{zrId:a,shadowCache:{},patternCache:{},gradientCache:{},clipPathCache:{},defs:{},cssNodes:{},cssAnims:{},cssStyleCache:{},cssAnimIdx:0,shadowIdx:0,gradientIdx:0,patternIdx:0,clipPathIdx:0}}function il(a,e,t,r){return At("svg","root",{width:a,height:e,xmlns:Qh,"xmlns:xlink":tv,version:"1.1",baseProfile:"full",viewBox:r?"0 0 "+a+" "+e:!1},t)}var Lg=0;function rv(){return Lg++}var nl={cubicIn:"0.32,0,0.67,0",cubicOut:"0.33,1,0.68,1",cubicInOut:"0.65,0,0.35,1",quadraticIn:"0.11,0,0.5,0",quadraticOut:"0.5,1,0.89,1",quadraticInOut:"0.45,0,0.55,1",quarticIn:"0.5,0,0.75,0",quarticOut:"0.25,1,0.5,1",quarticInOut:"0.76,0,0.24,1",quinticIn:"0.64,0,0.78,0",quinticOut:"0.22,1,0.36,1",quinticInOut:"0.83,0,0.17,1",sinusoidalIn:"0.12,0,0.39,0",sinusoidalOut:"0.61,1,0.88,1",sinusoidalInOut:"0.37,0,0.63,1",exponentialIn:"0.7,0,0.84,0",exponentialOut:"0.16,1,0.3,1",exponentialInOut:"0.87,0,0.13,1",circularIn:"0.55,0,1,0.45",circularOut:"0,0.55,0.45,1",circularInOut:"0.85,0,0.15,1"},ze="transform-origin";function Mg(a,e,t){var r=F({},a.shape);F(r,e),a.buildPath(t,r);var i=new Jh;return i.reset(fh(a)),t.rebuildPath(i,1),i.generateStr(),i.getStr()}function Pg(a,e){var t=e.originX,r=e.originY;(t||r)&&(a[ze]=t+"px "+r+"px")}var Rg={fill:"fill",opacity:"opacity",lineWidth:"stroke-width",lineDashOffset:"stroke-dashoffset"};function av(a,e){var t=e.zrId+"-ani-"+e.cssAnimIdx++;return e.cssAnims[t]=a,t}function Eg(a,e,t){var r=a.shape.paths,i={},n,o;if(D(r,function(l){var u=Kn(t.zrId);u.animation=!0,Ci(l,{},u,!0);var c=u.cssAnims,h=u.cssNodes,v=wt(c),f=v.length;if(f){o=v[f-1];var p=c[o];for(var d in p){var g=p[d];i[d]=i[d]||{d:""},i[d].d+=g.d||""}for(var y in h){var m=h[y].animation;m.indexOf(o)>=0&&(n=m)}}}),!!n){e.d=!1;var s=av(i,t);return n.replace(o,s)}}function ol(a){return tt(a)?nl[a]?"cubic-bezier("+nl[a]+")":fp(a)?a:"":""}function Ci(a,e,t,r){var i=a.animators,n=i.length,o=[];if(a instanceof hh){var s=Eg(a,e,t);if(s)o.push(s);else if(!n)return}else if(!n)return;for(var l={},u=0;u<n;u++){var c=i[u],h=[c.getMaxTime()/1e3+"s"],v=ol(c.getClip().easing),f=c.getDelay();v?h.push(v):h.push("linear"),f&&h.push(f/1e3+"s"),c.getLoop()&&h.push("infinite");var p=h.join(" ");l[p]=l[p]||[p,[]],l[p][1].push(c)}function d(m){var S=m[1],x=S.length,b={},_={},w={},A="animation-timing-function";function C(J,$,lt){for(var nt=J.getTracks(),vt=J.getMaxTime(),St=0;St<nt.length;St++){var Lt=nt[St];if(Lt.needsAnimate()){var ct=Lt.keyframes,gt=Lt.propName;if(lt&&(gt=lt(gt)),gt)for(var _t=0;_t<ct.length;_t++){var Xt=ct[_t],Bt=Math.round(Xt.time/vt*100)+"%",ke=ol(Xt.easing),Ae=Xt.rawValue;(tt(Ae)||jt(Ae))&&($[Bt]=$[Bt]||{},$[Bt][gt]=Xt.rawValue,ke&&($[Bt][A]=ke))}}}}for(var T=0;T<x;T++){var I=S[T],L=I.targetName;L?L==="shape"&&C(I,_):!r&&C(I,b)}for(var M in b){var R={};pp(R,a),F(R,b[M]);var P=vh(R),k=b[M][A];w[M]=P?{transform:P}:{},Pg(w[M],R),k&&(w[M][A]=k)}var V,N=!0;for(var M in _){w[M]=w[M]||{};var z=!V,k=_[M][A];z&&(V=new dp);var H=V.len();V.reset(),w[M].d=Mg(a,_[M],V);var Z=V.len();if(!z&&H!==Z){N=!1;break}k&&(w[M][A]=k)}if(!N)for(var M in w)delete w[M].d;if(!r)for(var T=0;T<x;T++){var I=S[T],L=I.targetName;L==="style"&&C(I,w,function(nt){return Rg[nt]})}for(var Y=wt(w),Q=!0,j,T=1;T<Y.length;T++){var rt=Y[T-1],It=Y[T];if(w[rt][ze]!==w[It][ze]){Q=!1;break}j=w[rt][ze]}if(Q&&j){for(var M in w)w[M][ze]&&delete w[M][ze];e[ze]=j}if(Yt(Y,function(J){return wt(w[J]).length>0}).length){var $t=av(w,t);return $t+" "+m[0]+" both"}}for(var g in l){var s=d(l[g]);s&&o.push(s)}if(o.length){var y=t.zrId+"-cls-"+rv();t.cssNodes["."+y]={animation:o.join(",")},e.class=y}}function kg(a,e,t){if(!a.ignore)if(a.isSilent()){var r={"pointer-events":"none"};sl(r,e,t)}else{var i=a.states.emphasis&&a.states.emphasis.style?a.states.emphasis.style:{},n=i.fill;if(!n){var o=a.style&&a.style.fill,s=a.states.select&&a.states.select.style&&a.states.select.style.fill,l=a.currentStates.indexOf("select")>=0&&s||o;l&&(n=gp(l))}var u=i.lineWidth;if(u){var c=!i.strokeNoScale&&a.transform?a.transform[0]:1;u=u/c}var r={cursor:"pointer"};n&&(r.fill=n),i.stroke&&(r.stroke=i.stroke),u&&(r["stroke-width"]=u),sl(r,e,t)}}function sl(a,e,t,r){var i=JSON.stringify(a),n=t.cssStyleCache[i];n||(n=t.zrId+"-cls-"+rv(),t.cssStyleCache[i]=n,t.cssNodes["."+n+":hover"]=a),e.class=e.class?e.class+" "+n:n}var qr=Math.round;function iv(a){return a&&tt(a.src)}function nv(a){return a&&st(a.toDataURL)}function Zo(a,e,t,r){wg(function(i,n){var o=i==="fill"||i==="stroke";o&&dh(n)?sv(e,a,i,r):o&&Io(n)?lv(t,a,i,r):a[i]=n,o&&r.ssr&&n==="none"&&(a["pointer-events"]="visible")},e,t,!1),Fg(t,a,r)}function Uo(a,e){var t=Lp(e);t&&(t.each(function(r,i){r!=null&&(a[(al+i).toLowerCase()]=r+"")}),e.isSilent()&&(a[al+"silent"]="true"))}function ll(a){return Te(a[0]-1)&&Te(a[1])&&Te(a[2])&&Te(a[3]-1)}function Vg(a){return Te(a[4])&&Te(a[5])}function Yo(a,e,t){if(e&&!(Vg(e)&&ll(e))){var r=1e4;a.transform=ll(e)?"translate("+qr(e[4]*r)/r+" "+qr(e[5]*r)/r+")":Mp(e)}}function ul(a,e,t){for(var r=a.points,i=[],n=0;n<r.length;n++)i.push(qr(r[n][0]*t)/t),i.push(qr(r[n][1]*t)/t);e.points=i.join(" ")}function cl(a){return!a.smooth}function Ng(a){var e=G(a,function(t){return typeof t=="string"?[t,t]:t});return function(t,r,i){for(var n=0;n<e.length;n++){var o=e[n],s=t[o[0]];s!=null&&(r[o[1]]=qr(s*i)/i)}}}var zg={circle:[Ng(["cx","cy","r"])],polyline:[ul,cl],polygon:[ul,cl]};function Og(a){for(var e=a.animators,t=0;t<e.length;t++)if(e[t].targetName==="shape")return!0;return!1}function ov(a,e){var t=a.style,r=a.shape,i=zg[a.type],n={},o=e.animation,s="path",l=a.style.strokePercent,u=e.compress&&fh(a)||4;if(i&&!e.willUpdate&&!(i[1]&&!i[1](r))&&!(o&&Og(a))&&!(l<1)){s=a.type;var c=Math.pow(10,u);i[0](r,n,c)}else{var h=!a.path||a.shapeChanged();a.path||a.createPathProxy();var v=a.path;h&&(v.beginPath(),a.buildPath(v,a.shape),a.pathUpdated());var f=v.getVersion(),p=a,d=p.__svgPathBuilder;(p.__svgPathVersion!==f||!d||l!==p.__svgPathStrokePercent)&&(d||(d=p.__svgPathBuilder=new Jh),d.reset(u),v.rebuildPath(d,l),d.generateStr(),p.__svgPathVersion=f,p.__svgPathStrokePercent=l),n.d=d.getStr()}return Yo(n,a.transform),Zo(n,t,a,e),Uo(n,a),e.animation&&Ci(a,n,e),e.emphasis&&kg(a,n,e),At(s,a.id+"",n)}function Bg(a,e){var t=a.style,r=t.image;if(r&&!tt(r)&&(iv(r)?r=r.src:nv(r)&&(r=r.toDataURL())),!!r){var i=t.x||0,n=t.y||0,o=t.width,s=t.height,l={href:r,width:o,height:s};return i&&(l.x=i),n&&(l.y=n),Yo(l,a.transform),Zo(l,t,a,e),Uo(l,a),e.animation&&Ci(a,l,e),At("image",a.id+"",l)}}function Gg(a,e){var t=a.style,r=t.text;if(r!=null&&(r+=""),!(!r||isNaN(t.x)||isNaN(t.y))){var i=t.font||_p,n=t.x||0,o=wp(t.y||0,Ap(i),t.textBaseline),s=Tp[t.textAlign]||t.textAlign,l={"dominant-baseline":"central","text-anchor":s};if(Dp(t)){var u="",c=t.fontStyle,h=Cp(t.fontSize);if(!parseFloat(h))return;var v=t.fontFamily||Ip,f=t.fontWeight;u+="font-size:"+h+";font-family:"+v+";",c&&c!=="normal"&&(u+="font-style:"+c+";"),f&&f!=="normal"&&(u+="font-weight:"+f+";"),l.style=u}else l.style="font: "+i;return r.match(/\s/)&&(l["xml:space"]="preserve"),n&&(l.x=n),o&&(l.y=o),Yo(l,a.transform),Zo(l,t,a,e),Uo(l,a),e.animation&&Ci(a,l,e),At("text",a.id+"",l,void 0,r)}}function hl(a,e){if(a instanceof ie)return ov(a,e);if(a instanceof pe)return Bg(a,e);if(a instanceof ph)return Gg(a,e)}function Fg(a,e,t){var r=a.style;if(Pp(r)){var i=Rp(a),n=t.shadowCache,o=n[i];if(!o){var s=a.getGlobalScale(),l=s[0],u=s[1];if(!l||!u)return;var c=r.shadowOffsetX||0,h=r.shadowOffsetY||0,v=r.shadowBlur,f=Ur(r.shadowColor),p=f.opacity,d=f.color,g=v/2/l,y=v/2/u,m=g+" "+y;o=t.zrId+"-s"+t.shadowIdx++,t.defs[o]=At("filter",o,{id:o,x:"-100%",y:"-100%",width:"300%",height:"300%"},[At("feDropShadow","",{dx:c/l,dy:h/u,stdDeviation:m,"flood-color":d,"flood-opacity":p})]),n[i]=o}e.filter=vi(o)}}function sv(a,e,t,r){var i=a[t],n,o={gradientUnits:i.global?"userSpaceOnUse":"objectBoundingBox"};if(yp(i))n="linearGradient",o.x1=i.x,o.y1=i.y,o.x2=i.x2,o.y2=i.y2;else if(mp(i))n="radialGradient",o.cx=Mt(i.x,.5),o.cy=Mt(i.y,.5),o.r=Mt(i.r,.5);else return;for(var s=i.colorStops,l=[],u=0,c=s.length;u<c;++u){var h=Sp(s[u].offset)*100+"%",v=s[u].color,f=Ur(v),p=f.color,d=f.opacity,g={offset:h};g["stop-color"]=p,d<1&&(g["stop-opacity"]=d),l.push(At("stop",u+"",g))}var y=At(n,"",o,l),m=Wo(y),S=r.gradientCache,x=S[m];x||(x=r.zrId+"-g"+r.gradientIdx++,S[m]=x,o.id=x,r.defs[x]=At(n,x,o,l)),e[t]=vi(x)}function lv(a,e,t,r){var i=a.style[t],n=a.getBoundingRect(),o={},s=i.repeat,l=s==="no-repeat",u=s==="repeat-x",c=s==="repeat-y",h;if(xp(i)){var v=i.imageWidth,f=i.imageHeight,p=void 0,d=i.image;if(tt(d)?p=d:iv(d)?p=d.src:nv(d)&&(p=d.toDataURL()),typeof Image>"u"){var g="Image width/height must been given explictly in svg-ssr renderer.";Yr(v,g),Yr(f,g)}else if(v==null||f==null){var y=function(T,I){if(T){var L=T.elm,M=v||I.width,R=f||I.height;T.tag==="pattern"&&(u?(R=1,M/=n.width):c&&(M=1,R/=n.height)),T.attrs.width=M,T.attrs.height=R,L&&(L.setAttribute("width",M),L.setAttribute("height",R))}},m=bp(p,null,a,function(T){l||y(_,T),y(h,T)});m&&m.width&&m.height&&(v=v||m.width,f=f||m.height)}h=At("image","img",{href:p,width:v,height:f}),o.width=v,o.height=f}else i.svgElement&&(h=it(i.svgElement),o.width=i.svgWidth,o.height=i.svgHeight);if(h){var S,x;l?S=x=1:u?(x=1,S=o.width/n.width):c?(S=1,x=o.height/n.height):o.patternUnits="userSpaceOnUse",S!=null&&!isNaN(S)&&(o.width=S),x!=null&&!isNaN(x)&&(o.height=x);var b=vh(i);b&&(o.patternTransform=b);var _=At("pattern","",o,[h]),w=Wo(_),A=r.patternCache,C=A[w];C||(C=r.zrId+"-p"+r.patternIdx++,A[w]=C,o.id=C,_=r.defs[C]=At("pattern",C,o,[h])),e[t]=vi(C)}}function Hg(a,e,t){var r=t.clipPathCache,i=t.defs,n=r[a.id];if(!n){n=t.zrId+"-c"+t.clipPathIdx++;var o={id:n};r[a.id]=n,i[n]=At("clipPath",n,o,[ov(a,t)])}e["clip-path"]=vi(n)}function vl(a){return document.createTextNode(a)}function He(a,e,t){a.insertBefore(e,t)}function fl(a,e){a.removeChild(e)}function pl(a,e){a.appendChild(e)}function uv(a){return a.parentNode}function cv(a){return a.nextSibling}function Fi(a,e){a.textContent=e}var dl=58,Wg=120,Zg=At("","");function qn(a){return a===void 0}function ve(a){return a!==void 0}function Ug(a,e,t){for(var r={},i=e;i<=t;++i){var n=a[i].key;n!==void 0&&(r[n]=i)}return r}function Or(a,e){var t=a.key===e.key,r=a.tag===e.tag;return r&&t}function jr(a){var e,t=a.children,r=a.tag;if(ve(r)){var i=a.elm=ev(r);if($o(Zg,a),U(t))for(e=0;e<t.length;++e){var n=t[e];n!=null&&pl(i,jr(n))}else ve(a.text)&&!Nt(a.text)&&pl(i,vl(a.text))}else a.elm=vl(a.text);return a.elm}function hv(a,e,t,r,i){for(;r<=i;++r){var n=t[r];n!=null&&He(a,jr(n),e)}}function ja(a,e,t,r){for(;t<=r;++t){var i=e[t];if(i!=null)if(ve(i.tag)){var n=uv(i.elm);fl(n,i.elm)}else fl(a,i.elm)}}function $o(a,e){var t,r=e.elm,i=a&&a.attrs||{},n=e.attrs||{};if(i!==n){for(t in n){var o=n[t],s=i[t];s!==o&&(o===!0?r.setAttribute(t,""):o===!1?r.removeAttribute(t):t==="style"?r.style.cssText=o:t.charCodeAt(0)!==Wg?r.setAttribute(t,o):t==="xmlns:xlink"||t==="xmlns"?r.setAttributeNS(Ag,t,o):t.charCodeAt(3)===dl?r.setAttributeNS(Tg,t,o):t.charCodeAt(5)===dl?r.setAttributeNS(tv,t,o):r.setAttribute(t,o))}for(t in i)t in n||r.removeAttribute(t)}}function Yg(a,e,t){for(var r=0,i=0,n=e.length-1,o=e[0],s=e[n],l=t.length-1,u=t[0],c=t[l],h,v,f,p;r<=n&&i<=l;)o==null?o=e[++r]:s==null?s=e[--n]:u==null?u=t[++i]:c==null?c=t[--l]:Or(o,u)?(sr(o,u),o=e[++r],u=t[++i]):Or(s,c)?(sr(s,c),s=e[--n],c=t[--l]):Or(o,c)?(sr(o,c),He(a,o.elm,cv(s.elm)),o=e[++r],c=t[--l]):Or(s,u)?(sr(s,u),He(a,s.elm,o.elm),s=e[--n],u=t[++i]):(qn(h)&&(h=Ug(e,r,n)),v=h[u.key],qn(v)?He(a,jr(u),o.elm):(f=e[v],f.tag!==u.tag?He(a,jr(u),o.elm):(sr(f,u),e[v]=void 0,He(a,f.elm,o.elm))),u=t[++i]);(r<=n||i<=l)&&(r>n?(p=t[l+1]==null?null:t[l+1].elm,hv(a,p,t,i,l)):ja(a,e,r,n))}function sr(a,e){var t=e.elm=a.elm,r=a.children,i=e.children;a!==e&&($o(a,e),qn(e.text)?ve(r)&&ve(i)?r!==i&&Yg(t,r,i):ve(i)?(ve(a.text)&&Fi(t,""),hv(t,null,i,0,i.length-1)):ve(r)?ja(t,r,0,r.length-1):ve(a.text)&&Fi(t,""):a.text!==e.text&&(ve(r)&&ja(t,r,0,r.length-1),Fi(t,e.text)))}function $g(a,e){if(Or(a,e))sr(a,e);else{var t=a.elm,r=uv(t);jr(e),r!==null&&(He(r,e.elm,cv(t)),ja(r,[a],0,0))}return e}var Xg=0,Kg=function(){function a(e,t,r){if(this.type="svg",this.refreshHover=gl(),this.configLayer=gl(),this.storage=t,this._opts=r=F({},r),this.root=e,this._id="zr"+Xg++,this._oldVNode=il(r.width,r.height),e&&!r.ssr){var i=this._viewport=document.createElement("div");i.style.cssText="position:relative;overflow:hidden";var n=this._svgDom=this._oldVNode.elm=ev("svg");$o(null,this._oldVNode),i.appendChild(n),e.appendChild(i)}this.resize(r.width,r.height)}return a.prototype.getType=function(){return this.type},a.prototype.getViewportRoot=function(){return this._viewport},a.prototype.getViewportRootOffset=function(){var e=this.getViewportRoot();if(e)return{offsetLeft:e.offsetLeft||0,offsetTop:e.offsetTop||0}},a.prototype.getSvgDom=function(){return this._svgDom},a.prototype.refresh=function(){if(this.root){var e=this.renderToVNode({willUpdate:!0});e.attrs.style="position:absolute;left:0;top:0;user-select:none",$g(this._oldVNode,e),this._oldVNode=e}},a.prototype.renderOneToVNode=function(e){return hl(e,Kn(this._id))},a.prototype.renderToVNode=function(e){e=e||{};var t=this.storage.getDisplayList(!0),r=this._width,i=this._height,n=Kn(this._id);n.animation=e.animation,n.willUpdate=e.willUpdate,n.compress=e.compress,n.emphasis=e.emphasis,n.ssr=this._opts.ssr;var o=[],s=this._bgVNode=qg(r,i,this._backgroundColor,n);s&&o.push(s);var l=e.compress?null:this._mainVNode=At("g","main",{},[]);this._paintList(t,n,l?l.children:o),l&&o.push(l);var u=G(wt(n.defs),function(v){return n.defs[v]});if(u.length&&o.push(At("defs","defs",{},u)),e.animation){var c=Ig(n.cssNodes,n.cssAnims,{newline:!0});if(c){var h=At("style","stl",{},[],c);o.push(h)}}return il(r,i,o,e.useViewBox)},a.prototype.renderToString=function(e){return e=e||{},Wo(this.renderToVNode({animation:Mt(e.cssAnimation,!0),emphasis:Mt(e.cssEmphasis,!0),willUpdate:!1,compress:!0,useViewBox:Mt(e.useViewBox,!0)}),{newline:!0})},a.prototype.setBackgroundColor=function(e){this._backgroundColor=e},a.prototype.getSvgRoot=function(){return this._mainVNode&&this._mainVNode.elm},a.prototype._paintList=function(e,t,r){for(var i=e.length,n=[],o=0,s,l,u=0,c=0;c<i;c++){var h=e[c];if(!h.invisible){var v=h.__clipPaths,f=v&&v.length||0,p=l&&l.length||0,d=void 0;for(d=Math.max(f-1,p-1);d>=0&&!(v&&l&&v[d]===l[d]);d--);for(var g=p-1;g>d;g--)o--,s=n[o-1];for(var y=d+1;y<f;y++){var m={};Hg(v[y],m,t);var S=At("g","clip-g-"+u++,m,[]);(s?s.children:r).push(S),n[o++]=S,s=S}l=v;var x=hl(h,t);x&&(s?s.children:r).push(x)}}},a.prototype.resize=function(e,t){var r=this._opts,i=this.root,n=this._viewport;if(e!=null&&(r.width=e),t!=null&&(r.height=t),i&&n&&(n.style.display="none",e=Bs(i,0,r),t=Bs(i,1,r),n.style.display=""),this._width!==e||this._height!==t){if(this._width=e,this._height=t,n){var o=n.style;o.width=e+"px",o.height=t+"px"}if(Io(this._backgroundColor))this.refresh();else{var s=this._svgDom;s&&(s.setAttribute("width",e),s.setAttribute("height",t));var l=this._bgVNode&&this._bgVNode.elm;l&&(l.setAttribute("width",e),l.setAttribute("height",t))}}},a.prototype.getWidth=function(){return this._width},a.prototype.getHeight=function(){return this._height},a.prototype.dispose=function(){this.root&&(this.root.innerHTML=""),this._svgDom=this._viewport=this.storage=this._oldVNode=this._bgVNode=this._mainVNode=null},a.prototype.clear=function(){this._svgDom&&(this._svgDom.innerHTML=null),this._oldVNode=null},a.prototype.toDataURL=function(e){var t=this.renderToString(),r="data:image/svg+xml;";return e?(t=Ep(t),t&&r+"base64,"+t):r+"charset=UTF-8,"+encodeURIComponent(t)},a}();function gl(a){return function(){}}function qg(a,e,t,r){var i;if(t&&t!=="none")if(i=At("rect","bg",{width:a,height:e,x:"0",y:"0"}),dh(t))sv({fill:t},i.attrs,"fill",r);else if(Io(t))lv({style:{fill:t},dirty:Pe,getBoundingRect:function(){return{width:a,height:e}}},i.attrs,"fill",r);else{var n=Ur(t),o=n.color,s=n.opacity;i.attrs.fill=o,s<1&&(i.attrs["fill-opacity"]=s)}return i}function jg(a){a.registerPainter("svg",Kg)}var yl=Math.PI*2,xa=Math.PI/180;function vv(a,e){return Rt(a.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function fv(a,e){var t=vv(a,e),r=a.get("center"),i=a.get("radius");U(i)||(i=[0,i]);var n=O(t.width,e.getWidth()),o=O(t.height,e.getHeight()),s=Math.min(n,o),l=O(i[0],s/2),u=O(i[1],s/2),c,h,v=a.coordinateSystem;if(v){var f=v.dataToPoint(r);c=f[0]||0,h=f[1]||0}else U(r)||(r=[r,r]),c=O(r[0],n)+t.x,h=O(r[1],o)+t.y;return{cx:c,cy:h,r0:l,r:u}}function Jg(a,e,t){e.eachSeriesByType(a,function(r){var i=r.getData(),n=i.mapDimension("value"),o=vv(r,t),s=fv(r,t),l=s.cx,u=s.cy,c=s.r,h=s.r0,v=-r.get("startAngle")*xa,f=r.get("endAngle"),p=r.get("padAngle")*xa;f=f==="auto"?v-yl:-f*xa;var d=r.get("minAngle")*xa,g=d+p,y=0;i.each(n,function(V){!isNaN(V)&&y++});var m=i.getSum(n),S=Math.PI/(m||y)*2,x=r.get("clockwise"),b=r.get("roseType"),_=r.get("stillShowZeroSum"),w=i.getDataExtent(n);w[0]=0;var A=x?1:-1,C=[v,f],T=A*p/2;gh(C,!x),v=C[0],f=C[1];var I=pv(r);I.startAngle=v,I.endAngle=f,I.clockwise=x;var L=Math.abs(f-v),M=L,R=0,P=v;if(i.setLayout({viewRect:o,r:c}),i.each(n,function(V,N){var z;if(isNaN(V)){i.setItemLayout(N,{angle:NaN,startAngle:NaN,endAngle:NaN,clockwise:x,cx:l,cy:u,r0:h,r:b?NaN:c});return}b!=="area"?z=m===0&&_?S:V*S:z=L/y,z<g?(z=g,M-=g):R+=V;var H=P+A*z,Z=0,Y=0;p>z?(Z=P+A*z/2,Y=Z):(Z=P+T,Y=H-T),i.setItemLayout(N,{angle:z,startAngle:Z,endAngle:Y,clockwise:x,cx:l,cy:u,r0:h,r:b?at(V,w,[h,c]):c}),P=H}),M<yl&&y)if(M<=.001){var k=L/y;i.each(n,function(V,N){if(!isNaN(V)){var z=i.getItemLayout(N);z.angle=k;var H=0,Z=0;k<p?(H=v+A*(N+1/2)*k,Z=H):(H=v+A*N*k+T,Z=v+A*(N+1)*k-T),z.startAngle=H,z.endAngle=Z}})}else S=M/R,P=v,i.each(n,function(V,N){if(!isNaN(V)){var z=i.getItemLayout(N),H=z.angle===g?g:V*S,Z=0,Y=0;H<p?(Z=P+A*H/2,Y=Z):(Z=P+T,Y=P+A*H-T),z.startAngle=Z,z.endAngle=Y,P+=A*H}})})}var pv=Dt();function fa(a){return{seriesType:a,reset:function(e,t){var r=t.findComponents({mainType:"legend"});if(!(!r||!r.length)){var i=e.getData();i.filterSelf(function(n){for(var o=i.getName(n),s=0;s<r.length;s++)if(!r[s].isSelected(o))return!1;return!0})}}}}var Qg=Math.PI/180;function ml(a,e,t,r,i,n,o,s,l,u){if(a.length<2)return;function c(d){for(var g=d.rB,y=g*g,m=0;m<d.list.length;m++){var S=d.list[m],x=Math.abs(S.label.y-t),b=r+S.len,_=b*b,w=Math.sqrt(Math.abs((1-x*x/y)*_)),A=e+(w+S.len2)*i,C=A-S.label.x,T=S.targetTextWidth-C*i;dv(S,T,!0),S.label.x=A}}function h(d){for(var g={list:[],maxY:0},y={list:[],maxY:0},m=0;m<d.length;m++)if(d[m].labelAlignTo==="none"){var S=d[m],x=S.label.y>t?y:g,b=Math.abs(S.label.y-t);if(b>=x.maxY){var _=S.label.x-e-S.len2*i,w=r+S.len,A=Math.abs(_)<w?Math.sqrt(b*b/(1-_*_/w/w)):w;x.rB=A,x.maxY=b}x.list.push(S)}c(g),c(y)}for(var v=a.length,f=0;f<v;f++)if(a[f].position==="outer"&&a[f].labelAlignTo==="labelLine"){var p=a[f].label.x-u;a[f].linePoints[1][0]+=p,a[f].label.x=u}Np(a,l,l+o)&&h(a)}function ty(a,e,t,r,i,n,o,s){for(var l=[],u=[],c=Number.MAX_VALUE,h=-Number.MAX_VALUE,v=0;v<a.length;v++){var f=a[v].label;Hi(a[v])||(f.x<e?(c=Math.min(c,f.x),l.push(a[v])):(h=Math.max(h,f.x),u.push(a[v])))}for(var v=0;v<a.length;v++){var p=a[v];if(!Hi(p)&&p.linePoints){if(p.labelStyleWidth!=null)continue;var f=p.label,d=p.linePoints,g=void 0;p.labelAlignTo==="edge"?f.x<e?g=d[2][0]-p.labelDistance-o-p.edgeDistance:g=o+i-p.edgeDistance-d[2][0]-p.labelDistance:p.labelAlignTo==="labelLine"?f.x<e?g=c-o-p.bleedMargin:g=o+i-h-p.bleedMargin:f.x<e?g=f.x-o-p.bleedMargin:g=o+i-f.x-p.bleedMargin,p.targetTextWidth=g,dv(p,g)}}ml(u,e,t,r,1,i,n,o,s,h),ml(l,e,t,r,-1,i,n,o,s,c);for(var v=0;v<a.length;v++){var p=a[v];if(!Hi(p)&&p.linePoints){var f=p.label,d=p.linePoints,y=p.labelAlignTo==="edge",m=f.style.padding,S=m?m[1]+m[3]:0,x=f.style.backgroundColor?0:S,b=p.rect.width+x,_=d[1][0]-d[2][0];y?f.x<e?d[2][0]=o+p.edgeDistance+b+p.labelDistance:d[2][0]=o+i-p.edgeDistance-b-p.labelDistance:(f.x<e?d[2][0]=f.x+p.labelDistance:d[2][0]=f.x-p.labelDistance,d[1][0]=d[2][0]+_),d[1][1]=d[2][1]=f.y}}}function dv(a,e,t){if(t===void 0&&(t=!1),a.labelStyleWidth==null){var r=a.label,i=r.style,n=a.rect,o=i.backgroundColor,s=i.padding,l=s?s[1]+s[3]:0,u=i.overflow,c=n.width+(o?0:l);if(e<c||t){var h=n.height;if(u&&u.match("break")){r.setStyle("backgroundColor",null),r.setStyle("width",e-l);var v=r.getBoundingRect();r.setStyle("width",Math.ceil(v.width)),r.setStyle("backgroundColor",o)}else{var f=e-l,p=e<c?f:t?f>a.unconstrainedWidth?null:f:null;r.setStyle("width",p)}var d=r.getBoundingRect();n.width=d.width;var g=(r.style.margin||0)+2.1;n.height=d.height+g,n.y-=(n.height-h)/2}}}function Hi(a){return a.position==="center"}function ey(a){var e=a.getData(),t=[],r,i,n=!1,o=(a.get("minShowLabelAngle")||0)*Qg,s=e.getLayout("viewRect"),l=e.getLayout("r"),u=s.width,c=s.x,h=s.y,v=s.height;function f(_){_.ignore=!0}function p(_){if(!_.ignore)return!0;for(var w in _.states)if(_.states[w].ignore===!1)return!0;return!1}e.each(function(_){var w=e.getItemGraphicEl(_),A=w.shape,C=w.getTextContent(),T=w.getTextGuideLine(),I=e.getItemModel(_),L=I.getModel("label"),M=L.get("position")||I.get(["emphasis","label","position"]),R=L.get("distanceToLabelLine"),P=L.get("alignTo"),k=O(L.get("edgeDistance"),u),V=L.get("bleedMargin"),N=I.getModel("labelLine"),z=N.get("length");z=O(z,u);var H=N.get("length2");if(H=O(H,u),Math.abs(A.endAngle-A.startAngle)<o){D(C.states,f),C.ignore=!0,T&&(D(T.states,f),T.ignore=!0);return}if(p(C)){var Z=(A.startAngle+A.endAngle)/2,Y=Math.cos(Z),Q=Math.sin(Z),j,rt,It,$t;r=A.cx,i=A.cy;var J=M==="inside"||M==="inner";if(M==="center")j=A.cx,rt=A.cy,$t="center";else{var $=(J?(A.r+A.r0)/2*Y:A.r*Y)+r,lt=(J?(A.r+A.r0)/2*Q:A.r*Q)+i;if(j=$+Y*3,rt=lt+Q*3,!J){var nt=$+Y*(z+l-A.r),vt=lt+Q*(z+l-A.r),St=nt+(Y<0?-1:1)*H,Lt=vt;P==="edge"?j=Y<0?c+k:c+u-k:j=St+(Y<0?-R:R),rt=Lt,It=[[$,lt],[nt,vt],[St,Lt]]}$t=J?"center":P==="edge"?Y>0?"right":"left":Y>0?"left":"right"}var ct=Math.PI,gt=0,_t=L.get("rotate");if(jt(_t))gt=_t*(ct/180);else if(M==="center")gt=0;else if(_t==="radial"||_t===!0){var Xt=Y<0?-Z+ct:-Z;gt=Xt}else if(_t==="tangential"&&M!=="outside"&&M!=="outer"){var Bt=Math.atan2(Y,Q);Bt<0&&(Bt=ct*2+Bt);var ke=Q>0;ke&&(Bt=ct+Bt),gt=Bt-ct}if(n=!!gt,C.x=j,C.y=rt,C.rotation=gt,C.setStyle({verticalAlign:"middle"}),J){C.setStyle({align:$t});var Vi=C.states.select;Vi&&(Vi.x+=C.x,Vi.y+=C.y)}else{var Ae=C.getBoundingRect().clone();Ae.applyTransform(C.getComputedTransform());var zs=(C.style.margin||0)+2.1;Ae.y-=zs/2,Ae.height+=zs,t.push({label:C,labelLine:T,position:M,len:z,len2:H,minTurnAngle:N.get("minTurnAngle"),maxSurfaceAngle:N.get("maxSurfaceAngle"),surfaceNormal:new Za(Y,Q),linePoints:It,textAlign:$t,labelDistance:R,labelAlignTo:P,edgeDistance:k,bleedMargin:V,rect:Ae,unconstrainedWidth:Ae.width,labelStyleWidth:C.style.width})}w.setTextConfig({inside:J})}}),!n&&a.get("avoidLabelOverlap")&&ty(t,r,i,l,u,v,c,h);for(var d=0;d<t.length;d++){var g=t[d],y=g.label,m=g.labelLine,S=isNaN(y.x)||isNaN(y.y);if(y){y.setStyle({align:g.textAlign}),S&&(D(y.states,f),y.ignore=!0);var x=y.states.select;x&&(x.x+=y.x,x.y+=y.y)}if(m){var b=g.linePoints;S||!b?(D(m.states,f),m.ignore=!0):(kp(b,g.minTurnAngle),Vp(b,g.surfaceNormal,g.maxSurfaceAngle),m.setShape({points:b}),y.__hostTarget.textGuideLineConfig={anchor:new Za(b[0][0],b[0][1])})}}}var ry=function(a){E(e,a);function e(t,r,i){var n=a.call(this)||this;n.z2=2;var o=new ut;return n.setTextContent(o),n.updateData(t,r,i,!0),n}return e.prototype.updateData=function(t,r,i,n){var o=this,s=t.hostModel,l=t.getItemModel(r),u=l.getModel("emphasis"),c=t.getItemLayout(r),h=F(cr(l.getModel("itemStyle"),c,!0),c);if(isNaN(h.startAngle)){o.setShape(h);return}if(n){o.setShape(h);var v=s.getShallow("animationType");s.ecModel.ssr?(Gt(o,{scaleX:0,scaleY:0},s,{dataIndex:r,isFrom:!0}),o.originX=h.cx,o.originY=h.cy):v==="scale"?(o.shape.r=c.r0,Gt(o,{shape:{r:c.r}},s,r)):i!=null?(o.setShape({startAngle:i,endAngle:i}),Gt(o,{shape:{startAngle:c.startAngle,endAngle:c.endAngle}},s,r)):(o.shape.endAngle=c.startAngle,dt(o,{shape:{endAngle:c.endAngle}},s,r))}else xe(o),dt(o,{shape:h},s,r);o.useStyle(t.getItemVisual(r,"style")),Ht(o,l);var f=(c.startAngle+c.endAngle)/2,p=s.get("selectedOffset"),d=Math.cos(f)*p,g=Math.sin(f)*p,y=l.getShallow("cursor");y&&o.attr("cursor",y),this._updateLabel(s,t,r),o.ensureState("emphasis").shape=F({r:c.r+(u.get("scale")&&u.get("scaleSize")||0)},cr(u.getModel("itemStyle"),c)),F(o.ensureState("select"),{x:d,y:g,shape:cr(l.getModel(["select","itemStyle"]),c)}),F(o.ensureState("blur"),{shape:cr(l.getModel(["blur","itemStyle"]),c)});var m=o.getTextGuideLine(),S=o.getTextContent();m&&F(m.ensureState("select"),{x:d,y:g}),F(S.ensureState("select"),{x:d,y:g}),xt(this,u.get("focus"),u.get("blurScope"),u.get("disabled"))},e.prototype._updateLabel=function(t,r,i){var n=this,o=r.getItemModel(i),s=o.getModel("labelLine"),l=r.getItemVisual(i,"style"),u=l&&l.fill,c=l&&l.opacity;Wt(n,zt(o),{labelFetcher:r.hostModel,labelDataIndex:i,inheritColor:u,defaultOpacity:c,defaultText:t.getFormattedLabel(i,"normal")||r.getName(i)});var h=n.getTextContent();n.setTextConfig({position:null,rotation:null}),h.attr({z2:10});var v=t.get(["label","position"]);if(v!=="outside"&&v!=="outer")n.removeTextGuideLine();else{var f=this.getTextGuideLine();f||(f=new le,this.setTextGuideLine(f)),yh(this,mh(o),{stroke:u,opacity:xr(s.get(["lineStyle","opacity"]),c,1)})}},e}(Ye),ay=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.ignoreLabelLineUpdate=!0,t}return e.prototype.render=function(t,r,i,n){var o=t.getData(),s=this._data,l=this.group,u;if(!s&&o.count()>0){for(var c=o.getItemLayout(0),h=1;isNaN(c&&c.startAngle)&&h<o.count();++h)c=o.getItemLayout(h);c&&(u=c.startAngle)}if(this._emptyCircleSector&&l.remove(this._emptyCircleSector),o.count()===0&&t.get("showEmptyCircle")){var v=pv(t),f=new Ye({shape:F(fv(t,i),v)});f.useStyle(t.getModel("emptyCircleStyle").getItemStyle()),this._emptyCircleSector=f,l.add(f)}o.diff(s).add(function(p){var d=new ry(o,p,u);o.setItemGraphicEl(p,d),l.add(d)}).update(function(p,d){var g=s.getItemGraphicEl(d);g.updateData(o,p,u),g.off("click"),l.add(g),o.setItemGraphicEl(p,g)}).remove(function(p){var d=s.getItemGraphicEl(p);Sh(d,t,p)}).execute(),ey(t),t.get("animationTypeUpdate")!=="expansion"&&(this._data=o)},e.prototype.dispose=function(){},e.prototype.containPoint=function(t,r){var i=r.getData(),n=i.getItemLayout(0);if(n){var o=t[0]-n.cx,s=t[1]-n.cy,l=Math.sqrt(o*o+s*s);return l<=n.r&&l>=n.r0}},e.type="pie",e}(bt);function Tr(a,e,t){e=U(e)&&{coordDimensions:e}||F({encodeDefine:a.getEncode()},e);var r=a.getSource(),i=fi(r,e).dimensions,n=new Ft(i,a);return n.initData(r,t),n}var pa=function(){function a(e,t){this._getDataWithEncodedVisual=e,this._getRawData=t}return a.prototype.getAllNames=function(){var e=this._getRawData();return e.mapArray(e.getName)},a.prototype.containName=function(e){var t=this._getRawData();return t.indexOfName(e)>=0},a.prototype.indexOfName=function(e){var t=this._getDataWithEncodedVisual();return t.indexOfName(e)},a.prototype.getItemVisual=function(e,t){var r=this._getDataWithEncodedVisual();return r.getItemVisual(e,t)},a}(),iy=Dt(),ny=function(a){E(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.init=function(t){a.prototype.init.apply(this,arguments),this.legendVisualProvider=new pa(W(this.getData,this),W(this.getRawData,this)),this._defaultLabelLine(t)},e.prototype.mergeOption=function(){a.prototype.mergeOption.apply(this,arguments)},e.prototype.getInitialData=function(){return Tr(this,{coordDimensions:["value"],encodeDefaulter:q(Lo,this)})},e.prototype.getDataParams=function(t){var r=this.getData(),i=iy(r),n=i.seats;if(!n){var o=[];r.each(r.mapDimension("value"),function(l){o.push(l)}),n=i.seats=zp(o,r.hostModel.get("percentPrecision"))}var s=a.prototype.getDataParams.call(this,t);return s.percent=n[t]||0,s.$vars.push("percent"),s},e.prototype._defaultLabelLine=function(t){oa(t,"labelLine",["show"]);var r=t.labelLine,i=t.emphasis.labelLine;r.show=r.show&&t.label.show,i.show=i.show&&t.emphasis.label.show},e.type="series.pie",e.defaultOption={z:2,legendHoverLink:!0,colorBy:"data",center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,endAngle:"auto",padAngle:0,minAngle:0,minShowLabelAngle:0,selectedOffset:10,percentPrecision:2,stillShowZeroSum:!0,left:0,top:0,right:0,bottom:0,width:null,height:null,label:{rotate:0,show:!0,overflow:"truncate",position:"outer",alignTo:"none",edgeDistance:"25%",bleedMargin:10,distanceToLabelLine:5},labelLine:{show:!0,length:15,length2:15,smooth:!1,minTurnAngle:90,maxSurfaceAngle:90,lineStyle:{width:1,type:"solid"}},itemStyle:{borderWidth:1,borderJoin:"round"},showEmptyCircle:!0,emptyCircleStyle:{color:"lightgray",opacity:1},labelLayout:{hideOverlap:!0},emphasis:{scale:!0,scaleSize:5},avoidLabelOverlap:!0,animationType:"expansion",animationDuration:1e3,animationTypeUpdate:"transition",animationEasingUpdate:"cubicInOut",animationDurationUpdate:500,animationEasing:"cubicInOut"},e}(Ct);function oy(a){return{seriesType:a,reset:function(e,t){var r=e.getData();r.filterSelf(function(i){var n=r.mapDimension("value"),o=r.get(n,i);return!(jt(o)&&!isNaN(o)&&o<0)})}}}function sy(a){a.registerChartView(ay),a.registerSeriesModel(ny),xh("pie",a.registerAction),a.registerLayout(q(Jg,"pie")),a.registerProcessor(fa("pie")),a.registerProcessor(oy("pie"))}var ly=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.getInitialData=function(t,r){return br(null,this,{useEncodeDefaulter:!0})},e.prototype.getProgressive=function(){var t=this.option.progressive;return t??(this.option.large?5e3:this.get("progressive"))},e.prototype.getProgressiveThreshold=function(){var t=this.option.progressiveThreshold;return t??(this.option.large?1e4:this.get("progressiveThreshold"))},e.prototype.brushSelector=function(t,r,i){return i.point(r.getItemLayout(t))},e.prototype.getZLevelKey=function(){return this.getData().count()>this.getProgressiveThreshold()?this.id:""},e.type="series.scatter",e.dependencies=["grid","polar","geo","singleAxis","calendar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,symbolSize:10,large:!1,largeThreshold:2e3,itemStyle:{opacity:.8},emphasis:{scale:!0},clip:!0,select:{itemStyle:{borderColor:"#212121"}},universalTransition:{divideShape:"clone"}},e}(Ct),gv=4,uy=function(){function a(){}return a}(),cy=function(a){E(e,a);function e(t){var r=a.call(this,t)||this;return r._off=0,r.hoverDataIdx=-1,r}return e.prototype.getDefaultShape=function(){return new uy},e.prototype.reset=function(){this.notClear=!1,this._off=0},e.prototype.buildPath=function(t,r){var i=r.points,n=r.size,o=this.symbolProxy,s=o.shape,l=t.getContext?t.getContext():t,u=l&&n[0]<gv,c=this.softClipShape,h;if(u){this._ctx=l;return}for(this._ctx=null,h=this._off;h<i.length;){var v=i[h++],f=i[h++];isNaN(v)||isNaN(f)||c&&!c.contain(v,f)||(s.x=v-n[0]/2,s.y=f-n[1]/2,s.width=n[0],s.height=n[1],o.buildPath(t,s,!0))}this.incremental&&(this._off=h,this.notClear=!0)},e.prototype.afterBrush=function(){var t=this.shape,r=t.points,i=t.size,n=this._ctx,o=this.softClipShape,s;if(n){for(s=this._off;s<r.length;){var l=r[s++],u=r[s++];isNaN(l)||isNaN(u)||o&&!o.contain(l,u)||n.fillRect(l-i[0]/2,u-i[1]/2,i[0],i[1])}this.incremental&&(this._off=s,this.notClear=!0)}},e.prototype.findDataIndex=function(t,r){for(var i=this.shape,n=i.points,o=i.size,s=Math.max(o[0],4),l=Math.max(o[1],4),u=n.length/2-1;u>=0;u--){var c=u*2,h=n[c]-s/2,v=n[c+1]-l/2;if(t>=h&&r>=v&&t<=h+s&&r<=v+l)return u}return-1},e.prototype.contain=function(t,r){var i=this.transformCoordToLocal(t,r),n=this.getBoundingRect();if(t=i[0],r=i[1],n.contain(t,r)){var o=this.hoverDataIdx=this.findDataIndex(t,r);return o>=0}return this.hoverDataIdx=-1,!1},e.prototype.getBoundingRect=function(){var t=this._rect;if(!t){for(var r=this.shape,i=r.points,n=r.size,o=n[0],s=n[1],l=1/0,u=1/0,c=-1/0,h=-1/0,v=0;v<i.length;){var f=i[v++],p=i[v++];l=Math.min(f,l),c=Math.max(f,c),u=Math.min(p,u),h=Math.max(p,h)}t=this._rect=new Pt(l-o/2,u-s/2,c-l+o,h-u+s)}return t},e}(ie),hy=function(){function a(){this.group=new X}return a.prototype.updateData=function(e,t){this._clear();var r=this._create();r.setShape({points:e.getLayout("points")}),this._setCommon(r,e,t)},a.prototype.updateLayout=function(e){var t=e.getLayout("points");this.group.eachChild(function(r){if(r.startIndex!=null){var i=(r.endIndex-r.startIndex)*2,n=r.startIndex*4*2;t=new Float32Array(t.buffer,n,i)}r.setShape("points",t),r.reset()})},a.prototype.incrementalPrepareUpdate=function(e){this._clear()},a.prototype.incrementalUpdate=function(e,t,r){var i=this._newAdded[0],n=t.getLayout("points"),o=i&&i.shape.points;if(o&&o.length<2e4){var s=o.length,l=new Float32Array(s+n.length);l.set(o),l.set(n,s),i.endIndex=e.end,i.setShape({points:l})}else{this._newAdded=[];var u=this._create();u.startIndex=e.start,u.endIndex=e.end,u.incremental=!0,u.setShape({points:n}),this._setCommon(u,t,r)}},a.prototype.eachRendered=function(e){this._newAdded[0]&&e(this._newAdded[0])},a.prototype._create=function(){var e=new cy({cursor:"default"});return e.ignoreCoarsePointer=!0,this.group.add(e),this._newAdded.push(e),e},a.prototype._setCommon=function(e,t,r){var i=t.hostModel;r=r||{};var n=t.getVisual("symbolSize");e.setShape("size",n instanceof Array?n:[n,n]),e.softClipShape=r.clipShape||null,e.symbolProxy=Et(t.getVisual("symbol"),0,0,0,0),e.setColor=e.symbolProxy.setColor;var o=e.shape.size[0]<gv;e.useStyle(i.getModel("itemStyle").getItemStyle(o?["color","shadowBlur","shadowColor"]:["color"]));var s=t.getVisual("style"),l=s&&s.fill;l&&e.setColor(l);var u=ht(e);u.seriesIndex=i.seriesIndex,e.on("mousemove",function(c){u.dataIndex=null;var h=e.hoverDataIdx;h>=0&&(u.dataIndex=h+(e.startIndex||0))})},a.prototype.remove=function(){this._clear()},a.prototype._clear=function(){this._newAdded=[],this.group.removeAll()},a}(),vy=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i){var n=t.getData(),o=this._updateSymbolDraw(n,t);o.updateData(n,{clipShape:this._getClipShape(t)}),this._finished=!0},e.prototype.incrementalPrepareRender=function(t,r,i){var n=t.getData(),o=this._updateSymbolDraw(n,t);o.incrementalPrepareUpdate(n),this._finished=!1},e.prototype.incrementalRender=function(t,r,i){this._symbolDraw.incrementalUpdate(t,r.getData(),{clipShape:this._getClipShape(r)}),this._finished=t.end===r.getData().count()},e.prototype.updateTransform=function(t,r,i){var n=t.getData();if(this.group.dirty(),!this._finished||n.count()>1e4)return{update:!0};var o=pi("").reset(t,r,i);o.progress&&o.progress({start:0,end:n.count(),count:n.count()},n),this._symbolDraw.updateLayout(n)},e.prototype.eachRendered=function(t){this._symbolDraw&&this._symbolDraw.eachRendered(t)},e.prototype._getClipShape=function(t){if(t.get("clip",!0)){var r=t.coordinateSystem;return r&&r.getArea&&r.getArea(.1)}},e.prototype._updateSymbolDraw=function(t,r){var i=this._symbolDraw,n=r.pipelineContext,o=n.large;return(!i||o!==this._isLargeDraw)&&(i&&i.remove(),i=this._symbolDraw=o?new hy:new di,this._isLargeDraw=o,this.group.removeAll()),this.group.add(i.group),i},e.prototype.remove=function(t,r){this._symbolDraw&&this._symbolDraw.remove(!0),this._symbolDraw=null},e.prototype.dispose=function(){},e.type="scatter",e}(bt);function fy(a){K(Op),a.registerSeriesModel(ly),a.registerChartView(vy),a.registerLayout(pi("scatter"))}function py(a){a.eachSeriesByType("radar",function(e){var t=e.getData(),r=[],i=e.coordinateSystem;if(i){var n=i.getIndicatorAxes();D(n,function(o,s){t.each(t.mapDimension(n[s].dim),function(l,u){r[u]=r[u]||[];var c=i.dataToPoint(l,s);r[u][s]=Sl(c)?c:xl(i)})}),t.each(function(o){var s=Bp(r[o],function(l){return Sl(l)})||xl(i);r[o].push(s.slice()),t.setItemLayout(o,r[o])})}})}function Sl(a){return!isNaN(a[0])&&!isNaN(a[1])}function xl(a){return[a.cx,a.cy]}function dy(a){var e=a.polar;if(e){U(e)||(e=[e]);var t=[];D(e,function(r,i){r.indicator?(r.type&&!r.shape&&(r.shape=r.type),a.radar=a.radar||[],U(a.radar)||(a.radar=[a.radar]),a.radar.push(r)):t.push(r)}),a.polar=t}D(a.series,function(r){r&&r.type==="radar"&&r.polarIndex&&(r.radarIndex=r.polarIndex)})}var gy=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i){var n=t.coordinateSystem,o=this.group,s=t.getData(),l=this._data;function u(v,f){var p=v.getItemVisual(f,"symbol")||"circle";if(p!=="none"){var d=sa(v.getItemVisual(f,"symbolSize")),g=Et(p,-1,-1,2,2),y=v.getItemVisual(f,"symbolRotate")||0;return g.attr({style:{strokeNoScale:!0},z2:100,scaleX:d[0]/2,scaleY:d[1]/2,rotation:y*Math.PI/180||0}),g}}function c(v,f,p,d,g,y){p.removeAll();for(var m=0;m<f.length-1;m++){var S=u(d,g);S&&(S.__dimIdx=m,v[m]?(S.setPosition(v[m]),_r[y?"initProps":"updateProps"](S,{x:f[m][0],y:f[m][1]},t,g)):S.setPosition(f[m]),p.add(S))}}function h(v){return G(v,function(f){return[n.cx,n.cy]})}s.diff(l).add(function(v){var f=s.getItemLayout(v);if(f){var p=new se,d=new le,g={shape:{points:f}};p.shape.points=h(f),d.shape.points=h(f),Gt(p,g,t,v),Gt(d,g,t,v);var y=new X,m=new X;y.add(d),y.add(p),y.add(m),c(d.shape.points,f,m,s,v,!0),s.setItemGraphicEl(v,y)}}).update(function(v,f){var p=l.getItemGraphicEl(f),d=p.childAt(0),g=p.childAt(1),y=p.childAt(2),m={shape:{points:s.getItemLayout(v)}};m.shape.points&&(c(d.shape.points,m.shape.points,y,s,v,!1),xe(g),xe(d),dt(d,m,t),dt(g,m,t),s.setItemGraphicEl(v,p))}).remove(function(v){o.remove(l.getItemGraphicEl(v))}).execute(),s.eachItemGraphicEl(function(v,f){var p=s.getItemModel(f),d=v.childAt(0),g=v.childAt(1),y=v.childAt(2),m=s.getItemVisual(f,"style"),S=m.fill;o.add(v),d.useStyle(ot(p.getModel("lineStyle").getLineStyle(),{fill:"none",stroke:S})),Ht(d,p,"lineStyle"),Ht(g,p,"areaStyle");var x=p.getModel("areaStyle"),b=x.isEmpty()&&x.parentModel.isEmpty();g.ignore=b,D(["emphasis","select","blur"],function(A){var C=p.getModel([A,"areaStyle"]),T=C.isEmpty()&&C.parentModel.isEmpty();g.ensureState(A).ignore=T&&b}),g.useStyle(ot(x.getAreaStyle(),{fill:S,opacity:.7,decal:m.decal}));var _=p.getModel("emphasis"),w=_.getModel("itemStyle").getItemStyle();y.eachChild(function(A){if(A instanceof pe){var C=A.style;A.useStyle(F({image:C.image,x:C.x,y:C.y,width:C.width,height:C.height},m))}else A.useStyle(m),A.setColor(S),A.style.strokeNoScale=!0;var T=A.ensureState("emphasis");T.style=it(w);var I=s.getStore().get(s.getDimensionIndex(A.__dimIdx),f);(I==null||isNaN(I))&&(I=""),Wt(A,zt(p),{labelFetcher:s.hostModel,labelDataIndex:f,labelDimIndex:A.__dimIdx,defaultText:I,inheritColor:S,defaultOpacity:m.opacity})}),xt(v,_.get("focus"),_.get("blurScope"),_.get("disabled"))}),this._data=s},e.prototype.remove=function(){this.group.removeAll(),this._data=null},e.type="radar",e}(bt),yy=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.init=function(t){a.prototype.init.apply(this,arguments),this.legendVisualProvider=new pa(W(this.getData,this),W(this.getRawData,this))},e.prototype.getInitialData=function(t,r){return Tr(this,{generateCoord:"indicator_",generateCoordCount:1/0})},e.prototype.formatTooltip=function(t,r,i){var n=this.getData(),o=this.coordinateSystem,s=o.getIndicatorAxes(),l=this.getData().getName(t),u=l===""?this.name:l,c=Gp(this,t);return Zt("section",{header:u,sortBlocks:!0,blocks:G(s,function(h){var v=n.get(n.mapDimension(h.dim),t);return Zt("nameValue",{markerType:"subItem",markerColor:c,name:h.name,value:v,sortParam:v})})})},e.prototype.getTooltipPosition=function(t){if(t!=null){for(var r=this.getData(),i=this.coordinateSystem,n=r.getValues(G(i.dimensions,function(u){return r.mapDimension(u)}),t),o=0,s=n.length;o<s;o++)if(!isNaN(n[o])){var l=i.getIndicatorAxes();return i.coordToPoint(l[o].dataToCoord(n[o]),o)}}},e.type="series.radar",e.dependencies=["radar"],e.defaultOption={z:2,colorBy:"data",coordinateSystem:"radar",legendHoverLink:!0,radarIndex:0,lineStyle:{width:2,type:"solid",join:"round"},label:{position:"top"},symbolSize:8},e}(Ct),Cr=Fp.value;function ba(a,e){return ot({show:e},a)}var my=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.optionUpdated=function(){var t=this.get("boundaryGap"),r=this.get("splitNumber"),i=this.get("scale"),n=this.get("axisLine"),o=this.get("axisTick"),s=this.get("axisLabel"),l=this.get("axisName"),u=this.get(["axisName","show"]),c=this.get(["axisName","formatter"]),h=this.get("axisNameGap"),v=this.get("triggerEvent"),f=G(this.get("indicator")||[],function(p){p.max!=null&&p.max>0&&!p.min?p.min=0:p.min!=null&&p.min<0&&!p.max&&(p.max=0);var d=l;p.color!=null&&(d=ot({color:p.color},l));var g=ft(it(p),{boundaryGap:t,splitNumber:r,scale:i,axisLine:n,axisTick:o,axisLabel:s,name:p.text,showName:u,nameLocation:"end",nameGap:h,nameTextStyle:d,triggerEvent:v},!1);if(tt(c)){var y=g.name;g.name=c.replace("{value}",y??"")}else st(c)&&(g.name=c(g.name,g));var m=new Ut(g,null,this.ecModel);return ue(m,gi.prototype),m.mainType="radar",m.componentIndex=this.componentIndex,m},this);this._indicatorModels=f},e.prototype.getIndicatorModels=function(){return this._indicatorModels},e.type="radar",e.defaultOption={z:0,center:["50%","50%"],radius:"75%",startAngle:90,axisName:{show:!0},boundaryGap:[0,0],splitNumber:5,axisNameGap:15,scale:!1,shape:"polygon",axisLine:ft({lineStyle:{color:"#bbb"}},Cr.axisLine),axisLabel:ba(Cr.axisLabel,!1),axisTick:ba(Cr.axisTick,!1),splitLine:ba(Cr.splitLine,!0),splitArea:ba(Cr.splitArea,!0),indicator:[]},e}(kt),Sy=["axisLine","axisTickLabel","axisName"],xy=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i){var n=this.group;n.removeAll(),this._buildAxes(t),this._buildSplitLineAndArea(t)},e.prototype._buildAxes=function(t){var r=t.coordinateSystem,i=r.getIndicatorAxes(),n=G(i,function(o){var s=o.model.get("showName")?o.name:"",l=new $e(o.model,{axisName:s,position:[r.cx,r.cy],rotation:o.angle,labelDirection:-1,tickDirection:-1,nameDirection:1});return l});D(n,function(o){D(Sy,o.add,o),this.group.add(o.getGroup())},this)},e.prototype._buildSplitLineAndArea=function(t){var r=t.coordinateSystem,i=r.getIndicatorAxes();if(!i.length)return;var n=t.get("shape"),o=t.getModel("splitLine"),s=t.getModel("splitArea"),l=o.getModel("lineStyle"),u=s.getModel("areaStyle"),c=o.get("show"),h=s.get("show"),v=l.get("color"),f=u.get("color"),p=U(v)?v:[v],d=U(f)?f:[f],g=[],y=[];function m(P,k,V){var N=V%k.length;return P[N]=P[N]||[],N}if(n==="circle")for(var S=i[0].getTicksCoords(),x=r.cx,b=r.cy,_=0;_<S.length;_++){if(c){var w=m(g,p,_);g[w].push(new la({shape:{cx:x,cy:b,r:S[_].coord}}))}if(h&&_<S.length-1){var w=m(y,d,_);y[w].push(new bh({shape:{cx:x,cy:b,r0:S[_].coord,r:S[_+1].coord}}))}}else for(var A,C=G(i,function(P,k){var V=P.getTicksCoords();return A=A==null?V.length-1:Math.min(V.length-1,A),G(V,function(N){return r.coordToPoint(N.coord,k)})}),T=[],_=0;_<=A;_++){for(var I=[],L=0;L<i.length;L++)I.push(C[L][_]);if(I[0]&&I.push(I[0].slice()),c){var w=m(g,p,_);g[w].push(new le({shape:{points:I}}))}if(h&&T){var w=m(y,d,_-1);y[w].push(new se({shape:{points:I.concat(T)}}))}T=I.slice().reverse()}var M=l.getLineStyle(),R=u.getAreaStyle();D(y,function(P,k){this.group.add(ne(P,{style:ot({stroke:"none",fill:d[k%d.length]},R),silent:!0}))},this),D(g,function(P,k){this.group.add(ne(P,{style:ot({fill:"none",stroke:p[k%p.length]},M),silent:!0}))},this)},e.type="radar",e}(Ot),by=function(a){E(e,a);function e(t,r,i){var n=a.call(this,t,r,i)||this;return n.type="value",n.angle=0,n.name="",n}return e}(de),_y=function(){function a(e,t,r){this.dimensions=[],this._model=e,this._indicatorAxes=G(e.getIndicatorModels(),function(i,n){var o="indicator_"+n,s=new by(o,new Bn);return s.name=i.get("name"),s.model=i,i.axis=s,this.dimensions.push(o),s},this),this.resize(e,r)}return a.prototype.getIndicatorAxes=function(){return this._indicatorAxes},a.prototype.dataToPoint=function(e,t){var r=this._indicatorAxes[t];return this.coordToPoint(r.dataToCoord(e),t)},a.prototype.coordToPoint=function(e,t){var r=this._indicatorAxes[t],i=r.angle,n=this.cx+e*Math.cos(i),o=this.cy-e*Math.sin(i);return[n,o]},a.prototype.pointToData=function(e){var t=e[0]-this.cx,r=e[1]-this.cy,i=Math.sqrt(t*t+r*r);t/=i,r/=i;for(var n=Math.atan2(-r,t),o=1/0,s,l=-1,u=0;u<this._indicatorAxes.length;u++){var c=this._indicatorAxes[u],h=Math.abs(n-c.angle);h<o&&(s=c,l=u,o=h)}return[l,+(s&&s.coordToData(i))]},a.prototype.resize=function(e,t){var r=e.get("center"),i=t.getWidth(),n=t.getHeight(),o=Math.min(i,n)/2;this.cx=O(r[0],i),this.cy=O(r[1],n),this.startAngle=e.get("startAngle")*Math.PI/180;var s=e.get("radius");(tt(s)||jt(s))&&(s=[0,s]),this.r0=O(s[0],o),this.r=O(s[1],o),D(this._indicatorAxes,function(l,u){l.setExtent(this.r0,this.r);var c=this.startAngle+u*Math.PI*2/this._indicatorAxes.length;c=Math.atan2(Math.sin(c),Math.cos(c)),l.angle=c},this)},a.prototype.update=function(e,t){var r=this._indicatorAxes,i=this._model;D(r,function(s){s.scale.setExtent(1/0,-1/0)}),e.eachSeriesByType("radar",function(s,l){if(!(s.get("coordinateSystem")!=="radar"||e.getComponent("radar",s.get("radarIndex"))!==i)){var u=s.getData();D(r,function(c){c.scale.unionExtentFromData(u,u.mapDimension(c.dim))})}},this);var n=i.get("splitNumber"),o=new Bn;o.setExtent(0,n),o.setInterval(1),D(r,function(s,l){Hp(s.scale,s.model,o)})},a.prototype.convertToPixel=function(e,t,r){return console.warn("Not implemented."),null},a.prototype.convertFromPixel=function(e,t,r){return console.warn("Not implemented."),null},a.prototype.containPoint=function(e){return console.warn("Not implemented."),!1},a.create=function(e,t){var r=[];return e.eachComponent("radar",function(i){var n=new a(i,e,t);r.push(n),i.coordinateSystem=n}),e.eachSeriesByType("radar",function(i){i.get("coordinateSystem")==="radar"&&(i.coordinateSystem=r[i.get("radarIndex")||0])}),r},a.dimensions=[],a}();function wy(a){a.registerCoordinateSystem("radar",_y),a.registerComponentModel(my),a.registerComponentView(xy),a.registerVisual({seriesType:"radar",reset:function(e){var t=e.getData();t.each(function(r){t.setItemVisual(r,"legendIcon","roundRect")}),t.setVisual("legendIcon","roundRect")}})}function Ay(a){K(wy),a.registerChartView(gy),a.registerSeriesModel(yy),a.registerLayout(py),a.registerProcessor(fa("radar")),a.registerPreprocessor(dy)}var bl="\0_ec_interaction_mutex";function Ty(a,e,t){var r=Xo(a);r[e]=t}function Dy(a,e,t){var r=Xo(a),i=r[e];i===t&&(r[e]=null)}function _l(a,e){return!!Xo(a)[e]}function Xo(a){return a[bl]||(a[bl]={})}yi({type:"takeGlobalCursor",event:"globalCursorTaken",update:"update"},Pe);var da=function(a){E(e,a);function e(t){var r=a.call(this)||this;r._zr=t;var i=W(r._mousedownHandler,r),n=W(r._mousemoveHandler,r),o=W(r._mouseupHandler,r),s=W(r._mousewheelHandler,r),l=W(r._pinchHandler,r);return r.enable=function(u,c){this.disable(),this._opt=ot(it(c)||{},{zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!1,preventDefaultMouseMove:!0}),u==null&&(u=!0),(u===!0||u==="move"||u==="pan")&&(t.on("mousedown",i),t.on("mousemove",n),t.on("mouseup",o)),(u===!0||u==="scale"||u==="zoom")&&(t.on("mousewheel",s),t.on("pinch",l))},r.disable=function(){t.off("mousedown",i),t.off("mousemove",n),t.off("mouseup",o),t.off("mousewheel",s),t.off("pinch",l)},r}return e.prototype.isDragging=function(){return this._dragging},e.prototype.isPinching=function(){return this._pinching},e.prototype.setPointerChecker=function(t){this.pointerChecker=t},e.prototype.dispose=function(){this.disable()},e.prototype._mousedownHandler=function(t){if(!Gs(t)){for(var r=t.target;r;){if(r.draggable)return;r=r.__hostTarget||r.parent}var i=t.offsetX,n=t.offsetY;this.pointerChecker&&this.pointerChecker(t,i,n)&&(this._x=i,this._y=n,this._dragging=!0)}},e.prototype._mousemoveHandler=function(t){if(!(!this._dragging||!Ba("moveOnMouseMove",t,this._opt)||t.gestureEvent==="pinch"||_l(this._zr,"globalPan"))){var r=t.offsetX,i=t.offsetY,n=this._x,o=this._y,s=r-n,l=i-o;this._x=r,this._y=i,this._opt.preventDefaultMouseMove&&Xe(t.event),yv(this,"pan","moveOnMouseMove",t,{dx:s,dy:l,oldX:n,oldY:o,newX:r,newY:i,isAvailableBehavior:null})}},e.prototype._mouseupHandler=function(t){Gs(t)||(this._dragging=!1)},e.prototype._mousewheelHandler=function(t){var r=Ba("zoomOnMouseWheel",t,this._opt),i=Ba("moveOnMouseWheel",t,this._opt),n=t.wheelDelta,o=Math.abs(n),s=t.offsetX,l=t.offsetY;if(!(n===0||!r&&!i)){if(r){var u=o>3?1.4:o>1?1.2:1.1,c=n>0?u:1/u;Wi(this,"zoom","zoomOnMouseWheel",t,{scale:c,originX:s,originY:l,isAvailableBehavior:null})}if(i){var h=Math.abs(n),v=(n>0?1:-1)*(h>3?.4:h>1?.15:.05);Wi(this,"scrollMove","moveOnMouseWheel",t,{scrollDelta:v,originX:s,originY:l,isAvailableBehavior:null})}}},e.prototype._pinchHandler=function(t){if(!_l(this._zr,"globalPan")){var r=t.pinchScale>1?1.1:1/1.1;Wi(this,"zoom",null,t,{scale:r,originX:t.pinchX,originY:t.pinchY,isAvailableBehavior:null})}},e}(_h);function Wi(a,e,t,r,i){a.pointerChecker&&a.pointerChecker(r,i.originX,i.originY)&&(Xe(r.event),yv(a,e,t,r,i))}function yv(a,e,t,r,i){i.isAvailableBehavior=W(Ba,null,t,r),a.trigger(e,i)}function Ba(a,e,t){var r=t[a];return!a||r&&(!tt(r)||e.event[r+"Key"])}function Ko(a,e,t){var r=a.target;r.x+=e,r.y+=t,r.dirty()}function qo(a,e,t,r){var i=a.target,n=a.zoomLimit,o=a.zoom=a.zoom||1;if(o*=e,n){var s=n.min||0,l=n.max||1/0;o=Math.max(Math.min(l,o),s)}var u=o/a.zoom;a.zoom=o,i.x-=(t-i.x)*(u-1),i.y-=(r-i.y)*(u-1),i.scaleX*=u,i.scaleY*=u,i.dirty()}var Cy={axisPointer:1,tooltip:1,brush:1};function Ii(a,e,t){var r=e.getComponentByElement(a.topTarget),i=r&&r.coordinateSystem;return r&&r!==t&&!Cy.hasOwnProperty(r.mainType)&&i&&i.model!==t}function mv(a){if(tt(a)){var e=new DOMParser;a=e.parseFromString(a,"text/xml")}var t=a;for(t.nodeType===9&&(t=t.firstChild);t.nodeName.toLowerCase()!=="svg"||t.nodeType!==1;)t=t.nextSibling;return t}var Zi,Ja={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-anchor":"textAlign",visibility:"visibility",display:"display"},wl=wt(Ja),Qa={"alignment-baseline":"textBaseline","stop-color":"stopColor"},Al=wt(Qa),Iy=function(){function a(){this._defs={},this._root=null}return a.prototype.parse=function(e,t){t=t||{};var r=mv(e);this._defsUsePending=[];var i=new X;this._root=i;var n=[],o=r.getAttribute("viewBox")||"",s=parseFloat(r.getAttribute("width")||t.width),l=parseFloat(r.getAttribute("height")||t.height);isNaN(s)&&(s=null),isNaN(l)&&(l=null),Kt(r,i,null,!0,!1);for(var u=r.firstChild;u;)this._parseNode(u,i,n,null,!1,!1),u=u.nextSibling;Py(this._defs,this._defsUsePending),this._defsUsePending=[];var c,h;if(o){var v=Li(o);v.length>=4&&(c={x:parseFloat(v[0]||0),y:parseFloat(v[1]||0),width:parseFloat(v[2]),height:parseFloat(v[3])})}if(c&&s!=null&&l!=null&&(h=xv(c,{x:0,y:0,width:s,height:l}),!t.ignoreViewBox)){var f=i;i=new X,i.add(f),f.scaleX=f.scaleY=h.scale,f.x=h.x,f.y=h.y}return!t.ignoreRootClip&&s!=null&&l!=null&&i.setClipPath(new pt({shape:{x:0,y:0,width:s,height:l}})),{root:i,width:s,height:l,viewBoxRect:c,viewBoxTransform:h,named:n}},a.prototype._parseNode=function(e,t,r,i,n,o){var s=e.nodeName.toLowerCase(),l,u=i;if(s==="defs"&&(n=!0),s==="text"&&(o=!0),s==="defs"||s==="switch")l=t;else{if(!n){var c=Zi[s];if(c&&B(Zi,s)){l=c.call(this,e,t);var h=e.getAttribute("name");if(h){var v={name:h,namedFrom:null,svgNodeTagLower:s,el:l};r.push(v),s==="g"&&(u=v)}else i&&r.push({name:i.name,namedFrom:i,svgNodeTagLower:s,el:l});t.add(l)}}var f=Tl[s];if(f&&B(Tl,s)){var p=f.call(this,e),d=e.getAttribute("id");d&&(this._defs[d]=p)}}if(l&&l.isGroup)for(var g=e.firstChild;g;)g.nodeType===1?this._parseNode(g,l,r,u,n,o):g.nodeType===3&&o&&this._parseText(g,l),g=g.nextSibling},a.prototype._parseText=function(e,t){var r=new ph({style:{text:e.textContent},silent:!0,x:this._textX||0,y:this._textY||0});Jt(t,r),Kt(e,r,this._defsUsePending,!1,!1),Ly(r,t);var i=r.style,n=i.fontSize;n&&n<9&&(i.fontSize=9,r.scaleX*=n/9,r.scaleY*=n/9);var o=(i.fontSize||i.fontFamily)&&[i.fontStyle,i.fontWeight,(i.fontSize||12)+"px",i.fontFamily||"sans-serif"].join(" ");i.font=o;var s=r.getBoundingRect();return this._textX+=s.width,t.add(r),r},a.internalField=function(){Zi={g:function(e,t){var r=new X;return Jt(t,r),Kt(e,r,this._defsUsePending,!1,!1),r},rect:function(e,t){var r=new pt;return Jt(t,r),Kt(e,r,this._defsUsePending,!1,!1),r.setShape({x:parseFloat(e.getAttribute("x")||"0"),y:parseFloat(e.getAttribute("y")||"0"),width:parseFloat(e.getAttribute("width")||"0"),height:parseFloat(e.getAttribute("height")||"0")}),r.silent=!0,r},circle:function(e,t){var r=new la;return Jt(t,r),Kt(e,r,this._defsUsePending,!1,!1),r.setShape({cx:parseFloat(e.getAttribute("cx")||"0"),cy:parseFloat(e.getAttribute("cy")||"0"),r:parseFloat(e.getAttribute("r")||"0")}),r.silent=!0,r},line:function(e,t){var r=new oe;return Jt(t,r),Kt(e,r,this._defsUsePending,!1,!1),r.setShape({x1:parseFloat(e.getAttribute("x1")||"0"),y1:parseFloat(e.getAttribute("y1")||"0"),x2:parseFloat(e.getAttribute("x2")||"0"),y2:parseFloat(e.getAttribute("y2")||"0")}),r.silent=!0,r},ellipse:function(e,t){var r=new Wp;return Jt(t,r),Kt(e,r,this._defsUsePending,!1,!1),r.setShape({cx:parseFloat(e.getAttribute("cx")||"0"),cy:parseFloat(e.getAttribute("cy")||"0"),rx:parseFloat(e.getAttribute("rx")||"0"),ry:parseFloat(e.getAttribute("ry")||"0")}),r.silent=!0,r},polygon:function(e,t){var r=e.getAttribute("points"),i;r&&(i=Il(r));var n=new se({shape:{points:i||[]},silent:!0});return Jt(t,n),Kt(e,n,this._defsUsePending,!1,!1),n},polyline:function(e,t){var r=e.getAttribute("points"),i;r&&(i=Il(r));var n=new le({shape:{points:i||[]},silent:!0});return Jt(t,n),Kt(e,n,this._defsUsePending,!1,!1),n},image:function(e,t){var r=new pe;return Jt(t,r),Kt(e,r,this._defsUsePending,!1,!1),r.setStyle({image:e.getAttribute("xlink:href")||e.getAttribute("href"),x:+e.getAttribute("x"),y:+e.getAttribute("y"),width:+e.getAttribute("width"),height:+e.getAttribute("height")}),r.silent=!0,r},text:function(e,t){var r=e.getAttribute("x")||"0",i=e.getAttribute("y")||"0",n=e.getAttribute("dx")||"0",o=e.getAttribute("dy")||"0";this._textX=parseFloat(r)+parseFloat(n),this._textY=parseFloat(i)+parseFloat(o);var s=new X;return Jt(t,s),Kt(e,s,this._defsUsePending,!1,!0),s},tspan:function(e,t){var r=e.getAttribute("x"),i=e.getAttribute("y");r!=null&&(this._textX=parseFloat(r)),i!=null&&(this._textY=parseFloat(i));var n=e.getAttribute("dx")||"0",o=e.getAttribute("dy")||"0",s=new X;return Jt(t,s),Kt(e,s,this._defsUsePending,!1,!0),this._textX+=parseFloat(n),this._textY+=parseFloat(o),s},path:function(e,t){var r=e.getAttribute("d")||"",i=Zp(r);return Jt(t,i),Kt(e,i,this._defsUsePending,!1,!1),i.silent=!0,i}}}(),a}(),Tl={lineargradient:function(a){var e=parseInt(a.getAttribute("x1")||"0",10),t=parseInt(a.getAttribute("y1")||"0",10),r=parseInt(a.getAttribute("x2")||"10",10),i=parseInt(a.getAttribute("y2")||"0",10),n=new Mo(e,t,r,i);return Dl(a,n),Cl(a,n),n},radialgradient:function(a){var e=parseInt(a.getAttribute("cx")||"0",10),t=parseInt(a.getAttribute("cy")||"0",10),r=parseInt(a.getAttribute("r")||"0",10),i=new Up(e,t,r);return Dl(a,i),Cl(a,i),i}};function Dl(a,e){var t=a.getAttribute("gradientUnits");t==="userSpaceOnUse"&&(e.global=!0)}function Cl(a,e){for(var t=a.firstChild;t;){if(t.nodeType===1&&t.nodeName.toLocaleLowerCase()==="stop"){var r=t.getAttribute("offset"),i=void 0;r&&r.indexOf("%")>0?i=parseInt(r,10)/100:r?i=parseFloat(r):i=0;var n={};Sv(t,n,n);var o=n.stopColor||t.getAttribute("stop-color")||"#000000";e.colorStops.push({offset:i,color:o})}t=t.nextSibling}}function Jt(a,e){a&&a.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),ot(e.__inheritedStyle,a.__inheritedStyle))}function Il(a){for(var e=Li(a),t=[],r=0;r<e.length;r+=2){var i=parseFloat(e[r]),n=parseFloat(e[r+1]);t.push([i,n])}return t}function Kt(a,e,t,r,i){var n=e,o=n.__inheritedStyle=n.__inheritedStyle||{},s={};a.nodeType===1&&(ky(a,e),Sv(a,o,s),r||Vy(a,o,s)),n.style=n.style||{},o.fill!=null&&(n.style.fill=Ll(n,"fill",o.fill,t)),o.stroke!=null&&(n.style.stroke=Ll(n,"stroke",o.stroke,t)),D(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],function(l){o[l]!=null&&(n.style[l]=parseFloat(o[l]))}),D(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign"],function(l){o[l]!=null&&(n.style[l]=o[l])}),i&&(n.__selfStyle=s),o.lineDash&&(n.style.lineDash=G(Li(o.lineDash),function(l){return parseFloat(l)})),(o.visibility==="hidden"||o.visibility==="collapse")&&(n.invisible=!0),o.display==="none"&&(n.ignore=!0)}function Ly(a,e){var t=e.__selfStyle;if(t){var r=t.textBaseline,i=r;!r||r==="auto"||r==="baseline"?i="alphabetic":r==="before-edge"||r==="text-before-edge"?i="top":r==="after-edge"||r==="text-after-edge"?i="bottom":(r==="central"||r==="mathematical")&&(i="middle"),a.style.textBaseline=i}var n=e.__inheritedStyle;if(n){var o=n.textAlign,s=o;o&&(o==="middle"&&(s="center"),a.style.textAlign=s)}}var My=/^url\(\s*#(.*?)\)/;function Ll(a,e,t,r){var i=t&&t.match(My);if(i){var n=Yp(i[1]);r.push([a,e,n]);return}return t==="none"&&(t=null),t}function Py(a,e){for(var t=0;t<e.length;t++){var r=e[t];r[0].style[r[1]]=a[r[2]]}}var Ry=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function Li(a){return a.match(Ry)||[]}var Ey=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.eE,]*)\)/g,Ui=Math.PI/180;function ky(a,e){var t=a.getAttribute("transform");if(t){t=t.replace(/,/g," ");var r=[],i=null;t.replace(Ey,function(h,v,f){return r.push(v,f),""});for(var n=r.length-1;n>0;n-=2){var o=r[n],s=r[n-1],l=Li(o);switch(i=i||wr(),s){case"translate":Ke(i,i,[parseFloat(l[0]),parseFloat(l[1]||"0")]);break;case"scale":wh(i,i,[parseFloat(l[0]),parseFloat(l[1]||l[0])]);break;case"rotate":mi(i,i,-parseFloat(l[0])*Ui,[parseFloat(l[1]||"0"),parseFloat(l[2]||"0")]);break;case"skewX":var u=Math.tan(parseFloat(l[0])*Ui);Fs(i,[1,0,u,1,0,0],i);break;case"skewY":var c=Math.tan(parseFloat(l[0])*Ui);Fs(i,[1,c,0,1,0,0],i);break;case"matrix":i[0]=parseFloat(l[0]),i[1]=parseFloat(l[1]),i[2]=parseFloat(l[2]),i[3]=parseFloat(l[3]),i[4]=parseFloat(l[4]),i[5]=parseFloat(l[5]);break}}e.setLocalTransform(i)}}var Ml=/([^\s:;]+)\s*:\s*([^:;]+)/g;function Sv(a,e,t){var r=a.getAttribute("style");if(r){Ml.lastIndex=0;for(var i;(i=Ml.exec(r))!=null;){var n=i[1],o=B(Ja,n)?Ja[n]:null;o&&(e[o]=i[2]);var s=B(Qa,n)?Qa[n]:null;s&&(t[s]=i[2])}}}function Vy(a,e,t){for(var r=0;r<wl.length;r++){var i=wl[r],n=a.getAttribute(i);n!=null&&(e[Ja[i]]=n)}for(var r=0;r<Al.length;r++){var i=Al[r],n=a.getAttribute(i);n!=null&&(t[Qa[i]]=n)}}function xv(a,e){var t=e.width/a.width,r=e.height/a.height,i=Math.min(t,r);return{scale:i,x:-(a.x+a.width/2)*i+(e.x+e.width/2),y:-(a.y+a.height/2)*i+(e.y+e.height/2)}}function Ny(a,e){var t=new Iy;return t.parse(a,e)}var zy=et(["rect","circle","line","ellipse","polygon","polyline","path","text","tspan","g"]),Oy=function(){function a(e,t){this.type="geoSVG",this._usedGraphicMap=et(),this._freedGraphics=[],this._mapName=e,this._parsedXML=mv(t)}return a.prototype.load=function(){var e=this._firstGraphic;if(!e){e=this._firstGraphic=this._buildGraphic(this._parsedXML),this._freedGraphics.push(e),this._boundingRect=this._firstGraphic.boundingRect.clone();var t=Gy(e.named),r=t.regions,i=t.regionsMap;this._regions=r,this._regionsMap=i}return{boundingRect:this._boundingRect,regions:this._regions,regionsMap:this._regionsMap}},a.prototype._buildGraphic=function(e){var t,r;try{t=e&&Ny(e,{ignoreViewBox:!0,ignoreRootClip:!0})||{},r=t.root,Yr(r!=null)}catch(g){throw new Error(`Invalid svg format
`+g.message)}var i=new X;i.add(r),i.isGeoSVGGraphicRoot=!0;var n=t.width,o=t.height,s=t.viewBoxRect,l=this._boundingRect;if(!l){var u=void 0,c=void 0,h=void 0,v=void 0;if(n!=null?(u=0,h=n):s&&(u=s.x,h=s.width),o!=null?(c=0,v=o):s&&(c=s.y,v=s.height),u==null||c==null){var f=r.getBoundingRect();u==null&&(u=f.x,h=f.width),c==null&&(c=f.y,v=f.height)}l=this._boundingRect=new Pt(u,c,h,v)}if(s){var p=xv(s,l);r.scaleX=r.scaleY=p.scale,r.x=p.x,r.y=p.y}i.setClipPath(new pt({shape:l.plain()}));var d=[];return D(t.named,function(g){zy.get(g.svgNodeTagLower)!=null&&(d.push(g),By(g.el))}),{root:i,boundingRect:l,named:d}},a.prototype.useGraphic=function(e){var t=this._usedGraphicMap,r=t.get(e);return r||(r=this._freedGraphics.pop()||this._buildGraphic(this._parsedXML),t.set(e,r),r)},a.prototype.freeGraphic=function(e){var t=this._usedGraphicMap,r=t.get(e);r&&(t.removeKey(e),this._freedGraphics.push(r))},a}();function By(a){a.silent=!1,a.isGroup&&a.traverse(function(e){e.silent=!1})}function Gy(a){var e=[],t=et();return D(a,function(r){if(r.namedFrom==null){var i=new $p(r.name,r.el);e.push(i),t.set(r.name,i)}}),{regions:e,regionsMap:t}}var jn=[126,25],Pl="南海诸岛",Oe=[[[0,3.5],[7,11.2],[15,11.9],[30,7],[42,.7],[52,.7],[56,7.7],[59,.7],[64,.7],[64,0],[5,0],[0,3.5]],[[13,16.1],[19,14.7],[16,21.7],[11,23.1],[13,16.1]],[[12,32.2],[14,38.5],[15,38.5],[13,32.2],[12,32.2]],[[16,47.6],[12,53.2],[13,53.2],[18,47.6],[16,47.6]],[[6,64.4],[8,70],[9,70],[8,64.4],[6,64.4]],[[23,82.6],[29,79.8],[30,79.8],[25,82.6],[23,82.6]],[[37,70.7],[43,62.3],[44,62.3],[39,70.7],[37,70.7]],[[48,51.1],[51,45.5],[53,45.5],[50,51.1],[48,51.1]],[[51,35],[51,28.7],[53,28.7],[53,35],[51,35]],[[52,22.4],[55,17.5],[56,17.5],[53,22.4],[52,22.4]],[[58,12.6],[62,7],[63,7],[60,12.6],[58,12.6]],[[0,3.5],[0,93.1],[64,93.1],[64,0],[63,0],[63,92.4],[1,92.4],[1,3.5],[0,3.5]]];for(var Ne=0;Ne<Oe.length;Ne++)for(var tr=0;tr<Oe[Ne].length;tr++)Oe[Ne][tr][0]/=10.5,Oe[Ne][tr][1]/=-10.5/.75,Oe[Ne][tr][0]+=jn[0],Oe[Ne][tr][1]+=jn[1];function Fy(a,e){if(a==="china"){for(var t=0;t<e.length;t++)if(e[t].name===Pl)return;e.push(new Xp(Pl,G(Oe,function(r){return{type:"polygon",exterior:r}}),jn))}}var Hy={南海诸岛:[32,80],广东:[0,-10],香港:[10,5],澳门:[-10,10],天津:[5,5]};function Wy(a,e){if(a==="china"){var t=Hy[e.name];if(t){var r=e.getCenter();r[0]+=t[0]/10.5,r[1]+=-t[1]/(10.5/.75),e.setCenter(r)}}}var Zy=[[[123.45165252685547,25.73527164402261],[123.49731445312499,25.73527164402261],[123.49731445312499,25.750734064600884],[123.45165252685547,25.750734064600884],[123.45165252685547,25.73527164402261]]];function Uy(a,e){a==="china"&&e.name==="台湾"&&e.geometries.push({type:"polygon",exterior:Zy[0]})}var Yy="name",$y=function(){function a(e,t,r){this.type="geoJSON",this._parsedMap=et(),this._mapName=e,this._specialAreas=r,this._geoJSON=Ky(t)}return a.prototype.load=function(e,t){t=t||Yy;var r=this._parsedMap.get(t);if(!r){var i=this._parseToRegions(t);r=this._parsedMap.set(t,{regions:i,boundingRect:Xy(i)})}var n=et(),o=[];return D(r.regions,function(s){var l=s.name;e&&B(e,l)&&(s=s.cloneShallow(l=e[l])),o.push(s),n.set(l,s)}),{regions:o,boundingRect:r.boundingRect||new Pt(0,0,0,0),regionsMap:n}},a.prototype._parseToRegions=function(e){var t=this._mapName,r=this._geoJSON,i;try{i=r?Kp(r,e):[]}catch(n){throw new Error(`Invalid geoJson format
`+n.message)}return Fy(t,i),D(i,function(n){var o=n.name;Wy(t,n),Uy(t,n);var s=this._specialAreas&&this._specialAreas[o];s&&n.transformTo(s.left,s.top,s.width,s.height)},this),i},a.prototype.getMapForUser=function(){return{geoJson:this._geoJSON,geoJSON:this._geoJSON,specialAreas:this._specialAreas}},a}();function Xy(a){for(var e,t=0;t<a.length;t++){var r=a[t].getBoundingRect();e=e||r.clone(),e.union(r)}return e}function Ky(a){return tt(a)?typeof JSON<"u"&&JSON.parse?JSON.parse(a):new Function("return ("+a+");")():a}var Ir=et();const _e={registerMap:function(a,e,t){if(e.svg){var r=new Oy(a,e.svg);Ir.set(a,r)}else{var i=e.geoJson||e.geoJSON;i&&!e.features?t=e.specialAreas:i=e;var r=new $y(a,i,t);Ir.set(a,r)}},getGeoResource:function(a){return Ir.get(a)},getMapForUser:function(a){var e=Ir.get(a);return e&&e.type==="geoJSON"&&e.getMapForUser()},load:function(a,e,t){var r=Ir.get(a);if(r)return r.load(e,t)}};var jo=["rect","circle","line","ellipse","polygon","polyline","path"],qy=et(jo),jy=et(jo.concat(["g"])),Jy=et(jo.concat(["g"])),bv=Dt();function _a(a){var e=a.getItemStyle(),t=a.get("areaColor");return t!=null&&(e.fill=t),e}function Rl(a){var e=a.style;e&&(e.stroke=e.stroke||e.fill,e.fill=null)}var _v=function(){function a(e){var t=new X;this.uid=Ah("ec_map_draw"),this._controller=new da(e.getZr()),this._controllerHost={target:t},this.group=t,t.add(this._regionsGroup=new X),t.add(this._svgGroup=new X)}return a.prototype.draw=function(e,t,r,i,n){var o=e.mainType==="geo",s=e.getData&&e.getData();o&&t.eachComponent({mainType:"series",subType:"map"},function(y){!s&&y.getHostGeoModel()===e&&(s=y.getData())});var l=e.coordinateSystem,u=this._regionsGroup,c=this.group,h=l.getTransformInfo(),v=h.raw,f=h.roam,p=!u.childAt(0)||n;p?(c.x=f.x,c.y=f.y,c.scaleX=f.scaleX,c.scaleY=f.scaleY,c.dirty()):dt(c,f,e);var d=s&&s.getVisual("visualMeta")&&s.getVisual("visualMeta").length>0,g={api:r,geo:l,mapOrGeoModel:e,data:s,isVisualEncodedByVisualMap:d,isGeo:o,transformInfoRaw:v};l.resourceType==="geoJSON"?this._buildGeoJSON(g):l.resourceType==="geoSVG"&&this._buildSVG(g),this._updateController(e,t,r),this._updateMapSelectHandler(e,u,r,i)},a.prototype._buildGeoJSON=function(e){var t=this._regionsGroupByName=et(),r=et(),i=this._regionsGroup,n=e.transformInfoRaw,o=e.mapOrGeoModel,s=e.data,l=e.geo.projection,u=l&&l.stream;function c(f,p){return p&&(f=p(f)),f&&[f[0]*n.scaleX+n.x,f[1]*n.scaleY+n.y]}function h(f){for(var p=[],d=!u&&l&&l.project,g=0;g<f.length;++g){var y=c(f[g],d);y&&p.push(y)}return p}function v(f){return{shape:{points:h(f)}}}i.removeAll(),D(e.geo.regions,function(f){var p=f.name,d=t.get(p),g=r.get(p)||{},y=g.dataIdx,m=g.regionModel;if(!d){d=t.set(p,new X),i.add(d),y=s?s.indexOfName(p):null,m=e.isGeo?o.getRegionModel(p):s?s.getItemModel(y):null;var S=m.get("silent",!0);S!=null&&(d.silent=S),r.set(p,{dataIdx:y,regionModel:m})}var x=[],b=[];D(f.geometries,function(A){if(A.type==="polygon"){var C=[A.exterior].concat(A.interiors||[]);u&&(C=Ol(C,u)),D(C,function(I){x.push(new se(v(I)))})}else{var T=A.points;u&&(T=Ol(T,u,!0)),D(T,function(I){b.push(new le(v(I)))})}});var _=c(f.getCenter(),l&&l.project);function w(A,C){if(A.length){var T=new hh({culling:!0,segmentIgnoreThreshold:1,shape:{paths:A}});d.add(T),El(e,T,y,m),kl(e,T,p,m,o,y,_),C&&(Rl(T),D(T.states,Rl))}}w(x),w(b,!0)}),t.each(function(f,p){var d=r.get(p),g=d.dataIdx,y=d.regionModel;Vl(e,f,p,y,o,g),Nl(e,f,p,y,o),zl(e,f,p,y,o)},this)},a.prototype._buildSVG=function(e){var t=e.geo.map,r=e.transformInfoRaw;this._svgGroup.x=r.x,this._svgGroup.y=r.y,this._svgGroup.scaleX=r.scaleX,this._svgGroup.scaleY=r.scaleY,this._svgResourceChanged(t)&&(this._freeSVG(),this._useSVG(t));var i=this._svgDispatcherMap=et(),n=!1;D(this._svgGraphicRecord.named,function(o){var s=o.name,l=e.mapOrGeoModel,u=e.data,c=o.svgNodeTagLower,h=o.el,v=u?u.indexOfName(s):null,f=l.getRegionModel(s);qy.get(c)!=null&&h instanceof dr&&El(e,h,v,f),h instanceof dr&&(h.culling=!0);var p=f.get("silent",!0);if(p!=null&&(h.silent=p),h.z2EmphasisLift=0,!o.namedFrom&&(Jy.get(c)!=null&&kl(e,h,s,f,l,v,null),Vl(e,h,s,f,l,v),Nl(e,h,s,f,l),jy.get(c)!=null)){var d=zl(e,h,s,f,l);d==="self"&&(n=!0);var g=i.get(s)||i.set(s,[]);g.push(h)}},this),this._enableBlurEntireSVG(n,e)},a.prototype._enableBlurEntireSVG=function(e,t){if(e&&t.isGeo){var r=t.mapOrGeoModel.getModel(["blur","itemStyle"]).getItemStyle(),i=r.opacity;this._svgGraphicRecord.root.traverse(function(n){if(!n.isGroup){gr(n);var o=n.ensureState("blur").style||{};o.opacity==null&&i!=null&&(o.opacity=i),n.ensureState("emphasis")}})}},a.prototype.remove=function(){this._regionsGroup.removeAll(),this._regionsGroupByName=null,this._svgGroup.removeAll(),this._freeSVG(),this._controller.dispose(),this._controllerHost=null},a.prototype.findHighDownDispatchers=function(e,t){if(e==null)return[];var r=t.coordinateSystem;if(r.resourceType==="geoJSON"){var i=this._regionsGroupByName;if(i){var n=i.get(e);return n?[n]:[]}}else if(r.resourceType==="geoSVG")return this._svgDispatcherMap&&this._svgDispatcherMap.get(e)||[]},a.prototype._svgResourceChanged=function(e){return this._svgMapName!==e},a.prototype._useSVG=function(e){var t=_e.getGeoResource(e);if(t&&t.type==="geoSVG"){var r=t.useGraphic(this.uid);this._svgGroup.add(r.root),this._svgGraphicRecord=r,this._svgMapName=e}},a.prototype._freeSVG=function(){var e=this._svgMapName;if(e!=null){var t=_e.getGeoResource(e);t&&t.type==="geoSVG"&&t.freeGraphic(this.uid),this._svgGraphicRecord=null,this._svgDispatcherMap=null,this._svgGroup.removeAll(),this._svgMapName=null}},a.prototype._updateController=function(e,t,r){var i=e.coordinateSystem,n=this._controller,o=this._controllerHost;o.zoomLimit=e.get("scaleLimit"),o.zoom=i.getZoom(),n.enable(e.get("roam")||!1);var s=e.mainType;function l(){var u={type:"geoRoam",componentType:s};return u[s+"Id"]=e.id,u}n.off("pan").on("pan",function(u){this._mouseDownFlag=!1,Ko(o,u.dx,u.dy),r.dispatchAction(F(l(),{dx:u.dx,dy:u.dy,animation:{duration:0}}))},this),n.off("zoom").on("zoom",function(u){this._mouseDownFlag=!1,qo(o,u.scale,u.originX,u.originY),r.dispatchAction(F(l(),{totalZoom:o.zoom,zoom:u.scale,originX:u.originX,originY:u.originY,animation:{duration:0}}))},this),n.setPointerChecker(function(u,c,h){return i.containPoint([c,h])&&!Ii(u,r,e)})},a.prototype.resetForLabelLayout=function(){this.group.traverse(function(e){var t=e.getTextContent();t&&(t.ignore=bv(t).ignore)})},a.prototype._updateMapSelectHandler=function(e,t,r,i){var n=this;t.off("mousedown"),t.off("click"),e.get("selectedMode")&&(t.on("mousedown",function(){n._mouseDownFlag=!0}),t.on("click",function(o){n._mouseDownFlag&&(n._mouseDownFlag=!1)}))},a}();function El(a,e,t,r){var i=r.getModel("itemStyle"),n=r.getModel(["emphasis","itemStyle"]),o=r.getModel(["blur","itemStyle"]),s=r.getModel(["select","itemStyle"]),l=_a(i),u=_a(n),c=_a(s),h=_a(o),v=a.data;if(v){var f=v.getItemVisual(t,"style"),p=v.getItemVisual(t,"decal");a.isVisualEncodedByVisualMap&&f.fill&&(l.fill=f.fill),p&&(l.decal=Si(p,a.api))}e.setStyle(l),e.style.strokeNoScale=!0,e.ensureState("emphasis").style=u,e.ensureState("select").style=c,e.ensureState("blur").style=h,gr(e)}function kl(a,e,t,r,i,n,o){var s=a.data,l=a.isGeo,u=s&&isNaN(s.get(s.mapDimension("value"),n)),c=s&&s.getItemLayout(n);if(l||u||c&&c.showLabel){var h=l?t:n,v=void 0;(!s||n>=0)&&(v=i);var f=o?{normal:{align:"center",verticalAlign:"middle"}}:null;Wt(e,zt(r),{labelFetcher:v,labelDataIndex:h,defaultText:t},f);var p=e.getTextContent();if(p&&(bv(p).ignore=p.ignore,e.textConfig&&o)){var d=e.getBoundingRect().clone();e.textConfig.layoutRect=d,e.textConfig.position=[(o[0]-d.x)/d.width*100+"%",(o[1]-d.y)/d.height*100+"%"]}e.disableLabelAnimation=!0}else e.removeTextContent(),e.removeTextConfig(),e.disableLabelAnimation=null}function Vl(a,e,t,r,i,n){a.data?a.data.setItemGraphicEl(n,e):ht(e).eventData={componentType:"geo",componentIndex:i.componentIndex,geoIndex:i.componentIndex,name:t,region:r&&r.option||{}}}function Nl(a,e,t,r,i){a.data||xi({el:e,componentModel:i,itemName:t,itemTooltipOption:r.get("tooltip")})}function zl(a,e,t,r,i){e.highDownSilentOnTouch=!!i.get("selectedMode");var n=r.getModel("emphasis"),o=n.get("focus");return xt(e,o,n.get("blurScope"),n.get("disabled")),a.isGeo&&qp(e,i,t),o}function Ol(a,e,t){var r=[],i;function n(){i=[]}function o(){i.length&&(r.push(i),i=[])}var s=e({polygonStart:n,polygonEnd:o,lineStart:n,lineEnd:o,point:function(l,u){isFinite(l)&&isFinite(u)&&i.push([l,u])},sphere:function(){}});return!t&&s.polygonStart(),D(a,function(l){s.lineStart();for(var u=0;u<l.length;u++)s.point(l[u][0],l[u][1]);s.lineEnd()}),!t&&s.polygonEnd(),r}var Qy=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i,n){if(!(n&&n.type==="mapToggleSelect"&&n.from===this.uid)){var o=this.group;if(o.removeAll(),!t.getHostGeoModel()){if(this._mapDraw&&n&&n.type==="geoRoam"&&this._mapDraw.resetForLabelLayout(),n&&n.type==="geoRoam"&&n.componentType==="series"&&n.seriesId===t.id){var s=this._mapDraw;s&&o.add(s.group)}else if(t.needsDrawMap){var s=this._mapDraw||new _v(i);o.add(s.group),s.draw(t,r,i,this,n),this._mapDraw=s}else this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null;t.get("showLegendSymbol")&&r.getComponent("legend")&&this._renderSymbols(t,r,i)}}},e.prototype.remove=function(){this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null,this.group.removeAll()},e.prototype.dispose=function(){this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null},e.prototype._renderSymbols=function(t,r,i){var n=t.originalData,o=this.group;n.each(n.mapDimension("value"),function(s,l){if(!isNaN(s)){var u=n.getItemLayout(l);if(!(!u||!u.point)){var c=u.point,h=u.offset,v=new la({style:{fill:t.getData().getVisual("style").fill},shape:{cx:c[0]+h*9,cy:c[1],r:3},silent:!0,z2:8+(h?0:ua+1)});if(!h){var f=t.mainSeries.getData(),p=n.getName(l),d=f.indexOfName(p),g=n.getItemModel(l),y=g.getModel("label"),m=f.getItemGraphicEl(d);Wt(v,zt(g),{labelFetcher:{getFormattedLabel:function(S,x){return t.getFormattedLabel(d,x)}},defaultText:p}),v.disableLabelAnimation=!0,y.get("position")||v.setTextConfig({position:"bottom"}),m.onHoverStateChange=function(S){Th(v,S)}}o.add(v)}}})},e.type="map",e}(bt),tm=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.needsDrawMap=!1,t.seriesGroup=[],t.getTooltipPosition=function(r){if(r!=null){var i=this.getData().getName(r),n=this.coordinateSystem,o=n.getRegion(i);return o&&n.dataToPoint(o.getCenter())}},t}return e.prototype.getInitialData=function(t){for(var r=Tr(this,{coordDimensions:["value"],encodeDefaulter:q(Lo,this)}),i=et(),n=[],o=0,s=r.count();o<s;o++){var l=r.getName(o);i.set(l,o)}var u=_e.load(this.getMapType(),this.option.nameMap,this.option.nameProperty);return D(u.regions,function(c){var h=c.name,v=i.get(h),f=c.properties&&c.properties.echartsStyle,p;v==null?(p={name:h},n.push(p)):p=r.getRawDataItem(v),f&&ft(p,f)}),r.appendData(n),r},e.prototype.getHostGeoModel=function(){var t=this.option.geoIndex;return t!=null?this.ecModel.getComponent("geo",t):null},e.prototype.getMapType=function(){return(this.getHostGeoModel()||this).option.map},e.prototype.getRawValue=function(t){var r=this.getData();return r.get(r.mapDimension("value"),t)},e.prototype.getRegionModel=function(t){var r=this.getData();return r.getItemModel(r.indexOfName(t))},e.prototype.formatTooltip=function(t,r,i){for(var n=this.getData(),o=this.getRawValue(t),s=n.getName(t),l=this.seriesGroup,u=[],c=0;c<l.length;c++){var h=l[c].originalData.indexOfName(s),v=n.mapDimension("value");isNaN(l[c].originalData.get(v,h))||u.push(l[c].name)}return Zt("section",{header:u.join(", "),noHeader:!u.length,blocks:[Zt("nameValue",{name:s,value:o})]})},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.getLegendIcon=function(t){var r=t.icon||"roundRect",i=Et(r,0,0,t.itemWidth,t.itemHeight,t.itemStyle.fill);return i.setStyle(t.itemStyle),i.style.stroke="none",r.indexOf("empty")>-1&&(i.style.stroke=i.style.fill,i.style.fill="#fff",i.style.lineWidth=2),i},e.type="series.map",e.dependencies=["geo"],e.layoutMode="box",e.defaultOption={z:2,coordinateSystem:"geo",map:"",left:"center",top:"center",aspectScale:null,showLegendSymbol:!0,boundingCoords:null,center:null,zoom:1,scaleLimit:null,selectedMode:!0,label:{show:!1,color:"#000"},itemStyle:{borderWidth:.5,borderColor:"#444",areaColor:"#eee"},emphasis:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{areaColor:"rgba(255,215,0,0.8)"}},select:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{color:"rgba(255,215,0,0.8)"}},nameProperty:"name"},e}(Ct);function em(a,e){var t={};return D(a,function(r){r.each(r.mapDimension("value"),function(i,n){var o="ec-"+r.getName(n);t[o]=t[o]||[],isNaN(i)||t[o].push(i)})}),a[0].map(a[0].mapDimension("value"),function(r,i){for(var n="ec-"+a[0].getName(i),o=0,s=1/0,l=-1/0,u=t[n].length,c=0;c<u;c++)s=Math.min(s,t[n][c]),l=Math.max(l,t[n][c]),o+=t[n][c];var h;return e==="min"?h=s:e==="max"?h=l:e==="average"?h=o/u:h=o,u===0?NaN:h})}function rm(a){var e={};a.eachSeriesByType("map",function(t){var r=t.getHostGeoModel(),i=r?"o"+r.id:"i"+t.getMapType();(e[i]=e[i]||[]).push(t)}),D(e,function(t,r){for(var i=em(G(t,function(o){return o.getData()}),t[0].get("mapValueCalculation")),n=0;n<t.length;n++)t[n].originalData=t[n].getData();for(var n=0;n<t.length;n++)t[n].seriesGroup=t,t[n].needsDrawMap=n===0&&!t[n].getHostGeoModel(),t[n].setData(i.cloneShallow()),t[n].mainSeries=t[0]})}function am(a){var e={};a.eachSeriesByType("map",function(t){var r=t.getMapType();if(!(t.getHostGeoModel()||e[r])){var i={};D(t.seriesGroup,function(o){var s=o.coordinateSystem,l=o.originalData;o.get("showLegendSymbol")&&a.getComponent("legend")&&l.each(l.mapDimension("value"),function(u,c){var h=l.getName(c),v=s.getRegion(h);if(!(!v||isNaN(u))){var f=i[h]||0,p=s.dataToPoint(v.getCenter());i[h]=f+1,l.setItemLayout(c,{point:p,offset:f})}})});var n=t.getData();n.each(function(o){var s=n.getName(o),l=n.getItemLayout(o)||{};l.showLabel=!i[s],n.setItemLayout(o,l)}),e[r]=!0}})}var Bl=Gn,ga=function(a){E(e,a);function e(t){var r=a.call(this)||this;return r.type="view",r.dimensions=["x","y"],r._roamTransformable=new ya,r._rawTransformable=new ya,r.name=t,r}return e.prototype.setBoundingRect=function(t,r,i,n){return this._rect=new Pt(t,r,i,n),this._rect},e.prototype.getBoundingRect=function(){return this._rect},e.prototype.setViewRect=function(t,r,i,n){this._transformTo(t,r,i,n),this._viewRect=new Pt(t,r,i,n)},e.prototype._transformTo=function(t,r,i,n){var o=this.getBoundingRect(),s=this._rawTransformable;s.transform=o.calculateTransform(new Pt(t,r,i,n));var l=s.parent;s.parent=null,s.decomposeTransform(),s.parent=l,this._updateTransform()},e.prototype.setCenter=function(t,r){t&&(this._center=[O(t[0],r.getWidth()),O(t[1],r.getHeight())],this._updateCenterAndZoom())},e.prototype.setZoom=function(t){t=t||1;var r=this.zoomLimit;r&&(r.max!=null&&(t=Math.min(r.max,t)),r.min!=null&&(t=Math.max(r.min,t))),this._zoom=t,this._updateCenterAndZoom()},e.prototype.getDefaultCenter=function(){var t=this.getBoundingRect(),r=t.x+t.width/2,i=t.y+t.height/2;return[r,i]},e.prototype.getCenter=function(){return this._center||this.getDefaultCenter()},e.prototype.getZoom=function(){return this._zoom||1},e.prototype.getRoamTransform=function(){return this._roamTransformable.getLocalTransform()},e.prototype._updateCenterAndZoom=function(){var t=this._rawTransformable.getLocalTransform(),r=this._roamTransformable,i=this.getDefaultCenter(),n=this.getCenter(),o=this.getZoom();n=Gn([],n,t),i=Gn([],i,t),r.originX=n[0],r.originY=n[1],r.x=i[0]-n[0],r.y=i[1]-n[1],r.scaleX=r.scaleY=o,this._updateTransform()},e.prototype._updateTransform=function(){var t=this._roamTransformable,r=this._rawTransformable;r.parent=t,t.updateTransform(),r.updateTransform(),jp(this.transform||(this.transform=[]),r.transform||wr()),this._rawTransform=r.getLocalTransform(),this.invTransform=this.invTransform||[],Jp(this.invTransform,this.transform),this.decomposeTransform()},e.prototype.getTransformInfo=function(){var t=this._rawTransformable,r=this._roamTransformable,i=new ya;return i.transform=r.transform,i.decomposeTransform(),{roam:{x:i.x,y:i.y,scaleX:i.scaleX,scaleY:i.scaleY},raw:{x:t.x,y:t.y,scaleX:t.scaleX,scaleY:t.scaleY}}},e.prototype.getViewRect=function(){return this._viewRect},e.prototype.getViewRectAfterRoam=function(){var t=this.getBoundingRect().clone();return t.applyTransform(this.transform),t},e.prototype.dataToPoint=function(t,r,i){var n=r?this._rawTransform:this.transform;return i=i||[],n?Bl(i,t,n):Vt(i,t)},e.prototype.pointToData=function(t){var r=this.invTransform;return r?Bl([],t,r):[t[0],t[1]]},e.prototype.convertToPixel=function(t,r,i){var n=Gl(r);return n===this?n.dataToPoint(i):null},e.prototype.convertFromPixel=function(t,r,i){var n=Gl(r);return n===this?n.pointToData(i):null},e.prototype.containPoint=function(t){return this.getViewRectAfterRoam().contain(t[0],t[1])},e.dimensions=["x","y"],e}(ya);function Gl(a){var e=a.seriesModel;return e?e.coordinateSystem:null}var im={geoJSON:{aspectScale:.75,invertLongitute:!0},geoSVG:{aspectScale:1,invertLongitute:!1}},wv=["lng","lat"],Jn=function(a){E(e,a);function e(t,r,i){var n=a.call(this,t)||this;n.dimensions=wv,n.type="geo",n._nameCoordMap=et(),n.map=r;var o=i.projection,s=_e.load(r,i.nameMap,i.nameProperty),l=_e.getGeoResource(r);n.resourceType=l?l.type:null;var u=n.regions=s.regions,c=im[l.type];n._regionsMap=s.regionsMap,n.regions=s.regions,n.projection=o;var h;if(o)for(var v=0;v<u.length;v++){var f=u[v].getBoundingRect(o);h=h||f.clone(),h.union(f)}else h=s.boundingRect;return n.setBoundingRect(h.x,h.y,h.width,h.height),n.aspectScale=o?1:Mt(i.aspectScale,c.aspectScale),n._invertLongitute=o?!1:c.invertLongitute,n}return e.prototype._transformTo=function(t,r,i,n){var o=this.getBoundingRect(),s=this._invertLongitute;o=o.clone(),s&&(o.y=-o.y-o.height);var l=this._rawTransformable;l.transform=o.calculateTransform(new Pt(t,r,i,n));var u=l.parent;l.parent=null,l.decomposeTransform(),l.parent=u,s&&(l.scaleY=-l.scaleY),this._updateTransform()},e.prototype.getRegion=function(t){return this._regionsMap.get(t)},e.prototype.getRegionByCoord=function(t){for(var r=this.regions,i=0;i<r.length;i++){var n=r[i];if(n.type==="geoJSON"&&n.contain(t))return r[i]}},e.prototype.addGeoCoord=function(t,r){this._nameCoordMap.set(t,r)},e.prototype.getGeoCoord=function(t){var r=this._regionsMap.get(t);return this._nameCoordMap.get(t)||r&&r.getCenter()},e.prototype.dataToPoint=function(t,r,i){if(tt(t)&&(t=this.getGeoCoord(t)),t){var n=this.projection;return n&&(t=n.project(t)),t&&this.projectedToPoint(t,r,i)}},e.prototype.pointToData=function(t){var r=this.projection;return r&&(t=r.unproject(t)),t&&this.pointToProjected(t)},e.prototype.pointToProjected=function(t){return a.prototype.pointToData.call(this,t)},e.prototype.projectedToPoint=function(t,r,i){return a.prototype.dataToPoint.call(this,t,r,i)},e.prototype.convertToPixel=function(t,r,i){var n=Fl(r);return n===this?n.dataToPoint(i):null},e.prototype.convertFromPixel=function(t,r,i){var n=Fl(r);return n===this?n.pointToData(i):null},e}(ga);ue(Jn,ga);function Fl(a){var e=a.geoModel,t=a.seriesModel;return e?e.coordinateSystem:t?t.coordinateSystem||(t.getReferringComponents("geo",fe).models[0]||{}).coordinateSystem:null}function Hl(a,e){var t=a.get("boundingCoords");if(t!=null){var r=t[0],i=t[1];if(isFinite(r[0])&&isFinite(r[1])&&isFinite(i[0])&&isFinite(i[1])){var n=this.projection;if(n){var o=r[0],s=r[1],l=i[0],u=i[1];r=[1/0,1/0],i=[-1/0,-1/0];var c=function(_,w,A,C){for(var T=A-_,I=C-w,L=0;L<=100;L++){var M=L/100,R=n.project([_+T*M,w+I*M]);Qp(r,r,R),td(i,i,R)}};c(o,s,l,s),c(l,s,l,u),c(l,u,o,u),c(o,u,l,s)}this.setBoundingRect(r[0],r[1],i[0]-r[0],i[1]-r[1])}}var h=this.getBoundingRect(),v=a.get("layoutCenter"),f=a.get("layoutSize"),p=e.getWidth(),d=e.getHeight(),g=h.width/h.height*this.aspectScale,y=!1,m,S;v&&f&&(m=[O(v[0],p),O(v[1],d)],S=O(f,Math.min(p,d)),!isNaN(m[0])&&!isNaN(m[1])&&!isNaN(S)&&(y=!0));var x;if(y)x={},g>1?(x.width=S,x.height=S/g):(x.height=S,x.width=S*g),x.y=m[1]-x.height/2,x.x=m[0]-x.width/2;else{var b=a.getBoxLayoutParams();b.aspect=g,x=Rt(b,{width:p,height:d})}this.setViewRect(x.x,x.y,x.width,x.height),this.setCenter(a.get("center"),e),this.setZoom(a.get("zoom"))}function nm(a,e){D(e.get("geoCoord"),function(t,r){a.addGeoCoord(r,t)})}var om=function(){function a(){this.dimensions=wv}return a.prototype.create=function(e,t){var r=[];function i(o){return{nameProperty:o.get("nameProperty"),aspectScale:o.get("aspectScale"),projection:o.get("projection")}}e.eachComponent("geo",function(o,s){var l=o.get("map"),u=new Jn(l+s,l,F({nameMap:o.get("nameMap")},i(o)));u.zoomLimit=o.get("scaleLimit"),r.push(u),o.coordinateSystem=u,u.model=o,u.resize=Hl,u.resize(o,t)}),e.eachSeries(function(o){var s=o.get("coordinateSystem");if(s==="geo"){var l=o.get("geoIndex")||0;o.coordinateSystem=r[l]}});var n={};return e.eachSeriesByType("map",function(o){if(!o.getHostGeoModel()){var s=o.getMapType();n[s]=n[s]||[],n[s].push(o)}}),D(n,function(o,s){var l=G(o,function(c){return c.get("nameMap")}),u=new Jn(s,s,F({nameMap:Po(l)},i(o[0])));u.zoomLimit=qt.apply(null,G(o,function(c){return c.get("scaleLimit")})),r.push(u),u.resize=Hl,u.resize(o[0],t),D(o,function(c){c.coordinateSystem=u,nm(u,c)})}),r},a.prototype.getFilledRegions=function(e,t,r,i){for(var n=(e||[]).slice(),o=et(),s=0;s<n.length;s++)o.set(n[s].name,n[s]);var l=_e.load(t,r,i);return D(l.regions,function(u){var c=u.name,h=o.get(c),v=u.properties&&u.properties.echartsStyle;h||(h={name:c},n.push(h)),v&&ft(h,v)}),n},a}(),Av=new om,sm=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r,i){var n=_e.getGeoResource(t.map);if(n&&n.type==="geoJSON"){var o=t.itemStyle=t.itemStyle||{};"color"in o||(o.color="#eee")}this.mergeDefaultAndTheme(t,i),oa(t,"label",["show"])},e.prototype.optionUpdated=function(){var t=this,r=this.option;r.regions=Av.getFilledRegions(r.regions,r.map,r.nameMap,r.nameProperty);var i={};this._optionModelMap=Dh(r.regions||[],function(n,o){var s=o.name;return s&&(n.set(s,new Ut(o,t,t.ecModel)),o.selected&&(i[s]=!0)),n},et()),r.selectedMap||(r.selectedMap=i)},e.prototype.getRegionModel=function(t){return this._optionModelMap.get(t)||new Ut(null,this,this.ecModel)},e.prototype.getFormattedLabel=function(t,r){var i=this.getRegionModel(t),n=r==="normal"?i.get(["label","formatter"]):i.get(["emphasis","label","formatter"]),o={name:t};if(st(n))return o.status=r,n(o);if(tt(n))return n.replace("{a}",t??"")},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.select=function(t){var r=this.option,i=r.selectedMode;if(i){i!=="multiple"&&(r.selectedMap=null);var n=r.selectedMap||(r.selectedMap={});n[t]=!0}},e.prototype.unSelect=function(t){var r=this.option.selectedMap;r&&(r[t]=!1)},e.prototype.toggleSelected=function(t){this[this.isSelected(t)?"unSelect":"select"](t)},e.prototype.isSelected=function(t){var r=this.option.selectedMap;return!!(r&&r[t])},e.type="geo",e.layoutMode="box",e.defaultOption={z:0,show:!0,left:"center",top:"center",aspectScale:null,silent:!1,map:"",boundingCoords:null,center:null,zoom:1,scaleLimit:null,label:{show:!1,color:"#000"},itemStyle:{borderWidth:.5,borderColor:"#444"},emphasis:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{color:"rgba(255,215,0,0.8)"}},select:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{color:"rgba(255,215,0,0.8)"}},regions:[]},e}(kt);function Wl(a,e){return a.pointToProjected?a.pointToProjected(e):a.pointToData(e)}function Jo(a,e,t,r){var i=a.getZoom(),n=a.getCenter(),o=e.zoom,s=a.projectedToPoint?a.projectedToPoint(n):a.dataToPoint(n);if(e.dx!=null&&e.dy!=null&&(s[0]-=e.dx,s[1]-=e.dy,a.setCenter(Wl(a,s),r)),o!=null){if(t){var l=t.min||0,u=t.max||1/0;o=Math.max(Math.min(i*o,u),l)/i}a.scaleX*=o,a.scaleY*=o;var c=(e.originX-a.x)*(o-1),h=(e.originY-a.y)*(o-1);a.x-=c,a.y-=h,a.updateTransform(),a.setCenter(Wl(a,s),r),a.setZoom(o*i)}return{center:a.getCenter(),zoom:a.getZoom()}}var lm=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.focusBlurEnabled=!0,t}return e.prototype.init=function(t,r){this._api=r},e.prototype.render=function(t,r,i,n){if(this._model=t,!t.get("show")){this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null;return}this._mapDraw||(this._mapDraw=new _v(i));var o=this._mapDraw;o.draw(t,r,i,this,n),o.group.on("click",this._handleRegionClick,this),o.group.silent=t.get("silent"),this.group.add(o.group),this.updateSelectStatus(t,r,i)},e.prototype._handleRegionClick=function(t){var r;Ch(t.target,function(i){return(r=ht(i).eventData)!=null},!0),r&&this._api.dispatchAction({type:"geoToggleSelect",geoId:this._model.id,name:r.name})},e.prototype.updateSelectStatus=function(t,r,i){var n=this;this._mapDraw.group.traverse(function(o){var s=ht(o).eventData;if(s)return n._model.isSelected(s.name)?i.enterSelect(o):i.leaveSelect(o),!0})},e.prototype.findHighDownDispatchers=function(t){return this._mapDraw&&this._mapDraw.findHighDownDispatchers(t,this._model)},e.prototype.dispose=function(){this._mapDraw&&this._mapDraw.remove()},e.type="geo",e}(Ot);function um(a,e,t){_e.registerMap(a,e,t)}function Tv(a){a.registerCoordinateSystem("geo",Av),a.registerComponentModel(sm),a.registerComponentView(lm),a.registerImpl("registerMap",um),a.registerImpl("getMap",function(t){return _e.getMapForUser(t)});function e(t,r){r.update="geo:updateSelectStatus",a.registerAction(r,function(i,n){var o={},s=[];return n.eachComponent({mainType:"geo",query:i},function(l){l[t](i.name);var u=l.coordinateSystem;D(u.regions,function(h){o[h.name]=l.isSelected(h.name)||!1});var c=[];D(o,function(h,v){o[v]&&c.push(v)}),s.push({geoIndex:l.componentIndex,name:c})}),{selected:o,allSelected:s,name:i.name}})}e("toggleSelected",{type:"geoToggleSelect",event:"geoselectchanged"}),e("select",{type:"geoSelect",event:"geoselected"}),e("unSelect",{type:"geoUnSelect",event:"geounselected"}),a.registerAction({type:"geoRoam",event:"geoRoam",update:"updateTransform"},function(t,r,i){var n=t.componentType||"series";r.eachComponent({mainType:n,query:t},function(o){var s=o.coordinateSystem;if(s.type==="geo"){var l=Jo(s,t,o.get("scaleLimit"),i);o.setCenter&&o.setCenter(l.center),o.setZoom&&o.setZoom(l.zoom),n==="series"&&D(o.seriesGroup,function(u){u.setCenter(l.center),u.setZoom(l.zoom)})}})})}function cm(a){K(Tv),a.registerChartView(Qy),a.registerSeriesModel(tm),a.registerLayout(am),a.registerProcessor(a.PRIORITY.PROCESSOR.STATISTIC,rm),xh("map",a.registerAction)}function hm(a){var e=a;e.hierNode={defaultAncestor:null,ancestor:e,prelim:0,modifier:0,change:0,shift:0,i:0,thread:null};for(var t=[e],r,i;r=t.pop();)if(i=r.children,r.isExpand&&i.length)for(var n=i.length,o=n-1;o>=0;o--){var s=i[o];s.hierNode={defaultAncestor:null,ancestor:s,prelim:0,modifier:0,change:0,shift:0,i:o,thread:null},t.push(s)}}function vm(a,e){var t=a.isExpand?a.children:[],r=a.parentNode.children,i=a.hierNode.i?r[a.hierNode.i-1]:null;if(t.length){dm(a);var n=(t[0].hierNode.prelim+t[t.length-1].hierNode.prelim)/2;i?(a.hierNode.prelim=i.hierNode.prelim+e(a,i),a.hierNode.modifier=a.hierNode.prelim-n):a.hierNode.prelim=n}else i&&(a.hierNode.prelim=i.hierNode.prelim+e(a,i));a.parentNode.hierNode.defaultAncestor=gm(a,i,a.parentNode.hierNode.defaultAncestor||r[0],e)}function fm(a){var e=a.hierNode.prelim+a.parentNode.hierNode.modifier;a.setLayout({x:e},!0),a.hierNode.modifier+=a.parentNode.hierNode.modifier}function Zl(a){return arguments.length?a:Sm}function Br(a,e){return a-=Math.PI/2,{x:e*Math.cos(a),y:e*Math.sin(a)}}function pm(a,e){return Rt(a.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function dm(a){for(var e=a.children,t=e.length,r=0,i=0;--t>=0;){var n=e[t];n.hierNode.prelim+=r,n.hierNode.modifier+=r,i+=n.hierNode.change,r+=n.hierNode.shift+i}}function gm(a,e,t,r){if(e){for(var i=a,n=a,o=n.parentNode.children[0],s=e,l=i.hierNode.modifier,u=n.hierNode.modifier,c=o.hierNode.modifier,h=s.hierNode.modifier;s=Yi(s),n=$i(n),s&&n;){i=Yi(i),o=$i(o),i.hierNode.ancestor=a;var v=s.hierNode.prelim+h-n.hierNode.prelim-u+r(s,n);v>0&&(mm(ym(s,a,t),a,v),u+=v,l+=v),h+=s.hierNode.modifier,u+=n.hierNode.modifier,l+=i.hierNode.modifier,c+=o.hierNode.modifier}s&&!Yi(i)&&(i.hierNode.thread=s,i.hierNode.modifier+=h-l),n&&!$i(o)&&(o.hierNode.thread=n,o.hierNode.modifier+=u-c,t=a)}return t}function Yi(a){var e=a.children;return e.length&&a.isExpand?e[e.length-1]:a.hierNode.thread}function $i(a){var e=a.children;return e.length&&a.isExpand?e[0]:a.hierNode.thread}function ym(a,e,t){return a.hierNode.ancestor.parentNode===e.parentNode?a.hierNode.ancestor:t}function mm(a,e,t){var r=t/(e.hierNode.i-a.hierNode.i);e.hierNode.change-=r,e.hierNode.shift+=t,e.hierNode.modifier+=t,e.hierNode.prelim+=t,a.hierNode.change+=r}function Sm(a,e){return a.parentNode===e.parentNode?1:2}var xm=function(){function a(){this.parentPoint=[],this.childPoints=[]}return a}(),bm=function(a){E(e,a);function e(t){return a.call(this,t)||this}return e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new xm},e.prototype.buildPath=function(t,r){var i=r.childPoints,n=i.length,o=r.parentPoint,s=i[0],l=i[n-1];if(n===1){t.moveTo(o[0],o[1]),t.lineTo(s[0],s[1]);return}var u=r.orient,c=u==="TB"||u==="BT"?0:1,h=1-c,v=O(r.forkPosition,1),f=[];f[c]=o[c],f[h]=o[h]+(l[h]-o[h])*v,t.moveTo(o[0],o[1]),t.lineTo(f[0],f[1]),t.moveTo(s[0],s[1]),f[c]=s[c],t.lineTo(f[0],f[1]),f[c]=l[c],t.lineTo(f[0],f[1]),t.lineTo(l[0],l[1]);for(var p=1;p<n-1;p++){var d=i[p];t.moveTo(d[0],d[1]),f[c]=d[c],t.lineTo(f[0],f[1])}},e}(ie),_m=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._mainGroup=new X,t}return e.prototype.init=function(t,r){this._controller=new da(r.getZr()),this._controllerHost={target:this.group},this.group.add(this._mainGroup)},e.prototype.render=function(t,r,i){var n=t.getData(),o=t.layoutInfo,s=this._mainGroup,l=t.get("layout");l==="radial"?(s.x=o.x+o.width/2,s.y=o.y+o.height/2):(s.x=o.x,s.y=o.y),this._updateViewCoordSys(t,i),this._updateController(t,r,i);var u=this._data;n.diff(u).add(function(c){Ul(n,c)&&Yl(n,c,null,s,t)}).update(function(c,h){var v=u.getItemGraphicEl(h);if(!Ul(n,c)){v&&Xl(u,h,v,s,t);return}Yl(n,c,v,s,t)}).remove(function(c){var h=u.getItemGraphicEl(c);h&&Xl(u,c,h,s,t)}).execute(),this._nodeScaleRatio=t.get("nodeScaleRatio"),this._updateNodeAndLinkScale(t),t.get("expandAndCollapse")===!0&&n.eachItemGraphicEl(function(c,h){c.off("click").on("click",function(){i.dispatchAction({type:"treeExpandAndCollapse",seriesId:t.id,dataIndex:h})})}),this._data=n},e.prototype._updateViewCoordSys=function(t,r){var i=t.getData(),n=[];i.each(function(h){var v=i.getItemLayout(h);v&&!isNaN(v.x)&&!isNaN(v.y)&&n.push([+v.x,+v.y])});var o=[],s=[];Ih(n,o,s);var l=this._min,u=this._max;s[0]-o[0]===0&&(o[0]=l?l[0]:o[0]-1,s[0]=u?u[0]:s[0]+1),s[1]-o[1]===0&&(o[1]=l?l[1]:o[1]-1,s[1]=u?u[1]:s[1]+1);var c=t.coordinateSystem=new ga;c.zoomLimit=t.get("scaleLimit"),c.setBoundingRect(o[0],o[1],s[0]-o[0],s[1]-o[1]),c.setCenter(t.get("center"),r),c.setZoom(t.get("zoom")),this.group.attr({x:c.x,y:c.y,scaleX:c.scaleX,scaleY:c.scaleY}),this._min=o,this._max=s},e.prototype._updateController=function(t,r,i){var n=this,o=this._controller,s=this._controllerHost,l=this.group;o.setPointerChecker(function(u,c,h){var v=l.getBoundingRect();return v.applyTransform(l.transform),v.contain(c,h)&&!Ii(u,i,t)}),o.enable(t.get("roam")),s.zoomLimit=t.get("scaleLimit"),s.zoom=t.coordinateSystem.getZoom(),o.off("pan").off("zoom").on("pan",function(u){Ko(s,u.dx,u.dy),i.dispatchAction({seriesId:t.id,type:"treeRoam",dx:u.dx,dy:u.dy})}).on("zoom",function(u){qo(s,u.scale,u.originX,u.originY),i.dispatchAction({seriesId:t.id,type:"treeRoam",zoom:u.scale,originX:u.originX,originY:u.originY}),n._updateNodeAndLinkScale(t),i.updateLabelLayout()})},e.prototype._updateNodeAndLinkScale=function(t){var r=t.getData(),i=this._getNodeGlobalScale(t);r.eachItemGraphicEl(function(n,o){n.setSymbolScale(i)})},e.prototype._getNodeGlobalScale=function(t){var r=t.coordinateSystem;if(r.type!=="view")return 1;var i=this._nodeScaleRatio,n=r.scaleX||1,o=r.getZoom(),s=(o-1)*i+1;return s/n},e.prototype.dispose=function(){this._controller&&this._controller.dispose(),this._controllerHost=null},e.prototype.remove=function(){this._mainGroup.removeAll(),this._data=null},e.type="tree",e}(bt);function Ul(a,e){var t=a.getItemLayout(e);return t&&!isNaN(t.x)&&!isNaN(t.y)}function Yl(a,e,t,r,i){var n=!t,o=a.tree.getNodeByDataIndex(e),s=o.getModel(),l=o.getVisual("style").fill,u=o.isExpand===!1&&o.children.length!==0?l:"#fff",c=a.tree.root,h=o.parentNode===c?o:o.parentNode||o,v=a.getItemGraphicEl(h.dataIndex),f=h.getLayout(),p=v?{x:v.__oldX,y:v.__oldY,rawX:v.__radialOldRawX,rawY:v.__radialOldRawY}:f,d=o.getLayout();n?(t=new Lh(a,e,null,{symbolInnerColor:u,useNameLabel:!0}),t.x=p.x,t.y=p.y):t.updateData(a,e,null,{symbolInnerColor:u,useNameLabel:!0}),t.__radialOldRawX=t.__radialRawX,t.__radialOldRawY=t.__radialRawY,t.__radialRawX=d.rawX,t.__radialRawY=d.rawY,r.add(t),a.setItemGraphicEl(e,t),t.__oldX=t.x,t.__oldY=t.y,dt(t,{x:d.x,y:d.y},i);var g=t.getSymbolPath();if(i.get("layout")==="radial"){var y=c.children[0],m=y.getLayout(),S=y.children.length,x=void 0,b=void 0;if(d.x===m.x&&o.isExpand===!0&&y.children.length){var _={x:(y.children[0].getLayout().x+y.children[S-1].getLayout().x)/2,y:(y.children[0].getLayout().y+y.children[S-1].getLayout().y)/2};x=Math.atan2(_.y-m.y,_.x-m.x),x<0&&(x=Math.PI*2+x),b=_.x<m.x,b&&(x=x-Math.PI)}else x=Math.atan2(d.y-m.y,d.x-m.x),x<0&&(x=Math.PI*2+x),o.children.length===0||o.children.length!==0&&o.isExpand===!1?(b=d.x<m.x,b&&(x=x-Math.PI)):(b=d.x>m.x,b||(x=x-Math.PI));var w=b?"left":"right",A=s.getModel("label"),C=A.get("rotate"),T=C*(Math.PI/180),I=g.getTextContent();I&&(g.setTextConfig({position:A.get("position")||w,rotation:C==null?-x:T,origin:"center"}),I.setStyle("verticalAlign","middle"))}var L=s.get(["emphasis","focus"]),M=L==="relative"?Ua(o.getAncestorsIndices(),o.getDescendantIndices()):L==="ancestor"?o.getAncestorsIndices():L==="descendant"?o.getDescendantIndices():null;M&&(ht(t).focus=M),wm(i,o,c,t,p,f,d,r),t.__edge&&(t.onHoverStateChange=function(R){if(R!=="blur"){var P=o.parentNode&&a.getItemGraphicEl(o.parentNode.dataIndex);P&&P.hoverState===ed||Th(t.__edge,R)}})}function wm(a,e,t,r,i,n,o,s){var l=e.getModel(),u=a.get("edgeShape"),c=a.get("layout"),h=a.getOrient(),v=a.get(["lineStyle","curveness"]),f=a.get("edgeForkPosition"),p=l.getModel("lineStyle").getLineStyle(),d=r.__edge;if(u==="curve")e.parentNode&&e.parentNode!==t&&(d||(d=r.__edge=new Mh({shape:Qn(c,h,v,i,i)})),dt(d,{shape:Qn(c,h,v,n,o)},a));else if(u==="polyline"&&c==="orthogonal"&&e!==t&&e.children&&e.children.length!==0&&e.isExpand===!0){for(var g=e.children,y=[],m=0;m<g.length;m++){var S=g[m].getLayout();y.push([S.x,S.y])}d||(d=r.__edge=new bm({shape:{parentPoint:[o.x,o.y],childPoints:[[o.x,o.y]],orient:h,forkPosition:f}})),dt(d,{shape:{parentPoint:[o.x,o.y],childPoints:y}},a)}d&&!(u==="polyline"&&!e.isExpand)&&(d.useStyle(ot({strokeNoScale:!0,fill:null},p)),Ht(d,l,"lineStyle"),gr(d),s.add(d))}function $l(a,e,t,r,i){var n=e.tree.root,o=Dv(n,a),s=o.source,l=o.sourceLayout,u=e.getItemGraphicEl(a.dataIndex);if(u){var c=e.getItemGraphicEl(s.dataIndex),h=c.__edge,v=u.__edge||(s.isExpand===!1||s.children.length===1?h:void 0),f=r.get("edgeShape"),p=r.get("layout"),d=r.get("orient"),g=r.get(["lineStyle","curveness"]);v&&(f==="curve"?Ya(v,{shape:Qn(p,d,g,l,l),style:{opacity:0}},r,{cb:function(){t.remove(v)},removeOpt:i}):f==="polyline"&&r.get("layout")==="orthogonal"&&Ya(v,{shape:{parentPoint:[l.x,l.y],childPoints:[[l.x,l.y]]},style:{opacity:0}},r,{cb:function(){t.remove(v)},removeOpt:i}))}}function Dv(a,e){for(var t=e.parentNode===a?e:e.parentNode||e,r;r=t.getLayout(),r==null;)t=t.parentNode===a?t:t.parentNode||t;return{source:t,sourceLayout:r}}function Xl(a,e,t,r,i){var n=a.tree.getNodeByDataIndex(e),o=a.tree.root,s=Dv(o,n).sourceLayout,l={duration:i.get("animationDurationUpdate"),easing:i.get("animationEasingUpdate")};Ya(t,{x:s.x+1,y:s.y+1},i,{cb:function(){r.remove(t),a.setItemGraphicEl(e,null)},removeOpt:l}),t.fadeOut(null,a.hostModel,{fadeLabel:!0,animation:l}),n.children.forEach(function(u){$l(u,a,r,i,l)}),$l(n,a,r,i,l)}function Qn(a,e,t,r,i){var n,o,s,l,u,c,h,v;if(a==="radial"){u=r.rawX,h=r.rawY,c=i.rawX,v=i.rawY;var f=Br(u,h),p=Br(u,h+(v-h)*t),d=Br(c,v+(h-v)*t),g=Br(c,v);return{x1:f.x||0,y1:f.y||0,x2:g.x||0,y2:g.y||0,cpx1:p.x||0,cpy1:p.y||0,cpx2:d.x||0,cpy2:d.y||0}}else u=r.x,h=r.y,c=i.x,v=i.y,(e==="LR"||e==="RL")&&(n=u+(c-u)*t,o=h,s=c+(u-c)*t,l=v),(e==="TB"||e==="BT")&&(n=u,o=h+(v-h)*t,s=c,l=v+(h-v)*t);return{x1:u,y1:h,x2:c,y2:v,cpx1:n,cpy1:o,cpx2:s,cpy2:l}}var ae=Dt();function Cv(a){var e=a.mainData,t=a.datas;t||(t={main:e},a.datasAttr={main:"data"}),a.datas=a.mainData=null,Iv(e,t,a),D(t,function(r){D(e.TRANSFERABLE_METHODS,function(i){r.wrapMethod(i,q(Am,a))})}),e.wrapMethod("cloneShallow",q(Dm,a)),D(e.CHANGABLE_METHODS,function(r){e.wrapMethod(r,q(Tm,a))}),Yr(t[e.dataType]===e)}function Am(a,e){if(Lm(this)){var t=F({},ae(this).datas);t[this.dataType]=e,Iv(e,t,a)}else Qo(e,this.dataType,ae(this).mainData,a);return e}function Tm(a,e){return a.struct&&a.struct.update(),e}function Dm(a,e){return D(ae(e).datas,function(t,r){t!==e&&Qo(t.cloneShallow(),r,e,a)}),e}function Cm(a){var e=ae(this).mainData;return a==null||e==null?e:ae(e).datas[a]}function Im(){var a=ae(this).mainData;return a==null?[{data:a}]:G(wt(ae(a).datas),function(e){return{type:e,data:ae(a).datas[e]}})}function Lm(a){return ae(a).mainData===a}function Iv(a,e,t){ae(a).datas={},D(e,function(r,i){Qo(r,i,a,t)})}function Qo(a,e,t,r){ae(t).datas[e]=a,ae(a).mainData=t,a.dataType=e,r.struct&&(a[r.structAttr]=r.struct,r.struct[r.datasAttr[e]]=a),a.getLinkedData=Cm,a.getLinkedDataAll=Im}var Mm=function(){function a(e,t){this.depth=0,this.height=0,this.dataIndex=-1,this.children=[],this.viewChildren=[],this.isExpand=!1,this.name=e||"",this.hostTree=t}return a.prototype.isRemoved=function(){return this.dataIndex<0},a.prototype.eachNode=function(e,t,r){st(e)&&(r=t,t=e,e=null),e=e||{},tt(e)&&(e={order:e});var i=e.order||"preorder",n=this[e.attr||"children"],o;i==="preorder"&&(o=t.call(r,this));for(var s=0;!o&&s<n.length;s++)n[s].eachNode(e,t,r);i==="postorder"&&t.call(r,this)},a.prototype.updateDepthAndHeight=function(e){var t=0;this.depth=e;for(var r=0;r<this.children.length;r++){var i=this.children[r];i.updateDepthAndHeight(e+1),i.height>t&&(t=i.height)}this.height=t+1},a.prototype.getNodeById=function(e){if(this.getId()===e)return this;for(var t=0,r=this.children,i=r.length;t<i;t++){var n=r[t].getNodeById(e);if(n)return n}},a.prototype.contains=function(e){if(e===this)return!0;for(var t=0,r=this.children,i=r.length;t<i;t++){var n=r[t].contains(e);if(n)return n}},a.prototype.getAncestors=function(e){for(var t=[],r=e?this:this.parentNode;r;)t.push(r),r=r.parentNode;return t.reverse(),t},a.prototype.getAncestorsIndices=function(){for(var e=[],t=this;t;)e.push(t.dataIndex),t=t.parentNode;return e.reverse(),e},a.prototype.getDescendantIndices=function(){var e=[];return this.eachNode(function(t){e.push(t.dataIndex)}),e},a.prototype.getValue=function(e){var t=this.hostTree.data;return t.getStore().get(t.getDimensionIndex(e||"value"),this.dataIndex)},a.prototype.setLayout=function(e,t){this.dataIndex>=0&&this.hostTree.data.setItemLayout(this.dataIndex,e,t)},a.prototype.getLayout=function(){return this.hostTree.data.getItemLayout(this.dataIndex)},a.prototype.getModel=function(e){if(!(this.dataIndex<0)){var t=this.hostTree,r=t.data.getItemModel(this.dataIndex);return r.getModel(e)}},a.prototype.getLevelModel=function(){return(this.hostTree.levelModels||[])[this.depth]},a.prototype.setVisual=function(e,t){this.dataIndex>=0&&this.hostTree.data.setItemVisual(this.dataIndex,e,t)},a.prototype.getVisual=function(e){return this.hostTree.data.getItemVisual(this.dataIndex,e)},a.prototype.getRawIndex=function(){return this.hostTree.data.getRawIndex(this.dataIndex)},a.prototype.getId=function(){return this.hostTree.data.getId(this.dataIndex)},a.prototype.getChildIndex=function(){if(this.parentNode){for(var e=this.parentNode.children,t=0;t<e.length;++t)if(e[t]===this)return t;return-1}return-1},a.prototype.isAncestorOf=function(e){for(var t=e.parentNode;t;){if(t===this)return!0;t=t.parentNode}return!1},a.prototype.isDescendantOf=function(e){return e!==this&&e.isAncestorOf(this)},a}(),ts=function(){function a(e){this.type="tree",this._nodes=[],this.hostModel=e}return a.prototype.eachNode=function(e,t,r){this.root.eachNode(e,t,r)},a.prototype.getNodeByDataIndex=function(e){var t=this.data.getRawIndex(e);return this._nodes[t]},a.prototype.getNodeById=function(e){return this.root.getNodeById(e)},a.prototype.update=function(){for(var e=this.data,t=this._nodes,r=0,i=t.length;r<i;r++)t[r].dataIndex=-1;for(var r=0,i=e.count();r<i;r++)t[e.getRawIndex(r)].dataIndex=r},a.prototype.clearLayouts=function(){this.data.clearItemLayouts()},a.createTree=function(e,t,r){var i=new a(t),n=[],o=1;s(e);function s(c,h){var v=c.value;o=Math.max(o,U(v)?v.length:1),n.push(c);var f=new Mm(ye(c.name,""),i);h?Pm(f,h):i.root=f,i._nodes.push(f);var p=c.children;if(p)for(var d=0;d<p.length;d++)s(p[d],f)}i.root.updateDepthAndHeight(0);var l=fi(n,{coordDimensions:["value"],dimensionsCount:o}).dimensions,u=new Ft(l,t);return u.initData(n),r&&r(u),Cv({mainData:u,struct:i,structAttr:"tree"}),i.update(),i},a}();function Pm(a,e){var t=e.children;a.parentNode!==e&&(t.push(a),a.parentNode=e)}function Jr(a,e,t){if(a&&mt(e,a.type)>=0){var r=t.getData().tree.root,i=a.targetNode;if(tt(i)&&(i=r.getNodeById(i)),i&&r.contains(i))return{node:i};var n=a.targetNodeId;if(n!=null&&(i=r.getNodeById(n)))return{node:i}}}function Lv(a){for(var e=[];a;)a=a.parentNode,a&&e.push(a);return e.reverse()}function es(a,e){var t=Lv(a);return mt(t,e)>=0}function Mi(a,e){for(var t=[];a;){var r=a.dataIndex;t.push({name:a.name,dataIndex:r,value:e.getRawValue(r)}),a=a.parentNode}return t.reverse(),t}var Rm=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.hasSymbolVisual=!0,t.ignoreStyleOnData=!0,t}return e.prototype.getInitialData=function(t){var r={name:t.name,children:t.data},i=t.leaves||{},n=new Ut(i,this,this.ecModel),o=ts.createTree(r,this,s);function s(h){h.wrapMethod("getItemModel",function(v,f){var p=o.getNodeByDataIndex(f);return p&&p.children.length&&p.isExpand||(v.parentModel=n),v})}var l=0;o.eachNode("preorder",function(h){h.depth>l&&(l=h.depth)});var u=t.expandAndCollapse,c=u&&t.initialTreeDepth>=0?t.initialTreeDepth:l;return o.root.eachNode("preorder",function(h){var v=h.hostTree.data.getRawDataItem(h.dataIndex);h.isExpand=v&&v.collapsed!=null?!v.collapsed:h.depth<=c}),o.data},e.prototype.getOrient=function(){var t=this.get("orient");return t==="horizontal"?t="LR":t==="vertical"&&(t="TB"),t},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.formatTooltip=function(t,r,i){for(var n=this.getData().tree,o=n.root.children[0],s=n.getNodeByDataIndex(t),l=s.getValue(),u=s.name;s&&s!==o;)u=s.parentNode.name+"."+u,s=s.parentNode;return Zt("nameValue",{name:u,value:l,noValue:isNaN(l)||l==null})},e.prototype.getDataParams=function(t){var r=a.prototype.getDataParams.apply(this,arguments),i=this.getData().tree.getNodeByDataIndex(t);return r.treeAncestors=Mi(i,this),r.collapsed=!i.isExpand,r},e.type="series.tree",e.layoutMode="box",e.defaultOption={z:2,coordinateSystem:"view",left:"12%",top:"12%",right:"12%",bottom:"12%",layout:"orthogonal",edgeShape:"curve",edgeForkPosition:"50%",roam:!1,nodeScaleRatio:.4,center:null,zoom:1,orient:"LR",symbol:"emptyCircle",symbolSize:7,expandAndCollapse:!0,initialTreeDepth:2,lineStyle:{color:"#ccc",width:1.5,curveness:.5},itemStyle:{color:"lightsteelblue",borderWidth:1.5},label:{show:!0},animationEasing:"linear",animationDuration:700,animationDurationUpdate:500},e}(Ct);function Em(a,e,t){for(var r=[a],i=[],n;n=r.pop();)if(i.push(n),n.isExpand){var o=n.children;if(o.length)for(var s=0;s<o.length;s++)r.push(o[s])}for(;n=i.pop();)e(n,t)}function Lr(a,e){for(var t=[a],r;r=t.pop();)if(e(r),r.isExpand){var i=r.children;if(i.length)for(var n=i.length-1;n>=0;n--)t.push(i[n])}}function km(a,e){a.eachSeriesByType("tree",function(t){Vm(t,e)})}function Vm(a,e){var t=pm(a,e);a.layoutInfo=t;var r=a.get("layout"),i=0,n=0,o=null;r==="radial"?(i=2*Math.PI,n=Math.min(t.height,t.width)/2,o=Zl(function(S,x){return(S.parentNode===x.parentNode?1:2)/S.depth})):(i=t.width,n=t.height,o=Zl());var s=a.getData().tree.root,l=s.children[0];if(l){hm(s),Em(l,vm,o),s.hierNode.modifier=-l.hierNode.prelim,Lr(l,fm);var u=l,c=l,h=l;Lr(l,function(S){var x=S.getLayout().x;x<u.getLayout().x&&(u=S),x>c.getLayout().x&&(c=S),S.depth>h.depth&&(h=S)});var v=u===c?1:o(u,c)/2,f=v-u.getLayout().x,p=0,d=0,g=0,y=0;if(r==="radial")p=i/(c.getLayout().x+v+f),d=n/(h.depth-1||1),Lr(l,function(S){g=(S.getLayout().x+f)*p,y=(S.depth-1)*d;var x=Br(g,y);S.setLayout({x:x.x,y:x.y,rawX:g,rawY:y},!0)});else{var m=a.getOrient();m==="RL"||m==="LR"?(d=n/(c.getLayout().x+v+f),p=i/(h.depth-1||1),Lr(l,function(S){y=(S.getLayout().x+f)*d,g=m==="LR"?(S.depth-1)*p:i-(S.depth-1)*p,S.setLayout({x:g,y},!0)})):(m==="TB"||m==="BT")&&(p=i/(c.getLayout().x+v+f),d=n/(h.depth-1||1),Lr(l,function(S){g=(S.getLayout().x+f)*p,y=m==="TB"?(S.depth-1)*d:n-(S.depth-1)*d,S.setLayout({x:g,y},!0)}))}}}function Nm(a){a.eachSeriesByType("tree",function(e){var t=e.getData(),r=t.tree;r.eachNode(function(i){var n=i.getModel(),o=n.getModel("itemStyle").getItemStyle(),s=t.ensureUniqueItemVisual(i.dataIndex,"style");F(s,o)})})}function zm(a){a.registerAction({type:"treeExpandAndCollapse",event:"treeExpandAndCollapse",update:"update"},function(e,t){t.eachComponent({mainType:"series",subType:"tree",query:e},function(r){var i=e.dataIndex,n=r.getData().tree,o=n.getNodeByDataIndex(i);o.isExpand=!o.isExpand})}),a.registerAction({type:"treeRoam",event:"treeRoam",update:"none"},function(e,t,r){t.eachComponent({mainType:"series",subType:"tree",query:e},function(i){var n=i.coordinateSystem,o=Jo(n,e,void 0,r);i.setCenter&&i.setCenter(o.center),i.setZoom&&i.setZoom(o.zoom)})})}function Om(a){a.registerChartView(_m),a.registerSeriesModel(Rm),a.registerLayout(km),a.registerVisual(Nm),zm(a)}var Kl=["treemapZoomToNode","treemapRender","treemapMove"];function Bm(a){for(var e=0;e<Kl.length;e++)a.registerAction({type:Kl[e],update:"updateView"},Pe);a.registerAction({type:"treemapRootToNode",update:"updateView"},function(t,r){r.eachComponent({mainType:"series",subType:"treemap",query:t},i);function i(n,o){var s=["treemapZoomToNode","treemapRootToNode"],l=Jr(t,s,n);if(l){var u=n.getViewRoot();u&&(t.direction=es(u,l.node)?"rollUp":"drillDown"),n.resetViewRoot(l.node)}}})}function Mv(a){var e=a.getData(),t=e.tree,r={};t.eachNode(function(i){for(var n=i;n&&n.depth>1;)n=n.parentNode;var o=Fn(a.ecModel,n.name||n.dataIndex+"",r);i.setVisual("decal",o)})}var Gm=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.preventUsingHoverLayer=!0,t}return e.prototype.getInitialData=function(t,r){var i={name:t.name,children:t.data};Pv(i);var n=t.levels||[],o=this.designatedVisualItemStyle={},s=new Ut({itemStyle:o},this,r);n=t.levels=Fm(n,r);var l=G(n||[],function(h){return new Ut(h,s,r)},this),u=ts.createTree(i,this,c);function c(h){h.wrapMethod("getItemModel",function(v,f){var p=u.getNodeByDataIndex(f),d=p?l[p.depth]:null;return v.parentModel=d||s,v})}return u.data},e.prototype.optionUpdated=function(){this.resetViewRoot()},e.prototype.formatTooltip=function(t,r,i){var n=this.getData(),o=this.getRawValue(t),s=n.getName(t);return Zt("nameValue",{name:s,value:o})},e.prototype.getDataParams=function(t){var r=a.prototype.getDataParams.apply(this,arguments),i=this.getData().tree.getNodeByDataIndex(t);return r.treeAncestors=Mi(i,this),r.treePathInfo=r.treeAncestors,r},e.prototype.setLayoutInfo=function(t){this.layoutInfo=this.layoutInfo||{},F(this.layoutInfo,t)},e.prototype.mapIdToIndex=function(t){var r=this._idIndexMap;r||(r=this._idIndexMap=et(),this._idIndexMapCount=0);var i=r.get(t);return i==null&&r.set(t,i=this._idIndexMapCount++),i},e.prototype.getViewRoot=function(){return this._viewRoot},e.prototype.resetViewRoot=function(t){t?this._viewRoot=t:t=this._viewRoot;var r=this.getRawData().tree.root;(!t||t!==r&&!r.contains(t))&&(this._viewRoot=r)},e.prototype.enableAriaDecal=function(){Mv(this)},e.type="series.treemap",e.layoutMode="box",e.defaultOption={progressive:0,left:"center",top:"middle",width:"80%",height:"80%",sort:!0,clipWindow:"origin",squareRatio:.5*(1+Math.sqrt(5)),leafDepth:null,drillDownIcon:"▶",zoomToNodeRatio:.32*.32,scaleLimit:null,roam:!0,nodeClick:"zoomToNode",animation:!0,animationDurationUpdate:900,animationEasing:"quinticInOut",breadcrumb:{show:!0,height:22,left:"center",top:"bottom",emptyItemWidth:25,itemStyle:{color:"rgba(0,0,0,0.7)",textStyle:{color:"#fff"}},emphasis:{itemStyle:{color:"rgba(0,0,0,0.9)"}}},label:{show:!0,distance:0,padding:5,position:"inside",color:"#fff",overflow:"truncate"},upperLabel:{show:!1,position:[0,"50%"],height:20,overflow:"truncate",verticalAlign:"middle"},itemStyle:{color:null,colorAlpha:null,colorSaturation:null,borderWidth:0,gapWidth:0,borderColor:"#fff",borderColorSaturation:null},emphasis:{upperLabel:{show:!0,position:[0,"50%"],overflow:"truncate",verticalAlign:"middle"}},visualDimension:0,visualMin:null,visualMax:null,color:[],colorAlpha:null,colorSaturation:null,colorMappingBy:"index",visibleMin:10,childrenVisibleMin:null,levels:[]},e}(Ct);function Pv(a){var e=0;D(a.children,function(r){Pv(r);var i=r.value;U(i)&&(i=i[0]),e+=i});var t=a.value;U(t)&&(t=t[0]),(t==null||isNaN(t))&&(t=e),t<0&&(t=0),U(a.value)?a.value[0]=t:a.value=t}function Fm(a,e){var t=be(e.get("color")),r=be(e.get(["aria","decal","decals"]));if(t){a=a||[];var i,n;D(a,function(s){var l=new Ut(s),u=l.get("color"),c=l.get("decal");(l.get(["itemStyle","color"])||u&&u!=="none")&&(i=!0),(l.get(["itemStyle","decal"])||c&&c!=="none")&&(n=!0)});var o=a[0]||(a[0]={});return i||(o.color=t.slice()),!n&&r&&(o.decal=r.slice()),a}}var Hm=8,ql=8,Xi=5,Wm=function(){function a(e){this.group=new X,e.add(this.group)}return a.prototype.render=function(e,t,r,i){var n=e.getModel("breadcrumb"),o=this.group;if(o.removeAll(),!(!n.get("show")||!r)){var s=n.getModel("itemStyle"),l=n.getModel("emphasis"),u=s.getModel("textStyle"),c=l.getModel(["itemStyle","textStyle"]),h={pos:{left:n.get("left"),right:n.get("right"),top:n.get("top"),bottom:n.get("bottom")},box:{width:t.getWidth(),height:t.getHeight()},emptyItemWidth:n.get("emptyItemWidth"),totalWidth:0,renderList:[]};this._prepare(r,h,u),this._renderContent(e,h,s,l,u,c,i),bi(o,h.pos,h.box)}},a.prototype._prepare=function(e,t,r){for(var i=e;i;i=i.parentNode){var n=ye(i.getModel().get("name"),""),o=r.getTextRect(n),s=Math.max(o.width+Hm*2,t.emptyItemWidth);t.totalWidth+=s+ql,t.renderList.push({node:i,text:n,width:s})}},a.prototype._renderContent=function(e,t,r,i,n,o,s){for(var l=0,u=t.emptyItemWidth,c=e.get(["breadcrumb","height"]),h=rd(t.pos,t.box),v=t.totalWidth,f=t.renderList,p=i.getModel("itemStyle").getItemStyle(),d=f.length-1;d>=0;d--){var g=f[d],y=g.node,m=g.width,S=g.text;v>h.width&&(v-=m-u,m=u,S=null);var x=new se({shape:{points:Zm(l,0,m,c,d===f.length-1,d===0)},style:ot(r.getItemStyle(),{lineJoin:"bevel"}),textContent:new ut({style:yt(n,{text:S})}),textConfig:{position:"inside"},z2:ua*1e4,onclick:q(s,y)});x.disableLabelAnimation=!0,x.getTextContent().ensureState("emphasis").style=yt(o,{text:S}),x.ensureState("emphasis").style=p,xt(x,i.get("focus"),i.get("blurScope"),i.get("disabled")),this.group.add(x),Um(x,e,y),l+=m+ql}},a.prototype.remove=function(){this.group.removeAll()},a}();function Zm(a,e,t,r,i,n){var o=[[i?a:a-Xi,e],[a+t,e],[a+t,e+r],[i?a:a-Xi,e+r]];return!n&&o.splice(2,0,[a+t+Xi,e+r/2]),!i&&o.push([a,e+r/2]),o}function Um(a,e,t){ht(a).eventData={componentType:"series",componentSubType:"treemap",componentIndex:e.componentIndex,seriesIndex:e.seriesIndex,seriesName:e.name,seriesType:"treemap",selfType:"breadcrumb",nodeData:{dataIndex:t&&t.dataIndex,name:t&&t.name},treePathInfo:t&&Mi(t,e)}}var Ym=function(){function a(){this._storage=[],this._elExistsMap={}}return a.prototype.add=function(e,t,r,i,n){return this._elExistsMap[e.id]?!1:(this._elExistsMap[e.id]=!0,this._storage.push({el:e,target:t,duration:r,delay:i,easing:n}),!0)},a.prototype.finished=function(e){return this._finishedCallback=e,this},a.prototype.start=function(){for(var e=this,t=this._storage.length,r=function(){t--,t<=0&&(e._storage.length=0,e._elExistsMap={},e._finishedCallback&&e._finishedCallback())},i=0,n=this._storage.length;i<n;i++){var o=this._storage[i];o.el.animateTo(o.target,{duration:o.duration,delay:o.delay,easing:o.easing,setToFinal:!0,done:r,aborted:r})}return this},a}();function $m(){return new Ym}var to=X,jl=pt,Jl=3,Ql="label",tu="upperLabel",Xm=ua*10,Km=ua*2,qm=ua*3,Be=Ph([["fill","color"],["stroke","strokeColor"],["lineWidth","strokeWidth"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),eu=function(a){var e=Be(a);return e.stroke=e.fill=e.lineWidth=null,e},ti=Dt(),jm=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._state="ready",t._storage=Mr(),t}return e.prototype.render=function(t,r,i,n){var o=r.findComponents({mainType:"series",subType:"treemap",query:n});if(!(mt(o,t)<0)){this.seriesModel=t,this.api=i,this.ecModel=r;var s=["treemapZoomToNode","treemapRootToNode"],l=Jr(n,s,t),u=n&&n.type,c=t.layoutInfo,h=!this._oldTree,v=this._storage,f=u==="treemapRootToNode"&&l&&v?{rootNodeGroup:v.nodeGroup[l.node.getRawIndex()],direction:n.direction}:null,p=this._giveContainerGroup(c),d=t.get("animation"),g=this._doRender(p,t,f);d&&!h&&(!u||u==="treemapZoomToNode"||u==="treemapRootToNode")?this._doAnimation(p,g,t,f):g.renderFinally(),this._resetController(i),this._renderBreadcrumb(t,i,l)}},e.prototype._giveContainerGroup=function(t){var r=this._containerGroup;return r||(r=this._containerGroup=new to,this._initEvents(r),this.group.add(r)),r.x=t.x,r.y=t.y,r},e.prototype._doRender=function(t,r,i){var n=r.getData().tree,o=this._oldTree,s=Mr(),l=Mr(),u=this._storage,c=[];function h(m,S,x,b){return Jm(r,l,u,i,s,c,m,S,x,b)}d(n.root?[n.root]:[],o&&o.root?[o.root]:[],t,n===o||!o,0);var v=g(u);if(this._oldTree=n,this._storage=l,this._controllerHost){var f=this.seriesModel.layoutInfo,p=n.root.getLayout();p.width===f.width&&p.height===f.height&&(this._controllerHost.zoom=1)}return{lastsForAnimation:s,willDeleteEls:v,renderFinally:y};function d(m,S,x,b,_){b?(S=m,D(m,function(C,T){!C.isRemoved()&&A(T,T)})):new Ar(S,m,w,w).add(A).update(A).remove(q(A,null)).execute();function w(C){return C.getId()}function A(C,T){var I=C!=null?m[C]:null,L=T!=null?S[T]:null,M=h(I,L,x,_);M&&d(I&&I.viewChildren||[],L&&L.viewChildren||[],M,b,_+1)}}function g(m){var S=Mr();return m&&D(m,function(x,b){var _=S[b];D(x,function(w){w&&(_.push(w),ti(w).willDelete=!0)})}),S}function y(){D(v,function(m){D(m,function(S){S.parent&&S.parent.remove(S)})}),D(c,function(m){m.invisible=!0,m.dirty()})}},e.prototype._doAnimation=function(t,r,i,n){var o=i.get("animationDurationUpdate"),s=i.get("animationEasing"),l=(st(o)?0:o)||0,u=(st(s)?null:s)||"cubicOut",c=$m();D(r.willDeleteEls,function(h,v){D(h,function(f,p){if(!f.invisible){var d=f.parent,g,y=ti(d);if(n&&n.direction==="drillDown")g=d===n.rootNodeGroup?{shape:{x:0,y:0,width:y.nodeWidth,height:y.nodeHeight},style:{opacity:0}}:{style:{opacity:0}};else{var m=0,S=0;y.willDelete||(m=y.nodeWidth/2,S=y.nodeHeight/2),g=v==="nodeGroup"?{x:m,y:S,style:{opacity:0}}:{shape:{x:m,y:S,width:0,height:0},style:{opacity:0}}}g&&c.add(f,g,l,0,u)}})}),D(this._storage,function(h,v){D(h,function(f,p){var d=r.lastsForAnimation[v][p],g={};d&&(f instanceof X?d.oldX!=null&&(g.x=f.x,g.y=f.y,f.x=d.oldX,f.y=d.oldY):(d.oldShape&&(g.shape=F({},f.shape),f.setShape(d.oldShape)),d.fadein?(f.setStyle("opacity",0),g.style={opacity:1}):f.style.opacity!==1&&(g.style={opacity:1})),c.add(f,g,l,0,u))})},this),this._state="animating",c.finished(W(function(){this._state="ready",r.renderFinally()},this)).start()},e.prototype._resetController=function(t){var r=this._controller,i=this._controllerHost;i||(this._controllerHost={target:this.group},i=this._controllerHost),r||(r=this._controller=new da(t.getZr()),r.enable(this.seriesModel.get("roam")),i.zoomLimit=this.seriesModel.get("scaleLimit"),i.zoom=this.seriesModel.get("zoom"),r.on("pan",W(this._onPan,this)),r.on("zoom",W(this._onZoom,this)));var n=new Pt(0,0,t.getWidth(),t.getHeight());r.setPointerChecker(function(o,s,l){return n.contain(s,l)})},e.prototype._clearController=function(){var t=this._controller;this._controllerHost=null,t&&(t.dispose(),t=null)},e.prototype._onPan=function(t){if(this._state!=="animating"&&(Math.abs(t.dx)>Jl||Math.abs(t.dy)>Jl)){var r=this.seriesModel.getData().tree.root;if(!r)return;var i=r.getLayout();if(!i)return;this.api.dispatchAction({type:"treemapMove",from:this.uid,seriesId:this.seriesModel.id,rootRect:{x:i.x+t.dx,y:i.y+t.dy,width:i.width,height:i.height}})}},e.prototype._onZoom=function(t){var r=t.originX,i=t.originY,n=t.scale;if(this._state!=="animating"){var o=this.seriesModel.getData().tree.root;if(!o)return;var s=o.getLayout();if(!s)return;var l=new Pt(s.x,s.y,s.width,s.height),u=null,c=this._controllerHost;u=c.zoomLimit;var h=c.zoom=c.zoom||1;if(h*=n,u){var v=u.min||0,f=u.max||1/0;h=Math.max(Math.min(f,h),v)}var p=h/c.zoom;c.zoom=h;var d=this.seriesModel.layoutInfo;r-=d.x,i-=d.y;var g=wr();Ke(g,g,[-r,-i]),wh(g,g,[p,p]),Ke(g,g,[r,i]),l.applyTransform(g),this.api.dispatchAction({type:"treemapRender",from:this.uid,seriesId:this.seriesModel.id,rootRect:{x:l.x,y:l.y,width:l.width,height:l.height}})}},e.prototype._initEvents=function(t){var r=this;t.on("click",function(i){if(r._state==="ready"){var n=r.seriesModel.get("nodeClick",!0);if(n){var o=r.findTarget(i.offsetX,i.offsetY);if(o){var s=o.node;if(s.getLayout().isLeafRoot)r._rootToNode(o);else if(n==="zoomToNode")r._zoomToNode(o);else if(n==="link"){var l=s.hostTree.data.getItemModel(s.dataIndex),u=l.get("link",!0),c=l.get("target",!0)||"blank";u&&Rh(u,c)}}}}},this)},e.prototype._renderBreadcrumb=function(t,r,i){var n=this;i||(i=t.get("leafDepth",!0)!=null?{node:t.getViewRoot()}:this.findTarget(r.getWidth()/2,r.getHeight()/2),i||(i={node:t.getData().tree.root})),(this._breadcrumb||(this._breadcrumb=new Wm(this.group))).render(t,r,i.node,function(o){n._state!=="animating"&&(es(t.getViewRoot(),o)?n._rootToNode({node:o}):n._zoomToNode({node:o}))})},e.prototype.remove=function(){this._clearController(),this._containerGroup&&this._containerGroup.removeAll(),this._storage=Mr(),this._state="ready",this._breadcrumb&&this._breadcrumb.remove()},e.prototype.dispose=function(){this._clearController()},e.prototype._zoomToNode=function(t){this.api.dispatchAction({type:"treemapZoomToNode",from:this.uid,seriesId:this.seriesModel.id,targetNode:t.node})},e.prototype._rootToNode=function(t){this.api.dispatchAction({type:"treemapRootToNode",from:this.uid,seriesId:this.seriesModel.id,targetNode:t.node})},e.prototype.findTarget=function(t,r){var i,n=this.seriesModel.getViewRoot();return n.eachNode({attr:"viewChildren",order:"preorder"},function(o){var s=this._storage.background[o.getRawIndex()];if(s){var l=s.transformCoordToLocal(t,r),u=s.shape;if(u.x<=l[0]&&l[0]<=u.x+u.width&&u.y<=l[1]&&l[1]<=u.y+u.height)i={node:o,offsetX:l[0],offsetY:l[1]};else return!1}},this),i},e.type="treemap",e}(bt);function Mr(){return{nodeGroup:[],background:[],content:[]}}function Jm(a,e,t,r,i,n,o,s,l,u){if(!o)return;var c=o.getLayout(),h=a.getData(),v=o.getModel();if(h.setItemGraphicEl(o.dataIndex,null),!c||!c.isInView)return;var f=c.width,p=c.height,d=c.borderWidth,g=c.invisible,y=o.getRawIndex(),m=s&&s.getRawIndex(),S=o.viewChildren,x=c.upperHeight,b=S&&S.length,_=v.getModel("itemStyle"),w=v.getModel(["emphasis","itemStyle"]),A=v.getModel(["blur","itemStyle"]),C=v.getModel(["select","itemStyle"]),T=_.get("borderRadius")||0,I=rt("nodeGroup",to);if(!I)return;if(l.add(I),I.x=c.x||0,I.y=c.y||0,I.markRedraw(),ti(I).nodeWidth=f,ti(I).nodeHeight=p,c.isAboveViewRoot)return I;var L=rt("background",jl,u,Km);L&&H(I,L,b&&c.upperLabelHeight);var M=v.getModel("emphasis"),R=M.get("focus"),P=M.get("blurScope"),k=M.get("disabled"),V=R==="ancestor"?o.getAncestorsIndices():R==="descendant"?o.getDescendantIndices():R;if(b)Hs(I)&&Nr(I,!1),L&&(Nr(L,!k),h.setItemGraphicEl(o.dataIndex,L),Ws(L,V,P));else{var N=rt("content",jl,u,qm);N&&Z(I,N),L.disableMorphing=!0,L&&Hs(L)&&Nr(L,!1),Nr(I,!k),h.setItemGraphicEl(o.dataIndex,I);var z=v.getShallow("cursor");z&&N.attr("cursor",z),Ws(I,V,P)}return I;function H(J,$,lt){var nt=ht($);if(nt.dataIndex=o.dataIndex,nt.seriesIndex=a.seriesIndex,$.setShape({x:0,y:0,width:f,height:p,r:T}),g)Y($);else{$.invisible=!1;var vt=o.getVisual("style"),St=vt.stroke,Lt=eu(_);Lt.fill=St;var ct=Be(w);ct.fill=w.get("borderColor");var gt=Be(A);gt.fill=A.get("borderColor");var _t=Be(C);if(_t.fill=C.get("borderColor"),lt){var Xt=f-2*d;Q($,St,vt.opacity,{x:d,y:0,width:Xt,height:x})}else $.removeTextContent();$.setStyle(Lt),$.ensureState("emphasis").style=ct,$.ensureState("blur").style=gt,$.ensureState("select").style=_t,gr($)}J.add($)}function Z(J,$){var lt=ht($);lt.dataIndex=o.dataIndex,lt.seriesIndex=a.seriesIndex;var nt=Math.max(f-2*d,0),vt=Math.max(p-2*d,0);if($.culling=!0,$.setShape({x:d,y:d,width:nt,height:vt,r:T}),g)Y($);else{$.invisible=!1;var St=o.getVisual("style"),Lt=St.fill,ct=eu(_);ct.fill=Lt,ct.decal=St.decal;var gt=Be(w),_t=Be(A),Xt=Be(C);Q($,Lt,St.opacity,null),$.setStyle(ct),$.ensureState("emphasis").style=gt,$.ensureState("blur").style=_t,$.ensureState("select").style=Xt,gr($)}J.add($)}function Y(J){!J.invisible&&n.push(J)}function Q(J,$,lt,nt){var vt=v.getModel(nt?tu:Ql),St=ye(v.get("name"),null),Lt=vt.getShallow("show");Wt(J,zt(v,nt?tu:Ql),{defaultText:Lt?St:null,inheritColor:$,defaultOpacity:lt,labelFetcher:a,labelDataIndex:o.dataIndex});var ct=J.getTextContent();if(ct){var gt=ct.style,_t=ad(gt.padding||0);nt&&(J.setTextConfig({layoutRect:nt}),ct.disableLabelLayout=!0),ct.beforeUpdate=function(){var Bt=Math.max((nt?nt.width:J.shape.width)-_t[1]-_t[3],0),ke=Math.max((nt?nt.height:J.shape.height)-_t[0]-_t[2],0);(gt.width!==Bt||gt.height!==ke)&&ct.setStyle({width:Bt,height:ke})},gt.truncateMinChar=2,gt.lineOverflow="truncate",j(gt,nt,c);var Xt=ct.getState("emphasis");j(Xt?Xt.style:null,nt,c)}}function j(J,$,lt){var nt=J?J.text:null;if(!$&&lt.isLeafRoot&&nt!=null){var vt=a.get("drillDownIcon",!0);J.text=vt?vt+" "+nt:nt}}function rt(J,$,lt,nt){var vt=m!=null&&t[J][m],St=i[J];return vt?(t[J][m]=null,It(St,vt)):g||(vt=new $,vt instanceof dr&&(vt.z2=Qm(lt,nt)),$t(St,vt)),e[J][y]=vt}function It(J,$){var lt=J[y]={};$ instanceof to?(lt.oldX=$.x,lt.oldY=$.y):lt.oldShape=F({},$.shape)}function $t(J,$){var lt=J[y]={},nt=o.parentNode,vt=$ instanceof X;if(nt&&(!r||r.direction==="drillDown")){var St=0,Lt=0,ct=i.background[nt.getRawIndex()];!r&&ct&&ct.oldShape&&(St=ct.oldShape.width,Lt=ct.oldShape.height),vt?(lt.oldX=0,lt.oldY=Lt):lt.oldShape={x:St,y:Lt,width:0,height:0}}lt.fadein=!vt}}function Qm(a,e){return a*Xm+e}var Qr=D,t0=Nt,ei=-1,Tt=function(){function a(e){var t=e.mappingMethod,r=e.type,i=this.option=it(e);this.type=r,this.mappingMethod=t,this._normalizeData=a0[t];var n=a.visualHandlers[r];this.applyVisual=n.applyVisual,this.getColorMapper=n.getColorMapper,this._normalizedToVisual=n._normalizedToVisual[t],t==="piecewise"?(Ki(i),e0(i)):t==="category"?i.categories?r0(i):Ki(i,!0):(Yr(t!=="linear"||i.dataExtent),Ki(i))}return a.prototype.mapValueToVisual=function(e){var t=this._normalizeData(e);return this._normalizedToVisual(t,e)},a.prototype.getNormalizer=function(){return W(this._normalizeData,this)},a.listVisualTypes=function(){return wt(a.visualHandlers)},a.isValidType=function(e){return a.visualHandlers.hasOwnProperty(e)},a.eachVisual=function(e,t,r){Nt(e)?D(e,t,r):t.call(r,e)},a.mapVisual=function(e,t,r){var i,n=U(e)?[]:Nt(e)?{}:(i=!0,null);return a.eachVisual(e,function(o,s){var l=t.call(r,o,s);i?n=l:n[s]=l}),n},a.retrieveVisuals=function(e){var t={},r;return e&&Qr(a.visualHandlers,function(i,n){e.hasOwnProperty(n)&&(t[n]=e[n],r=!0)}),r?t:null},a.prepareVisualTypes=function(e){if(U(e))e=e.slice();else if(t0(e)){var t=[];Qr(e,function(r,i){t.push(i)}),e=t}else return[];return e.sort(function(r,i){return i==="color"&&r!=="color"&&r.indexOf("color")===0?1:-1}),e},a.dependsOn=function(e,t){return t==="color"?!!(e&&e.indexOf(t)===0):e===t},a.findPieceIndex=function(e,t,r){for(var i,n=1/0,o=0,s=t.length;o<s;o++){var l=t[o].value;if(l!=null){if(l===e||tt(l)&&l===e+"")return o;r&&v(l,o)}}for(var o=0,s=t.length;o<s;o++){var u=t[o],c=u.interval,h=u.close;if(c){if(c[0]===-1/0){if(Aa(h[1],e,c[1]))return o}else if(c[1]===1/0){if(Aa(h[0],c[0],e))return o}else if(Aa(h[0],c[0],e)&&Aa(h[1],e,c[1]))return o;r&&v(c[0],o),r&&v(c[1],o)}}if(r)return e===1/0?t.length-1:e===-1/0?0:i;function v(f,p){var d=Math.abs(f-e);d<n&&(n=d,i=p)}},a.visualHandlers={color:{applyVisual:Pr("color"),getColorMapper:function(){var e=this.option;return W(e.mappingMethod==="category"?function(t,r){return!r&&(t=this._normalizeData(t)),Gr.call(this,t)}:function(t,r,i){var n=!!i;return!r&&(t=this._normalizeData(t)),i=Ni(t,e.parsedVisual,i),n?i:Oa(i,"rgba")},this)},_normalizedToVisual:{linear:function(e){return Oa(Ni(e,this.option.parsedVisual),"rgba")},category:Gr,piecewise:function(e,t){var r=ro.call(this,t);return r==null&&(r=Oa(Ni(e,this.option.parsedVisual),"rgba")),r},fixed:Ge}},colorHue:wa(function(e,t){return Wr(e,t)}),colorSaturation:wa(function(e,t){return Wr(e,null,t)}),colorLightness:wa(function(e,t){return Wr(e,null,null,t)}),colorAlpha:wa(function(e,t){return $a(e,t)}),decal:{applyVisual:Pr("decal"),_normalizedToVisual:{linear:null,category:Gr,piecewise:null,fixed:null}},opacity:{applyVisual:Pr("opacity"),_normalizedToVisual:eo([0,1])},liftZ:{applyVisual:Pr("liftZ"),_normalizedToVisual:{linear:Ge,category:Ge,piecewise:Ge,fixed:Ge}},symbol:{applyVisual:function(e,t,r){var i=this.mapValueToVisual(e);r("symbol",i)},_normalizedToVisual:{linear:ru,category:Gr,piecewise:function(e,t){var r=ro.call(this,t);return r==null&&(r=ru.call(this,e)),r},fixed:Ge}},symbolSize:{applyVisual:Pr("symbolSize"),_normalizedToVisual:eo([0,1])}},a}();function e0(a){var e=a.pieceList;a.hasSpecialVisual=!1,D(e,function(t,r){t.originIndex=r,t.visual!=null&&(a.hasSpecialVisual=!0)})}function r0(a){var e=a.categories,t=a.categoryMap={},r=a.visual;if(Qr(e,function(o,s){t[o]=s}),!U(r)){var i=[];Nt(r)?Qr(r,function(o,s){var l=t[s];i[l??ei]=o}):i[ei]=r,r=Rv(a,i)}for(var n=e.length-1;n>=0;n--)r[n]==null&&(delete t[e[n]],e.pop())}function Ki(a,e){var t=a.visual,r=[];Nt(t)?Qr(t,function(n){r.push(n)}):t!=null&&r.push(t);var i={color:1,symbol:1};!e&&r.length===1&&!i.hasOwnProperty(a.type)&&(r[1]=r[0]),Rv(a,r)}function wa(a){return{applyVisual:function(e,t,r){var i=this.mapValueToVisual(e);r("color",a(t("color"),i))},_normalizedToVisual:eo([0,1])}}function ru(a){var e=this.option.visual;return e[Math.round(at(a,[0,1],[0,e.length-1],!0))]||{}}function Pr(a){return function(e,t,r){r(a,this.mapValueToVisual(e))}}function Gr(a){var e=this.option.visual;return e[this.option.loop&&a!==ei?a%e.length:a]}function Ge(){return this.option.visual[0]}function eo(a){return{linear:function(e){return at(e,a,this.option.visual,!0)},category:Gr,piecewise:function(e,t){var r=ro.call(this,t);return r==null&&(r=at(e,a,this.option.visual,!0)),r},fixed:Ge}}function ro(a){var e=this.option,t=e.pieceList;if(e.hasSpecialVisual){var r=Tt.findPieceIndex(a,t),i=t[r];if(i&&i.visual)return i.visual[this.type]}}function Rv(a,e){return a.visual=e,a.type==="color"&&(a.parsedVisual=G(e,function(t){var r=Eh(t);return r||[0,0,0,1]})),e}var a0={linear:function(a){return at(a,this.option.dataExtent,[0,1],!0)},piecewise:function(a){var e=this.option.pieceList,t=Tt.findPieceIndex(a,e,!0);if(t!=null)return at(t,[0,e.length-1],[0,1],!0)},category:function(a){var e=this.option.categories?this.option.categoryMap[a]:a;return e??ei},fixed:Pe};function Aa(a,e,t){return a?e<=t:e<t}var i0="itemStyle",Ev=Dt();const n0={seriesType:"treemap",reset:function(a){var e=a.getData().tree,t=e.root;t.isRemoved()||kv(t,{},a.getViewRoot().getAncestors(),a)}};function kv(a,e,t,r){var i=a.getModel(),n=a.getLayout(),o=a.hostTree.data;if(!(!n||n.invisible||!n.isInView)){var s=i.getModel(i0),l=o0(s,e,r),u=o.ensureUniqueItemVisual(a.dataIndex,"style"),c=s.get("borderColor"),h=s.get("borderColorSaturation"),v;h!=null&&(v=au(l),c=s0(h,v)),u.stroke=c;var f=a.viewChildren;if(!f||!f.length)v=au(l),u.fill=v;else{var p=l0(a,i,n,s,l,f);D(f,function(d,g){if(d.depth>=t.length||d===t[d.depth]){var y=u0(i,l,d,g,p,r);kv(d,y,t,r)}})}}}function o0(a,e,t){var r=F({},e),i=t.designatedVisualItemStyle;return D(["color","colorAlpha","colorSaturation"],function(n){i[n]=e[n];var o=a.get(n);i[n]=null,o!=null&&(r[n]=o)}),r}function au(a){var e=qi(a,"color");if(e){var t=qi(a,"colorAlpha"),r=qi(a,"colorSaturation");return r&&(e=Wr(e,null,null,r)),t&&(e=$a(e,t)),e}}function s0(a,e){return e!=null?Wr(e,null,null,a):null}function qi(a,e){var t=a[e];if(t!=null&&t!=="none")return t}function l0(a,e,t,r,i,n){if(!(!n||!n.length)){var o=ji(e,"color")||i.color!=null&&i.color!=="none"&&(ji(e,"colorAlpha")||ji(e,"colorSaturation"));if(o){var s=e.get("visualMin"),l=e.get("visualMax"),u=t.dataExtent.slice();s!=null&&s<u[0]&&(u[0]=s),l!=null&&l>u[1]&&(u[1]=l);var c=e.get("colorMappingBy"),h={type:o.name,dataExtent:u,visual:o.range};h.type==="color"&&(c==="index"||c==="id")?(h.mappingMethod="category",h.loop=!0):h.mappingMethod="linear";var v=new Tt(h);return Ev(v).drColorMappingBy=c,v}}}function ji(a,e){var t=a.get(e);return U(t)&&t.length?{name:e,range:t}:null}function u0(a,e,t,r,i,n){var o=F({},e);if(i){var s=i.type,l=s==="color"&&Ev(i).drColorMappingBy,u=l==="index"?r:l==="id"?n.mapIdToIndex(t.getId()):t.getValue(a.get("visualDimension"));o[s]=i.mapValueToVisual(u)}return o}var ta=Math.max,ri=Math.min,iu=qt,rs=D,Vv=["itemStyle","borderWidth"],c0=["itemStyle","gapWidth"],h0=["upperLabel","show"],v0=["upperLabel","height"];const f0={seriesType:"treemap",reset:function(a,e,t,r){var i=t.getWidth(),n=t.getHeight(),o=a.option,s=Rt(a.getBoxLayoutParams(),{width:t.getWidth(),height:t.getHeight()}),l=o.size||[],u=O(iu(s.width,l[0]),i),c=O(iu(s.height,l[1]),n),h=r&&r.type,v=["treemapZoomToNode","treemapRootToNode"],f=Jr(r,v,a),p=h==="treemapRender"||h==="treemapMove"?r.rootRect:null,d=a.getViewRoot(),g=Lv(d);if(h!=="treemapMove"){var y=h==="treemapZoomToNode"?S0(a,f,d,u,c):p?[p.width,p.height]:[u,c],m=o.sort;m&&m!=="asc"&&m!=="desc"&&(m="desc");var S={squareRatio:o.squareRatio,sort:m,leafDepth:o.leafDepth};d.hostTree.clearLayouts();var x={x:0,y:0,width:y[0],height:y[1],area:y[0]*y[1]};d.setLayout(x),Nv(d,S,!1,0),x=d.getLayout(),rs(g,function(_,w){var A=(g[w+1]||d).getValue();_.setLayout(F({dataExtent:[A,A],borderWidth:0,upperHeight:0},x))})}var b=a.getData().tree.root;b.setLayout(x0(s,p,f),!0),a.setLayoutInfo(s),zv(b,new Pt(-s.x,-s.y,i,n),g,d,0)}};function Nv(a,e,t,r){var i,n;if(!a.isRemoved()){var o=a.getLayout();i=o.width,n=o.height;var s=a.getModel(),l=s.get(Vv),u=s.get(c0)/2,c=Ov(s),h=Math.max(l,c),v=l-u,f=h-u;a.setLayout({borderWidth:l,upperHeight:h,upperLabelHeight:c},!0),i=ta(i-2*v,0),n=ta(n-v-f,0);var p=i*n,d=p0(a,s,p,e,t,r);if(d.length){var g={x:v,y:f,width:i,height:n},y=ri(i,n),m=1/0,S=[];S.area=0;for(var x=0,b=d.length;x<b;){var _=d[x];S.push(_),S.area+=_.getLayout().area;var w=m0(S,y,e.squareRatio);w<=m?(x++,m=w):(S.area-=S.pop().getLayout().area,nu(S,y,g,u,!1),y=ri(g.width,g.height),S.length=S.area=0,m=1/0)}if(S.length&&nu(S,y,g,u,!0),!t){var A=s.get("childrenVisibleMin");A!=null&&p<A&&(t=!0)}for(var x=0,b=d.length;x<b;x++)Nv(d[x],e,t,r+1)}}}function p0(a,e,t,r,i,n){var o=a.children||[],s=r.sort;s!=="asc"&&s!=="desc"&&(s=null);var l=r.leafDepth!=null&&r.leafDepth<=n;if(i&&!l)return a.viewChildren=[];o=Yt(o,function(f){return!f.isRemoved()}),g0(o,s);var u=y0(e,o,s);if(u.sum===0)return a.viewChildren=[];if(u.sum=d0(e,t,u.sum,s,o),u.sum===0)return a.viewChildren=[];for(var c=0,h=o.length;c<h;c++){var v=o[c].getValue()/u.sum*t;o[c].setLayout({area:v})}return l&&(o.length&&a.setLayout({isLeafRoot:!0},!0),o.length=0),a.viewChildren=o,a.setLayout({dataExtent:u.dataExtent},!0),o}function d0(a,e,t,r,i){if(!r)return t;for(var n=a.get("visibleMin"),o=i.length,s=o,l=o-1;l>=0;l--){var u=i[r==="asc"?o-l-1:l].getValue();u/t*e<n&&(s=l,t-=u)}return r==="asc"?i.splice(0,o-s):i.splice(s,o-s),t}function g0(a,e){return e&&a.sort(function(t,r){var i=e==="asc"?t.getValue()-r.getValue():r.getValue()-t.getValue();return i===0?e==="asc"?t.dataIndex-r.dataIndex:r.dataIndex-t.dataIndex:i}),a}function y0(a,e,t){for(var r=0,i=0,n=e.length;i<n;i++)r+=e[i].getValue();var o=a.get("visualDimension"),s;return!e||!e.length?s=[NaN,NaN]:o==="value"&&t?(s=[e[e.length-1].getValue(),e[0].getValue()],t==="asc"&&s.reverse()):(s=[1/0,-1/0],rs(e,function(l){var u=l.getValue(o);u<s[0]&&(s[0]=u),u>s[1]&&(s[1]=u)})),{sum:r,dataExtent:s}}function m0(a,e,t){for(var r=0,i=1/0,n=0,o=void 0,s=a.length;n<s;n++)o=a[n].getLayout().area,o&&(o<i&&(i=o),o>r&&(r=o));var l=a.area*a.area,u=e*e*t;return l?ta(u*r/l,l/(u*i)):1/0}function nu(a,e,t,r,i){var n=e===t.width?0:1,o=1-n,s=["x","y"],l=["width","height"],u=t[s[n]],c=e?a.area/e:0;(i||c>t[l[o]])&&(c=t[l[o]]);for(var h=0,v=a.length;h<v;h++){var f=a[h],p={},d=c?f.getLayout().area/c:0,g=p[l[o]]=ta(c-2*r,0),y=t[s[n]]+t[l[n]]-u,m=h===v-1||y<d?y:d,S=p[l[n]]=ta(m-2*r,0);p[s[o]]=t[s[o]]+ri(r,g/2),p[s[n]]=u+ri(r,S/2),u+=m,f.setLayout(p,!0)}t[s[o]]+=c,t[l[o]]-=c}function S0(a,e,t,r,i){var n=(e||{}).node,o=[r,i];if(!n||n===t)return o;for(var s,l=r*i,u=l*a.option.zoomToNodeRatio;s=n.parentNode;){for(var c=0,h=s.children,v=0,f=h.length;v<f;v++)c+=h[v].getValue();var p=n.getValue();if(p===0)return o;u*=c/p;var d=s.getModel(),g=d.get(Vv),y=Math.max(g,Ov(d));u+=4*g*g+(3*g+y)*Math.pow(u,.5),u>Zs&&(u=Zs),n=s}u<l&&(u=l);var m=Math.pow(u/l,.5);return[r*m,i*m]}function x0(a,e,t){if(e)return{x:e.x,y:e.y};var r={x:0,y:0};if(!t)return r;var i=t.node,n=i.getLayout();if(!n)return r;for(var o=[n.width/2,n.height/2],s=i;s;){var l=s.getLayout();o[0]+=l.x,o[1]+=l.y,s=s.parentNode}return{x:a.width/2-o[0],y:a.height/2-o[1]}}function zv(a,e,t,r,i){var n=a.getLayout(),o=t[i],s=o&&o===a;if(!(o&&!s||i===t.length&&a!==r)){a.setLayout({isInView:!0,invisible:!s&&!e.intersect(n),isAboveViewRoot:s},!0);var l=new Pt(e.x-n.x,e.y-n.y,e.width,e.height);rs(a.viewChildren||[],function(u){zv(u,l,t,r,i+1)})}}function Ov(a){return a.get(h0)?a.get(v0):0}function b0(a){a.registerSeriesModel(Gm),a.registerChartView(jm),a.registerVisual(n0),a.registerLayout(f0),Bm(a)}function _0(a){var e=a.findComponents({mainType:"legend"});!e||!e.length||a.eachSeriesByType("graph",function(t){var r=t.getCategoriesData(),i=t.getGraph(),n=i.data,o=r.mapArray(r.getName);n.filterSelf(function(s){var l=n.getItemModel(s),u=l.getShallow("category");if(u!=null){jt(u)&&(u=o[u]);for(var c=0;c<e.length;c++)if(!e[c].isSelected(u))return!1}return!0})})}function w0(a){var e={};a.eachSeriesByType("graph",function(t){var r=t.getCategoriesData(),i=t.getData(),n={};r.each(function(o){var s=r.getName(o);n["ec-"+s]=o;var l=r.getItemModel(o),u=l.getModel("itemStyle").getItemStyle();u.fill||(u.fill=t.getColorFromPalette(s,e)),r.setItemVisual(o,"style",u);for(var c=["symbol","symbolSize","symbolKeepAspect"],h=0;h<c.length;h++){var v=l.getShallow(c[h],!0);v!=null&&r.setItemVisual(o,c[h],v)}}),r.count()&&i.each(function(o){var s=i.getItemModel(o),l=s.getShallow("category");if(l!=null){tt(l)&&(l=n["ec-"+l]);var u=r.getItemVisual(l,"style"),c=i.ensureUniqueItemVisual(o,"style");F(c,u);for(var h=["symbol","symbolSize","symbolKeepAspect"],v=0;v<h.length;v++)i.setItemVisual(o,h[v],r.getItemVisual(l,h[v]))}})})}function Ta(a){return a instanceof Array||(a=[a,a]),a}function A0(a){a.eachSeriesByType("graph",function(e){var t=e.getGraph(),r=e.getEdgeData(),i=Ta(e.get("edgeSymbol")),n=Ta(e.get("edgeSymbolSize"));r.setVisual("fromSymbol",i&&i[0]),r.setVisual("toSymbol",i&&i[1]),r.setVisual("fromSymbolSize",n&&n[0]),r.setVisual("toSymbolSize",n&&n[1]),r.setVisual("style",e.getModel("lineStyle").getLineStyle()),r.each(function(o){var s=r.getItemModel(o),l=t.getEdgeByIndex(o),u=Ta(s.getShallow("symbol",!0)),c=Ta(s.getShallow("symbolSize",!0)),h=s.getModel("lineStyle").getLineStyle(),v=r.ensureUniqueItemVisual(o,"style");switch(F(v,h),v.stroke){case"source":{var f=l.node1.getVisual("style");v.stroke=f&&f.fill;break}case"target":{var f=l.node2.getVisual("style");v.stroke=f&&f.fill;break}}u[0]&&l.setVisual("fromSymbol",u[0]),u[1]&&l.setVisual("toSymbol",u[1]),c[0]&&l.setVisual("fromSymbolSize",c[0]),c[1]&&l.setVisual("toSymbolSize",c[1])})})}var ao="-->",Pi=function(a){return a.get("autoCurveness")||null},Bv=function(a,e){var t=Pi(a),r=20,i=[];if(jt(t))r=t;else if(U(t)){a.__curvenessList=t;return}e>r&&(r=e);var n=r%2?r+2:r+3;i=[];for(var o=0;o<n;o++)i.push((o%2?o+1:o)/10*(o%2?-1:1));a.__curvenessList=i},ea=function(a,e,t){var r=[a.id,a.dataIndex].join("."),i=[e.id,e.dataIndex].join(".");return[t.uid,r,i].join(ao)},Gv=function(a){var e=a.split(ao);return[e[0],e[2],e[1]].join(ao)},T0=function(a,e){var t=ea(a.node1,a.node2,e);return e.__edgeMap[t]},D0=function(a,e){var t=io(ea(a.node1,a.node2,e),e),r=io(ea(a.node2,a.node1,e),e);return t+r},io=function(a,e){var t=e.__edgeMap;return t[a]?t[a].length:0};function C0(a){Pi(a)&&(a.__curvenessList=[],a.__edgeMap={},Bv(a))}function I0(a,e,t,r){if(Pi(t)){var i=ea(a,e,t),n=t.__edgeMap,o=n[Gv(i)];n[i]&&!o?n[i].isForward=!0:o&&n[i]&&(o.isForward=!0,n[i].isForward=!1),n[i]=n[i]||[],n[i].push(r)}}function as(a,e,t,r){var i=Pi(e),n=U(i);if(!i)return null;var o=T0(a,e);if(!o)return null;for(var s=-1,l=0;l<o.length;l++)if(o[l]===t){s=l;break}var u=D0(a,e);Bv(e,u),a.lineStyle=a.lineStyle||{};var c=ea(a.node1,a.node2,e),h=e.__curvenessList,v=n||u%2?0:1;if(o.isForward)return h[v+s];var f=Gv(c),p=io(f,e),d=h[s+p+v];return r?n?i&&i[0]===0?(p+v)%2?d:-d:((p%2?0:1)+v)%2?d:-d:(p+v)%2?d:-d:h[s+p+v]}function Fv(a){var e=a.coordinateSystem;if(!(e&&e.type!=="view")){var t=a.getGraph();t.eachNode(function(r){var i=r.getModel();r.setLayout([+i.get("x"),+i.get("y")])}),is(t,a)}}function is(a,e){a.eachEdge(function(t,r){var i=xr(t.getModel().get(["lineStyle","curveness"]),-as(t,e,r,!0),0),n=Ie(t.node1.getLayout()),o=Ie(t.node2.getLayout()),s=[n,o];+i&&s.push([(n[0]+o[0])/2-(n[1]-o[1])*i,(n[1]+o[1])/2-(o[0]-n[0])*i]),t.setLayout(s)})}function L0(a,e){a.eachSeriesByType("graph",function(t){var r=t.get("layout"),i=t.coordinateSystem;if(i&&i.type!=="view"){var n=t.getData(),o=[];D(i.dimensions,function(v){o=o.concat(n.mapDimensionsAll(v))});for(var s=0;s<n.count();s++){for(var l=[],u=!1,c=0;c<o.length;c++){var h=n.get(o[c],s);isNaN(h)||(u=!0),l.push(h)}u?n.setItemLayout(s,i.dataToPoint(l)):n.setItemLayout(s,[NaN,NaN])}is(n.graph,t)}else(!r||r==="none")&&Fv(t)})}function Fr(a){var e=a.coordinateSystem;if(e.type!=="view")return 1;var t=a.option.nodeScaleRatio,r=e.scaleX,i=e.getZoom(),n=(i-1)*t+1;return n/r}function Hr(a){var e=a.getVisual("symbolSize");return e instanceof Array&&(e=(e[0]+e[1])/2),+e}var ou=Math.PI,Ji=[];function ns(a,e,t,r){var i=a.coordinateSystem;if(!(i&&i.type!=="view")){var n=i.getBoundingRect(),o=a.getData(),s=o.graph,l=n.width/2+n.x,u=n.height/2+n.y,c=Math.min(n.width,n.height)/2,h=o.count();if(o.setLayout({cx:l,cy:u}),!!h){if(t){var v=i.pointToData(r),f=v[0],p=v[1],d=[f-l,p-u];ca(d,d),id(d,d,c),t.setLayout([l+d[0],u+d[1]],!0);var g=a.get(["circular","rotateLabel"]);Hv(t,g,l,u)}M0[e](a,s,o,c,l,u,h),s.eachEdge(function(y,m){var S=xr(y.getModel().get(["lineStyle","curveness"]),as(y,a,m),0),x=Ie(y.node1.getLayout()),b=Ie(y.node2.getLayout()),_,w=(x[0]+b[0])/2,A=(x[1]+b[1])/2;+S&&(S*=3,_=[l*S+w*(1-S),u*S+A*(1-S)]),y.setLayout([x,b,_])})}}}var M0={value:function(a,e,t,r,i,n,o){var s=0,l=t.getSum("value"),u=Math.PI*2/(l||o);e.eachNode(function(c){var h=c.getValue("value"),v=u*(l?h:1)/2;s+=v,c.setLayout([r*Math.cos(s)+i,r*Math.sin(s)+n]),s+=v})},symbolSize:function(a,e,t,r,i,n,o){var s=0;Ji.length=o;var l=Fr(a);e.eachNode(function(h){var v=Hr(h);isNaN(v)&&(v=2),v<0&&(v=0),v*=l;var f=Math.asin(v/2/r);isNaN(f)&&(f=ou/2),Ji[h.dataIndex]=f,s+=f*2});var u=(2*ou-s)/o/2,c=0;e.eachNode(function(h){var v=u+Ji[h.dataIndex];c+=v,(!h.getLayout()||!h.getLayout().fixed)&&h.setLayout([r*Math.cos(c)+i,r*Math.sin(c)+n]),c+=v})}};function Hv(a,e,t,r){var i=a.getGraphicEl();if(i){var n=a.getModel(),o=n.get(["label","rotate"])||0,s=i.getSymbolPath();if(e){var l=a.getLayout(),u=Math.atan2(l[1]-r,l[0]-t);u<0&&(u=Math.PI*2+u);var c=l[0]<t;c&&(u=u-Math.PI);var h=c?"left":"right";s.setTextConfig({rotation:-u,position:h,origin:"center"});var v=s.ensureState("emphasis");F(v.textConfig||(v.textConfig={}),{position:h})}else s.setTextConfig({rotation:o*=Math.PI/180})}}function P0(a){a.eachSeriesByType("graph",function(e){e.get("layout")==="circular"&&ns(e,"symbolSize")})}var er=Hn;function R0(a,e,t){for(var r=a,i=e,n=t.rect,o=n.width,s=n.height,l=[n.x+o/2,n.y+s/2],u=t.gravity==null?.1:t.gravity,c=0;c<r.length;c++){var h=r[c];h.p||(h.p=nd(o*(Math.random()-.5)+l[0],s*(Math.random()-.5)+l[1])),h.pp=Ie(h.p),h.edges=null}var v=t.friction==null?.6:t.friction,f=v,p,d;return{warmUp:function(){f=v*.8},setFixed:function(g){r[g].fixed=!0},setUnfixed:function(g){r[g].fixed=!1},beforeStep:function(g){p=g},afterStep:function(g){d=g},step:function(g){p&&p(r,i);for(var y=[],m=r.length,S=0;S<i.length;S++){var x=i[S];if(!x.ignoreForceLayout){var b=x.n1,_=x.n2;hr(y,_.p,b.p);var w=Us(y)-x.d,A=_.w/(b.w+_.w);isNaN(A)&&(A=0),ca(y,y),!b.fixed&&er(b.p,b.p,y,A*w*f),!_.fixed&&er(_.p,_.p,y,-(1-A)*w*f)}}for(var S=0;S<m;S++){var C=r[S];C.fixed||(hr(y,l,C.p),er(C.p,C.p,y,u*f))}for(var S=0;S<m;S++)for(var b=r[S],T=S+1;T<m;T++){var _=r[T];hr(y,_.p,b.p);var w=Us(y);w===0&&(od(y,Math.random()-.5,Math.random()-.5),w=1);var I=(b.rep+_.rep)/w/w;!b.fixed&&er(b.pp,b.pp,y,I),!_.fixed&&er(_.pp,_.pp,y,-I)}for(var L=[],S=0;S<m;S++){var C=r[S];C.fixed||(hr(L,C.p,C.pp),er(C.p,C.p,L,f),Vt(C.pp,C.p))}f=f*.992;var M=f<.01;d&&d(r,i,M),g&&g(M)}}}function E0(a){a.eachSeriesByType("graph",function(e){var t=e.coordinateSystem;if(!(t&&t.type!=="view"))if(e.get("layout")==="force"){var r=e.preservedPoints||{},i=e.getGraph(),n=i.data,o=i.edgeData,s=e.getModel("force"),l=s.get("initLayout");e.preservedPoints?n.each(function(S){var x=n.getId(S);n.setItemLayout(S,r[x]||[NaN,NaN])}):!l||l==="none"?Fv(e):l==="circular"&&ns(e,"value");var u=n.getDataExtent("value"),c=o.getDataExtent("value"),h=s.get("repulsion"),v=s.get("edgeLength"),f=U(h)?h:[h,h],p=U(v)?v:[v,v];p=[p[1],p[0]];var d=n.mapArray("value",function(S,x){var b=n.getItemLayout(x),_=at(S,u,f);return isNaN(_)&&(_=(f[0]+f[1])/2),{w:_,rep:_,fixed:n.getItemModel(x).get("fixed"),p:!b||isNaN(b[0])||isNaN(b[1])?null:b}}),g=o.mapArray("value",function(S,x){var b=i.getEdgeByIndex(x),_=at(S,c,p);isNaN(_)&&(_=(p[0]+p[1])/2);var w=b.getModel(),A=xr(b.getModel().get(["lineStyle","curveness"]),-as(b,e,x,!0),0);return{n1:d[b.node1.dataIndex],n2:d[b.node2.dataIndex],d:_,curveness:A,ignoreForceLayout:w.get("ignoreForceLayout")}}),y=t.getBoundingRect(),m=R0(d,g,{rect:y,gravity:s.get("gravity"),friction:s.get("friction")});m.beforeStep(function(S,x){for(var b=0,_=S.length;b<_;b++)S[b].fixed&&Vt(S[b].p,i.getNodeByIndex(b).getLayout())}),m.afterStep(function(S,x,b){for(var _=0,w=S.length;_<w;_++)S[_].fixed||i.getNodeByIndex(_).setLayout(S[_].p),r[n.getId(_)]=S[_].p;for(var _=0,w=x.length;_<w;_++){var A=x[_],C=i.getEdgeByIndex(_),T=A.n1.p,I=A.n2.p,L=C.getLayout();L=L?L.slice():[],L[0]=L[0]||[],L[1]=L[1]||[],Vt(L[0],T),Vt(L[1],I),+A.curveness&&(L[2]=[(T[0]+I[0])/2-(T[1]-I[1])*A.curveness,(T[1]+I[1])/2-(I[0]-T[0])*A.curveness]),C.setLayout(L)}}),e.forceLayout=m,e.preservedPoints=r,m.step()}else e.forceLayout=null})}function k0(a,e,t){var r=F(a.getBoxLayoutParams(),{aspect:t});return Rt(r,{width:e.getWidth(),height:e.getHeight()})}function V0(a,e){var t=[];return a.eachSeriesByType("graph",function(r){var i=r.get("coordinateSystem");if(!i||i==="view"){var n=r.getData(),o=n.mapArray(function(g){var y=n.getItemModel(g);return[+y.get("x"),+y.get("y")]}),s=[],l=[];Ih(o,s,l),l[0]-s[0]===0&&(l[0]+=1,s[0]-=1),l[1]-s[1]===0&&(l[1]+=1,s[1]-=1);var u=(l[0]-s[0])/(l[1]-s[1]),c=k0(r,e,u);isNaN(u)&&(s=[c.x,c.y],l=[c.x+c.width,c.y+c.height]);var h=l[0]-s[0],v=l[1]-s[1],f=c.width,p=c.height,d=r.coordinateSystem=new ga;d.zoomLimit=r.get("scaleLimit"),d.setBoundingRect(s[0],s[1],h,v),d.setViewRect(c.x,c.y,f,p),d.setCenter(r.get("center"),e),d.setZoom(r.get("zoom")),t.push(d)}}),t}var su=oe.prototype,Qi=Mh.prototype,Wv=function(){function a(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}return a}();(function(a){E(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e})(Wv);function tn(a){return isNaN(+a.cpx1)||isNaN(+a.cpy1)}var N0=function(a){E(e,a);function e(t){var r=a.call(this,t)||this;return r.type="ec-line",r}return e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new Wv},e.prototype.buildPath=function(t,r){tn(r)?su.buildPath.call(this,t,r):Qi.buildPath.call(this,t,r)},e.prototype.pointAt=function(t){return tn(this.shape)?su.pointAt.call(this,t):Qi.pointAt.call(this,t)},e.prototype.tangentAt=function(t){var r=this.shape,i=tn(r)?[r.x2-r.x1,r.y2-r.y1]:Qi.tangentAt.call(this,t);return ca(i,i)},e}(ie),en=["fromSymbol","toSymbol"];function lu(a){return"_"+a+"Type"}function uu(a,e,t){var r=e.getItemVisual(t,a);if(!r||r==="none")return r;var i=e.getItemVisual(t,a+"Size"),n=e.getItemVisual(t,a+"Rotate"),o=e.getItemVisual(t,a+"Offset"),s=e.getItemVisual(t,a+"KeepAspect"),l=sa(i),u=ha(o||0,l);return r+l+u+(n||"")+(s||"")}function cu(a,e,t){var r=e.getItemVisual(t,a);if(!(!r||r==="none")){var i=e.getItemVisual(t,a+"Size"),n=e.getItemVisual(t,a+"Rotate"),o=e.getItemVisual(t,a+"Offset"),s=e.getItemVisual(t,a+"KeepAspect"),l=sa(i),u=ha(o||0,l),c=Et(r,-l[0]/2+u[0],-l[1]/2+u[1],l[0],l[1],null,s);return c.__specifiedRotation=n==null||isNaN(n)?void 0:+n*Math.PI/180||0,c.name=a,c}}function z0(a){var e=new N0({name:"line",subPixelOptimize:!0});return no(e.shape,a),e}function no(a,e){a.x1=e[0][0],a.y1=e[0][1],a.x2=e[1][0],a.y2=e[1][1],a.percent=1;var t=e[2];t?(a.cpx1=t[0],a.cpy1=t[1]):(a.cpx1=NaN,a.cpy1=NaN)}var os=function(a){E(e,a);function e(t,r,i){var n=a.call(this)||this;return n._createLine(t,r,i),n}return e.prototype._createLine=function(t,r,i){var n=t.hostModel,o=t.getItemLayout(r),s=z0(o);s.shape.percent=0,Gt(s,{shape:{percent:1}},n,r),this.add(s),D(en,function(l){var u=cu(l,t,r);this.add(u),this[lu(l)]=uu(l,t,r)},this),this._updateCommonStl(t,r,i)},e.prototype.updateData=function(t,r,i){var n=t.hostModel,o=this.childOfName("line"),s=t.getItemLayout(r),l={shape:{}};no(l.shape,s),dt(o,l,n,r),D(en,function(u){var c=uu(u,t,r),h=lu(u);if(this[h]!==c){this.remove(this.childOfName(u));var v=cu(u,t,r);this.add(v)}this[h]=c},this),this._updateCommonStl(t,r,i)},e.prototype.getLinePath=function(){return this.childAt(0)},e.prototype._updateCommonStl=function(t,r,i){var n=t.hostModel,o=this.childOfName("line"),s=i&&i.emphasisLineStyle,l=i&&i.blurLineStyle,u=i&&i.selectLineStyle,c=i&&i.labelStatesModels,h=i&&i.emphasisDisabled,v=i&&i.focus,f=i&&i.blurScope;if(!i||t.hasItemOption){var p=t.getItemModel(r),d=p.getModel("emphasis");s=d.getModel("lineStyle").getLineStyle(),l=p.getModel(["blur","lineStyle"]).getLineStyle(),u=p.getModel(["select","lineStyle"]).getLineStyle(),h=d.get("disabled"),v=d.get("focus"),f=d.get("blurScope"),c=zt(p)}var g=t.getItemVisual(r,"style"),y=g.stroke;o.useStyle(g),o.style.fill=null,o.style.strokeNoScale=!0,o.ensureState("emphasis").style=s,o.ensureState("blur").style=l,o.ensureState("select").style=u,D(en,function(_){var w=this.childOfName(_);if(w){w.setColor(y),w.style.opacity=g.opacity;for(var A=0;A<Wn.length;A++){var C=Wn[A],T=o.getState(C);if(T){var I=T.style||{},L=w.ensureState(C),M=L.style||(L.style={});I.stroke!=null&&(M[w.__isEmptyBrush?"stroke":"fill"]=I.stroke),I.opacity!=null&&(M.opacity=I.opacity)}}w.markRedraw()}},this);var m=n.getRawValue(r);Wt(this,c,{labelDataIndex:r,labelFetcher:{getFormattedLabel:function(_,w){return n.getFormattedLabel(_,w,t.dataType)}},inheritColor:y||"#000",defaultOpacity:g.opacity,defaultText:(m==null?t.getName(r):isFinite(m)?Ro(m):m)+""});var S=this.getTextContent();if(S){var x=c.normal;S.__align=S.style.align,S.__verticalAlign=S.style.verticalAlign,S.__position=x.get("position")||"middle";var b=x.get("distance");U(b)||(b=[b,b]),S.__labelDistance=b}this.setTextConfig({position:null,local:!0,inside:!1}),xt(this,v,f,h)},e.prototype.highlight=function(){$r(this)},e.prototype.downplay=function(){Xr(this)},e.prototype.updateLayout=function(t,r){this.setLinePoints(t.getItemLayout(r))},e.prototype.setLinePoints=function(t){var r=this.childOfName("line");no(r.shape,t),r.dirty()},e.prototype.beforeUpdate=function(){var t=this,r=t.childOfName("fromSymbol"),i=t.childOfName("toSymbol"),n=t.getTextContent();if(!r&&!i&&(!n||n.ignore))return;for(var o=1,s=this.parent;s;)s.scaleX&&(o/=s.scaleX),s=s.parent;var l=t.childOfName("line");if(!this.__dirty&&!l.__dirty)return;var u=l.shape.percent,c=l.pointAt(0),h=l.pointAt(u),v=hr([],h,c);ca(v,v);function f(T,I){var L=T.__specifiedRotation;if(L==null){var M=l.tangentAt(I);T.attr("rotation",(I===1?-1:1)*Math.PI/2-Math.atan2(M[1],M[0]))}else T.attr("rotation",L)}if(r&&(r.setPosition(c),f(r,0),r.scaleX=r.scaleY=o*u,r.markRedraw()),i&&(i.setPosition(h),f(i,1),i.scaleX=i.scaleY=o*u,i.markRedraw()),n&&!n.ignore){n.x=n.y=0,n.originX=n.originY=0;var p=void 0,d=void 0,g=n.__labelDistance,y=g[0]*o,m=g[1]*o,S=u/2,x=l.tangentAt(S),b=[x[1],-x[0]],_=l.pointAt(S);b[1]>0&&(b[0]=-b[0],b[1]=-b[1]);var w=x[0]<0?-1:1;if(n.__position!=="start"&&n.__position!=="end"){var A=-Math.atan2(x[1],x[0]);h[0]<c[0]&&(A=Math.PI+A),n.rotation=A}var C=void 0;switch(n.__position){case"insideStartTop":case"insideMiddleTop":case"insideEndTop":case"middle":C=-m,d="bottom";break;case"insideStartBottom":case"insideMiddleBottom":case"insideEndBottom":C=m,d="top";break;default:C=0,d="middle"}switch(n.__position){case"end":n.x=v[0]*y+h[0],n.y=v[1]*m+h[1],p=v[0]>.8?"left":v[0]<-.8?"right":"center",d=v[1]>.8?"top":v[1]<-.8?"bottom":"middle";break;case"start":n.x=-v[0]*y+c[0],n.y=-v[1]*m+c[1],p=v[0]>.8?"right":v[0]<-.8?"left":"center",d=v[1]>.8?"bottom":v[1]<-.8?"top":"middle";break;case"insideStartTop":case"insideStart":case"insideStartBottom":n.x=y*w+c[0],n.y=c[1]+C,p=x[0]<0?"right":"left",n.originX=-y*w,n.originY=-C;break;case"insideMiddleTop":case"insideMiddle":case"insideMiddleBottom":case"middle":n.x=_[0],n.y=_[1]+C,p="center",n.originY=-C;break;case"insideEndTop":case"insideEnd":case"insideEndBottom":n.x=-y*w+h[0],n.y=h[1]+C,p=x[0]>=0?"right":"left",n.originX=y*w,n.originY=-C;break}n.scaleX=n.scaleY=o,n.setStyle({verticalAlign:n.__verticalAlign||d,align:n.__align||p})}},e}(X),ss=function(){function a(e){this.group=new X,this._LineCtor=e||os}return a.prototype.updateData=function(e){var t=this;this._progressiveEls=null;var r=this,i=r.group,n=r._lineData;r._lineData=e,n||i.removeAll();var o=hu(e);e.diff(n).add(function(s){t._doAdd(e,s,o)}).update(function(s,l){t._doUpdate(n,e,l,s,o)}).remove(function(s){i.remove(n.getItemGraphicEl(s))}).execute()},a.prototype.updateLayout=function(){var e=this._lineData;e&&e.eachItemGraphicEl(function(t,r){t.updateLayout(e,r)},this)},a.prototype.incrementalPrepareUpdate=function(e){this._seriesScope=hu(e),this._lineData=null,this.group.removeAll()},a.prototype.incrementalUpdate=function(e,t){this._progressiveEls=[];function r(s){!s.isGroup&&!O0(s)&&(s.incremental=!0,s.ensureState("emphasis").hoverLayer=!0)}for(var i=e.start;i<e.end;i++){var n=t.getItemLayout(i);if(rn(n)){var o=new this._LineCtor(t,i,this._seriesScope);o.traverse(r),this.group.add(o),t.setItemGraphicEl(i,o),this._progressiveEls.push(o)}}},a.prototype.remove=function(){this.group.removeAll()},a.prototype.eachRendered=function(e){_i(this._progressiveEls||this.group,e)},a.prototype._doAdd=function(e,t,r){var i=e.getItemLayout(t);if(rn(i)){var n=new this._LineCtor(e,t,r);e.setItemGraphicEl(t,n),this.group.add(n)}},a.prototype._doUpdate=function(e,t,r,i,n){var o=e.getItemGraphicEl(r);if(!rn(t.getItemLayout(i))){this.group.remove(o);return}o?o.updateData(t,i,n):o=new this._LineCtor(t,i,n),t.setItemGraphicEl(i,o),this.group.add(o)},a}();function O0(a){return a.animators&&a.animators.length>0}function hu(a){var e=a.hostModel,t=e.getModel("emphasis");return{lineStyle:e.getModel("lineStyle").getLineStyle(),emphasisLineStyle:t.getModel(["lineStyle"]).getLineStyle(),blurLineStyle:e.getModel(["blur","lineStyle"]).getLineStyle(),selectLineStyle:e.getModel(["select","lineStyle"]).getLineStyle(),emphasisDisabled:t.get("disabled"),blurScope:t.get("blurScope"),focus:t.get("focus"),labelStatesModels:zt(e)}}function vu(a){return isNaN(a[0])||isNaN(a[1])}function rn(a){return a&&!vu(a[0])&&!vu(a[1])}var an=[],nn=[],on=[],rr=kh,sn=ld,fu=Math.abs;function pu(a,e,t){for(var r=a[0],i=a[1],n=a[2],o=1/0,s,l=t*t,u=.1,c=.1;c<=.9;c+=.1){an[0]=rr(r[0],i[0],n[0],c),an[1]=rr(r[1],i[1],n[1],c);var h=fu(sn(an,e)-l);h<o&&(o=h,s=c)}for(var v=0;v<32;v++){var f=s+u;nn[0]=rr(r[0],i[0],n[0],s),nn[1]=rr(r[1],i[1],n[1],s),on[0]=rr(r[0],i[0],n[0],f),on[1]=rr(r[1],i[1],n[1],f);var h=sn(nn,e)-l;if(fu(h)<.01)break;var p=sn(on,e)-l;u/=2,h<0?p>=0?s=s+u:s=s-u:p>=0?s=s-u:s=s+u}return s}function ln(a,e){var t=[],r=sd,i=[[],[],[]],n=[[],[]],o=[];e/=2,a.eachEdge(function(s,l){var u=s.getLayout(),c=s.getVisual("fromSymbol"),h=s.getVisual("toSymbol");u.__original||(u.__original=[Ie(u[0]),Ie(u[1])],u[2]&&u.__original.push(Ie(u[2])));var v=u.__original;if(u[2]!=null){if(Vt(i[0],v[0]),Vt(i[1],v[2]),Vt(i[2],v[1]),c&&c!=="none"){var f=Hr(s.node1),p=pu(i,v[0],f*e);r(i[0][0],i[1][0],i[2][0],p,t),i[0][0]=t[3],i[1][0]=t[4],r(i[0][1],i[1][1],i[2][1],p,t),i[0][1]=t[3],i[1][1]=t[4]}if(h&&h!=="none"){var f=Hr(s.node2),p=pu(i,v[1],f*e);r(i[0][0],i[1][0],i[2][0],p,t),i[1][0]=t[1],i[2][0]=t[2],r(i[0][1],i[1][1],i[2][1],p,t),i[1][1]=t[1],i[2][1]=t[2]}Vt(u[0],i[0]),Vt(u[1],i[2]),Vt(u[2],i[1])}else{if(Vt(n[0],v[0]),Vt(n[1],v[1]),hr(o,n[1],n[0]),ca(o,o),c&&c!=="none"){var f=Hr(s.node1);Hn(n[0],n[0],o,f*e)}if(h&&h!=="none"){var f=Hr(s.node2);Hn(n[1],n[1],o,-f*e)}Vt(u[0],n[0]),Vt(u[1],n[1])}})}function du(a){return a.type==="view"}var B0=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r){var i=new di,n=new ss,o=this.group;this._controller=new da(r.getZr()),this._controllerHost={target:o},o.add(i.group),o.add(n.group),this._symbolDraw=i,this._lineDraw=n,this._firstRender=!0},e.prototype.render=function(t,r,i){var n=this,o=t.coordinateSystem;this._model=t;var s=this._symbolDraw,l=this._lineDraw,u=this.group;if(du(o)){var c={x:o.x,y:o.y,scaleX:o.scaleX,scaleY:o.scaleY};this._firstRender?u.attr(c):dt(u,c,t)}ln(t.getGraph(),Fr(t));var h=t.getData();s.updateData(h);var v=t.getEdgeData();l.updateData(v),this._updateNodeAndLinkScale(),this._updateController(t,r,i),clearTimeout(this._layoutTimeout);var f=t.forceLayout,p=t.get(["force","layoutAnimation"]);f&&this._startForceLayoutIteration(f,p);var d=t.get("layout");h.graph.eachNode(function(S){var x=S.dataIndex,b=S.getGraphicEl(),_=S.getModel();if(b){b.off("drag").off("dragend");var w=_.get("draggable");w&&b.on("drag",function(C){switch(d){case"force":f.warmUp(),!n._layouting&&n._startForceLayoutIteration(f,p),f.setFixed(x),h.setItemLayout(x,[b.x,b.y]);break;case"circular":h.setItemLayout(x,[b.x,b.y]),S.setLayout({fixed:!0},!0),ns(t,"symbolSize",S,[C.offsetX,C.offsetY]),n.updateLayout(t);break;case"none":default:h.setItemLayout(x,[b.x,b.y]),is(t.getGraph(),t),n.updateLayout(t);break}}).on("dragend",function(){f&&f.setUnfixed(x)}),b.setDraggable(w,!!_.get("cursor"));var A=_.get(["emphasis","focus"]);A==="adjacency"&&(ht(b).focus=S.getAdjacentDataIndices())}}),h.graph.eachEdge(function(S){var x=S.getGraphicEl(),b=S.getModel().get(["emphasis","focus"]);x&&b==="adjacency"&&(ht(x).focus={edge:[S.dataIndex],node:[S.node1.dataIndex,S.node2.dataIndex]})});var g=t.get("layout")==="circular"&&t.get(["circular","rotateLabel"]),y=h.getLayout("cx"),m=h.getLayout("cy");h.graph.eachNode(function(S){Hv(S,g,y,m)}),this._firstRender=!1},e.prototype.dispose=function(){this.remove(),this._controller&&this._controller.dispose(),this._controllerHost=null},e.prototype._startForceLayoutIteration=function(t,r){var i=this;(function n(){t.step(function(o){i.updateLayout(i._model),(i._layouting=!o)&&(r?i._layoutTimeout=setTimeout(n,16):n())})})()},e.prototype._updateController=function(t,r,i){var n=this,o=this._controller,s=this._controllerHost,l=this.group;if(o.setPointerChecker(function(u,c,h){var v=l.getBoundingRect();return v.applyTransform(l.transform),v.contain(c,h)&&!Ii(u,i,t)}),!du(t.coordinateSystem)){o.disable();return}o.enable(t.get("roam")),s.zoomLimit=t.get("scaleLimit"),s.zoom=t.coordinateSystem.getZoom(),o.off("pan").off("zoom").on("pan",function(u){Ko(s,u.dx,u.dy),i.dispatchAction({seriesId:t.id,type:"graphRoam",dx:u.dx,dy:u.dy})}).on("zoom",function(u){qo(s,u.scale,u.originX,u.originY),i.dispatchAction({seriesId:t.id,type:"graphRoam",zoom:u.scale,originX:u.originX,originY:u.originY}),n._updateNodeAndLinkScale(),ln(t.getGraph(),Fr(t)),n._lineDraw.updateLayout(),i.updateLabelLayout()})},e.prototype._updateNodeAndLinkScale=function(){var t=this._model,r=t.getData(),i=Fr(t);r.eachItemGraphicEl(function(n,o){n&&n.setSymbolScale(i)})},e.prototype.updateLayout=function(t){ln(t.getGraph(),Fr(t)),this._symbolDraw.updateLayout(),this._lineDraw.updateLayout()},e.prototype.remove=function(){clearTimeout(this._layoutTimeout),this._layouting=!1,this._layoutTimeout=null,this._symbolDraw&&this._symbolDraw.remove(),this._lineDraw&&this._lineDraw.remove()},e.type="graph",e}(bt);function ar(a){return"_EC_"+a}var G0=function(){function a(e){this.type="graph",this.nodes=[],this.edges=[],this._nodesMap={},this._edgesMap={},this._directed=e||!1}return a.prototype.isDirected=function(){return this._directed},a.prototype.addNode=function(e,t){e=e==null?""+t:""+e;var r=this._nodesMap;if(!r[ar(e)]){var i=new Fe(e,t);return i.hostGraph=this,this.nodes.push(i),r[ar(e)]=i,i}},a.prototype.getNodeByIndex=function(e){var t=this.data.getRawIndex(e);return this.nodes[t]},a.prototype.getNodeById=function(e){return this._nodesMap[ar(e)]},a.prototype.addEdge=function(e,t,r){var i=this._nodesMap,n=this._edgesMap;if(jt(e)&&(e=this.nodes[e]),jt(t)&&(t=this.nodes[t]),e instanceof Fe||(e=i[ar(e)]),t instanceof Fe||(t=i[ar(t)]),!(!e||!t)){var o=e.id+"-"+t.id,s=new Zv(e,t,r);return s.hostGraph=this,this._directed&&(e.outEdges.push(s),t.inEdges.push(s)),e.edges.push(s),e!==t&&t.edges.push(s),this.edges.push(s),n[o]=s,s}},a.prototype.getEdgeByIndex=function(e){var t=this.edgeData.getRawIndex(e);return this.edges[t]},a.prototype.getEdge=function(e,t){e instanceof Fe&&(e=e.id),t instanceof Fe&&(t=t.id);var r=this._edgesMap;return this._directed?r[e+"-"+t]:r[e+"-"+t]||r[t+"-"+e]},a.prototype.eachNode=function(e,t){for(var r=this.nodes,i=r.length,n=0;n<i;n++)r[n].dataIndex>=0&&e.call(t,r[n],n)},a.prototype.eachEdge=function(e,t){for(var r=this.edges,i=r.length,n=0;n<i;n++)r[n].dataIndex>=0&&r[n].node1.dataIndex>=0&&r[n].node2.dataIndex>=0&&e.call(t,r[n],n)},a.prototype.breadthFirstTraverse=function(e,t,r,i){if(t instanceof Fe||(t=this._nodesMap[ar(t)]),!!t){for(var n=r==="out"?"outEdges":r==="in"?"inEdges":"edges",o=0;o<this.nodes.length;o++)this.nodes[o].__visited=!1;if(!e.call(i,t,null))for(var s=[t];s.length;)for(var l=s.shift(),u=l[n],o=0;o<u.length;o++){var c=u[o],h=c.node1===l?c.node2:c.node1;if(!h.__visited){if(e.call(i,h,l))return;s.push(h),h.__visited=!0}}}},a.prototype.update=function(){for(var e=this.data,t=this.edgeData,r=this.nodes,i=this.edges,n=0,o=r.length;n<o;n++)r[n].dataIndex=-1;for(var n=0,o=e.count();n<o;n++)r[e.getRawIndex(n)].dataIndex=n;t.filterSelf(function(s){var l=i[t.getRawIndex(s)];return l.node1.dataIndex>=0&&l.node2.dataIndex>=0});for(var n=0,o=i.length;n<o;n++)i[n].dataIndex=-1;for(var n=0,o=t.count();n<o;n++)i[t.getRawIndex(n)].dataIndex=n},a.prototype.clone=function(){for(var e=new a(this._directed),t=this.nodes,r=this.edges,i=0;i<t.length;i++)e.addNode(t[i].id,t[i].dataIndex);for(var i=0;i<r.length;i++){var n=r[i];e.addEdge(n.node1.id,n.node2.id,n.dataIndex)}return e},a}(),Fe=function(){function a(e,t){this.inEdges=[],this.outEdges=[],this.edges=[],this.dataIndex=-1,this.id=e??"",this.dataIndex=t??-1}return a.prototype.degree=function(){return this.edges.length},a.prototype.inDegree=function(){return this.inEdges.length},a.prototype.outDegree=function(){return this.outEdges.length},a.prototype.getModel=function(e){if(!(this.dataIndex<0)){var t=this.hostGraph,r=t.data.getItemModel(this.dataIndex);return r.getModel(e)}},a.prototype.getAdjacentDataIndices=function(){for(var e={edge:[],node:[]},t=0;t<this.edges.length;t++){var r=this.edges[t];r.dataIndex<0||(e.edge.push(r.dataIndex),e.node.push(r.node1.dataIndex,r.node2.dataIndex))}return e},a.prototype.getTrajectoryDataIndices=function(){for(var e=et(),t=et(),r=0;r<this.edges.length;r++){var i=this.edges[r];if(!(i.dataIndex<0)){e.set(i.dataIndex,!0);for(var n=[i.node1],o=[i.node2],s=0;s<n.length;){var l=n[s];s++,t.set(l.dataIndex,!0);for(var u=0;u<l.inEdges.length;u++)e.set(l.inEdges[u].dataIndex,!0),n.push(l.inEdges[u].node1)}for(s=0;s<o.length;){var c=o[s];s++,t.set(c.dataIndex,!0);for(var u=0;u<c.outEdges.length;u++)e.set(c.outEdges[u].dataIndex,!0),o.push(c.outEdges[u].node2)}}}return{edge:e.keys(),node:t.keys()}},a}(),Zv=function(){function a(e,t,r){this.dataIndex=-1,this.node1=e,this.node2=t,this.dataIndex=r??-1}return a.prototype.getModel=function(e){if(!(this.dataIndex<0)){var t=this.hostGraph,r=t.edgeData.getItemModel(this.dataIndex);return r.getModel(e)}},a.prototype.getAdjacentDataIndices=function(){return{edge:[this.dataIndex],node:[this.node1.dataIndex,this.node2.dataIndex]}},a.prototype.getTrajectoryDataIndices=function(){var e=et(),t=et();e.set(this.dataIndex,!0);for(var r=[this.node1],i=[this.node2],n=0;n<r.length;){var o=r[n];n++,t.set(o.dataIndex,!0);for(var s=0;s<o.inEdges.length;s++)e.set(o.inEdges[s].dataIndex,!0),r.push(o.inEdges[s].node1)}for(n=0;n<i.length;){var l=i[n];n++,t.set(l.dataIndex,!0);for(var s=0;s<l.outEdges.length;s++)e.set(l.outEdges[s].dataIndex,!0),i.push(l.outEdges[s].node2)}return{edge:e.keys(),node:t.keys()}},a}();function Uv(a,e){return{getValue:function(t){var r=this[a][e];return r.getStore().get(r.getDimensionIndex(t||"value"),this.dataIndex)},setVisual:function(t,r){this.dataIndex>=0&&this[a][e].setItemVisual(this.dataIndex,t,r)},getVisual:function(t){return this[a][e].getItemVisual(this.dataIndex,t)},setLayout:function(t,r){this.dataIndex>=0&&this[a][e].setItemLayout(this.dataIndex,t,r)},getLayout:function(){return this[a][e].getItemLayout(this.dataIndex)},getGraphicEl:function(){return this[a][e].getItemGraphicEl(this.dataIndex)},getRawIndex:function(){return this[a][e].getRawIndex(this.dataIndex)}}}ue(Fe,Uv("hostGraph","data"));ue(Zv,Uv("hostGraph","edgeData"));function Yv(a,e,t,r,i){for(var n=new G0(r),o=0;o<a.length;o++)n.addNode(qt(a[o].id,a[o].name,o),o);for(var s=[],l=[],u=0,o=0;o<e.length;o++){var c=e[o],h=c.source,v=c.target;n.addEdge(h,v,u)&&(l.push(c),s.push(qt(ye(c.id,null),h+" > "+v)),u++)}var f=t.get("coordinateSystem"),p;if(f==="cartesian2d"||f==="polar")p=br(a,t);else{var d=Vh.get(f),g=d?d.dimensions||[]:[];mt(g,"value")<0&&g.concat(["value"]);var y=fi(a,{coordDimensions:g,encodeDefine:t.getEncode()}).dimensions;p=new Ft(y,t),p.initData(a)}var m=new Ft(["value"],t);return m.initData(l,s),i&&i(p,m),Cv({mainData:p,struct:n,structAttr:"graph",datas:{node:p,edge:m},datasAttr:{node:"data",edge:"edgeData"}}),n.update(),n}var F0=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.init=function(t){a.prototype.init.apply(this,arguments);var r=this;function i(){return r._categoriesData}this.legendVisualProvider=new pa(i,i),this.fillDataTextStyle(t.edges||t.links),this._updateCategoriesData()},e.prototype.mergeOption=function(t){a.prototype.mergeOption.apply(this,arguments),this.fillDataTextStyle(t.edges||t.links),this._updateCategoriesData()},e.prototype.mergeDefaultAndTheme=function(t){a.prototype.mergeDefaultAndTheme.apply(this,arguments),oa(t,"edgeLabel",["show"])},e.prototype.getInitialData=function(t,r){var i=t.edges||t.links||[],n=t.data||t.nodes||[],o=this;{C0(this);var s=Yv(n,i,this,!0,l);return D(s.edges,function(u){I0(u.node1,u.node2,this,u.dataIndex)},this),s.data}function l(u,c){u.wrapMethod("getItemModel",function(p){var d=o._categoriesModels,g=p.getShallow("category"),y=d[g];return y&&(y.parentModel=p.parentModel,p.parentModel=y),p});var h=Ut.prototype.getModel;function v(p,d){var g=h.call(this,p,d);return g.resolveParentPath=f,g}c.wrapMethod("getItemModel",function(p){return p.resolveParentPath=f,p.getModel=v,p});function f(p){if(p&&(p[0]==="label"||p[1]==="label")){var d=p.slice();return p[0]==="label"?d[0]="edgeLabel":p[1]==="label"&&(d[1]="edgeLabel"),d}return p}}},e.prototype.getGraph=function(){return this.getData().graph},e.prototype.getEdgeData=function(){return this.getGraph().edgeData},e.prototype.getCategoriesData=function(){return this._categoriesData},e.prototype.formatTooltip=function(t,r,i){if(i==="edge"){var n=this.getData(),o=this.getDataParams(t,i),s=n.graph.getEdgeByIndex(t),l=n.getName(s.node1.dataIndex),u=n.getName(s.node2.dataIndex),c=[];return l!=null&&c.push(l),u!=null&&c.push(u),Zt("nameValue",{name:c.join(" > "),value:o.value,noValue:o.value==null})}var h=ud({series:this,dataIndex:t,multipleSeries:r});return h},e.prototype._updateCategoriesData=function(){var t=G(this.option.categories||[],function(i){return i.value!=null?i:F({value:0},i)}),r=new Ft(["value"],this);r.initData(t),this._categoriesData=r,this._categoriesModels=r.mapArray(function(i){return r.getItemModel(i)})},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.isAnimationEnabled=function(){return a.prototype.isAnimationEnabled.call(this)&&!(this.get("layout")==="force"&&this.get(["force","layoutAnimation"]))},e.type="series.graph",e.dependencies=["grid","polar","geo","singleAxis","calendar"],e.defaultOption={z:2,coordinateSystem:"view",legendHoverLink:!0,layout:null,circular:{rotateLabel:!1},force:{initLayout:null,repulsion:[0,50],gravity:.1,friction:.6,edgeLength:30,layoutAnimation:!0},left:"center",top:"center",symbol:"circle",symbolSize:10,edgeSymbol:["none","none"],edgeSymbolSize:10,edgeLabel:{position:"middle",distance:5},draggable:!1,roam:!1,center:null,zoom:1,nodeScaleRatio:.6,label:{show:!1,formatter:"{b}"},itemStyle:{},lineStyle:{color:"#aaa",width:1,opacity:.5},emphasis:{scale:!0,label:{show:!0}},select:{itemStyle:{borderColor:"#212121"}}},e}(Ct),H0={type:"graphRoam",event:"graphRoam",update:"none"};function W0(a){a.registerChartView(B0),a.registerSeriesModel(F0),a.registerProcessor(_0),a.registerVisual(w0),a.registerVisual(A0),a.registerLayout(L0),a.registerLayout(a.PRIORITY.VISUAL.POST_CHART_LAYOUT,P0),a.registerLayout(E0),a.registerCoordinateSystem("graphView",{dimensions:ga.dimensions,create:V0}),a.registerAction({type:"focusNodeAdjacency",event:"focusNodeAdjacency",update:"series:focusNodeAdjacency"},Pe),a.registerAction({type:"unfocusNodeAdjacency",event:"unfocusNodeAdjacency",update:"series:unfocusNodeAdjacency"},Pe),a.registerAction(H0,function(e,t,r){t.eachComponent({mainType:"series",query:e},function(i){var n=i.coordinateSystem,o=Jo(n,e,void 0,r);i.setCenter&&i.setCenter(o.center),i.setZoom&&i.setZoom(o.zoom)})})}var Z0=function(){function a(){this.angle=0,this.width=10,this.r=10,this.x=0,this.y=0}return a}(),U0=function(a){E(e,a);function e(t){var r=a.call(this,t)||this;return r.type="pointer",r}return e.prototype.getDefaultShape=function(){return new Z0},e.prototype.buildPath=function(t,r){var i=Math.cos,n=Math.sin,o=r.r,s=r.width,l=r.angle,u=r.x-i(l)*s*(s>=o/3?1:2),c=r.y-n(l)*s*(s>=o/3?1:2);l=r.angle-Math.PI/2,t.moveTo(u,c),t.lineTo(r.x+i(l)*s,r.y+n(l)*s),t.lineTo(r.x+i(r.angle)*o,r.y+n(r.angle)*o),t.lineTo(r.x-i(l)*s,r.y-n(l)*s),t.lineTo(u,c)},e}(ie);function Y0(a,e){var t=a.get("center"),r=e.getWidth(),i=e.getHeight(),n=Math.min(r,i),o=O(t[0],e.getWidth()),s=O(t[1],e.getHeight()),l=O(a.get("radius"),n/2);return{cx:o,cy:s,r:l}}function Da(a,e){var t=a==null?"":a+"";return e&&(tt(e)?t=e.replace("{value}",t):st(e)&&(t=e(a))),t}var $0=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i){this.group.removeAll();var n=t.get(["axisLine","lineStyle","color"]),o=Y0(t,i);this._renderMain(t,r,i,n,o),this._data=t.getData()},e.prototype.dispose=function(){},e.prototype._renderMain=function(t,r,i,n,o){var s=this.group,l=t.get("clockwise"),u=-t.get("startAngle")/180*Math.PI,c=-t.get("endAngle")/180*Math.PI,h=t.getModel("axisLine"),v=h.get("roundCap"),f=v?$s:Ye,p=h.get("show"),d=h.getModel("lineStyle"),g=d.get("width"),y=[u,c];gh(y,!l),u=y[0],c=y[1];for(var m=c-u,S=u,x=[],b=0;p&&b<n.length;b++){var _=Math.min(Math.max(n[b][0],0),1);c=u+m*_;var w=new f({shape:{startAngle:S,endAngle:c,cx:o.cx,cy:o.cy,clockwise:l,r0:o.r-g,r:o.r},silent:!0});w.setStyle({fill:n[b][1]}),w.setStyle(d.getLineStyle(["color","width"])),x.push(w),S=c}x.reverse(),D(x,function(C){return s.add(C)});var A=function(C){if(C<=0)return n[0][1];var T;for(T=0;T<n.length;T++)if(n[T][0]>=C&&(T===0?0:n[T-1][0])<C)return n[T][1];return n[T-1][1]};this._renderTicks(t,r,i,A,o,u,c,l,g),this._renderTitleAndDetail(t,r,i,A,o),this._renderAnchor(t,o),this._renderPointer(t,r,i,A,o,u,c,l,g)},e.prototype._renderTicks=function(t,r,i,n,o,s,l,u,c){for(var h=this.group,v=o.cx,f=o.cy,p=o.r,d=+t.get("min"),g=+t.get("max"),y=t.getModel("splitLine"),m=t.getModel("axisTick"),S=t.getModel("axisLabel"),x=t.get("splitNumber"),b=m.get("splitNumber"),_=O(y.get("length"),p),w=O(m.get("length"),p),A=s,C=(l-s)/x,T=C/b,I=y.getModel("lineStyle").getLineStyle(),L=m.getModel("lineStyle").getLineStyle(),M=y.get("distance"),R,P,k=0;k<=x;k++){if(R=Math.cos(A),P=Math.sin(A),y.get("show")){var V=M?M+c:c,N=new oe({shape:{x1:R*(p-V)+v,y1:P*(p-V)+f,x2:R*(p-_-V)+v,y2:P*(p-_-V)+f},style:I,silent:!0});I.stroke==="auto"&&N.setStyle({stroke:n(k/x)}),h.add(N)}if(S.get("show")){var V=S.get("distance")+M,z=Da(Ro(k/x*(g-d)+d),S.get("formatter")),H=n(k/x),Z=R*(p-_-V)+v,Y=P*(p-_-V)+f,Q=S.get("rotate"),j=0;Q==="radial"?(j=-A+2*Math.PI,j>Math.PI/2&&(j+=Math.PI)):Q==="tangential"?j=-A-Math.PI/2:jt(Q)&&(j=Q*Math.PI/180),j===0?h.add(new ut({style:yt(S,{text:z,x:Z,y:Y,verticalAlign:P<-.8?"top":P>.8?"bottom":"middle",align:R<-.4?"left":R>.4?"right":"center"},{inheritColor:H}),silent:!0})):h.add(new ut({style:yt(S,{text:z,x:Z,y:Y,verticalAlign:"middle",align:"center"},{inheritColor:H}),silent:!0,originX:Z,originY:Y,rotation:j}))}if(m.get("show")&&k!==x){var V=m.get("distance");V=V?V+c:c;for(var rt=0;rt<=b;rt++){R=Math.cos(A),P=Math.sin(A);var It=new oe({shape:{x1:R*(p-V)+v,y1:P*(p-V)+f,x2:R*(p-w-V)+v,y2:P*(p-w-V)+f},silent:!0,style:L});L.stroke==="auto"&&It.setStyle({stroke:n((k+rt/b)/x)}),h.add(It),A+=T}A-=T}else A+=C}},e.prototype._renderPointer=function(t,r,i,n,o,s,l,u,c){var h=this.group,v=this._data,f=this._progressEls,p=[],d=t.get(["pointer","show"]),g=t.getModel("progress"),y=g.get("show"),m=t.getData(),S=m.mapDimension("value"),x=+t.get("min"),b=+t.get("max"),_=[x,b],w=[s,l];function A(T,I){var L=m.getItemModel(T),M=L.getModel("pointer"),R=O(M.get("width"),o.r),P=O(M.get("length"),o.r),k=t.get(["pointer","icon"]),V=M.get("offsetCenter"),N=O(V[0],o.r),z=O(V[1],o.r),H=M.get("keepAspect"),Z;return k?Z=Et(k,N-R/2,z-P,R,P,null,H):Z=new U0({shape:{angle:-Math.PI/2,width:R,r:P,x:N,y:z}}),Z.rotation=-(I+Math.PI/2),Z.x=o.cx,Z.y=o.cy,Z}function C(T,I){var L=g.get("roundCap"),M=L?$s:Ye,R=g.get("overlap"),P=R?g.get("width"):c/m.count(),k=R?o.r-P:o.r-(T+1)*P,V=R?o.r:o.r-T*P,N=new M({shape:{startAngle:s,endAngle:I,cx:o.cx,cy:o.cy,clockwise:u,r0:k,r:V}});return R&&(N.z2=at(m.get(S,T),[x,b],[100,0],!0)),N}(y||d)&&(m.diff(v).add(function(T){var I=m.get(S,T);if(d){var L=A(T,s);Gt(L,{rotation:-((isNaN(+I)?w[0]:at(I,_,w,!0))+Math.PI/2)},t),h.add(L),m.setItemGraphicEl(T,L)}if(y){var M=C(T,s),R=g.get("clip");Gt(M,{shape:{endAngle:at(I,_,w,R)}},t),h.add(M),Ys(t.seriesIndex,m.dataType,T,M),p[T]=M}}).update(function(T,I){var L=m.get(S,T);if(d){var M=v.getItemGraphicEl(I),R=M?M.rotation:s,P=A(T,R);P.rotation=R,dt(P,{rotation:-((isNaN(+L)?w[0]:at(L,_,w,!0))+Math.PI/2)},t),h.add(P),m.setItemGraphicEl(T,P)}if(y){var k=f[I],V=k?k.shape.endAngle:s,N=C(T,V),z=g.get("clip");dt(N,{shape:{endAngle:at(L,_,w,z)}},t),h.add(N),Ys(t.seriesIndex,m.dataType,T,N),p[T]=N}}).execute(),m.each(function(T){var I=m.getItemModel(T),L=I.getModel("emphasis"),M=L.get("focus"),R=L.get("blurScope"),P=L.get("disabled");if(d){var k=m.getItemGraphicEl(T),V=m.getItemVisual(T,"style"),N=V.fill;if(k instanceof pe){var z=k.style;k.useStyle(F({image:z.image,x:z.x,y:z.y,width:z.width,height:z.height},V))}else k.useStyle(V),k.type!=="pointer"&&k.setColor(N);k.setStyle(I.getModel(["pointer","itemStyle"]).getItemStyle()),k.style.fill==="auto"&&k.setStyle("fill",n(at(m.get(S,T),_,[0,1],!0))),k.z2EmphasisLift=0,Ht(k,I),xt(k,M,R,P)}if(y){var H=p[T];H.useStyle(m.getItemVisual(T,"style")),H.setStyle(I.getModel(["progress","itemStyle"]).getItemStyle()),H.z2EmphasisLift=0,Ht(H,I),xt(H,M,R,P)}}),this._progressEls=p)},e.prototype._renderAnchor=function(t,r){var i=t.getModel("anchor"),n=i.get("show");if(n){var o=i.get("size"),s=i.get("icon"),l=i.get("offsetCenter"),u=i.get("keepAspect"),c=Et(s,r.cx-o/2+O(l[0],r.r),r.cy-o/2+O(l[1],r.r),o,o,null,u);c.z2=i.get("showAbove")?1:0,c.setStyle(i.getModel("itemStyle").getItemStyle()),this.group.add(c)}},e.prototype._renderTitleAndDetail=function(t,r,i,n,o){var s=this,l=t.getData(),u=l.mapDimension("value"),c=+t.get("min"),h=+t.get("max"),v=new X,f=[],p=[],d=t.isAnimationEnabled(),g=t.get(["pointer","showAbove"]);l.diff(this._data).add(function(y){f[y]=new ut({silent:!0}),p[y]=new ut({silent:!0})}).update(function(y,m){f[y]=s._titleEls[m],p[y]=s._detailEls[m]}).execute(),l.each(function(y){var m=l.getItemModel(y),S=l.get(u,y),x=new X,b=n(at(S,[c,h],[0,1],!0)),_=m.getModel("title");if(_.get("show")){var w=_.get("offsetCenter"),A=o.cx+O(w[0],o.r),C=o.cy+O(w[1],o.r),T=f[y];T.attr({z2:g?0:2,style:yt(_,{x:A,y:C,text:l.getName(y),align:"center",verticalAlign:"middle"},{inheritColor:b})}),x.add(T)}var I=m.getModel("detail");if(I.get("show")){var L=I.get("offsetCenter"),M=o.cx+O(L[0],o.r),R=o.cy+O(L[1],o.r),P=O(I.get("width"),o.r),k=O(I.get("height"),o.r),V=t.get(["progress","show"])?l.getItemVisual(y,"style").fill:b,T=p[y],N=I.get("formatter");T.attr({z2:g?0:2,style:yt(I,{x:M,y:R,text:Da(S,N),width:isNaN(P)?null:P,height:isNaN(k)?null:k,align:"center",verticalAlign:"middle"},{inheritColor:V})}),cd(T,{normal:I},S,function(H){return Da(H,N)}),d&&hd(T,y,l,t,{getFormattedLabel:function(H,Z,Y,Q,j,rt){return Da(rt?rt.interpolatedValue:S,N)}}),x.add(T)}v.add(x)}),this.group.add(v),this._titleEls=f,this._detailEls=p},e.type="gauge",e}(bt),X0=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.visualStyleAccessPath="itemStyle",t}return e.prototype.getInitialData=function(t,r){return Tr(this,["value"])},e.type="series.gauge",e.defaultOption={z:2,colorBy:"data",center:["50%","50%"],legendHoverLink:!0,radius:"75%",startAngle:225,endAngle:-45,clockwise:!0,min:0,max:100,splitNumber:10,axisLine:{show:!0,roundCap:!1,lineStyle:{color:[[1,"#E6EBF8"]],width:10}},progress:{show:!1,overlap:!0,width:10,roundCap:!1,clip:!0},splitLine:{show:!0,length:10,distance:10,lineStyle:{color:"#63677A",width:3,type:"solid"}},axisTick:{show:!0,splitNumber:5,length:6,distance:10,lineStyle:{color:"#63677A",width:1,type:"solid"}},axisLabel:{show:!0,distance:15,color:"#464646",fontSize:12,rotate:0},pointer:{icon:null,offsetCenter:[0,0],show:!0,showAbove:!0,length:"60%",width:6,keepAspect:!1},anchor:{show:!1,showAbove:!1,size:6,icon:"circle",offsetCenter:[0,0],keepAspect:!1,itemStyle:{color:"#fff",borderWidth:0,borderColor:"#5470c6"}},title:{show:!0,offsetCenter:[0,"20%"],color:"#464646",fontSize:16,valueAnimation:!1},detail:{show:!0,backgroundColor:"rgba(0,0,0,0)",borderWidth:0,borderColor:"#ccc",width:100,height:null,padding:[5,10],offsetCenter:[0,"40%"],color:"#464646",fontSize:30,fontWeight:"bold",lineHeight:30,valueAnimation:!1}},e}(Ct);function K0(a){a.registerChartView($0),a.registerSeriesModel(X0)}var q0=["itemStyle","opacity"],j0=function(a){E(e,a);function e(t,r){var i=a.call(this)||this,n=i,o=new le,s=new ut;return n.setTextContent(s),i.setTextGuideLine(o),i.updateData(t,r,!0),i}return e.prototype.updateData=function(t,r,i){var n=this,o=t.hostModel,s=t.getItemModel(r),l=t.getItemLayout(r),u=s.getModel("emphasis"),c=s.get(q0);c=c??1,i||xe(n),n.useStyle(t.getItemVisual(r,"style")),n.style.lineJoin="round",i?(n.setShape({points:l.points}),n.style.opacity=0,Gt(n,{style:{opacity:c}},o,r)):dt(n,{style:{opacity:c},shape:{points:l.points}},o,r),Ht(n,s),this._updateLabel(t,r),xt(this,u.get("focus"),u.get("blurScope"),u.get("disabled"))},e.prototype._updateLabel=function(t,r){var i=this,n=this.getTextGuideLine(),o=i.getTextContent(),s=t.hostModel,l=t.getItemModel(r),u=t.getItemLayout(r),c=u.label,h=t.getItemVisual(r,"style"),v=h.fill;Wt(o,zt(l),{labelFetcher:t.hostModel,labelDataIndex:r,defaultOpacity:h.opacity,defaultText:t.getName(r)},{normal:{align:c.textAlign,verticalAlign:c.verticalAlign}}),i.setTextConfig({local:!0,inside:!!c.inside,insideStroke:v,outsideFill:v});var f=c.linePoints;n.setShape({points:f}),i.textGuideLineConfig={anchor:f?new Za(f[0][0],f[0][1]):null},dt(o,{style:{x:c.x,y:c.y}},s,r),o.attr({rotation:c.rotation,originX:c.x,originY:c.y,z2:10}),yh(i,mh(l),{stroke:v})},e}(se),J0=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.ignoreLabelLineUpdate=!0,t}return e.prototype.render=function(t,r,i){var n=t.getData(),o=this._data,s=this.group;n.diff(o).add(function(l){var u=new j0(n,l);n.setItemGraphicEl(l,u),s.add(u)}).update(function(l,u){var c=o.getItemGraphicEl(u);c.updateData(n,l),s.add(c),n.setItemGraphicEl(l,c)}).remove(function(l){var u=o.getItemGraphicEl(l);Sh(u,t,l)}).execute(),this._data=n},e.prototype.remove=function(){this.group.removeAll(),this._data=null},e.prototype.dispose=function(){},e.type="funnel",e}(bt),Q0=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t){a.prototype.init.apply(this,arguments),this.legendVisualProvider=new pa(W(this.getData,this),W(this.getRawData,this)),this._defaultLabelLine(t)},e.prototype.getInitialData=function(t,r){return Tr(this,{coordDimensions:["value"],encodeDefaulter:q(Lo,this)})},e.prototype._defaultLabelLine=function(t){oa(t,"labelLine",["show"]);var r=t.labelLine,i=t.emphasis.labelLine;r.show=r.show&&t.label.show,i.show=i.show&&t.emphasis.label.show},e.prototype.getDataParams=function(t){var r=this.getData(),i=a.prototype.getDataParams.call(this,t),n=r.mapDimension("value"),o=r.getSum(n);return i.percent=o?+(r.get(n,t)/o*100).toFixed(2):0,i.$vars.push("percent"),i},e.type="series.funnel",e.defaultOption={z:2,legendHoverLink:!0,colorBy:"data",left:80,top:60,right:80,bottom:60,minSize:"0%",maxSize:"100%",sort:"descending",orient:"vertical",gap:0,funnelAlign:"center",label:{show:!0,position:"outer"},labelLine:{show:!0,length:20,lineStyle:{width:1}},itemStyle:{borderColor:"#fff",borderWidth:1},emphasis:{label:{show:!0}},select:{itemStyle:{borderColor:"#212121"}}},e}(Ct);function tS(a,e){return Rt(a.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function eS(a,e){for(var t=a.mapDimension("value"),r=a.mapArray(t,function(l){return l}),i=[],n=e==="ascending",o=0,s=a.count();o<s;o++)i[o]=o;return st(e)?i.sort(e):e!=="none"&&i.sort(function(l,u){return n?r[l]-r[u]:r[u]-r[l]}),i}function rS(a){var e=a.hostModel,t=e.get("orient");a.each(function(r){var i=a.getItemModel(r),n=i.getModel("label"),o=n.get("position"),s=i.getModel("labelLine"),l=a.getItemLayout(r),u=l.points,c=o==="inner"||o==="inside"||o==="center"||o==="insideLeft"||o==="insideRight",h,v,f,p;if(c)o==="insideLeft"?(v=(u[0][0]+u[3][0])/2+5,f=(u[0][1]+u[3][1])/2,h="left"):o==="insideRight"?(v=(u[1][0]+u[2][0])/2-5,f=(u[1][1]+u[2][1])/2,h="right"):(v=(u[0][0]+u[1][0]+u[2][0]+u[3][0])/4,f=(u[0][1]+u[1][1]+u[2][1]+u[3][1])/4,h="center"),p=[[v,f],[v,f]];else{var d=void 0,g=void 0,y=void 0,m=void 0,S=s.get("length");o==="left"?(d=(u[3][0]+u[0][0])/2,g=(u[3][1]+u[0][1])/2,y=d-S,v=y-5,h="right"):o==="right"?(d=(u[1][0]+u[2][0])/2,g=(u[1][1]+u[2][1])/2,y=d+S,v=y+5,h="left"):o==="top"?(d=(u[3][0]+u[0][0])/2,g=(u[3][1]+u[0][1])/2,m=g-S,f=m-5,h="center"):o==="bottom"?(d=(u[1][0]+u[2][0])/2,g=(u[1][1]+u[2][1])/2,m=g+S,f=m+5,h="center"):o==="rightTop"?(d=t==="horizontal"?u[3][0]:u[1][0],g=t==="horizontal"?u[3][1]:u[1][1],t==="horizontal"?(m=g-S,f=m-5,h="center"):(y=d+S,v=y+5,h="top")):o==="rightBottom"?(d=u[2][0],g=u[2][1],t==="horizontal"?(m=g+S,f=m+5,h="center"):(y=d+S,v=y+5,h="bottom")):o==="leftTop"?(d=u[0][0],g=t==="horizontal"?u[0][1]:u[1][1],t==="horizontal"?(m=g-S,f=m-5,h="center"):(y=d-S,v=y-5,h="right")):o==="leftBottom"?(d=t==="horizontal"?u[1][0]:u[3][0],g=t==="horizontal"?u[1][1]:u[2][1],t==="horizontal"?(m=g+S,f=m+5,h="center"):(y=d-S,v=y-5,h="right")):(d=(u[1][0]+u[2][0])/2,g=(u[1][1]+u[2][1])/2,t==="horizontal"?(m=g+S,f=m+5,h="center"):(y=d+S,v=y+5,h="left")),t==="horizontal"?(y=d,v=y):(m=g,f=m),p=[[d,g],[y,m]]}l.label={linePoints:p,x:v,y:f,verticalAlign:"middle",textAlign:h,inside:c}})}function aS(a,e){a.eachSeriesByType("funnel",function(t){var r=t.getData(),i=r.mapDimension("value"),n=t.get("sort"),o=tS(t,e),s=t.get("orient"),l=o.width,u=o.height,c=eS(r,n),h=o.x,v=o.y,f=s==="horizontal"?[O(t.get("minSize"),u),O(t.get("maxSize"),u)]:[O(t.get("minSize"),l),O(t.get("maxSize"),l)],p=r.getDataExtent(i),d=t.get("min"),g=t.get("max");d==null&&(d=Math.min(p[0],0)),g==null&&(g=p[1]);var y=t.get("funnelAlign"),m=t.get("gap"),S=s==="horizontal"?l:u,x=(S-m*(r.count()-1))/r.count(),b=function(R,P){if(s==="horizontal"){var k=r.get(i,R)||0,V=at(k,[d,g],f,!0),N=void 0;switch(y){case"top":N=v;break;case"center":N=v+(u-V)/2;break;case"bottom":N=v+(u-V);break}return[[P,N],[P,N+V]]}var z=r.get(i,R)||0,H=at(z,[d,g],f,!0),Z;switch(y){case"left":Z=h;break;case"center":Z=h+(l-H)/2;break;case"right":Z=h+l-H;break}return[[Z,P],[Z+H,P]]};n==="ascending"&&(x=-x,m=-m,s==="horizontal"?h+=l:v+=u,c=c.reverse());for(var _=0;_<c.length;_++){var w=c[_],A=c[_+1],C=r.getItemModel(w);if(s==="horizontal"){var T=C.get(["itemStyle","width"]);T==null?T=x:(T=O(T,l),n==="ascending"&&(T=-T));var I=b(w,h),L=b(A,h+T);h+=T+m,r.setItemLayout(w,{points:I.concat(L.slice().reverse())})}else{var M=C.get(["itemStyle","height"]);M==null?M=x:(M=O(M,u),n==="ascending"&&(M=-M));var I=b(w,v),L=b(A,v+M);v+=M+m,r.setItemLayout(w,{points:I.concat(L.slice().reverse())})}}rS(r)})}function iS(a){a.registerChartView(J0),a.registerSeriesModel(Q0),a.registerLayout(aS),a.registerProcessor(fa("funnel"))}var nS=.3,oS=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._dataGroup=new X,t._initialized=!1,t}return e.prototype.init=function(){this.group.add(this._dataGroup)},e.prototype.render=function(t,r,i,n){this._progressiveEls=null;var o=this._dataGroup,s=t.getData(),l=this._data,u=t.coordinateSystem,c=u.dimensions,h=yu(t);s.diff(l).add(v).update(f).remove(p).execute();function v(g){var y=gu(s,o,g,c,u);un(y,s,g,h)}function f(g,y){var m=l.getItemGraphicEl(y),S=$v(s,g,c,u);s.setItemGraphicEl(g,m),dt(m,{shape:{points:S}},t,g),xe(m),un(m,s,g,h)}function p(g){var y=l.getItemGraphicEl(g);o.remove(y)}if(!this._initialized){this._initialized=!0;var d=sS(u,t,function(){setTimeout(function(){o.removeClipPath()})});o.setClipPath(d)}this._data=s},e.prototype.incrementalPrepareRender=function(t,r,i){this._initialized=!0,this._data=null,this._dataGroup.removeAll()},e.prototype.incrementalRender=function(t,r,i){for(var n=r.getData(),o=r.coordinateSystem,s=o.dimensions,l=yu(r),u=this._progressiveEls=[],c=t.start;c<t.end;c++){var h=gu(n,this._dataGroup,c,s,o);h.incremental=!0,un(h,n,c,l),u.push(h)}},e.prototype.remove=function(){this._dataGroup&&this._dataGroup.removeAll(),this._data=null},e.type="parallel",e}(bt);function sS(a,e,t){var r=a.model,i=a.getRect(),n=new pt({shape:{x:i.x,y:i.y,width:i.width,height:i.height}}),o=r.get("layout")==="horizontal"?"width":"height";return n.setShape(o,0),Gt(n,{shape:{width:i.width,height:i.height}},e,t),n}function $v(a,e,t,r){for(var i=[],n=0;n<t.length;n++){var o=t[n],s=a.get(a.mapDimension(o),e);lS(s,r.getAxis(o).type)||i.push(r.dataToPoint(s,o))}return i}function gu(a,e,t,r,i){var n=$v(a,t,r,i),o=new le({shape:{points:n},z2:10});return e.add(o),a.setItemGraphicEl(t,o),o}function yu(a){var e=a.get("smooth",!0);return e===!0&&(e=nS),e=vd(e),fd(e)&&(e=0),{smooth:e}}function un(a,e,t,r){a.useStyle(e.getItemVisual(t,"style")),a.style.fill=null,a.setShape("smooth",r.smooth);var i=e.getItemModel(t),n=i.getModel("emphasis");Ht(a,i,"lineStyle"),xt(a,n.get("focus"),n.get("blurScope"),n.get("disabled"))}function lS(a,e){return e==="category"?a==null:a==null||isNaN(a)}var uS=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.visualStyleAccessPath="lineStyle",t.visualDrawType="stroke",t}return e.prototype.getInitialData=function(t,r){return br(null,this,{useEncodeDefaulter:W(cS,null,this)})},e.prototype.getRawIndicesByActiveState=function(t){var r=this.coordinateSystem,i=this.getData(),n=[];return r.eachActiveState(i,function(o,s){t===o&&n.push(i.getRawIndex(s))}),n},e.type="series.parallel",e.dependencies=["parallel"],e.defaultOption={z:2,coordinateSystem:"parallel",parallelIndex:0,label:{show:!1},inactiveOpacity:.05,activeOpacity:1,lineStyle:{width:1,opacity:.45,type:"solid"},emphasis:{label:{show:!1}},progressive:500,smooth:!1,animationEasing:"linear"},e}(Ct);function cS(a){var e=a.ecModel.getComponent("parallel",a.get("parallelIndex"));if(e){var t={};return D(e.dimensions,function(r){var i=hS(r);t[r]=i}),t}}function hS(a){return+a.replace("dim","")}var vS=["lineStyle","opacity"],fS={seriesType:"parallel",reset:function(a,e){var t=a.coordinateSystem,r={normal:a.get(["lineStyle","opacity"]),active:a.get("activeOpacity"),inactive:a.get("inactiveOpacity")};return{progress:function(i,n){t.eachActiveState(n,function(o,s){var l=r[o];if(o==="normal"&&n.hasItemOption){var u=n.getItemModel(s).get(vS,!0);u!=null&&(l=u)}var c=n.ensureUniqueItemVisual(s,"style");c.opacity=l},i.start,i.end)}}}};function pS(a){dS(a),gS(a)}function dS(a){if(!a.parallel){var e=!1;D(a.series,function(t){t&&t.type==="parallel"&&(e=!0)}),e&&(a.parallel=[{}])}}function gS(a){var e=be(a.parallelAxis);D(e,function(t){if(Nt(t)){var r=t.parallelIndex||0,i=be(a.parallel)[r];i&&i.parallelAxisDefault&&ft(t,i.parallelAxisDefault,!1)}})}var yS=5,mS=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i){this._model=t,this._api=i,this._handlers||(this._handlers={},D(SS,function(n,o){i.getZr().on(o,this._handlers[o]=W(n,this))},this)),wi(this,"_throttledDispatchExpand",t.get("axisExpandRate"),"fixRate")},e.prototype.dispose=function(t,r){Nh(this,"_throttledDispatchExpand"),D(this._handlers,function(i,n){r.getZr().off(n,i)}),this._handlers=null},e.prototype._throttledDispatchExpand=function(t){this._dispatchExpand(t)},e.prototype._dispatchExpand=function(t){t&&this._api.dispatchAction(F({type:"parallelAxisExpand"},t))},e.type="parallel",e}(Ot),SS={mousedown:function(a){cn(this,"click")&&(this._mouseDownPoint=[a.offsetX,a.offsetY])},mouseup:function(a){var e=this._mouseDownPoint;if(cn(this,"click")&&e){var t=[a.offsetX,a.offsetY],r=Math.pow(e[0]-t[0],2)+Math.pow(e[1]-t[1],2);if(r>yS)return;var i=this._model.coordinateSystem.getSlidedAxisExpandWindow([a.offsetX,a.offsetY]);i.behavior!=="none"&&this._dispatchExpand({axisExpandWindow:i.axisExpandWindow})}this._mouseDownPoint=null},mousemove:function(a){if(!(this._mouseDownPoint||!cn(this,"mousemove"))){var e=this._model,t=e.coordinateSystem.getSlidedAxisExpandWindow([a.offsetX,a.offsetY]),r=t.behavior;r==="jump"&&this._throttledDispatchExpand.debounceNextCall(e.get("axisExpandDebounce")),this._throttledDispatchExpand(r==="none"?null:{axisExpandWindow:t.axisExpandWindow,animation:r==="jump"?null:{duration:0}})}}};function cn(a,e){var t=a._model;return t.get("axisExpandable")&&t.get("axisExpandTriggerOn")===e}var xS=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(){a.prototype.init.apply(this,arguments),this.mergeOption({})},e.prototype.mergeOption=function(t){var r=this.option;t&&ft(r,t,!0),this._initDimensions()},e.prototype.contains=function(t,r){var i=t.get("parallelIndex");return i!=null&&r.getComponent("parallel",i)===this},e.prototype.setAxisExpand=function(t){D(["axisExpandable","axisExpandCenter","axisExpandCount","axisExpandWidth","axisExpandWindow"],function(r){t.hasOwnProperty(r)&&(this.option[r]=t[r])},this)},e.prototype._initDimensions=function(){var t=this.dimensions=[],r=this.parallelAxisIndex=[],i=Yt(this.ecModel.queryComponents({mainType:"parallelAxis"}),function(n){return(n.get("parallelIndex")||0)===this.componentIndex},this);D(i,function(n){t.push("dim"+n.get("dim")),r.push(n.componentIndex)})},e.type="parallel",e.dependencies=["parallelAxis"],e.layoutMode="box",e.defaultOption={z:0,left:80,top:60,right:80,bottom:60,layout:"horizontal",axisExpandable:!1,axisExpandCenter:null,axisExpandCount:0,axisExpandWidth:50,axisExpandRate:17,axisExpandDebounce:50,axisExpandSlideTriggerArea:[-.15,.05,.4],axisExpandTriggerOn:"click",parallelAxisDefault:null},e}(kt),bS=function(a){E(e,a);function e(t,r,i,n,o){var s=a.call(this,t,r,i)||this;return s.type=n||"value",s.axisIndex=o,s}return e.prototype.isHorizontal=function(){return this.coordinateSystem.getModel().get("layout")!=="horizontal"},e}(de);function Qe(a,e,t,r,i,n){a=a||0;var o=t[1]-t[0];if(i!=null&&(i=ir(i,[0,o])),n!=null&&(n=Math.max(n,i??0)),r==="all"){var s=Math.abs(e[1]-e[0]);s=ir(s,[0,o]),i=n=ir(s,[i,n]),r=0}e[0]=ir(e[0],t),e[1]=ir(e[1],t);var l=hn(e,r);e[r]+=a;var u=i||0,c=t.slice();l.sign<0?c[0]+=u:c[1]-=u,e[r]=ir(e[r],c);var h;return h=hn(e,r),i!=null&&(h.sign!==l.sign||h.span<i)&&(e[1-r]=e[r]+l.sign*i),h=hn(e,r),n!=null&&h.span>n&&(e[1-r]=e[r]+h.sign*n),e}function hn(a,e){var t=a[e]-a[1-e];return{span:Math.abs(t),sign:t>0?-1:t<0?1:e?-1:1}}function ir(a,e){return Math.min(e[1]!=null?e[1]:1/0,Math.max(e[0]!=null?e[0]:-1/0,a))}var vn=D,Xv=Math.min,Kv=Math.max,mu=Math.floor,_S=Math.ceil,Su=Ro,wS=Math.PI,AS=function(){function a(e,t,r){this.type="parallel",this._axesMap=et(),this._axesLayout={},this.dimensions=e.dimensions,this._model=e,this._init(e,t,r)}return a.prototype._init=function(e,t,r){var i=e.dimensions,n=e.parallelAxisIndex;vn(i,function(o,s){var l=n[s],u=t.getComponent("parallelAxis",l),c=this._axesMap.set(o,new bS(o,Eo(u),[0,0],u.get("type"),l)),h=c.type==="category";c.onBand=h&&u.get("boundaryGap"),c.inverse=u.get("inverse"),u.axis=c,c.model=u,c.coordinateSystem=u.coordinateSystem=this},this)},a.prototype.update=function(e,t){this._updateAxesFromSeries(this._model,e)},a.prototype.containPoint=function(e){var t=this._makeLayoutInfo(),r=t.axisBase,i=t.layoutBase,n=t.pixelDimIndex,o=e[1-n],s=e[n];return o>=r&&o<=r+t.axisLength&&s>=i&&s<=i+t.layoutLength},a.prototype.getModel=function(){return this._model},a.prototype._updateAxesFromSeries=function(e,t){t.eachSeries(function(r){if(e.contains(r,t)){var i=r.getData();vn(this.dimensions,function(n){var o=this._axesMap.get(n);o.scale.unionExtentFromData(i,i.mapDimension(n)),Xa(o.scale,o.model)},this)}},this)},a.prototype.resize=function(e,t){this._rect=Rt(e.getBoxLayoutParams(),{width:t.getWidth(),height:t.getHeight()}),this._layoutAxes()},a.prototype.getRect=function(){return this._rect},a.prototype._makeLayoutInfo=function(){var e=this._model,t=this._rect,r=["x","y"],i=["width","height"],n=e.get("layout"),o=n==="horizontal"?0:1,s=t[i[o]],l=[0,s],u=this.dimensions.length,c=Ca(e.get("axisExpandWidth"),l),h=Ca(e.get("axisExpandCount")||0,[0,u]),v=e.get("axisExpandable")&&u>3&&u>h&&h>1&&c>0&&s>0,f=e.get("axisExpandWindow"),p;if(f)p=Ca(f[1]-f[0],l),f[1]=f[0]+p;else{p=Ca(c*(h-1),l);var d=e.get("axisExpandCenter")||mu(u/2);f=[c*d-p/2],f[1]=f[0]+p}var g=(s-p)/(u-h);g<3&&(g=0);var y=[mu(Su(f[0]/c,1))+1,_S(Su(f[1]/c,1))-1],m=g/c*f[0];return{layout:n,pixelDimIndex:o,layoutBase:t[r[o]],layoutLength:s,axisBase:t[r[1-o]],axisLength:t[i[1-o]],axisExpandable:v,axisExpandWidth:c,axisCollapseWidth:g,axisExpandWindow:f,axisCount:u,winInnerIndices:y,axisExpandWindow0Pos:m}},a.prototype._layoutAxes=function(){var e=this._rect,t=this._axesMap,r=this.dimensions,i=this._makeLayoutInfo(),n=i.layout;t.each(function(o){var s=[0,i.axisLength],l=o.inverse?1:0;o.setExtent(s[l],s[1-l])}),vn(r,function(o,s){var l=(i.axisExpandable?DS:TS)(s,i),u={horizontal:{x:l.position,y:i.axisLength},vertical:{x:0,y:l.position}},c={horizontal:wS/2,vertical:0},h=[u[n].x+e.x,u[n].y+e.y],v=c[n],f=wr();mi(f,f,v),Ke(f,f,h),this._axesLayout[o]={position:h,rotation:v,transform:f,axisNameAvailableWidth:l.axisNameAvailableWidth,axisLabelShow:l.axisLabelShow,nameTruncateMaxWidth:l.nameTruncateMaxWidth,tickDirection:1,labelDirection:1}},this)},a.prototype.getAxis=function(e){return this._axesMap.get(e)},a.prototype.dataToPoint=function(e,t){return this.axisCoordToPoint(this._axesMap.get(t).dataToCoord(e),t)},a.prototype.eachActiveState=function(e,t,r,i){r==null&&(r=0),i==null&&(i=e.count());var n=this._axesMap,o=this.dimensions,s=[],l=[];D(o,function(g){s.push(e.mapDimension(g)),l.push(n.get(g).model)});for(var u=this.hasAxisBrushed(),c=r;c<i;c++){var h=void 0;if(!u)h="normal";else{h="active";for(var v=e.getValues(s,c),f=0,p=o.length;f<p;f++){var d=l[f].getActiveState(v[f]);if(d==="inactive"){h="inactive";break}}}t(h,c)}},a.prototype.hasAxisBrushed=function(){for(var e=this.dimensions,t=this._axesMap,r=!1,i=0,n=e.length;i<n;i++)t.get(e[i]).model.getActiveState()!=="normal"&&(r=!0);return r},a.prototype.axisCoordToPoint=function(e,t){var r=this._axesLayout[t];return Le([e,0],r.transform)},a.prototype.getAxisLayout=function(e){return it(this._axesLayout[e])},a.prototype.getSlidedAxisExpandWindow=function(e){var t=this._makeLayoutInfo(),r=t.pixelDimIndex,i=t.axisExpandWindow.slice(),n=i[1]-i[0],o=[0,t.axisExpandWidth*(t.axisCount-1)];if(!this.containPoint(e))return{behavior:"none",axisExpandWindow:i};var s=e[r]-t.layoutBase-t.axisExpandWindow0Pos,l,u="slide",c=t.axisCollapseWidth,h=this._model.get("axisExpandSlideTriggerArea"),v=h[0]!=null;if(c)v&&c&&s<n*h[0]?(u="jump",l=s-n*h[2]):v&&c&&s>n*(1-h[0])?(u="jump",l=s-n*(1-h[2])):(l=s-n*h[1])>=0&&(l=s-n*(1-h[1]))<=0&&(l=0),l*=t.axisExpandWidth/c,l?Qe(l,i,o,"all"):u="none";else{var f=i[1]-i[0],p=o[1]*s/f;i=[Kv(0,p-f/2)],i[1]=Xv(o[1],i[0]+f),i[0]=i[1]-f}return{axisExpandWindow:i,behavior:u}},a}();function Ca(a,e){return Xv(Kv(a,e[0]),e[1])}function TS(a,e){var t=e.layoutLength/(e.axisCount-1);return{position:t*a,axisNameAvailableWidth:t,axisLabelShow:!0}}function DS(a,e){var t=e.layoutLength,r=e.axisExpandWidth,i=e.axisCount,n=e.axisCollapseWidth,o=e.winInnerIndices,s,l=n,u=!1,c;return a<o[0]?(s=a*n,c=n):a<=o[1]?(s=e.axisExpandWindow0Pos+a*r-e.axisExpandWindow[0],l=r,u=!0):(s=t-(i-1-a)*n,c=n),{position:s,axisNameAvailableWidth:l,axisLabelShow:u,nameTruncateMaxWidth:c}}function CS(a,e){var t=[];return a.eachComponent("parallel",function(r,i){var n=new AS(r,a,e);n.name="parallel_"+i,n.resize(r,e),r.coordinateSystem=n,n.model=r,t.push(n)}),a.eachSeries(function(r){if(r.get("coordinateSystem")==="parallel"){var i=r.getReferringComponents("parallel",fe).models[0];r.coordinateSystem=i.coordinateSystem}}),t}var IS={create:CS},oo=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.activeIntervals=[],t}return e.prototype.getAreaSelectStyle=function(){return Ph([["fill","color"],["lineWidth","borderWidth"],["stroke","borderColor"],["width","width"],["opacity","opacity"]])(this.getModel("areaSelectStyle"))},e.prototype.setActiveIntervals=function(t){var r=this.activeIntervals=it(t);if(r)for(var i=r.length-1;i>=0;i--)re(r[i])},e.prototype.getActiveState=function(t){var r=this.activeIntervals;if(!r.length)return"normal";if(t==null||isNaN(+t))return"inactive";if(r.length===1){var i=r[0];if(i[0]<=t&&t<=i[1])return"active"}else for(var n=0,o=r.length;n<o;n++)if(r[n][0]<=t&&t<=r[n][1])return"active";return"inactive"},e}(kt);ue(oo,gi);var qe=!0,ra=Math.min,yr=Math.max,LS=Math.pow,MS=1e4,PS=6,RS=6,xu="globalPan",ES={w:[0,0],e:[0,1],n:[1,0],s:[1,1]},kS={w:"ew",e:"ew",n:"ns",s:"ns",ne:"nesw",sw:"nesw",nw:"nwse",se:"nwse"},bu={brushStyle:{lineWidth:2,stroke:"rgba(210,219,238,0.3)",fill:"#D2DBEE"},transformable:!0,brushMode:"single",removeOnClick:!1},VS=0,ls=function(a){E(e,a);function e(t){var r=a.call(this)||this;return r._track=[],r._covers=[],r._handlers={},r._zr=t,r.group=new X,r._uid="brushController_"+VS++,D(HS,function(i,n){this._handlers[n]=W(i,this)},r),r}return e.prototype.enableBrush=function(t){return this._brushType&&this._doDisableBrush(),t.brushType&&this._doEnableBrush(t),this},e.prototype._doEnableBrush=function(t){var r=this._zr;this._enableGlobalPan||Ty(r,xu,this._uid),D(this._handlers,function(i,n){r.on(n,i)}),this._brushType=t.brushType,this._brushOption=ft(it(bu),t,!0)},e.prototype._doDisableBrush=function(){var t=this._zr;Dy(t,xu,this._uid),D(this._handlers,function(r,i){t.off(i,r)}),this._brushType=this._brushOption=null},e.prototype.setPanels=function(t){if(t&&t.length){var r=this._panels={};D(t,function(i){r[i.panelId]=it(i)})}else this._panels=null;return this},e.prototype.mount=function(t){t=t||{},this._enableGlobalPan=t.enableGlobalPan;var r=this.group;return this._zr.add(r),r.attr({x:t.x||0,y:t.y||0,rotation:t.rotation||0,scaleX:t.scaleX||1,scaleY:t.scaleY||1}),this._transform=r.getLocalTransform(),this},e.prototype.updateCovers=function(t){t=G(t,function(v){return ft(it(bu),v,!0)});var r="\0-brush-index-",i=this._covers,n=this._covers=[],o=this,s=this._creatingCover;return new Ar(i,t,u,l).add(c).update(c).remove(h).execute(),this;function l(v,f){return(v.id!=null?v.id:r+f)+"-"+v.brushType}function u(v,f){return l(v.__brushOption,f)}function c(v,f){var p=t[v];if(f!=null&&i[f]===s)n[v]=i[f];else{var d=n[v]=f!=null?(i[f].__brushOption=p,i[f]):jv(o,qv(o,p));us(o,d)}}function h(v){i[v]!==s&&o.group.remove(i[v])}},e.prototype.unmount=function(){return this.enableBrush(!1),so(this),this._zr.remove(this.group),this},e.prototype.dispose=function(){this.unmount(),this.off()},e}(_h);function qv(a,e){var t=Ri[e.brushType].createCover(a,e);return t.__brushOption=e,Qv(t,e),a.group.add(t),t}function jv(a,e){var t=cs(e);return t.endCreating&&(t.endCreating(a,e),Qv(e,e.__brushOption)),e}function Jv(a,e){var t=e.__brushOption;cs(e).updateCoverShape(a,e,t.range,t)}function Qv(a,e){var t=e.z;t==null&&(t=MS),a.traverse(function(r){r.z=t,r.z2=t})}function us(a,e){cs(e).updateCommon(a,e),Jv(a,e)}function cs(a){return Ri[a.__brushOption.brushType]}function hs(a,e,t){var r=a._panels;if(!r)return qe;var i,n=a._transform;return D(r,function(o){o.isTargetByCursor(e,t,n)&&(i=o)}),i}function tf(a,e){var t=a._panels;if(!t)return qe;var r=e.__brushOption.panelId;return r!=null?t[r]:qe}function so(a){var e=a._covers,t=e.length;return D(e,function(r){a.group.remove(r)},a),e.length=0,!!t}function je(a,e){var t=G(a._covers,function(r){var i=r.__brushOption,n=it(i.range);return{brushType:i.brushType,panelId:i.panelId,range:n}});a.trigger("brush",{areas:t,isEnd:!!e.isEnd,removeOnClick:!!e.removeOnClick})}function NS(a){var e=a._track;if(!e.length)return!1;var t=e[e.length-1],r=e[0],i=t[0]-r[0],n=t[1]-r[1],o=LS(i*i+n*n,.5);return o>PS}function ef(a){var e=a.length-1;return e<0&&(e=0),[a[0],a[e]]}function rf(a,e,t,r){var i=new X;return i.add(new pt({name:"main",style:vs(t),silent:!0,draggable:!0,cursor:"move",drift:q(_u,a,e,i,["n","s","w","e"]),ondragend:q(je,e,{isEnd:!0})})),D(r,function(n){i.add(new pt({name:n.join(""),style:{opacity:0},draggable:!0,silent:!0,invisible:!0,drift:q(_u,a,e,i,n),ondragend:q(je,e,{isEnd:!0})}))}),i}function af(a,e,t,r){var i=r.brushStyle.lineWidth||0,n=yr(i,RS),o=t[0][0],s=t[1][0],l=o-i/2,u=s-i/2,c=t[0][1],h=t[1][1],v=c-n+i/2,f=h-n+i/2,p=c-o,d=h-s,g=p+i,y=d+i;ge(a,e,"main",o,s,p,d),r.transformable&&(ge(a,e,"w",l,u,n,y),ge(a,e,"e",v,u,n,y),ge(a,e,"n",l,u,g,n),ge(a,e,"s",l,f,g,n),ge(a,e,"nw",l,u,n,n),ge(a,e,"ne",v,u,n,n),ge(a,e,"sw",l,f,n,n),ge(a,e,"se",v,f,n,n))}function lo(a,e){var t=e.__brushOption,r=t.transformable,i=e.childAt(0);i.useStyle(vs(t)),i.attr({silent:!r,cursor:r?"move":"default"}),D([["w"],["e"],["n"],["s"],["s","e"],["s","w"],["n","e"],["n","w"]],function(n){var o=e.childOfName(n.join("")),s=n.length===1?uo(a,n[0]):OS(a,n);o&&o.attr({silent:!r,invisible:!r,cursor:r?kS[s]+"-resize":null})})}function ge(a,e,t,r,i,n,o){var s=e.childOfName(t);s&&s.setShape(GS(fs(a,e,[[r,i],[r+n,i+o]])))}function vs(a){return ot({strokeNoScale:!0},a.brushStyle)}function nf(a,e,t,r){var i=[ra(a,t),ra(e,r)],n=[yr(a,t),yr(e,r)];return[[i[0],n[0]],[i[1],n[1]]]}function zS(a){return vr(a.group)}function uo(a,e){var t={w:"left",e:"right",n:"top",s:"bottom"},r={left:"w",right:"e",top:"n",bottom:"s"},i=ko(t[e],zS(a));return r[i]}function OS(a,e){var t=[uo(a,e[0]),uo(a,e[1])];return(t[0]==="e"||t[0]==="w")&&t.reverse(),t.join("")}function _u(a,e,t,r,i,n){var o=t.__brushOption,s=a.toRectRange(o.range),l=of(e,i,n);D(r,function(u){var c=ES[u];s[c[0]][c[1]]+=l[c[0]]}),o.range=a.fromRectRange(nf(s[0][0],s[1][0],s[0][1],s[1][1])),us(e,t),je(e,{isEnd:!1})}function BS(a,e,t,r){var i=e.__brushOption.range,n=of(a,t,r);D(i,function(o){o[0]+=n[0],o[1]+=n[1]}),us(a,e),je(a,{isEnd:!1})}function of(a,e,t){var r=a.group,i=r.transformCoordToLocal(e,t),n=r.transformCoordToLocal(0,0);return[i[0]-n[0],i[1]-n[1]]}function fs(a,e,t){var r=tf(a,e);return r&&r!==qe?r.clipPath(t,a._transform):it(t)}function GS(a){var e=ra(a[0][0],a[1][0]),t=ra(a[0][1],a[1][1]),r=yr(a[0][0],a[1][0]),i=yr(a[0][1],a[1][1]);return{x:e,y:t,width:r-e,height:i-t}}function FS(a,e,t){if(!(!a._brushType||WS(a,e.offsetX,e.offsetY))){var r=a._zr,i=a._covers,n=hs(a,e,t);if(!a._dragging)for(var o=0;o<i.length;o++){var s=i[o].__brushOption;if(n&&(n===qe||s.panelId===n.panelId)&&Ri[s.brushType].contain(i[o],t[0],t[1]))return}n&&r.setCursorStyle("crosshair")}}function co(a){var e=a.event;e.preventDefault&&e.preventDefault()}function ho(a,e,t){return a.childOfName("main").contain(e,t)}function sf(a,e,t,r){var i=a._creatingCover,n=a._creatingPanel,o=a._brushOption,s;if(a._track.push(t.slice()),NS(a)||i){if(n&&!i){o.brushMode==="single"&&so(a);var l=it(o);l.brushType=wu(l.brushType,n),l.panelId=n===qe?null:n.panelId,i=a._creatingCover=qv(a,l),a._covers.push(i)}if(i){var u=Ri[wu(a._brushType,n)],c=i.__brushOption;c.range=u.getCreatingRange(fs(a,i,a._track)),r&&(jv(a,i),u.updateCommon(a,i)),Jv(a,i),s={isEnd:r}}}else r&&o.brushMode==="single"&&o.removeOnClick&&hs(a,e,t)&&so(a)&&(s={isEnd:r,removeOnClick:!0});return s}function wu(a,e){return a==="auto"?e.defaultBrushType:a}var HS={mousedown:function(a){if(this._dragging)Au(this,a);else if(!a.target||!a.target.draggable){co(a);var e=this.group.transformCoordToLocal(a.offsetX,a.offsetY);this._creatingCover=null;var t=this._creatingPanel=hs(this,a,e);t&&(this._dragging=!0,this._track=[e.slice()])}},mousemove:function(a){var e=a.offsetX,t=a.offsetY,r=this.group.transformCoordToLocal(e,t);if(FS(this,a,r),this._dragging){co(a);var i=sf(this,a,r,!1);i&&je(this,i)}},mouseup:function(a){Au(this,a)}};function Au(a,e){if(a._dragging){co(e);var t=e.offsetX,r=e.offsetY,i=a.group.transformCoordToLocal(t,r),n=sf(a,e,i,!0);a._dragging=!1,a._track=[],a._creatingCover=null,n&&je(a,n)}}function WS(a,e,t){var r=a._zr;return e<0||e>r.getWidth()||t<0||t>r.getHeight()}var Ri={lineX:Tu(0),lineY:Tu(1),rect:{createCover:function(a,e){function t(r){return r}return rf({toRectRange:t,fromRectRange:t},a,e,[["w"],["e"],["n"],["s"],["s","e"],["s","w"],["n","e"],["n","w"]])},getCreatingRange:function(a){var e=ef(a);return nf(e[1][0],e[1][1],e[0][0],e[0][1])},updateCoverShape:function(a,e,t,r){af(a,e,t,r)},updateCommon:lo,contain:ho},polygon:{createCover:function(a,e){var t=new X;return t.add(new le({name:"main",style:vs(e),silent:!0})),t},getCreatingRange:function(a){return a},endCreating:function(a,e){e.remove(e.childAt(0)),e.add(new se({name:"main",draggable:!0,drift:q(BS,a,e),ondragend:q(je,a,{isEnd:!0})}))},updateCoverShape:function(a,e,t,r){e.childAt(0).setShape({points:fs(a,e,t)})},updateCommon:lo,contain:ho}};function Tu(a){return{createCover:function(e,t){return rf({toRectRange:function(r){var i=[r,[0,100]];return a&&i.reverse(),i},fromRectRange:function(r){return r[a]}},e,t,[[["w"],["e"]],[["n"],["s"]]][a])},getCreatingRange:function(e){var t=ef(e),r=ra(t[0][a],t[1][a]),i=yr(t[0][a],t[1][a]);return[r,i]},updateCoverShape:function(e,t,r,i){var n,o=tf(e,t);if(o!==qe&&o.getLinearBrushOtherExtent)n=o.getLinearBrushOtherExtent(a);else{var s=e._zr;n=[0,[s.getWidth(),s.getHeight()][1-a]]}var l=[r,n];a&&l.reverse(),af(e,t,l,i)},updateCommon:lo,contain:ho}}function lf(a){return a=ps(a),function(e){return pd(e,a)}}function uf(a,e){return a=ps(a),function(t){var r=e??t,i=r?a.width:a.height,n=r?a.x:a.y;return[n,n+(i||0)]}}function cf(a,e,t){var r=ps(a);return function(i,n){return r.contain(n[0],n[1])&&!Ii(i,e,t)}}function ps(a){return Pt.create(a)}var ZS=["axisLine","axisTickLabel","axisName"],US=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r){a.prototype.init.apply(this,arguments),(this._brushController=new ls(r.getZr())).on("brush",W(this._onBrush,this))},e.prototype.render=function(t,r,i,n){if(!YS(t,r,n)){this.axisModel=t,this.api=i,this.group.removeAll();var o=this._axisGroup;if(this._axisGroup=new X,this.group.add(this._axisGroup),!!t.get("show")){var s=XS(t,r),l=s.coordinateSystem,u=t.getAreaSelectStyle(),c=u.width,h=t.axis.dim,v=l.getAxisLayout(h),f=F({strokeContainThreshold:c},v),p=new $e(t,f);D(ZS,p.add,p),this._axisGroup.add(p.getGroup()),this._refreshBrushController(f,u,t,s,c,i),Vo(o,this._axisGroup,t)}}},e.prototype._refreshBrushController=function(t,r,i,n,o,s){var l=i.axis.getExtent(),u=l[1]-l[0],c=Math.min(30,Math.abs(u)*.1),h=Pt.create({x:l[0],y:-o/2,width:u,height:o});h.x-=c,h.width+=2*c,this._brushController.mount({enableGlobalPan:!0,rotation:t.rotation,x:t.position[0],y:t.position[1]}).setPanels([{panelId:"pl",clipPath:lf(h),isTargetByCursor:cf(h,s,n),getLinearBrushOtherExtent:uf(h,0)}]).enableBrush({brushType:"lineX",brushStyle:r,removeOnClick:!0}).updateCovers($S(i))},e.prototype._onBrush=function(t){var r=t.areas,i=this.axisModel,n=i.axis,o=G(r,function(s){return[n.coordToData(s.range[0],!0),n.coordToData(s.range[1],!0)]});(!i.option.realtime===t.isEnd||t.removeOnClick)&&this.api.dispatchAction({type:"axisAreaSelect",parallelAxisId:i.id,intervals:o})},e.prototype.dispose=function(){this._brushController.dispose()},e.type="parallelAxis",e}(Ot);function YS(a,e,t){return t&&t.type==="axisAreaSelect"&&e.findComponents({mainType:"parallelAxis",query:t})[0]===a}function $S(a){var e=a.axis;return G(a.activeIntervals,function(t){return{brushType:"lineX",panelId:"pl",range:[e.dataToCoord(t[0],!0),e.dataToCoord(t[1],!0)]}})}function XS(a,e){return e.getComponent("parallel",a.get("parallelIndex"))}var KS={type:"axisAreaSelect",event:"axisAreaSelected"};function qS(a){a.registerAction(KS,function(e,t){t.eachComponent({mainType:"parallelAxis",query:e},function(r){r.axis.model.setActiveIntervals(e.intervals)})}),a.registerAction("parallelAxisExpand",function(e,t){t.eachComponent({mainType:"parallel",query:e},function(r){r.setAxisExpand(e)})})}var jS={type:"value",areaSelectStyle:{width:20,borderWidth:1,borderColor:"rgba(160,197,232)",color:"rgba(160,197,232)",opacity:.3},realtime:!0,z:10};function hf(a){a.registerComponentView(mS),a.registerComponentModel(xS),a.registerCoordinateSystem("parallel",IS),a.registerPreprocessor(pS),a.registerComponentModel(oo),a.registerComponentView(US),Ka(a,"parallel",oo,jS),qS(a)}function JS(a){K(hf),a.registerChartView(oS),a.registerSeriesModel(uS),a.registerVisual(a.PRIORITY.VISUAL.BRUSH,fS)}var QS=function(){function a(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.cpx2=0,this.cpy2=0,this.extent=0}return a}(),tx=function(a){E(e,a);function e(t){return a.call(this,t)||this}return e.prototype.getDefaultShape=function(){return new QS},e.prototype.buildPath=function(t,r){var i=r.extent;t.moveTo(r.x1,r.y1),t.bezierCurveTo(r.cpx1,r.cpy1,r.cpx2,r.cpy2,r.x2,r.y2),r.orient==="vertical"?(t.lineTo(r.x2+i,r.y2),t.bezierCurveTo(r.cpx2+i,r.cpy2,r.cpx1+i,r.cpy1,r.x1+i,r.y1)):(t.lineTo(r.x2,r.y2+i),t.bezierCurveTo(r.cpx2,r.cpy2+i,r.cpx1,r.cpy1+i,r.x1,r.y1+i)),t.closePath()},e.prototype.highlight=function(){$r(this)},e.prototype.downplay=function(){Xr(this)},e}(ie),ex=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._focusAdjacencyDisabled=!1,t}return e.prototype.render=function(t,r,i){var n=this,o=t.getGraph(),s=this.group,l=t.layoutInfo,u=l.width,c=l.height,h=t.getData(),v=t.getData("edge"),f=t.get("orient");this._model=t,s.removeAll(),s.x=l.x,s.y=l.y,o.eachEdge(function(p){var d=new tx,g=ht(d);g.dataIndex=p.dataIndex,g.seriesIndex=t.seriesIndex,g.dataType="edge";var y=p.getModel(),m=y.getModel("lineStyle"),S=m.get("curveness"),x=p.node1.getLayout(),b=p.node1.getModel(),_=b.get("localX"),w=b.get("localY"),A=p.node2.getLayout(),C=p.node2.getModel(),T=C.get("localX"),I=C.get("localY"),L=p.getLayout(),M,R,P,k,V,N,z,H;d.shape.extent=Math.max(1,L.dy),d.shape.orient=f,f==="vertical"?(M=(_!=null?_*u:x.x)+L.sy,R=(w!=null?w*c:x.y)+x.dy,P=(T!=null?T*u:A.x)+L.ty,k=I!=null?I*c:A.y,V=M,N=R*(1-S)+k*S,z=P,H=R*S+k*(1-S)):(M=(_!=null?_*u:x.x)+x.dx,R=(w!=null?w*c:x.y)+L.sy,P=T!=null?T*u:A.x,k=(I!=null?I*c:A.y)+L.ty,V=M*(1-S)+P*S,N=R,z=M*S+P*(1-S),H=k),d.setShape({x1:M,y1:R,x2:P,y2:k,cpx1:V,cpy1:N,cpx2:z,cpy2:H}),d.useStyle(m.getItemStyle()),Du(d.style,f,p);var Z=""+y.get("value"),Y=zt(y,"edgeLabel");Wt(d,Y,{labelFetcher:{getFormattedLabel:function(rt,It,$t,J,$,lt){return t.getFormattedLabel(rt,It,"edge",J,xr($,Y.normal&&Y.normal.get("formatter"),Z),lt)}},labelDataIndex:p.dataIndex,defaultText:Z}),d.setTextConfig({position:"inside"});var Q=y.getModel("emphasis");Ht(d,y,"lineStyle",function(rt){var It=rt.getItemStyle();return Du(It,f,p),It}),s.add(d),v.setItemGraphicEl(p.dataIndex,d);var j=Q.get("focus");xt(d,j==="adjacency"?p.getAdjacentDataIndices():j==="trajectory"?p.getTrajectoryDataIndices():j,Q.get("blurScope"),Q.get("disabled"))}),o.eachNode(function(p){var d=p.getLayout(),g=p.getModel(),y=g.get("localX"),m=g.get("localY"),S=g.getModel("emphasis"),x=g.get(["itemStyle","borderRadius"])||0,b=new pt({shape:{x:y!=null?y*u:d.x,y:m!=null?m*c:d.y,width:d.dx,height:d.dy,r:x},style:g.getModel("itemStyle").getItemStyle(),z2:10});Wt(b,zt(g),{labelFetcher:{getFormattedLabel:function(w,A){return t.getFormattedLabel(w,A,"node")}},labelDataIndex:p.dataIndex,defaultText:p.id}),b.disableLabelAnimation=!0,b.setStyle("fill",p.getVisual("color")),b.setStyle("decal",p.getVisual("style").decal),Ht(b,g),s.add(b),h.setItemGraphicEl(p.dataIndex,b),ht(b).dataType="node";var _=S.get("focus");xt(b,_==="adjacency"?p.getAdjacentDataIndices():_==="trajectory"?p.getTrajectoryDataIndices():_,S.get("blurScope"),S.get("disabled"))}),h.eachItemGraphicEl(function(p,d){var g=h.getItemModel(d);g.get("draggable")&&(p.drift=function(y,m){n._focusAdjacencyDisabled=!0,this.shape.x+=y,this.shape.y+=m,this.dirty(),i.dispatchAction({type:"dragNode",seriesId:t.id,dataIndex:h.getRawIndex(d),localX:this.shape.x/u,localY:this.shape.y/c})},p.ondragend=function(){n._focusAdjacencyDisabled=!1},p.draggable=!0,p.cursor="move")}),!this._data&&t.isAnimationEnabled()&&s.setClipPath(rx(s.getBoundingRect(),t,function(){s.removeClipPath()})),this._data=t.getData()},e.prototype.dispose=function(){},e.type="sankey",e}(bt);function Du(a,e,t){switch(a.fill){case"source":a.fill=t.node1.getVisual("color"),a.decal=t.node1.getVisual("style").decal;break;case"target":a.fill=t.node2.getVisual("color"),a.decal=t.node2.getVisual("style").decal;break;case"gradient":var r=t.node1.getVisual("color"),i=t.node2.getVisual("color");tt(r)&&tt(i)&&(a.fill=new Mo(0,0,+(e==="horizontal"),+(e==="vertical"),[{color:r,offset:0},{color:i,offset:1}]))}}function rx(a,e,t){var r=new pt({shape:{x:a.x-10,y:a.y-10,width:0,height:a.height+20}});return Gt(r,{shape:{width:a.width+20}},e,t),r}var ax=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getInitialData=function(t,r){var i=t.edges||t.links||[],n=t.data||t.nodes||[],o=t.levels||[];this.levelModels=[];for(var s=this.levelModels,l=0;l<o.length;l++)o[l].depth!=null&&o[l].depth>=0&&(s[o[l].depth]=new Ut(o[l],this,r));var u=Yv(n,i,this,!0,c);return u.data;function c(h,v){h.wrapMethod("getItemModel",function(f,p){var d=f.parentModel,g=d.getData().getItemLayout(p);if(g){var y=g.depth,m=d.levelModels[y];m&&(f.parentModel=m)}return f}),v.wrapMethod("getItemModel",function(f,p){var d=f.parentModel,g=d.getGraph().getEdgeByIndex(p),y=g.node1.getLayout();if(y){var m=y.depth,S=d.levelModels[m];S&&(f.parentModel=S)}return f})}},e.prototype.setNodePosition=function(t,r){var i=this.option.data||this.option.nodes,n=i[t];n.localX=r[0],n.localY=r[1]},e.prototype.getGraph=function(){return this.getData().graph},e.prototype.getEdgeData=function(){return this.getGraph().edgeData},e.prototype.formatTooltip=function(t,r,i){function n(f){return isNaN(f)||f==null}if(i==="edge"){var o=this.getDataParams(t,i),s=o.data,l=o.value,u=s.source+" -- "+s.target;return Zt("nameValue",{name:u,value:l,noValue:n(l)})}else{var c=this.getGraph().getNodeByIndex(t),h=c.getLayout().value,v=this.getDataParams(t,i).data.name;return Zt("nameValue",{name:v!=null?v+"":null,value:h,noValue:n(h)})}},e.prototype.optionUpdated=function(){},e.prototype.getDataParams=function(t,r){var i=a.prototype.getDataParams.call(this,t,r);if(i.value==null&&r==="node"){var n=this.getGraph().getNodeByIndex(t),o=n.getLayout().value;i.value=o}return i},e.type="series.sankey",e.defaultOption={z:2,coordinateSystem:"view",left:"5%",top:"5%",right:"20%",bottom:"5%",orient:"horizontal",nodeWidth:20,nodeGap:8,draggable:!0,layoutIterations:32,label:{show:!0,position:"right",fontSize:12},edgeLabel:{show:!1,fontSize:12},levels:[],nodeAlign:"justify",lineStyle:{color:"#314656",opacity:.2,curveness:.5},emphasis:{label:{show:!0},lineStyle:{opacity:.5}},select:{itemStyle:{borderColor:"#212121"}},animationEasing:"linear",animationDuration:1e3},e}(Ct);function ix(a,e){a.eachSeriesByType("sankey",function(t){var r=t.get("nodeWidth"),i=t.get("nodeGap"),n=nx(t,e);t.layoutInfo=n;var o=n.width,s=n.height,l=t.getGraph(),u=l.nodes,c=l.edges;sx(u);var h=Yt(u,function(d){return d.getLayout().value===0}),v=h.length!==0?0:t.get("layoutIterations"),f=t.get("orient"),p=t.get("nodeAlign");ox(u,c,r,i,o,s,v,f,p)})}function nx(a,e){return Rt(a.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function ox(a,e,t,r,i,n,o,s,l){lx(a,e,t,i,n,s,l),vx(a,e,n,i,r,o,s),bx(a,s)}function sx(a){D(a,function(e){var t=Me(e.outEdges,ai),r=Me(e.inEdges,ai),i=e.getValue()||0,n=Math.max(t,r,i);e.setLayout({value:n},!0)})}function lx(a,e,t,r,i,n,o){for(var s=[],l=[],u=[],c=[],h=0,v=0;v<e.length;v++)s[v]=1;for(var v=0;v<a.length;v++)l[v]=a[v].inEdges.length,l[v]===0&&u.push(a[v]);for(var f=-1;u.length;){for(var p=0;p<u.length;p++){var d=u[p],g=d.hostGraph.data.getRawDataItem(d.dataIndex),y=g.depth!=null&&g.depth>=0;y&&g.depth>f&&(f=g.depth),d.setLayout({depth:y?g.depth:h},!0),n==="vertical"?d.setLayout({dy:t},!0):d.setLayout({dx:t},!0);for(var m=0;m<d.outEdges.length;m++){var S=d.outEdges[m],x=e.indexOf(S);s[x]=0;var b=S.node2,_=a.indexOf(b);--l[_]===0&&c.indexOf(b)<0&&c.push(b)}}++h,u=c,c=[]}for(var v=0;v<s.length;v++)if(s[v]===1)throw new Error("Sankey is a DAG, the original data has cycle!");var w=f>h-1?f:h-1;o&&o!=="left"&&ux(a,o,n,w);var A=n==="vertical"?(i-t)/w:(r-t)/w;hx(a,A,n)}function vf(a){var e=a.hostGraph.data.getRawDataItem(a.dataIndex);return e.depth!=null&&e.depth>=0}function ux(a,e,t,r){if(e==="right"){for(var i=[],n=a,o=0;n.length;){for(var s=0;s<n.length;s++){var l=n[s];l.setLayout({skNodeHeight:o},!0);for(var u=0;u<l.inEdges.length;u++){var c=l.inEdges[u];i.indexOf(c.node1)<0&&i.push(c.node1)}}n=i,i=[],++o}D(a,function(h){vf(h)||h.setLayout({depth:Math.max(0,r-h.getLayout().skNodeHeight)},!0)})}else e==="justify"&&cx(a,r)}function cx(a,e){D(a,function(t){!vf(t)&&!t.outEdges.length&&t.setLayout({depth:e},!0)})}function hx(a,e,t){D(a,function(r){var i=r.getLayout().depth*e;t==="vertical"?r.setLayout({y:i},!0):r.setLayout({x:i},!0)})}function vx(a,e,t,r,i,n,o){var s=fx(a,o);px(s,e,t,r,i,o),fn(s,i,t,r,o);for(var l=1;n>0;n--)l*=.99,dx(s,l,o),fn(s,i,t,r,o),xx(s,l,o),fn(s,i,t,r,o)}function fx(a,e){var t=[],r=e==="vertical"?"y":"x",i=Zn(a,function(n){return n.getLayout()[r]});return i.keys.sort(function(n,o){return n-o}),D(i.keys,function(n){t.push(i.buckets.get(n))}),t}function px(a,e,t,r,i,n){var o=1/0;D(a,function(s){var l=s.length,u=0;D(s,function(h){u+=h.getLayout().value});var c=n==="vertical"?(r-(l-1)*i)/u:(t-(l-1)*i)/u;c<o&&(o=c)}),D(a,function(s){D(s,function(l,u){var c=l.getLayout().value*o;n==="vertical"?(l.setLayout({x:u},!0),l.setLayout({dx:c},!0)):(l.setLayout({y:u},!0),l.setLayout({dy:c},!0))})}),D(e,function(s){var l=+s.getValue()*o;s.setLayout({dy:l},!0)})}function fn(a,e,t,r,i){var n=i==="vertical"?"x":"y";D(a,function(o){o.sort(function(d,g){return d.getLayout()[n]-g.getLayout()[n]});for(var s,l,u,c=0,h=o.length,v=i==="vertical"?"dx":"dy",f=0;f<h;f++)l=o[f],u=c-l.getLayout()[n],u>0&&(s=l.getLayout()[n]+u,i==="vertical"?l.setLayout({x:s},!0):l.setLayout({y:s},!0)),c=l.getLayout()[n]+l.getLayout()[v]+e;var p=i==="vertical"?r:t;if(u=c-e-p,u>0){s=l.getLayout()[n]-u,i==="vertical"?l.setLayout({x:s},!0):l.setLayout({y:s},!0),c=s;for(var f=h-2;f>=0;--f)l=o[f],u=l.getLayout()[n]+l.getLayout()[v]+e-c,u>0&&(s=l.getLayout()[n]-u,i==="vertical"?l.setLayout({x:s},!0):l.setLayout({y:s},!0)),c=l.getLayout()[n]}})}function dx(a,e,t){D(a.slice().reverse(),function(r){D(r,function(i){if(i.outEdges.length){var n=Me(i.outEdges,gx,t)/Me(i.outEdges,ai);if(isNaN(n)){var o=i.outEdges.length;n=o?Me(i.outEdges,yx,t)/o:0}if(t==="vertical"){var s=i.getLayout().x+(n-Re(i,t))*e;i.setLayout({x:s},!0)}else{var l=i.getLayout().y+(n-Re(i,t))*e;i.setLayout({y:l},!0)}}})})}function gx(a,e){return Re(a.node2,e)*a.getValue()}function yx(a,e){return Re(a.node2,e)}function mx(a,e){return Re(a.node1,e)*a.getValue()}function Sx(a,e){return Re(a.node1,e)}function Re(a,e){return e==="vertical"?a.getLayout().x+a.getLayout().dx/2:a.getLayout().y+a.getLayout().dy/2}function ai(a){return a.getValue()}function Me(a,e,t){for(var r=0,i=a.length,n=-1;++n<i;){var o=+e(a[n],t);isNaN(o)||(r+=o)}return r}function xx(a,e,t){D(a,function(r){D(r,function(i){if(i.inEdges.length){var n=Me(i.inEdges,mx,t)/Me(i.inEdges,ai);if(isNaN(n)){var o=i.inEdges.length;n=o?Me(i.inEdges,Sx,t)/o:0}if(t==="vertical"){var s=i.getLayout().x+(n-Re(i,t))*e;i.setLayout({x:s},!0)}else{var l=i.getLayout().y+(n-Re(i,t))*e;i.setLayout({y:l},!0)}}})})}function bx(a,e){var t=e==="vertical"?"x":"y";D(a,function(r){r.outEdges.sort(function(i,n){return i.node2.getLayout()[t]-n.node2.getLayout()[t]}),r.inEdges.sort(function(i,n){return i.node1.getLayout()[t]-n.node1.getLayout()[t]})}),D(a,function(r){var i=0,n=0;D(r.outEdges,function(o){o.setLayout({sy:i},!0),i+=o.getLayout().dy}),D(r.inEdges,function(o){o.setLayout({ty:n},!0),n+=o.getLayout().dy})})}function _x(a){a.eachSeriesByType("sankey",function(e){var t=e.getGraph(),r=t.nodes,i=t.edges;if(r.length){var n=1/0,o=-1/0;D(r,function(s){var l=s.getLayout().value;l<n&&(n=l),l>o&&(o=l)}),D(r,function(s){var l=new Tt({type:"color",mappingMethod:"linear",dataExtent:[n,o],visual:e.get("color")}),u=l.mapValueToVisual(s.getLayout().value),c=s.getModel().get(["itemStyle","color"]);c!=null?(s.setVisual("color",c),s.setVisual("style",{fill:c})):(s.setVisual("color",u),s.setVisual("style",{fill:u}))})}i.length&&D(i,function(s){var l=s.getModel().get("lineStyle");s.setVisual("style",l)})})}function wx(a){a.registerChartView(ex),a.registerSeriesModel(ax),a.registerLayout(ix),a.registerVisual(_x),a.registerAction({type:"dragNode",event:"dragnode",update:"update"},function(e,t){t.eachComponent({mainType:"series",subType:"sankey",query:e},function(r){r.setNodePosition(e.dataIndex,[e.localX,e.localY])})})}var ff=function(){function a(){}return a.prototype._hasEncodeRule=function(e){var t=this.getEncode();return t&&t.get(e)!=null},a.prototype.getInitialData=function(e,t){var r,i=t.getComponent("xAxis",this.get("xAxisIndex")),n=t.getComponent("yAxis",this.get("yAxisIndex")),o=i.get("type"),s=n.get("type"),l;o==="category"?(e.layout="horizontal",r=i.getOrdinalMeta(),l=!this._hasEncodeRule("x")):s==="category"?(e.layout="vertical",r=n.getOrdinalMeta(),l=!this._hasEncodeRule("y")):e.layout=e.layout||"horizontal";var u=["x","y"],c=e.layout==="horizontal"?0:1,h=this._baseAxisDim=u[c],v=u[1-c],f=[i,n],p=f[c].get("type"),d=f[1-c].get("type"),g=e.data;if(g&&l){var y=[];D(g,function(x,b){var _;U(x)?(_=x.slice(),x.unshift(b)):U(x.value)?(_=F({},x),_.value=_.value.slice(),x.value.unshift(b)):_=x,y.push(_)}),e.data=y}var m=this.defaultValueDimensions,S=[{name:h,type:Un(p),ordinalMeta:r,otherDims:{tooltip:!1,itemName:0},dimsDef:["base"]},{name:v,type:Un(d),dimsDef:m.slice()}];return Tr(this,{coordDimensions:S,dimensionsCount:m.length+1,encodeDefaulter:q(dd,S,this)})},a.prototype.getBaseAxis=function(){var e=this._baseAxisDim;return this.ecModel.getComponent(e+"Axis",this.get(e+"AxisIndex")).axis},a}(),pf=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.defaultValueDimensions=[{name:"min",defaultTooltip:!0},{name:"Q1",defaultTooltip:!0},{name:"median",defaultTooltip:!0},{name:"Q3",defaultTooltip:!0},{name:"max",defaultTooltip:!0}],t.visualDrawType="stroke",t}return e.type="series.boxplot",e.dependencies=["xAxis","yAxis","grid"],e.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,layout:null,boxWidth:[7,50],itemStyle:{color:"#fff",borderWidth:1},emphasis:{scale:!0,itemStyle:{borderWidth:2,shadowBlur:5,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0,0,0,0.2)"}},animationDuration:800},e}(Ct);ue(pf,ff,!0);var Ax=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i){var n=t.getData(),o=this.group,s=this._data;this._data||o.removeAll();var l=t.get("layout")==="horizontal"?1:0;n.diff(s).add(function(u){if(n.hasValue(u)){var c=n.getItemLayout(u),h=Cu(c,n,u,l,!0);n.setItemGraphicEl(u,h),o.add(h)}}).update(function(u,c){var h=s.getItemGraphicEl(c);if(!n.hasValue(u)){o.remove(h);return}var v=n.getItemLayout(u);h?(xe(h),df(v,h,n,u)):h=Cu(v,n,u,l),o.add(h),n.setItemGraphicEl(u,h)}).remove(function(u){var c=s.getItemGraphicEl(u);c&&o.remove(c)}).execute(),this._data=n},e.prototype.remove=function(t){var r=this.group,i=this._data;this._data=null,i&&i.eachItemGraphicEl(function(n){n&&r.remove(n)})},e.type="boxplot",e}(bt),Tx=function(){function a(){}return a}(),Dx=function(a){E(e,a);function e(t){var r=a.call(this,t)||this;return r.type="boxplotBoxPath",r}return e.prototype.getDefaultShape=function(){return new Tx},e.prototype.buildPath=function(t,r){var i=r.points,n=0;for(t.moveTo(i[n][0],i[n][1]),n++;n<4;n++)t.lineTo(i[n][0],i[n][1]);for(t.closePath();n<i.length;n++)t.moveTo(i[n][0],i[n][1]),n++,t.lineTo(i[n][0],i[n][1])},e}(ie);function Cu(a,e,t,r,i){var n=a.ends,o=new Dx({shape:{points:i?Cx(n,r,a):n}});return df(a,o,e,t,i),o}function df(a,e,t,r,i){var n=t.hostModel,o=_r[i?"initProps":"updateProps"];o(e,{shape:{points:a.ends}},n,r),e.useStyle(t.getItemVisual(r,"style")),e.style.strokeNoScale=!0,e.z2=100;var s=t.getItemModel(r),l=s.getModel("emphasis");Ht(e,s),xt(e,l.get("focus"),l.get("blurScope"),l.get("disabled"))}function Cx(a,e,t){return G(a,function(r){return r=r.slice(),r[e]=t.initBaseline,r})}var Zr=D;function Ix(a){var e=Lx(a);Zr(e,function(t){var r=t.seriesModels;r.length&&(Mx(t),Zr(r,function(i,n){Px(i,t.boxOffsetList[n],t.boxWidthList[n])}))})}function Lx(a){var e=[],t=[];return a.eachSeriesByType("boxplot",function(r){var i=r.getBaseAxis(),n=mt(t,i);n<0&&(n=t.length,t[n]=i,e[n]={axis:i,seriesModels:[]}),e[n].seriesModels.push(r)}),e}function Mx(a){var e=a.axis,t=a.seriesModels,r=t.length,i=a.boxWidthList=[],n=a.boxOffsetList=[],o=[],s;if(e.type==="category")s=e.getBandWidth();else{var l=0;Zr(t,function(p){l=Math.max(l,p.getData().count())});var u=e.getExtent();s=Math.abs(u[1]-u[0])/l}Zr(t,function(p){var d=p.get("boxWidth");U(d)||(d=[d,d]),o.push([O(d[0],s)||0,O(d[1],s)||0])});var c=s*.8-2,h=c/r*.3,v=(c-h*(r-1))/r,f=v/2-c/2;Zr(t,function(p,d){n.push(f),f+=h+v,i.push(Math.min(Math.max(v,o[d][0]),o[d][1]))})}function Px(a,e,t){var r=a.coordinateSystem,i=a.getData(),n=t/2,o=a.get("layout")==="horizontal"?0:1,s=1-o,l=["x","y"],u=i.mapDimension(l[o]),c=i.mapDimensionsAll(l[s]);if(u==null||c.length<5)return;for(var h=0;h<i.count();h++){var v=i.get(u,h),f=S(v,c[2],h),p=S(v,c[0],h),d=S(v,c[1],h),g=S(v,c[3],h),y=S(v,c[4],h),m=[];x(m,d,!1),x(m,g,!0),m.push(p,d,y,g),b(m,p),b(m,y),b(m,f),i.setItemLayout(h,{initBaseline:f[s],ends:m})}function S(_,w,A){var C=i.get(w,A),T=[];T[o]=_,T[s]=C;var I;return isNaN(_)||isNaN(C)?I=[NaN,NaN]:(I=r.dataToPoint(T),I[o]+=e),I}function x(_,w,A){var C=w.slice(),T=w.slice();C[o]+=n,T[o]-=n,A?_.push(C,T):_.push(T,C)}function b(_,w){var A=w.slice(),C=w.slice();A[o]-=n,C[o]+=n,_.push(A,C)}}function Rx(a,e){e=e||{};for(var t=[],r=[],i=e.boundIQR,n=i==="none"||i===0,o=0;o<a.length;o++){var s=re(a[o].slice()),l=zi(s,.25),u=zi(s,.5),c=zi(s,.75),h=s[0],v=s[s.length-1],f=(i??1.5)*(c-l),p=n?h:Math.max(h,l-f),d=n?v:Math.min(v,c+f),g=e.itemNameFormatter,y=st(g)?g({value:o}):tt(g)?g.replace("{value}",o+""):o+"";t.push([y,p,l,u,c,d]);for(var m=0;m<s.length;m++){var S=s[m];if(S<p||S>d){var x=[y,S];r.push(x)}}}return{boxData:t,outliers:r}}var Ex={type:"echarts:boxplot",transform:function(e){var t=e.upstream;if(t.sourceFormat!==gd){var r="";zh(r)}var i=Rx(t.getRawData(),e.config);return[{dimensions:["ItemName","Low","Q1","Q2","Q3","High"],data:i.boxData},{data:i.outliers}]}};function kx(a){a.registerSeriesModel(pf),a.registerChartView(Ax),a.registerLayout(Ix),a.registerTransform(Ex)}var Vx=["itemStyle","borderColor"],Nx=["itemStyle","borderColor0"],zx=["itemStyle","borderColorDoji"],Ox=["itemStyle","color"],Bx=["itemStyle","color0"];function ds(a,e){return e.get(a>0?Ox:Bx)}function gs(a,e){return e.get(a===0?zx:a>0?Vx:Nx)}var Gx={seriesType:"candlestick",plan:No(),performRawSeries:!0,reset:function(a,e){if(!e.isSeriesFiltered(a)){var t=a.pipelineContext.large;return!t&&{progress:function(r,i){for(var n;(n=r.next())!=null;){var o=i.getItemModel(n),s=i.getItemLayout(n).sign,l=o.getItemStyle();l.fill=ds(s,o),l.stroke=gs(s,o)||l.fill;var u=i.ensureUniqueItemVisual(n,"style");F(u,l)}}}}}},Fx=["color","borderColor"],Hx=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i){this.group.removeClipPath(),this._progressiveEls=null,this._updateDrawMode(t),this._isLargeDraw?this._renderLarge(t):this._renderNormal(t)},e.prototype.incrementalPrepareRender=function(t,r,i){this._clear(),this._updateDrawMode(t)},e.prototype.incrementalRender=function(t,r,i,n){this._progressiveEls=[],this._isLargeDraw?this._incrementalRenderLarge(t,r):this._incrementalRenderNormal(t,r)},e.prototype.eachRendered=function(t){_i(this._progressiveEls||this.group,t)},e.prototype._updateDrawMode=function(t){var r=t.pipelineContext.large;(this._isLargeDraw==null||r!==this._isLargeDraw)&&(this._isLargeDraw=r,this._clear())},e.prototype._renderNormal=function(t){var r=t.getData(),i=this._data,n=this.group,o=r.getLayout("isSimpleBox"),s=t.get("clip",!0),l=t.coordinateSystem,u=l.getArea&&l.getArea();this._data||n.removeAll(),r.diff(i).add(function(c){if(r.hasValue(c)){var h=r.getItemLayout(c);if(s&&Iu(u,h))return;var v=pn(h,c,!0);Gt(v,{shape:{points:h.ends}},t,c),dn(v,r,c,o),n.add(v),r.setItemGraphicEl(c,v)}}).update(function(c,h){var v=i.getItemGraphicEl(h);if(!r.hasValue(c)){n.remove(v);return}var f=r.getItemLayout(c);if(s&&Iu(u,f)){n.remove(v);return}v?(dt(v,{shape:{points:f.ends}},t,c),xe(v)):v=pn(f),dn(v,r,c,o),n.add(v),r.setItemGraphicEl(c,v)}).remove(function(c){var h=i.getItemGraphicEl(c);h&&n.remove(h)}).execute(),this._data=r},e.prototype._renderLarge=function(t){this._clear(),Lu(t,this.group);var r=t.get("clip",!0)?Ai(t.coordinateSystem,!1,t):null;r?this.group.setClipPath(r):this.group.removeClipPath()},e.prototype._incrementalRenderNormal=function(t,r){for(var i=r.getData(),n=i.getLayout("isSimpleBox"),o;(o=t.next())!=null;){var s=i.getItemLayout(o),l=pn(s);dn(l,i,o,n),l.incremental=!0,this.group.add(l),this._progressiveEls.push(l)}},e.prototype._incrementalRenderLarge=function(t,r){Lu(r,this.group,this._progressiveEls,!0)},e.prototype.remove=function(t){this._clear()},e.prototype._clear=function(){this.group.removeAll(),this._data=null},e.type="candlestick",e}(bt),Wx=function(){function a(){}return a}(),Zx=function(a){E(e,a);function e(t){var r=a.call(this,t)||this;return r.type="normalCandlestickBox",r}return e.prototype.getDefaultShape=function(){return new Wx},e.prototype.buildPath=function(t,r){var i=r.points;this.__simpleBox?(t.moveTo(i[4][0],i[4][1]),t.lineTo(i[6][0],i[6][1])):(t.moveTo(i[0][0],i[0][1]),t.lineTo(i[1][0],i[1][1]),t.lineTo(i[2][0],i[2][1]),t.lineTo(i[3][0],i[3][1]),t.closePath(),t.moveTo(i[4][0],i[4][1]),t.lineTo(i[5][0],i[5][1]),t.moveTo(i[6][0],i[6][1]),t.lineTo(i[7][0],i[7][1]))},e}(ie);function pn(a,e,t){var r=a.ends;return new Zx({shape:{points:t?Ux(r,a):r},z2:100})}function Iu(a,e){for(var t=!0,r=0;r<e.ends.length;r++)if(a.contain(e.ends[r][0],e.ends[r][1])){t=!1;break}return t}function dn(a,e,t,r){var i=e.getItemModel(t);a.useStyle(e.getItemVisual(t,"style")),a.style.strokeNoScale=!0,a.__simpleBox=r,Ht(a,i);var n=e.getItemLayout(t).sign;D(a.states,function(s,l){var u=i.getModel(l),c=ds(n,u),h=gs(n,u)||c,v=s.style||(s.style={});c&&(v.fill=c),h&&(v.stroke=h)});var o=i.getModel("emphasis");xt(a,o.get("focus"),o.get("blurScope"),o.get("disabled"))}function Ux(a,e){return G(a,function(t){return t=t.slice(),t[1]=e.initBaseline,t})}var Yx=function(){function a(){}return a}(),gn=function(a){E(e,a);function e(t){var r=a.call(this,t)||this;return r.type="largeCandlestickBox",r}return e.prototype.getDefaultShape=function(){return new Yx},e.prototype.buildPath=function(t,r){for(var i=r.points,n=0;n<i.length;)if(this.__sign===i[n++]){var o=i[n++];t.moveTo(o,i[n++]),t.lineTo(o,i[n++])}else n+=3},e}(ie);function Lu(a,e,t,r){var i=a.getData(),n=i.getLayout("largePoints"),o=new gn({shape:{points:n},__sign:1,ignoreCoarsePointer:!0});e.add(o);var s=new gn({shape:{points:n},__sign:-1,ignoreCoarsePointer:!0});e.add(s);var l=new gn({shape:{points:n},__sign:0,ignoreCoarsePointer:!0});e.add(l),yn(1,o,a),yn(-1,s,a),yn(0,l,a),r&&(o.incremental=!0,s.incremental=!0),t&&t.push(o,s)}function yn(a,e,t,r){var i=gs(a,t)||ds(a,t),n=t.getModel("itemStyle").getItemStyle(Fx);e.useStyle(n),e.style.fill=null,e.style.stroke=i}var gf=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.defaultValueDimensions=[{name:"open",defaultTooltip:!0},{name:"close",defaultTooltip:!0},{name:"lowest",defaultTooltip:!0},{name:"highest",defaultTooltip:!0}],t}return e.prototype.getShadowDim=function(){return"open"},e.prototype.brushSelector=function(t,r,i){var n=r.getItemLayout(t);return n&&i.rect(n.brushRect)},e.type="series.candlestick",e.dependencies=["xAxis","yAxis","grid"],e.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,layout:null,clip:!0,itemStyle:{color:"#eb5454",color0:"#47b262",borderColor:"#eb5454",borderColor0:"#47b262",borderColorDoji:null,borderWidth:1},emphasis:{itemStyle:{borderWidth:2}},barMaxWidth:null,barMinWidth:null,barWidth:null,large:!0,largeThreshold:600,progressive:3e3,progressiveThreshold:1e4,progressiveChunkMode:"mod",animationEasing:"linear",animationDuration:300},e}(Ct);ue(gf,ff,!0);function $x(a){!a||!U(a.series)||D(a.series,function(e){Nt(e)&&e.type==="k"&&(e.type="candlestick")})}var Xx={seriesType:"candlestick",plan:No(),reset:function(a){var e=a.coordinateSystem,t=a.getData(),r=Kx(a,t),i=0,n=1,o=["x","y"],s=t.getDimensionIndex(t.mapDimension(o[i])),l=G(t.mapDimensionsAll(o[n]),t.getDimensionIndex,t),u=l[0],c=l[1],h=l[2],v=l[3];if(t.setLayout({candleWidth:r,isSimpleBox:r<=1.3}),s<0||l.length<4)return;return{progress:a.pipelineContext.large?p:f};function f(d,g){for(var y,m=g.getStore();(y=d.next())!=null;){var S=m.get(s,y),x=m.get(u,y),b=m.get(c,y),_=m.get(h,y),w=m.get(v,y),A=Math.min(x,b),C=Math.max(x,b),T=V(A,S),I=V(C,S),L=V(_,S),M=V(w,S),R=[];N(R,I,0),N(R,T,1),R.push(H(M),H(I),H(L),H(T));var P=g.getItemModel(y),k=!!P.get(["itemStyle","borderColorDoji"]);g.setItemLayout(y,{sign:Mu(m,y,x,b,c,k),initBaseline:x>b?I[n]:T[n],ends:R,brushRect:z(_,w,S)})}function V(Z,Y){var Q=[];return Q[i]=Y,Q[n]=Z,isNaN(Y)||isNaN(Z)?[NaN,NaN]:e.dataToPoint(Q)}function N(Z,Y,Q){var j=Y.slice(),rt=Y.slice();j[i]=Oi(j[i]+r/2,1,!1),rt[i]=Oi(rt[i]-r/2,1,!0),Q?Z.push(j,rt):Z.push(rt,j)}function z(Z,Y,Q){var j=V(Z,Q),rt=V(Y,Q);return j[i]-=r/2,rt[i]-=r/2,{x:j[0],y:j[1],width:r,height:rt[1]-j[1]}}function H(Z){return Z[i]=Oi(Z[i],1),Z}}function p(d,g){for(var y=yd(d.count*4),m=0,S,x=[],b=[],_,w=g.getStore(),A=!!a.get(["itemStyle","borderColorDoji"]);(_=d.next())!=null;){var C=w.get(s,_),T=w.get(u,_),I=w.get(c,_),L=w.get(h,_),M=w.get(v,_);if(isNaN(C)||isNaN(L)||isNaN(M)){y[m++]=NaN,m+=3;continue}y[m++]=Mu(w,_,T,I,c,A),x[i]=C,x[n]=L,S=e.dataToPoint(x,null,b),y[m++]=S?S[0]:NaN,y[m++]=S?S[1]:NaN,x[n]=M,S=e.dataToPoint(x,null,b),y[m++]=S?S[1]:NaN}g.setLayout("largePoints",y)}}};function Mu(a,e,t,r,i,n){var o;return t>r?o=-1:t<r?o=1:o=n?0:e>0?a.get(i,e-1)<=r?1:-1:1,o}function Kx(a,e){var t=a.getBaseAxis(),r,i=t.type==="category"?t.getBandWidth():(r=t.getExtent(),Math.abs(r[1]-r[0])/e.count()),n=O(Mt(a.get("barMaxWidth"),i),i),o=O(Mt(a.get("barMinWidth"),1),i),s=a.get("barWidth");return s!=null?O(s,i):Math.max(Math.min(i/2,n),o)}function qx(a){a.registerChartView(Hx),a.registerSeriesModel(gf),a.registerPreprocessor($x),a.registerVisual(Gx),a.registerLayout(Xx)}function Pu(a,e){var t=e.rippleEffectColor||e.color;a.eachChild(function(r){r.attr({z:e.z,zlevel:e.zlevel,style:{stroke:e.brushType==="stroke"?t:null,fill:e.brushType==="fill"?t:null}})})}var jx=function(a){E(e,a);function e(t,r){var i=a.call(this)||this,n=new Lh(t,r),o=new X;return i.add(n),i.add(o),i.updateData(t,r),i}return e.prototype.stopEffectAnimation=function(){this.childAt(1).removeAll()},e.prototype.startEffectAnimation=function(t){for(var r=t.symbolType,i=t.color,n=t.rippleNumber,o=this.childAt(1),s=0;s<n;s++){var l=Et(r,-1,-1,2,2,i);l.attr({style:{strokeNoScale:!0},z2:99,silent:!0,scaleX:.5,scaleY:.5});var u=-s/n*t.period+t.effectOffset;l.animate("",!0).when(t.period,{scaleX:t.rippleScale/2,scaleY:t.rippleScale/2}).delay(u).start(),l.animateStyle(!0).when(t.period,{opacity:0}).delay(u).start(),o.add(l)}Pu(o,t)},e.prototype.updateEffectAnimation=function(t){for(var r=this._effectCfg,i=this.childAt(1),n=["symbolType","period","rippleScale","rippleNumber"],o=0;o<n.length;o++){var s=n[o];if(r[s]!==t[s]){this.stopEffectAnimation(),this.startEffectAnimation(t);return}}Pu(i,t)},e.prototype.highlight=function(){$r(this)},e.prototype.downplay=function(){Xr(this)},e.prototype.getSymbolType=function(){var t=this.childAt(0);return t&&t.getSymbolType()},e.prototype.updateData=function(t,r){var i=this,n=t.hostModel;this.childAt(0).updateData(t,r);var o=this.childAt(1),s=t.getItemModel(r),l=t.getItemVisual(r,"symbol"),u=sa(t.getItemVisual(r,"symbolSize")),c=t.getItemVisual(r,"style"),h=c&&c.fill,v=s.getModel("emphasis");o.setScale(u),o.traverse(function(g){g.setStyle("fill",h)});var f=ha(t.getItemVisual(r,"symbolOffset"),u);f&&(o.x=f[0],o.y=f[1]);var p=t.getItemVisual(r,"symbolRotate");o.rotation=(p||0)*Math.PI/180||0;var d={};d.showEffectOn=n.get("showEffectOn"),d.rippleScale=s.get(["rippleEffect","scale"]),d.brushType=s.get(["rippleEffect","brushType"]),d.period=s.get(["rippleEffect","period"])*1e3,d.effectOffset=r/t.count(),d.z=n.getShallow("z")||0,d.zlevel=n.getShallow("zlevel")||0,d.symbolType=l,d.color=h,d.rippleEffectColor=s.get(["rippleEffect","color"]),d.rippleNumber=s.get(["rippleEffect","number"]),d.showEffectOn==="render"?(this._effectCfg?this.updateEffectAnimation(d):this.startEffectAnimation(d),this._effectCfg=d):(this._effectCfg=null,this.stopEffectAnimation(),this.onHoverStateChange=function(g){g==="emphasis"?d.showEffectOn!=="render"&&i.startEffectAnimation(d):g==="normal"&&d.showEffectOn!=="render"&&i.stopEffectAnimation()}),this._effectCfg=d,xt(this,v.get("focus"),v.get("blurScope"),v.get("disabled"))},e.prototype.fadeOut=function(t){t&&t()},e}(X),Jx=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(){this._symbolDraw=new di(jx)},e.prototype.render=function(t,r,i){var n=t.getData(),o=this._symbolDraw;o.updateData(n,{clipShape:this._getClipShape(t)}),this.group.add(o.group)},e.prototype._getClipShape=function(t){var r=t.coordinateSystem,i=r&&r.getArea&&r.getArea();return t.get("clip",!0)?i:null},e.prototype.updateTransform=function(t,r,i){var n=t.getData();this.group.dirty();var o=pi("").reset(t,r,i);o.progress&&o.progress({start:0,end:n.count(),count:n.count()},n),this._symbolDraw.updateLayout()},e.prototype._updateGroupTransform=function(t){var r=t.coordinateSystem;r&&r.getRoamTransform&&(this.group.transform=md(r.getRoamTransform()),this.group.decomposeTransform())},e.prototype.remove=function(t,r){this._symbolDraw&&this._symbolDraw.remove(!0)},e.type="effectScatter",e}(bt),Qx=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.getInitialData=function(t,r){return br(null,this,{useEncodeDefaulter:!0})},e.prototype.brushSelector=function(t,r,i){return i.point(r.getItemLayout(t))},e.type="series.effectScatter",e.dependencies=["grid","polar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,effectType:"ripple",progressive:0,showEffectOn:"render",clip:!0,rippleEffect:{period:4,scale:2.5,brushType:"fill",number:3},universalTransition:{divideShape:"clone"},symbolSize:10},e}(Ct);function t1(a){a.registerChartView(Jx),a.registerSeriesModel(Qx),a.registerLayout(pi("effectScatter"))}var yf=function(a){E(e,a);function e(t,r,i){var n=a.call(this)||this;return n.add(n.createLine(t,r,i)),n._updateEffectSymbol(t,r),n}return e.prototype.createLine=function(t,r,i){return new os(t,r,i)},e.prototype._updateEffectSymbol=function(t,r){var i=t.getItemModel(r),n=i.getModel("effect"),o=n.get("symbolSize"),s=n.get("symbol");U(o)||(o=[o,o]);var l=t.getItemVisual(r,"style"),u=n.get("color")||l&&l.stroke,c=this.childAt(1);this._symbolType!==s&&(this.remove(c),c=Et(s,-.5,-.5,1,1,u),c.z2=100,c.culling=!0,this.add(c)),c&&(c.setStyle("shadowColor",u),c.setStyle(n.getItemStyle(["color"])),c.scaleX=o[0],c.scaleY=o[1],c.setColor(u),this._symbolType=s,this._symbolScale=o,this._updateEffectAnimation(t,n,r))},e.prototype._updateEffectAnimation=function(t,r,i){var n=this.childAt(1);if(n){var o=t.getItemLayout(i),s=r.get("period")*1e3,l=r.get("loop"),u=r.get("roundTrip"),c=r.get("constantSpeed"),h=qt(r.get("delay"),function(f){return f/t.count()*s/3});if(n.ignore=!0,this._updateAnimationPoints(n,o),c>0&&(s=this._getLineLength(n)/c*1e3),s!==this._period||l!==this._loop||u!==this._roundTrip){n.stopAnimation();var v=void 0;st(h)?v=h(i):v=h,n.__t>0&&(v=-s*n.__t),this._animateSymbol(n,s,v,l,u)}this._period=s,this._loop=l,this._roundTrip=u}},e.prototype._animateSymbol=function(t,r,i,n,o){if(r>0){t.__t=0;var s=this,l=t.animate("",n).when(o?r*2:r,{__t:o?2:1}).delay(i).during(function(){s._updateSymbolPosition(t)});n||l.done(function(){s.remove(t)}),l.start()}},e.prototype._getLineLength=function(t){return zr(t.__p1,t.__cp1)+zr(t.__cp1,t.__p2)},e.prototype._updateAnimationPoints=function(t,r){t.__p1=r[0],t.__p2=r[1],t.__cp1=r[2]||[(r[0][0]+r[1][0])/2,(r[0][1]+r[1][1])/2]},e.prototype.updateData=function(t,r,i){this.childAt(0).updateData(t,r,i),this._updateEffectSymbol(t,r)},e.prototype._updateSymbolPosition=function(t){var r=t.__p1,i=t.__p2,n=t.__cp1,o=t.__t<1?t.__t:2-t.__t,s=[t.x,t.y],l=s.slice(),u=kh,c=Sd;s[0]=u(r[0],n[0],i[0],o),s[1]=u(r[1],n[1],i[1],o);var h=t.__t<1?c(r[0],n[0],i[0],o):c(i[0],n[0],r[0],1-o),v=t.__t<1?c(r[1],n[1],i[1],o):c(i[1],n[1],r[1],1-o);t.rotation=-Math.atan2(v,h)-Math.PI/2,(this._symbolType==="line"||this._symbolType==="rect"||this._symbolType==="roundRect")&&(t.__lastT!==void 0&&t.__lastT<t.__t?(t.scaleY=zr(l,s)*1.05,o===1&&(s[0]=l[0]+(s[0]-l[0])/2,s[1]=l[1]+(s[1]-l[1])/2)):t.__lastT===1?t.scaleY=2*zr(r,s):t.scaleY=this._symbolScale[1]),t.__lastT=t.__t,t.ignore=!1,t.x=s[0],t.y=s[1]},e.prototype.updateLayout=function(t,r){this.childAt(0).updateLayout(t,r);var i=t.getItemModel(r).getModel("effect");this._updateEffectAnimation(t,i,r)},e}(X),mf=function(a){E(e,a);function e(t,r,i){var n=a.call(this)||this;return n._createPolyline(t,r,i),n}return e.prototype._createPolyline=function(t,r,i){var n=t.getItemLayout(r),o=new le({shape:{points:n}});this.add(o),this._updateCommonStl(t,r,i)},e.prototype.updateData=function(t,r,i){var n=t.hostModel,o=this.childAt(0),s={shape:{points:t.getItemLayout(r)}};dt(o,s,n,r),this._updateCommonStl(t,r,i)},e.prototype._updateCommonStl=function(t,r,i){var n=this.childAt(0),o=t.getItemModel(r),s=i&&i.emphasisLineStyle,l=i&&i.focus,u=i&&i.blurScope,c=i&&i.emphasisDisabled;if(!i||t.hasItemOption){var h=o.getModel("emphasis");s=h.getModel("lineStyle").getLineStyle(),c=h.get("disabled"),l=h.get("focus"),u=h.get("blurScope")}n.useStyle(t.getItemVisual(r,"style")),n.style.fill=null,n.style.strokeNoScale=!0;var v=n.ensureState("emphasis");v.style=s,xt(this,l,u,c)},e.prototype.updateLayout=function(t,r){var i=this.childAt(0);i.setShape("points",t.getItemLayout(r))},e}(X),e1=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t._lastFrame=0,t._lastFramePercent=0,t}return e.prototype.createLine=function(t,r,i){return new mf(t,r,i)},e.prototype._updateAnimationPoints=function(t,r){this._points=r;for(var i=[0],n=0,o=1;o<r.length;o++){var s=r[o-1],l=r[o];n+=zr(s,l),i.push(n)}if(n===0){this._length=0;return}for(var o=0;o<i.length;o++)i[o]/=n;this._offsets=i,this._length=n},e.prototype._getLineLength=function(){return this._length},e.prototype._updateSymbolPosition=function(t){var r=t.__t<1?t.__t:2-t.__t,i=this._points,n=this._offsets,o=i.length;if(n){var s=this._lastFrame,l;if(r<this._lastFramePercent){var u=Math.min(s+1,o-1);for(l=u;l>=0&&!(n[l]<=r);l--);l=Math.min(l,o-2)}else{for(l=s;l<o&&!(n[l]>r);l++);l=Math.min(l-1,o-2)}var c=(r-n[l])/(n[l+1]-n[l]),h=i[l],v=i[l+1];t.x=h[0]*(1-c)+c*v[0],t.y=h[1]*(1-c)+c*v[1];var f=t.__t<1?v[0]-h[0]:h[0]-v[0],p=t.__t<1?v[1]-h[1]:h[1]-v[1];t.rotation=-Math.atan2(p,f)-Math.PI/2,this._lastFrame=l,this._lastFramePercent=r,t.ignore=!1}},e}(yf),r1=function(){function a(){this.polyline=!1,this.curveness=0,this.segs=[]}return a}(),a1=function(a){E(e,a);function e(t){var r=a.call(this,t)||this;return r._off=0,r.hoverDataIdx=-1,r}return e.prototype.reset=function(){this.notClear=!1,this._off=0},e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new r1},e.prototype.buildPath=function(t,r){var i=r.segs,n=r.curveness,o;if(r.polyline)for(o=this._off;o<i.length;){var s=i[o++];if(s>0){t.moveTo(i[o++],i[o++]);for(var l=1;l<s;l++)t.lineTo(i[o++],i[o++])}}else for(o=this._off;o<i.length;){var u=i[o++],c=i[o++],h=i[o++],v=i[o++];if(t.moveTo(u,c),n>0){var f=(u+h)/2-(c-v)*n,p=(c+v)/2-(h-u)*n;t.quadraticCurveTo(f,p,h,v)}else t.lineTo(h,v)}this.incremental&&(this._off=o,this.notClear=!0)},e.prototype.findDataIndex=function(t,r){var i=this.shape,n=i.segs,o=i.curveness,s=this.style.lineWidth;if(i.polyline)for(var l=0,u=0;u<n.length;){var c=n[u++];if(c>0)for(var h=n[u++],v=n[u++],f=1;f<c;f++){var p=n[u++],d=n[u++];if(Xs(h,v,p,d,s,t,r))return l}l++}else for(var l=0,u=0;u<n.length;){var h=n[u++],v=n[u++],p=n[u++],d=n[u++];if(o>0){var g=(h+p)/2-(v-d)*o,y=(v+d)/2-(p-h)*o;if(xd(h,v,g,y,p,d,s,t,r))return l}else if(Xs(h,v,p,d,s,t,r))return l;l++}return-1},e.prototype.contain=function(t,r){var i=this.transformCoordToLocal(t,r),n=this.getBoundingRect();if(t=i[0],r=i[1],n.contain(t,r)){var o=this.hoverDataIdx=this.findDataIndex(t,r);return o>=0}return this.hoverDataIdx=-1,!1},e.prototype.getBoundingRect=function(){var t=this._rect;if(!t){for(var r=this.shape,i=r.segs,n=1/0,o=1/0,s=-1/0,l=-1/0,u=0;u<i.length;){var c=i[u++],h=i[u++];n=Math.min(c,n),s=Math.max(c,s),o=Math.min(h,o),l=Math.max(h,l)}t=this._rect=new Pt(n,o,s,l)}return t},e}(ie),i1=function(){function a(){this.group=new X}return a.prototype.updateData=function(e){this._clear();var t=this._create();t.setShape({segs:e.getLayout("linesPoints")}),this._setCommon(t,e)},a.prototype.incrementalPrepareUpdate=function(e){this.group.removeAll(),this._clear()},a.prototype.incrementalUpdate=function(e,t){var r=this._newAdded[0],i=t.getLayout("linesPoints"),n=r&&r.shape.segs;if(n&&n.length<2e4){var o=n.length,s=new Float32Array(o+i.length);s.set(n),s.set(i,o),r.setShape({segs:s})}else{this._newAdded=[];var l=this._create();l.incremental=!0,l.setShape({segs:i}),this._setCommon(l,t),l.__startIndex=e.start}},a.prototype.remove=function(){this._clear()},a.prototype.eachRendered=function(e){this._newAdded[0]&&e(this._newAdded[0])},a.prototype._create=function(){var e=new a1({cursor:"default",ignoreCoarsePointer:!0});return this._newAdded.push(e),this.group.add(e),e},a.prototype._setCommon=function(e,t,r){var i=t.hostModel;e.setShape({polyline:i.get("polyline"),curveness:i.get(["lineStyle","curveness"])}),e.useStyle(i.getModel("lineStyle").getLineStyle()),e.style.strokeNoScale=!0;var n=t.getVisual("style");n&&n.stroke&&e.setStyle("stroke",n.stroke),e.setStyle("fill",null);var o=ht(e);o.seriesIndex=i.seriesIndex,e.on("mousemove",function(s){o.dataIndex=null;var l=e.hoverDataIdx;l>0&&(o.dataIndex=l+e.__startIndex)})},a.prototype._clear=function(){this._newAdded=[],this.group.removeAll()},a}(),Sf={seriesType:"lines",plan:No(),reset:function(a){var e=a.coordinateSystem;if(e){var t=a.get("polyline"),r=a.pipelineContext.large;return{progress:function(i,n){var o=[];if(r){var s=void 0,l=i.end-i.start;if(t){for(var u=0,c=i.start;c<i.end;c++)u+=a.getLineCoordsCount(c);s=new Float32Array(l+u*2)}else s=new Float32Array(l*4);for(var h=0,v=[],c=i.start;c<i.end;c++){var f=a.getLineCoords(c,o);t&&(s[h++]=f);for(var p=0;p<f;p++)v=e.dataToPoint(o[p],!1,v),s[h++]=v[0],s[h++]=v[1]}n.setLayout("linesPoints",s)}else for(var c=i.start;c<i.end;c++){var d=n.getItemModel(c),f=a.getLineCoords(c,o),g=[];if(t)for(var y=0;y<f;y++)g.push(e.dataToPoint(o[y]));else{g[0]=e.dataToPoint(o[0]),g[1]=e.dataToPoint(o[1]);var m=d.get(["lineStyle","curveness"]);+m&&(g[2]=[(g[0][0]+g[1][0])/2-(g[0][1]-g[1][1])*m,(g[0][1]+g[1][1])/2-(g[1][0]-g[0][0])*m])}n.setItemLayout(c,g)}}}}}},n1=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i){var n=t.getData(),o=this._updateLineDraw(n,t),s=t.get("zlevel"),l=t.get(["effect","trailLength"]),u=i.getZr(),c=u.painter.getType()==="svg";c||u.painter.getLayer(s).clear(!0),this._lastZlevel!=null&&!c&&u.configLayer(this._lastZlevel,{motionBlur:!1}),this._showEffect(t)&&l>0&&(c||u.configLayer(s,{motionBlur:!0,lastFrameAlpha:Math.max(Math.min(l/10+.9,1),0)})),o.updateData(n);var h=t.get("clip",!0)&&Ai(t.coordinateSystem,!1,t);h?this.group.setClipPath(h):this.group.removeClipPath(),this._lastZlevel=s,this._finished=!0},e.prototype.incrementalPrepareRender=function(t,r,i){var n=t.getData(),o=this._updateLineDraw(n,t);o.incrementalPrepareUpdate(n),this._clearLayer(i),this._finished=!1},e.prototype.incrementalRender=function(t,r,i){this._lineDraw.incrementalUpdate(t,r.getData()),this._finished=t.end===r.getData().count()},e.prototype.eachRendered=function(t){this._lineDraw&&this._lineDraw.eachRendered(t)},e.prototype.updateTransform=function(t,r,i){var n=t.getData(),o=t.pipelineContext;if(!this._finished||o.large||o.progressiveRender)return{update:!0};var s=Sf.reset(t,r,i);s.progress&&s.progress({start:0,end:n.count(),count:n.count()},n),this._lineDraw.updateLayout(),this._clearLayer(i)},e.prototype._updateLineDraw=function(t,r){var i=this._lineDraw,n=this._showEffect(r),o=!!r.get("polyline"),s=r.pipelineContext,l=s.large;return(!i||n!==this._hasEffet||o!==this._isPolyline||l!==this._isLargeDraw)&&(i&&i.remove(),i=this._lineDraw=l?new i1:new ss(o?n?e1:mf:n?yf:os),this._hasEffet=n,this._isPolyline=o,this._isLargeDraw=l),this.group.add(i.group),i},e.prototype._showEffect=function(t){return!!t.get(["effect","show"])},e.prototype._clearLayer=function(t){var r=t.getZr(),i=r.painter.getType()==="svg";!i&&this._lastZlevel!=null&&r.painter.getLayer(this._lastZlevel).clear(!0)},e.prototype.remove=function(t,r){this._lineDraw&&this._lineDraw.remove(),this._lineDraw=null,this._clearLayer(r)},e.prototype.dispose=function(t,r){this.remove(t,r)},e.type="lines",e}(bt),o1=typeof Uint32Array>"u"?Array:Uint32Array,s1=typeof Float64Array>"u"?Array:Float64Array;function Ru(a){var e=a.data;e&&e[0]&&e[0][0]&&e[0][0].coord&&(a.data=G(e,function(t){var r=[t[0].coord,t[1].coord],i={coords:r};return t[0].name&&(i.fromName=t[0].name),t[1].name&&(i.toName=t[1].name),Po([i,t[0],t[1]])}))}var l1=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.visualStyleAccessPath="lineStyle",t.visualDrawType="stroke",t}return e.prototype.init=function(t){t.data=t.data||[],Ru(t);var r=this._processFlatCoordsArray(t.data);this._flatCoords=r.flatCoords,this._flatCoordsOffset=r.flatCoordsOffset,r.flatCoords&&(t.data=new Float32Array(r.count)),a.prototype.init.apply(this,arguments)},e.prototype.mergeOption=function(t){if(Ru(t),t.data){var r=this._processFlatCoordsArray(t.data);this._flatCoords=r.flatCoords,this._flatCoordsOffset=r.flatCoordsOffset,r.flatCoords&&(t.data=new Float32Array(r.count))}a.prototype.mergeOption.apply(this,arguments)},e.prototype.appendData=function(t){var r=this._processFlatCoordsArray(t.data);r.flatCoords&&(this._flatCoords?(this._flatCoords=Ua(this._flatCoords,r.flatCoords),this._flatCoordsOffset=Ua(this._flatCoordsOffset,r.flatCoordsOffset)):(this._flatCoords=r.flatCoords,this._flatCoordsOffset=r.flatCoordsOffset),t.data=new Float32Array(r.count)),this.getRawData().appendData(t.data)},e.prototype._getCoordsFromItemModel=function(t){var r=this.getData().getItemModel(t),i=r.option instanceof Array?r.option:r.getShallow("coords");return i},e.prototype.getLineCoordsCount=function(t){return this._flatCoordsOffset?this._flatCoordsOffset[t*2+1]:this._getCoordsFromItemModel(t).length},e.prototype.getLineCoords=function(t,r){if(this._flatCoordsOffset){for(var i=this._flatCoordsOffset[t*2],n=this._flatCoordsOffset[t*2+1],o=0;o<n;o++)r[o]=r[o]||[],r[o][0]=this._flatCoords[i+o*2],r[o][1]=this._flatCoords[i+o*2+1];return n}else{for(var s=this._getCoordsFromItemModel(t),o=0;o<s.length;o++)r[o]=r[o]||[],r[o][0]=s[o][0],r[o][1]=s[o][1];return s.length}},e.prototype._processFlatCoordsArray=function(t){var r=0;if(this._flatCoords&&(r=this._flatCoords.length),jt(t[0])){for(var i=t.length,n=new o1(i),o=new s1(i),s=0,l=0,u=0,c=0;c<i;){u++;var h=t[c++];n[l++]=s+r,n[l++]=h;for(var v=0;v<h;v++){var f=t[c++],p=t[c++];o[s++]=f,o[s++]=p}}return{flatCoordsOffset:new Uint32Array(n.buffer,0,l),flatCoords:o,count:u}}return{flatCoordsOffset:null,flatCoords:null,count:t.length}},e.prototype.getInitialData=function(t,r){var i=new Ft(["value"],this);return i.hasItemOption=!1,i.initData(t.data,[],function(n,o,s,l){if(n instanceof Array)return NaN;i.hasItemOption=!0;var u=n.value;if(u!=null)return u instanceof Array?u[l]:u}),i},e.prototype.formatTooltip=function(t,r,i){var n=this.getData(),o=n.getItemModel(t),s=o.get("name");if(s)return s;var l=o.get("fromName"),u=o.get("toName"),c=[];return l!=null&&c.push(l),u!=null&&c.push(u),Zt("nameValue",{name:c.join(" > ")})},e.prototype.preventIncremental=function(){return!!this.get(["effect","show"])},e.prototype.getProgressive=function(){var t=this.option.progressive;return t??(this.option.large?1e4:this.get("progressive"))},e.prototype.getProgressiveThreshold=function(){var t=this.option.progressiveThreshold;return t??(this.option.large?2e4:this.get("progressiveThreshold"))},e.prototype.getZLevelKey=function(){var t=this.getModel("effect"),r=t.get("trailLength");return this.getData().count()>this.getProgressiveThreshold()?this.id:t.get("show")&&r>0?r+"":""},e.type="series.lines",e.dependencies=["grid","polar","geo","calendar"],e.defaultOption={coordinateSystem:"geo",z:2,legendHoverLink:!0,xAxisIndex:0,yAxisIndex:0,symbol:["none","none"],symbolSize:[10,10],geoIndex:0,effect:{show:!1,period:4,constantSpeed:0,symbol:"circle",symbolSize:3,loop:!0,trailLength:.2},large:!1,largeThreshold:2e3,polyline:!1,clip:!0,label:{show:!1,position:"end"},lineStyle:{opacity:.5}},e}(Ct);function Ia(a){return a instanceof Array||(a=[a,a]),a}var u1={seriesType:"lines",reset:function(a){var e=Ia(a.get("symbol")),t=Ia(a.get("symbolSize")),r=a.getData();r.setVisual("fromSymbol",e&&e[0]),r.setVisual("toSymbol",e&&e[1]),r.setVisual("fromSymbolSize",t&&t[0]),r.setVisual("toSymbolSize",t&&t[1]);function i(n,o){var s=n.getItemModel(o),l=Ia(s.getShallow("symbol",!0)),u=Ia(s.getShallow("symbolSize",!0));l[0]&&n.setItemVisual(o,"fromSymbol",l[0]),l[1]&&n.setItemVisual(o,"toSymbol",l[1]),u[0]&&n.setItemVisual(o,"fromSymbolSize",u[0]),u[1]&&n.setItemVisual(o,"toSymbolSize",u[1])}return{dataEach:r.hasItemOption?i:null}}};function c1(a){a.registerChartView(n1),a.registerSeriesModel(l1),a.registerLayout(Sf),a.registerVisual(u1)}var h1=256,v1=function(){function a(){this.blurSize=30,this.pointSize=20,this.maxOpacity=1,this.minOpacity=0,this._gradientPixels={inRange:null,outOfRange:null};var e=Ks.createCanvas();this.canvas=e}return a.prototype.update=function(e,t,r,i,n,o){var s=this._getBrush(),l=this._getGradient(n,"inRange"),u=this._getGradient(n,"outOfRange"),c=this.pointSize+this.blurSize,h=this.canvas,v=h.getContext("2d"),f=e.length;h.width=t,h.height=r;for(var p=0;p<f;++p){var d=e[p],g=d[0],y=d[1],m=d[2],S=i(m);v.globalAlpha=S,v.drawImage(s,g-c,y-c)}if(!h.width||!h.height)return h;for(var x=v.getImageData(0,0,h.width,h.height),b=x.data,_=0,w=b.length,A=this.minOpacity,C=this.maxOpacity,T=C-A;_<w;){var S=b[_+3]/256,I=Math.floor(S*(h1-1))*4;if(S>0){var L=o(S)?l:u;S>0&&(S=S*T+A),b[_++]=L[I],b[_++]=L[I+1],b[_++]=L[I+2],b[_++]=L[I+3]*S*256}else _+=4}return v.putImageData(x,0,0),h},a.prototype._getBrush=function(){var e=this._brushCanvas||(this._brushCanvas=Ks.createCanvas()),t=this.pointSize+this.blurSize,r=t*2;e.width=r,e.height=r;var i=e.getContext("2d");return i.clearRect(0,0,r,r),i.shadowOffsetX=r,i.shadowBlur=this.blurSize,i.shadowColor="#000",i.beginPath(),i.arc(-t,t,this.pointSize,0,Math.PI*2,!0),i.closePath(),i.fill(),e},a.prototype._getGradient=function(e,t){for(var r=this._gradientPixels,i=r[t]||(r[t]=new Uint8ClampedArray(256*4)),n=[0,0,0,0],o=0,s=0;s<256;s++)e[t](s/255,!0,n),i[o++]=n[0],i[o++]=n[1],i[o++]=n[2],i[o++]=n[3];return i},a}();function f1(a,e,t){var r=a[1]-a[0];e=G(e,function(o){return{interval:[(o.interval[0]-a[0])/r,(o.interval[1]-a[0])/r]}});var i=e.length,n=0;return function(o){var s;for(s=n;s<i;s++){var l=e[s].interval;if(l[0]<=o&&o<=l[1]){n=s;break}}if(s===i)for(s=n-1;s>=0;s--){var l=e[s].interval;if(l[0]<=o&&o<=l[1]){n=s;break}}return s>=0&&s<i&&t[s]}}function p1(a,e){var t=a[1]-a[0];return e=[(e[0]-a[0])/t,(e[1]-a[0])/t],function(r){return r>=e[0]&&r<=e[1]}}function Eu(a){var e=a.dimensions;return e[0]==="lng"&&e[1]==="lat"}var d1=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i){var n;r.eachComponent("visualMap",function(s){s.eachTargetSeries(function(l){l===t&&(n=s)})}),this._progressiveEls=null,this.group.removeAll();var o=t.coordinateSystem;o.type==="cartesian2d"||o.type==="calendar"?this._renderOnCartesianAndCalendar(t,i,0,t.getData().count()):Eu(o)&&this._renderOnGeo(o,t,n,i)},e.prototype.incrementalPrepareRender=function(t,r,i){this.group.removeAll()},e.prototype.incrementalRender=function(t,r,i,n){var o=r.coordinateSystem;o&&(Eu(o)?this.render(r,i,n):(this._progressiveEls=[],this._renderOnCartesianAndCalendar(r,n,t.start,t.end,!0)))},e.prototype.eachRendered=function(t){_i(this._progressiveEls||this.group,t)},e.prototype._renderOnCartesianAndCalendar=function(t,r,i,n,o){var s=t.coordinateSystem,l=Ti(s,"cartesian2d"),u,c,h,v;if(l){var f=s.getAxis("x"),p=s.getAxis("y");u=f.getBandWidth()+.5,c=p.getBandWidth()+.5,h=f.scale.getExtent(),v=p.scale.getExtent()}for(var d=this.group,g=t.getData(),y=t.getModel(["emphasis","itemStyle"]).getItemStyle(),m=t.getModel(["blur","itemStyle"]).getItemStyle(),S=t.getModel(["select","itemStyle"]).getItemStyle(),x=t.get(["itemStyle","borderRadius"]),b=zt(t),_=t.getModel("emphasis"),w=_.get("focus"),A=_.get("blurScope"),C=_.get("disabled"),T=l?[g.mapDimension("x"),g.mapDimension("y"),g.mapDimension("value")]:[g.mapDimension("time"),g.mapDimension("value")],I=i;I<n;I++){var L=void 0,M=g.getItemVisual(I,"style");if(l){var R=g.get(T[0],I),P=g.get(T[1],I);if(isNaN(g.get(T[2],I))||isNaN(R)||isNaN(P)||R<h[0]||R>h[1]||P<v[0]||P>v[1])continue;var k=s.dataToPoint([R,P]);L=new pt({shape:{x:k[0]-u/2,y:k[1]-c/2,width:u,height:c},style:M})}else{if(isNaN(g.get(T[1],I)))continue;L=new pt({z2:1,shape:s.dataToRect([g.get(T[0],I)]).contentShape,style:M})}if(g.hasItemOption){var V=g.getItemModel(I),N=V.getModel("emphasis");y=N.getModel("itemStyle").getItemStyle(),m=V.getModel(["blur","itemStyle"]).getItemStyle(),S=V.getModel(["select","itemStyle"]).getItemStyle(),x=V.get(["itemStyle","borderRadius"]),w=N.get("focus"),A=N.get("blurScope"),C=N.get("disabled"),b=zt(V)}L.shape.r=x;var z=t.getRawValue(I),H="-";z&&z[2]!=null&&(H=z[2]+""),Wt(L,b,{labelFetcher:t,labelDataIndex:I,defaultOpacity:M.opacity,defaultText:H}),L.ensureState("emphasis").style=y,L.ensureState("blur").style=m,L.ensureState("select").style=S,xt(L,w,A,C),L.incremental=o,o&&(L.states.emphasis.hoverLayer=!0),d.add(L),g.setItemGraphicEl(I,L),this._progressiveEls&&this._progressiveEls.push(L)}},e.prototype._renderOnGeo=function(t,r,i,n){var o=i.targetVisuals.inRange,s=i.targetVisuals.outOfRange,l=r.getData(),u=this._hmLayer||this._hmLayer||new v1;u.blurSize=r.get("blurSize"),u.pointSize=r.get("pointSize"),u.minOpacity=r.get("minOpacity"),u.maxOpacity=r.get("maxOpacity");var c=t.getViewRect().clone(),h=t.getRoamTransform();c.applyTransform(h);var v=Math.max(c.x,0),f=Math.max(c.y,0),p=Math.min(c.width+c.x,n.getWidth()),d=Math.min(c.height+c.y,n.getHeight()),g=p-v,y=d-f,m=[l.mapDimension("lng"),l.mapDimension("lat"),l.mapDimension("value")],S=l.mapArray(m,function(w,A,C){var T=t.dataToPoint([w,A]);return T[0]-=v,T[1]-=f,T.push(C),T}),x=i.getExtent(),b=i.type==="visualMap.continuous"?p1(x,i.option.range):f1(x,i.getPieceList(),i.option.selected);u.update(S,g,y,o.color.getNormalizer(),{inRange:o.color.getColorMapper(),outOfRange:s.color.getColorMapper()},b);var _=new pe({style:{width:g,height:y,x:v,y:f,image:u.canvas},silent:!0});this.group.add(_)},e.type="heatmap",e}(bt),g1=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getInitialData=function(t,r){return br(null,this,{generateCoord:"value"})},e.prototype.preventIncremental=function(){var t=Vh.get(this.get("coordinateSystem"));if(t&&t.dimensions)return t.dimensions[0]==="lng"&&t.dimensions[1]==="lat"},e.type="series.heatmap",e.dependencies=["grid","geo","calendar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,geoIndex:0,blurSize:30,pointSize:20,maxOpacity:1,minOpacity:0,select:{itemStyle:{borderColor:"#212121"}}},e}(Ct);function y1(a){a.registerChartView(d1),a.registerSeriesModel(g1)}var m1=["itemStyle","borderWidth"],ku=[{xy:"x",wh:"width",index:0,posDesc:["left","right"]},{xy:"y",wh:"height",index:1,posDesc:["top","bottom"]}],mn=new la,S1=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i){var n=this.group,o=t.getData(),s=this._data,l=t.coordinateSystem,u=l.getBaseAxis(),c=u.isHorizontal(),h=l.master.getRect(),v={ecSize:{width:i.getWidth(),height:i.getHeight()},seriesModel:t,coordSys:l,coordSysExtent:[[h.x,h.x+h.width],[h.y,h.y+h.height]],isHorizontal:c,valueDim:ku[+c],categoryDim:ku[1-+c]};o.diff(s).add(function(p){if(o.hasValue(p)){var d=Nu(o,p),g=Vu(o,p,d,v),y=zu(o,v,g);o.setItemGraphicEl(p,y),n.add(y),Bu(y,v,g)}}).update(function(p,d){var g=s.getItemGraphicEl(d);if(!o.hasValue(p)){n.remove(g);return}var y=Nu(o,p),m=Vu(o,p,y,v),S=Tf(o,m);g&&S!==g.__pictorialShapeStr&&(n.remove(g),o.setItemGraphicEl(p,null),g=null),g?D1(g,v,m):g=zu(o,v,m,!0),o.setItemGraphicEl(p,g),g.__pictorialSymbolMeta=m,n.add(g),Bu(g,v,m)}).remove(function(p){var d=s.getItemGraphicEl(p);d&&Ou(s,p,d.__pictorialSymbolMeta.animationModel,d)}).execute();var f=t.get("clip",!0)?Ai(t.coordinateSystem,!1,t):null;return f?n.setClipPath(f):n.removeClipPath(),this._data=o,this.group},e.prototype.remove=function(t,r){var i=this.group,n=this._data;t.get("animation")?n&&n.eachItemGraphicEl(function(o){Ou(n,ht(o).dataIndex,t,o)}):i.removeAll()},e.type="pictorialBar",e}(bt);function Vu(a,e,t,r){var i=a.getItemLayout(e),n=t.get("symbolRepeat"),o=t.get("symbolClip"),s=t.get("symbolPosition")||"start",l=t.get("symbolRotate"),u=(l||0)*Math.PI/180||0,c=t.get("symbolPatternSize")||2,h=t.isAnimationEnabled(),v={dataIndex:e,layout:i,itemModel:t,symbolType:a.getItemVisual(e,"symbol")||"circle",style:a.getItemVisual(e,"style"),symbolClip:o,symbolRepeat:n,symbolRepeatDirection:t.get("symbolRepeatDirection"),symbolPatternSize:c,rotation:u,animationModel:h?t:null,hoverScale:h&&t.get(["emphasis","scale"]),z2:t.getShallow("z",!0)||0};x1(t,n,i,r,v),b1(a,e,i,n,o,v.boundingLength,v.pxSign,c,r,v),_1(t,v.symbolScale,u,r,v);var f=v.symbolSize,p=ha(t.get("symbolOffset"),f);return w1(t,f,i,n,o,p,s,v.valueLineWidth,v.boundingLength,v.repeatCutLength,r,v),v}function x1(a,e,t,r,i){var n=r.valueDim,o=a.get("symbolBoundingData"),s=r.coordSys.getOtherAxis(r.coordSys.getBaseAxis()),l=s.toGlobalCoord(s.dataToCoord(0)),u=1-+(t[n.wh]<=0),c;if(U(o)){var h=[Sn(s,o[0])-l,Sn(s,o[1])-l];h[1]<h[0]&&h.reverse(),c=h[u]}else o!=null?c=Sn(s,o)-l:e?c=r.coordSysExtent[n.index][u]-l:c=t[n.wh];i.boundingLength=c,e&&(i.repeatCutLength=t[n.wh]);var v=n.xy==="x",f=s.inverse;i.pxSign=v&&!f||!v&&f?c>=0?1:-1:c>0?1:-1}function Sn(a,e){return a.toGlobalCoord(a.dataToCoord(a.scale.parse(e)))}function b1(a,e,t,r,i,n,o,s,l,u){var c=l.valueDim,h=l.categoryDim,v=Math.abs(t[h.wh]),f=a.getItemVisual(e,"symbolSize"),p;U(f)?p=f.slice():f==null?p=["100%","100%"]:p=[f,f],p[h.index]=O(p[h.index],v),p[c.index]=O(p[c.index],r?v:Math.abs(n)),u.symbolSize=p;var d=u.symbolScale=[p[0]/s,p[1]/s];d[c.index]*=(l.isHorizontal?-1:1)*o}function _1(a,e,t,r,i){var n=a.get(m1)||0;n&&(mn.attr({scaleX:e[0],scaleY:e[1],rotation:t}),mn.updateTransform(),n/=mn.getLineScale(),n*=e[r.valueDim.index]),i.valueLineWidth=n||0}function w1(a,e,t,r,i,n,o,s,l,u,c,h){var v=c.categoryDim,f=c.valueDim,p=h.pxSign,d=Math.max(e[f.index]+s,0),g=d;if(r){var y=Math.abs(l),m=qt(a.get("symbolMargin"),"15%")+"",S=!1;m.lastIndexOf("!")===m.length-1&&(S=!0,m=m.slice(0,m.length-1));var x=O(m,e[f.index]),b=Math.max(d+x*2,0),_=S?0:x*2,w=bd(r),A=w?r:Gu((y+_)/b),C=y-A*d;x=C/2/(S?A:Math.max(A-1,1)),b=d+x*2,_=S?0:x*2,!w&&r!=="fixed"&&(A=u?Gu((Math.abs(u)+_)/b):0),g=A*b-_,h.repeatTimes=A,h.symbolMargin=x}var T=p*(g/2),I=h.pathPosition=[];I[v.index]=t[v.wh]/2,I[f.index]=o==="start"?T:o==="end"?l-T:l/2,n&&(I[0]+=n[0],I[1]+=n[1]);var L=h.bundlePosition=[];L[v.index]=t[v.xy],L[f.index]=t[f.xy];var M=h.barRectShape=F({},t);M[f.wh]=p*Math.max(Math.abs(t[f.wh]),Math.abs(I[f.index]+T)),M[v.wh]=t[v.wh];var R=h.clipShape={};R[v.xy]=-t[v.xy],R[v.wh]=c.ecSize[v.wh],R[f.xy]=0,R[f.wh]=t[f.wh]}function xf(a){var e=a.symbolPatternSize,t=Et(a.symbolType,-e/2,-e/2,e,e);return t.attr({culling:!0}),t.type!=="image"&&t.setStyle({strokeNoScale:!0}),t}function bf(a,e,t,r){var i=a.__pictorialBundle,n=t.symbolSize,o=t.valueLineWidth,s=t.pathPosition,l=e.valueDim,u=t.repeatTimes||0,c=0,h=n[e.valueDim.index]+o+t.symbolMargin*2;for(ys(a,function(d){d.__pictorialAnimationIndex=c,d.__pictorialRepeatTimes=u,c<u?pr(d,null,p(c),t,r):pr(d,null,{scaleX:0,scaleY:0},t,r,function(){i.remove(d)}),c++});c<u;c++){var v=xf(t);v.__pictorialAnimationIndex=c,v.__pictorialRepeatTimes=u,i.add(v);var f=p(c);pr(v,{x:f.x,y:f.y,scaleX:0,scaleY:0},{scaleX:f.scaleX,scaleY:f.scaleY,rotation:f.rotation},t,r)}function p(d){var g=s.slice(),y=t.pxSign,m=d;return(t.symbolRepeatDirection==="start"?y>0:y<0)&&(m=u-1-d),g[l.index]=h*(m-u/2+.5)+s[l.index],{x:g[0],y:g[1],scaleX:t.symbolScale[0],scaleY:t.symbolScale[1],rotation:t.rotation}}}function _f(a,e,t,r){var i=a.__pictorialBundle,n=a.__pictorialMainPath;n?pr(n,null,{x:t.pathPosition[0],y:t.pathPosition[1],scaleX:t.symbolScale[0],scaleY:t.symbolScale[1],rotation:t.rotation},t,r):(n=a.__pictorialMainPath=xf(t),i.add(n),pr(n,{x:t.pathPosition[0],y:t.pathPosition[1],scaleX:0,scaleY:0,rotation:t.rotation},{scaleX:t.symbolScale[0],scaleY:t.symbolScale[1]},t,r))}function wf(a,e,t){var r=F({},e.barRectShape),i=a.__pictorialBarRect;i?pr(i,null,{shape:r},e,t):(i=a.__pictorialBarRect=new pt({z2:2,shape:r,silent:!0,style:{stroke:"transparent",fill:"transparent",lineWidth:0}}),i.disableMorphing=!0,a.add(i))}function Af(a,e,t,r){if(t.symbolClip){var i=a.__pictorialClipPath,n=F({},t.clipShape),o=e.valueDim,s=t.animationModel,l=t.dataIndex;if(i)dt(i,{shape:n},s,l);else{n[o.wh]=0,i=new pt({shape:n}),a.__pictorialBundle.setClipPath(i),a.__pictorialClipPath=i;var u={};u[o.wh]=t.clipShape[o.wh],_r[r?"updateProps":"initProps"](i,{shape:u},s,l)}}}function Nu(a,e){var t=a.getItemModel(e);return t.getAnimationDelayParams=A1,t.isAnimationEnabled=T1,t}function A1(a){return{index:a.__pictorialAnimationIndex,count:a.__pictorialRepeatTimes}}function T1(){return this.parentModel.isAnimationEnabled()&&!!this.getShallow("animation")}function zu(a,e,t,r){var i=new X,n=new X;return i.add(n),i.__pictorialBundle=n,n.x=t.bundlePosition[0],n.y=t.bundlePosition[1],t.symbolRepeat?bf(i,e,t):_f(i,e,t),wf(i,t,r),Af(i,e,t,r),i.__pictorialShapeStr=Tf(a,t),i.__pictorialSymbolMeta=t,i}function D1(a,e,t){var r=t.animationModel,i=t.dataIndex,n=a.__pictorialBundle;dt(n,{x:t.bundlePosition[0],y:t.bundlePosition[1]},r,i),t.symbolRepeat?bf(a,e,t,!0):_f(a,e,t,!0),wf(a,t,!0),Af(a,e,t,!0)}function Ou(a,e,t,r){var i=r.__pictorialBarRect;i&&i.removeTextContent();var n=[];ys(r,function(o){n.push(o)}),r.__pictorialMainPath&&n.push(r.__pictorialMainPath),r.__pictorialClipPath&&(t=null),D(n,function(o){Ya(o,{scaleX:0,scaleY:0},t,e,function(){r.parent&&r.parent.remove(r)})}),a.setItemGraphicEl(e,null)}function Tf(a,e){return[a.getItemVisual(e.dataIndex,"symbol")||"none",!!e.symbolRepeat,!!e.symbolClip].join(":")}function ys(a,e,t){D(a.__pictorialBundle.children(),function(r){r!==a.__pictorialBarRect&&e.call(t,r)})}function pr(a,e,t,r,i,n){e&&a.attr(e),r.symbolClip&&!i?t&&a.attr(t):t&&_r[i?"updateProps":"initProps"](a,t,r.animationModel,r.dataIndex,n)}function Bu(a,e,t){var r=t.dataIndex,i=t.itemModel,n=i.getModel("emphasis"),o=n.getModel("itemStyle").getItemStyle(),s=i.getModel(["blur","itemStyle"]).getItemStyle(),l=i.getModel(["select","itemStyle"]).getItemStyle(),u=i.getShallow("cursor"),c=n.get("focus"),h=n.get("blurScope"),v=n.get("scale");ys(a,function(d){if(d instanceof pe){var g=d.style;d.useStyle(F({image:g.image,x:g.x,y:g.y,width:g.width,height:g.height},t.style))}else d.useStyle(t.style);var y=d.ensureState("emphasis");y.style=o,v&&(y.scaleX=d.scaleX*1.1,y.scaleY=d.scaleY*1.1),d.ensureState("blur").style=s,d.ensureState("select").style=l,u&&(d.cursor=u),d.z2=t.z2});var f=e.valueDim.posDesc[+(t.boundingLength>0)],p=a.__pictorialBarRect;p.ignoreClip=!0,Wt(p,zt(i),{labelFetcher:e.seriesModel,labelDataIndex:r,defaultText:Yn(e.seriesModel.getData(),r),inheritColor:t.style.fill,defaultOpacity:t.style.opacity,defaultOutsidePosition:f}),xt(a,c,h,n.get("disabled"))}function Gu(a){var e=Math.round(a);return Math.abs(a-e)<1e-4?e:Math.ceil(a)}var C1=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t.defaultSymbol="roundRect",t}return e.prototype.getInitialData=function(t){return t.stack=null,a.prototype.getInitialData.apply(this,arguments)},e.type="series.pictorialBar",e.dependencies=["grid"],e.defaultOption=Je(qs.defaultOption,{symbol:"circle",symbolSize:null,symbolRotate:null,symbolPosition:null,symbolOffset:null,symbolMargin:null,symbolRepeat:!1,symbolRepeatDirection:"end",symbolClip:!1,symbolBoundingData:null,symbolPatternSize:400,barGap:"-100%",clip:!1,progressive:0,emphasis:{scale:!1},select:{itemStyle:{borderColor:"#212121"}}}),e}(qs);function I1(a){a.registerChartView(S1),a.registerSeriesModel(C1),a.registerLayout(a.PRIORITY.VISUAL.LAYOUT,q(wd,"pictorialBar")),a.registerLayout(a.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,_d("pictorialBar"))}var L1=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._layers=[],t}return e.prototype.render=function(t,r,i){var n=t.getData(),o=this,s=this.group,l=t.getLayerSeries(),u=n.getLayout("layoutInfo"),c=u.rect,h=u.boundaryGap;s.x=0,s.y=c.y+h[0];function v(g){return g.name}var f=new Ar(this._layersSeries||[],l,v,v),p=[];f.add(W(d,this,"add")).update(W(d,this,"update")).remove(W(d,this,"remove")).execute();function d(g,y,m){var S=o._layers;if(g==="remove"){s.remove(S[y]);return}for(var x=[],b=[],_,w=l[y].indices,A=0;A<w.length;A++){var C=n.getItemLayout(w[A]),T=C.x,I=C.y0,L=C.y;x.push(T,I),b.push(T,I+L),_=n.getItemVisual(w[A],"style")}var M,R=n.getItemLayout(w[0]),P=t.getModel("label"),k=P.get("margin"),V=t.getModel("emphasis");if(g==="add"){var N=p[y]=new X;M=new Ad({shape:{points:x,stackedOnPoints:b,smooth:.4,stackedOnSmooth:.4,smoothConstraint:!1},z2:0}),N.add(M),s.add(N),t.isAnimationEnabled()&&M.setClipPath(M1(M.getBoundingRect(),t,function(){M.removeClipPath()}))}else{var N=S[m];M=N.childAt(0),s.add(N),p[y]=N,dt(M,{shape:{points:x,stackedOnPoints:b}},t),xe(M)}Wt(M,zt(t),{labelDataIndex:w[A-1],defaultText:n.getName(w[A-1]),inheritColor:_.fill},{normal:{verticalAlign:"middle"}}),M.setTextConfig({position:null,local:!0});var z=M.getTextContent();z&&(z.x=R.x-k,z.y=R.y0+R.y/2),M.useStyle(_),n.setItemGraphicEl(y,M),Ht(M,t),xt(M,V.get("focus"),V.get("blurScope"),V.get("disabled"))}this._layersSeries=l,this._layers=p},e.type="themeRiver",e}(bt);function M1(a,e,t){var r=new pt({shape:{x:a.x-10,y:a.y-10,width:0,height:a.height+20}});return Gt(r,{shape:{x:a.x-50,width:a.width+100,height:a.height+20}},e,t),r}var xn=2,P1=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t){a.prototype.init.apply(this,arguments),this.legendVisualProvider=new pa(W(this.getData,this),W(this.getRawData,this))},e.prototype.fixData=function(t){var r=t.length,i={},n=Zn(t,function(v){return i.hasOwnProperty(v[0]+"")||(i[v[0]+""]=-1),v[2]}),o=[];n.buckets.each(function(v,f){o.push({name:f,dataList:v})});for(var s=o.length,l=0;l<s;++l){for(var u=o[l].name,c=0;c<o[l].dataList.length;++c){var h=o[l].dataList[c][0]+"";i[h]=l}for(var h in i)i.hasOwnProperty(h)&&i[h]!==l&&(i[h]=l,t[r]=[h,0,u],r++)}return t},e.prototype.getInitialData=function(t,r){for(var i=this.getReferringComponents("singleAxis",fe).models[0],n=i.get("type"),o=Yt(t.data,function(p){return p[2]!==void 0}),s=this.fixData(o||[]),l=[],u=this.nameMap=et(),c=0,h=0;h<s.length;++h)l.push(s[h][xn]),u.get(s[h][xn])||(u.set(s[h][xn],c),c++);var v=fi(s,{coordDimensions:["single"],dimensionsDefine:[{name:"time",type:Un(n)},{name:"value",type:"float"},{name:"name",type:"ordinal"}],encodeDefine:{single:0,value:1,itemName:2}}).dimensions,f=new Ft(v,this);return f.initData(s),f},e.prototype.getLayerSeries=function(){for(var t=this.getData(),r=t.count(),i=[],n=0;n<r;++n)i[n]=n;var o=t.mapDimension("single"),s=Zn(i,function(u){return t.get("name",u)}),l=[];return s.buckets.each(function(u,c){u.sort(function(h,v){return t.get(o,h)-t.get(o,v)}),l.push({name:c,indices:u})}),l},e.prototype.getAxisTooltipData=function(t,r,i){U(t)||(t=t?[t]:[]);for(var n=this.getData(),o=this.getLayerSeries(),s=[],l=o.length,u,c=0;c<l;++c){for(var h=Number.MAX_VALUE,v=-1,f=o[c].indices.length,p=0;p<f;++p){var d=n.get(t[0],o[c].indices[p]),g=Math.abs(d-r);g<=h&&(u=d,h=g,v=o[c].indices[p])}s.push(v)}return{dataIndices:s,nestestValue:u}},e.prototype.formatTooltip=function(t,r,i){var n=this.getData(),o=n.getName(t),s=n.get(n.mapDimension("value"),t);return Zt("nameValue",{name:o,value:s})},e.type="series.themeRiver",e.dependencies=["singleAxis"],e.defaultOption={z:2,colorBy:"data",coordinateSystem:"singleAxis",boundaryGap:["10%","10%"],singleAxisIndex:0,animationEasing:"linear",label:{margin:4,show:!0,position:"left",fontSize:11},emphasis:{label:{show:!0}}},e}(Ct);function R1(a,e){a.eachSeriesByType("themeRiver",function(t){var r=t.getData(),i=t.coordinateSystem,n={},o=i.getRect();n.rect=o;var s=t.get("boundaryGap"),l=i.getAxis();if(n.boundaryGap=s,l.orient==="horizontal"){s[0]=O(s[0],o.height),s[1]=O(s[1],o.height);var u=o.height-s[0]-s[1];Fu(r,t,u)}else{s[0]=O(s[0],o.width),s[1]=O(s[1],o.width);var c=o.width-s[0]-s[1];Fu(r,t,c)}r.setLayout("layoutInfo",n)})}function Fu(a,e,t){if(a.count())for(var r=e.coordinateSystem,i=e.getLayerSeries(),n=a.mapDimension("single"),o=a.mapDimension("value"),s=G(i,function(g){return G(g.indices,function(y){var m=r.dataToPoint(a.get(n,y));return m[1]=a.get(o,y),m})}),l=E1(s),u=l.y0,c=t/l.max,h=i.length,v=i[0].indices.length,f,p=0;p<v;++p){f=u[p]*c,a.setItemLayout(i[0].indices[p],{layerIndex:0,x:s[0][p][0],y0:f,y:s[0][p][1]*c});for(var d=1;d<h;++d)f+=s[d-1][p][1]*c,a.setItemLayout(i[d].indices[p],{layerIndex:d,x:s[d][p][0],y0:f,y:s[d][p][1]*c})}}function E1(a){for(var e=a.length,t=a[0].length,r=[],i=[],n=0,o=0;o<t;++o){for(var s=0,l=0;l<e;++l)s+=a[l][o][1];s>n&&(n=s),r.push(s)}for(var u=0;u<t;++u)i[u]=(n-r[u])/2;n=0;for(var c=0;c<t;++c){var h=r[c]+i[c];h>n&&(n=h)}return{y0:i,max:n}}function k1(a){a.registerChartView(L1),a.registerSeriesModel(P1),a.registerLayout(R1),a.registerProcessor(fa("themeRiver"))}var V1=2,N1=4,Hu=function(a){E(e,a);function e(t,r,i,n){var o=a.call(this)||this;o.z2=V1,o.textConfig={inside:!0},ht(o).seriesIndex=r.seriesIndex;var s=new ut({z2:N1,silent:t.getModel().get(["label","silent"])});return o.setTextContent(s),o.updateData(!0,t,r,i,n),o}return e.prototype.updateData=function(t,r,i,n,o){this.node=r,r.piece=this,i=i||this._seriesModel,n=n||this._ecModel;var s=this;ht(s).dataIndex=r.dataIndex;var l=r.getModel(),u=l.getModel("emphasis"),c=r.getLayout(),h=F({},c);h.label=null;var v=r.getVisual("style");v.lineJoin="bevel";var f=r.getVisual("decal");f&&(v.decal=Si(f,o));var p=cr(l.getModel("itemStyle"),h,!0);F(h,p),D(Wn,function(m){var S=s.ensureState(m),x=l.getModel([m,"itemStyle"]);S.style=x.getItemStyle();var b=cr(x,h);b&&(S.shape=b)}),t?(s.setShape(h),s.shape.r=c.r0,Gt(s,{shape:{r:c.r}},i,r.dataIndex)):(dt(s,{shape:h},i),xe(s)),s.useStyle(v),this._updateLabel(i);var d=l.getShallow("cursor");d&&s.attr("cursor",d),this._seriesModel=i||this._seriesModel,this._ecModel=n||this._ecModel;var g=u.get("focus"),y=g==="relative"?Ua(r.getAncestorsIndices(),r.getDescendantIndices()):g==="ancestor"?r.getAncestorsIndices():g==="descendant"?r.getDescendantIndices():g;xt(this,y,u.get("blurScope"),u.get("disabled"))},e.prototype._updateLabel=function(t){var r=this,i=this.node.getModel(),n=i.getModel("label"),o=this.node.getLayout(),s=o.endAngle-o.startAngle,l=(o.startAngle+o.endAngle)/2,u=Math.cos(l),c=Math.sin(l),h=this,v=h.getTextContent(),f=this.node.dataIndex,p=n.get("minAngle")/180*Math.PI,d=n.get("show")&&!(p!=null&&Math.abs(s)<p);v.ignore=!d,D(Td,function(y){var m=y==="normal"?i.getModel("label"):i.getModel([y,"label"]),S=y==="normal",x=S?v:v.ensureState(y),b=t.getFormattedLabel(f,y);S&&(b=b||r.node.name),x.style=yt(m,{},null,y!=="normal",!0),b&&(x.style.text=b);var _=m.get("show");_!=null&&!S&&(x.ignore=!_);var w=g(m,"position"),A=S?h:h.states[y],C=A.style.fill;A.textConfig={outsideFill:m.get("color")==="inherit"?C:null,inside:w!=="outside"};var T,I=g(m,"distance")||0,L=g(m,"align"),M=g(m,"rotate"),R=Math.PI*.5,P=Math.PI*1.5,k=ma(M==="tangential"?Math.PI/2-l:l),V=k>R&&!Dd(k-R)&&k<P;w==="outside"?(T=o.r+I,L=V?"right":"left"):!L||L==="center"?(s===2*Math.PI&&o.r0===0?T=0:T=(o.r+o.r0)/2,L="center"):L==="left"?(T=o.r0+I,L=V?"right":"left"):L==="right"&&(T=o.r-I,L=V?"left":"right"),x.style.align=L,x.style.verticalAlign=g(m,"verticalAlign")||"middle",x.x=T*u+o.cx,x.y=T*c+o.cy;var N=0;M==="radial"?N=ma(-l)+(V?Math.PI:0):M==="tangential"?N=ma(Math.PI/2-l)+(V?Math.PI:0):jt(M)&&(N=M*Math.PI/180),x.rotation=ma(N)});function g(y,m){var S=y.get(m);return S??n.get(m)}v.dirtyStyle()},e}(Ye),vo="sunburstRootToNode",Wu="sunburstHighlight",z1="sunburstUnhighlight";function O1(a){a.registerAction({type:vo,update:"updateView"},function(e,t){t.eachComponent({mainType:"series",subType:"sunburst",query:e},r);function r(i,n){var o=Jr(e,[vo],i);if(o){var s=i.getViewRoot();s&&(e.direction=es(s,o.node)?"rollUp":"drillDown"),i.resetViewRoot(o.node)}}}),a.registerAction({type:Wu,update:"none"},function(e,t,r){e=F({},e),t.eachComponent({mainType:"series",subType:"sunburst",query:e},i);function i(n){var o=Jr(e,[Wu],n);o&&(e.dataIndex=o.node.dataIndex)}r.dispatchAction(F(e,{type:"highlight"}))}),a.registerAction({type:z1,update:"updateView"},function(e,t,r){e=F({},e),r.dispatchAction(F(e,{type:"downplay"}))})}var B1=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i,n){var o=this;this.seriesModel=t,this.api=i,this.ecModel=r;var s=t.getData(),l=s.tree.root,u=t.getViewRoot(),c=this.group,h=t.get("renderLabelForZeroData"),v=[];u.eachNode(function(m){v.push(m)});var f=this._oldChildren||[];p(v,f),y(l,u),this._initEvents(),this._oldChildren=v;function p(m,S){if(m.length===0&&S.length===0)return;new Ar(S,m,x,x).add(b).update(b).remove(q(b,null)).execute();function x(_){return _.getId()}function b(_,w){var A=_==null?null:m[_],C=w==null?null:S[w];d(A,C)}}function d(m,S){if(!h&&m&&!m.getValue()&&(m=null),m!==l&&S!==l){if(S&&S.piece)m?(S.piece.updateData(!1,m,t,r,i),s.setItemGraphicEl(m.dataIndex,S.piece)):g(S);else if(m){var x=new Hu(m,t,r,i);c.add(x),s.setItemGraphicEl(m.dataIndex,x)}}}function g(m){m&&m.piece&&(c.remove(m.piece),m.piece=null)}function y(m,S){S.depth>0?(o.virtualPiece?o.virtualPiece.updateData(!1,m,t,r,i):(o.virtualPiece=new Hu(m,t,r,i),c.add(o.virtualPiece)),S.piece.off("click"),o.virtualPiece.on("click",function(x){o._rootToNode(S.parentNode)})):o.virtualPiece&&(c.remove(o.virtualPiece),o.virtualPiece=null)}},e.prototype._initEvents=function(){var t=this;this.group.off("click"),this.group.on("click",function(r){var i=!1,n=t.seriesModel.getViewRoot();n.eachNode(function(o){if(!i&&o.piece&&o.piece===r.target){var s=o.getModel().get("nodeClick");if(s==="rootToNode")t._rootToNode(o);else if(s==="link"){var l=o.getModel(),u=l.get("link");if(u){var c=l.get("target",!0)||"_blank";Rh(u,c)}}i=!0}})})},e.prototype._rootToNode=function(t){t!==this.seriesModel.getViewRoot()&&this.api.dispatchAction({type:vo,from:this.uid,seriesId:this.seriesModel.id,targetNode:t})},e.prototype.containPoint=function(t,r){var i=r.getData(),n=i.getItemLayout(0);if(n){var o=t[0]-n.cx,s=t[1]-n.cy,l=Math.sqrt(o*o+s*s);return l<=n.r&&l>=n.r0}},e.type="sunburst",e}(bt),G1=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.ignoreStyleOnData=!0,t}return e.prototype.getInitialData=function(t,r){var i={name:t.name,children:t.data};Df(i);var n=this._levelModels=G(t.levels||[],function(l){return new Ut(l,this,r)},this),o=ts.createTree(i,this,s);function s(l){l.wrapMethod("getItemModel",function(u,c){var h=o.getNodeByDataIndex(c),v=n[h.depth];return v&&(u.parentModel=v),u})}return o.data},e.prototype.optionUpdated=function(){this.resetViewRoot()},e.prototype.getDataParams=function(t){var r=a.prototype.getDataParams.apply(this,arguments),i=this.getData().tree.getNodeByDataIndex(t);return r.treePathInfo=Mi(i,this),r},e.prototype.getLevelModel=function(t){return this._levelModels&&this._levelModels[t.depth]},e.prototype.getViewRoot=function(){return this._viewRoot},e.prototype.resetViewRoot=function(t){t?this._viewRoot=t:t=this._viewRoot;var r=this.getRawData().tree.root;(!t||t!==r&&!r.contains(t))&&(this._viewRoot=r)},e.prototype.enableAriaDecal=function(){Mv(this)},e.type="series.sunburst",e.defaultOption={z:2,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,stillShowZeroSum:!0,nodeClick:"rootToNode",renderLabelForZeroData:!1,label:{rotate:"radial",show:!0,opacity:1,align:"center",position:"inside",distance:5,silent:!0},itemStyle:{borderWidth:1,borderColor:"white",borderType:"solid",shadowBlur:0,shadowColor:"rgba(0, 0, 0, 0.2)",shadowOffsetX:0,shadowOffsetY:0,opacity:1},emphasis:{focus:"descendant"},blur:{itemStyle:{opacity:.2},label:{opacity:.1}},animationType:"expansion",animationDuration:1e3,animationDurationUpdate:500,data:[],sort:"desc"},e}(Ct);function Df(a){var e=0;D(a.children,function(r){Df(r);var i=r.value;U(i)&&(i=i[0]),e+=i});var t=a.value;U(t)&&(t=t[0]),(t==null||isNaN(t))&&(t=e),t<0&&(t=0),U(a.value)?a.value[0]=t:a.value=t}var Zu=Math.PI/180;function F1(a,e,t){e.eachSeriesByType(a,function(r){var i=r.get("center"),n=r.get("radius");U(n)||(n=[0,n]),U(i)||(i=[i,i]);var o=t.getWidth(),s=t.getHeight(),l=Math.min(o,s),u=O(i[0],o),c=O(i[1],s),h=O(n[0],l/2),v=O(n[1],l/2),f=-r.get("startAngle")*Zu,p=r.get("minAngle")*Zu,d=r.getData().tree.root,g=r.getViewRoot(),y=g.depth,m=r.get("sort");m!=null&&Cf(g,m);var S=0;D(g.children,function(k){!isNaN(k.getValue())&&S++});var x=g.getValue(),b=Math.PI/(x||S)*2,_=g.depth>0,w=g.height-(_?-1:1),A=(v-h)/(w||1),C=r.get("clockwise"),T=r.get("stillShowZeroSum"),I=C?1:-1,L=function(k,V){if(k){var N=V;if(k!==d){var z=k.getValue(),H=x===0&&T?b:z*b;H<p&&(H=p),N=V+I*H;var Z=k.depth-y-(_?-1:1),Y=h+A*Z,Q=h+A*(Z+1),j=r.getLevelModel(k);if(j){var rt=j.get("r0",!0),It=j.get("r",!0),$t=j.get("radius",!0);$t!=null&&(rt=$t[0],It=$t[1]),rt!=null&&(Y=O(rt,l/2)),It!=null&&(Q=O(It,l/2))}k.setLayout({angle:H,startAngle:V,endAngle:N,clockwise:C,cx:u,cy:c,r0:Y,r:Q})}if(k.children&&k.children.length){var J=0;D(k.children,function($){J+=L($,V+J)})}return N-V}};if(_){var M=h,R=h+A,P=Math.PI*2;d.setLayout({angle:P,startAngle:f,endAngle:f+P,clockwise:C,cx:u,cy:c,r0:M,r:R})}L(g,f)})}function Cf(a,e){var t=a.children||[];a.children=H1(t,e),t.length&&D(a.children,function(r){Cf(r,e)})}function H1(a,e){if(st(e)){var t=G(a,function(i,n){var o=i.getValue();return{params:{depth:i.depth,height:i.height,dataIndex:i.dataIndex,getValue:function(){return o}},index:n}});return t.sort(function(i,n){return e(i.params,n.params)}),G(t,function(i){return a[i.index]})}else{var r=e==="asc";return a.sort(function(i,n){var o=(i.getValue()-n.getValue())*(r?1:-1);return o===0?(i.dataIndex-n.dataIndex)*(r?-1:1):o})}}function W1(a){var e={};function t(r,i,n){for(var o=r;o&&o.depth>1;)o=o.parentNode;var s=i.getColorFromPalette(o.name||o.dataIndex+"",e);return r.depth>1&&tt(s)&&(s=Cd(s,(r.depth-1)/(n-1)*.5)),s}a.eachSeriesByType("sunburst",function(r){var i=r.getData(),n=i.tree;n.eachNode(function(o){var s=o.getModel(),l=s.getModel("itemStyle").getItemStyle();l.fill||(l.fill=t(o,r,n.root.height));var u=i.ensureUniqueItemVisual(o.dataIndex,"style");F(u,l)})})}function Z1(a){a.registerChartView(B1),a.registerSeriesModel(G1),a.registerLayout(q(F1,"sunburst")),a.registerProcessor(q(fa,"sunburst")),a.registerVisual(W1),O1(a)}var Uu={color:"fill",borderColor:"stroke"},U1={symbol:1,symbolSize:1,symbolKeepAspect:1,legendIcon:1,visualMeta:1,liftZ:1,decal:1},me=Dt(),Y1=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.optionUpdated=function(){this.currentZLevel=this.get("zlevel",!0),this.currentZ=this.get("z",!0)},e.prototype.getInitialData=function(t,r){return br(null,this)},e.prototype.getDataParams=function(t,r,i){var n=a.prototype.getDataParams.call(this,t,r);return i&&(n.info=me(i).info),n},e.type="series.custom",e.dependencies=["grid","polar","geo","singleAxis","calendar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,clip:!1},e}(Ct);function $1(a,e){return e=e||[0,0],G(["x","y"],function(t,r){var i=this.getAxis(t),n=e[r],o=a[r]/2;return i.type==="category"?i.getBandWidth():Math.abs(i.dataToCoord(n-o)-i.dataToCoord(n+o))},this)}function X1(a){var e=a.master.getRect();return{coordSys:{type:"cartesian2d",x:e.x,y:e.y,width:e.width,height:e.height},api:{coord:function(t){return a.dataToPoint(t)},size:W($1,a)}}}function K1(a,e){return e=e||[0,0],G([0,1],function(t){var r=e[t],i=a[t]/2,n=[],o=[];return n[t]=r-i,o[t]=r+i,n[1-t]=o[1-t]=e[1-t],Math.abs(this.dataToPoint(n)[t]-this.dataToPoint(o)[t])},this)}function q1(a){var e=a.getBoundingRect();return{coordSys:{type:"geo",x:e.x,y:e.y,width:e.width,height:e.height,zoom:a.getZoom()},api:{coord:function(t){return a.dataToPoint(t)},size:W(K1,a)}}}function j1(a,e){var t=this.getAxis(),r=e instanceof Array?e[0]:e,i=(a instanceof Array?a[0]:a)/2;return t.type==="category"?t.getBandWidth():Math.abs(t.dataToCoord(r-i)-t.dataToCoord(r+i))}function J1(a){var e=a.getRect();return{coordSys:{type:"singleAxis",x:e.x,y:e.y,width:e.width,height:e.height},api:{coord:function(t){return a.dataToPoint(t)},size:W(j1,a)}}}function Q1(a,e){return e=e||[0,0],G(["Radius","Angle"],function(t,r){var i="get"+t+"Axis",n=this[i](),o=e[r],s=a[r]/2,l=n.type==="category"?n.getBandWidth():Math.abs(n.dataToCoord(o-s)-n.dataToCoord(o+s));return t==="Angle"&&(l=l*Math.PI/180),l},this)}function tb(a){var e=a.getRadiusAxis(),t=a.getAngleAxis(),r=e.getExtent();return r[0]>r[1]&&r.reverse(),{coordSys:{type:"polar",cx:a.cx,cy:a.cy,r:r[1],r0:r[0]},api:{coord:function(i){var n=e.dataToRadius(i[0]),o=t.dataToAngle(i[1]),s=a.coordToPoint([n,o]);return s.push(n,o*Math.PI/180),s},size:W(Q1,a)}}}function eb(a){var e=a.getRect(),t=a.getRangeInfo();return{coordSys:{type:"calendar",x:e.x,y:e.y,width:e.width,height:e.height,cellWidth:a.getCellWidth(),cellHeight:a.getCellHeight(),rangeInfo:{start:t.start,end:t.end,weeks:t.weeks,dayCount:t.allDay}},api:{coord:function(r,i){return a.dataToPoint(r,i)}}}}function If(a,e,t,r){return a&&(a.legacy||a.legacy!==!1&&!t&&!r&&e!=="tspan"&&(e==="text"||B(a,"text")))}function Lf(a,e,t){var r=a,i,n,o;if(e==="text")o=r;else{o={},B(r,"text")&&(o.text=r.text),B(r,"rich")&&(o.rich=r.rich),B(r,"textFill")&&(o.fill=r.textFill),B(r,"textStroke")&&(o.stroke=r.textStroke),B(r,"fontFamily")&&(o.fontFamily=r.fontFamily),B(r,"fontSize")&&(o.fontSize=r.fontSize),B(r,"fontStyle")&&(o.fontStyle=r.fontStyle),B(r,"fontWeight")&&(o.fontWeight=r.fontWeight),n={type:"text",style:o,silent:!0},i={};var s=B(r,"textPosition");t?i.position=s?r.textPosition:"inside":s&&(i.position=r.textPosition),B(r,"textPosition")&&(i.position=r.textPosition),B(r,"textOffset")&&(i.offset=r.textOffset),B(r,"textRotation")&&(i.rotation=r.textRotation),B(r,"textDistance")&&(i.distance=r.textDistance)}return Yu(o,a),D(o.rich,function(l){Yu(l,l)}),{textConfig:i,textContent:n}}function Yu(a,e){e&&(e.font=e.textFont||e.font,B(e,"textStrokeWidth")&&(a.lineWidth=e.textStrokeWidth),B(e,"textAlign")&&(a.align=e.textAlign),B(e,"textVerticalAlign")&&(a.verticalAlign=e.textVerticalAlign),B(e,"textLineHeight")&&(a.lineHeight=e.textLineHeight),B(e,"textWidth")&&(a.width=e.textWidth),B(e,"textHeight")&&(a.height=e.textHeight),B(e,"textBackgroundColor")&&(a.backgroundColor=e.textBackgroundColor),B(e,"textPadding")&&(a.padding=e.textPadding),B(e,"textBorderColor")&&(a.borderColor=e.textBorderColor),B(e,"textBorderWidth")&&(a.borderWidth=e.textBorderWidth),B(e,"textBorderRadius")&&(a.borderRadius=e.textBorderRadius),B(e,"textBoxShadowColor")&&(a.shadowColor=e.textBoxShadowColor),B(e,"textBoxShadowBlur")&&(a.shadowBlur=e.textBoxShadowBlur),B(e,"textBoxShadowOffsetX")&&(a.shadowOffsetX=e.textBoxShadowOffsetX),B(e,"textBoxShadowOffsetY")&&(a.shadowOffsetY=e.textBoxShadowOffsetY))}function $u(a,e,t){var r=a;r.textPosition=r.textPosition||t.position||"inside",t.offset!=null&&(r.textOffset=t.offset),t.rotation!=null&&(r.textRotation=t.rotation),t.distance!=null&&(r.textDistance=t.distance);var i=r.textPosition.indexOf("inside")>=0,n=a.fill||"#000";Xu(r,e);var o=r.textFill==null;return i?o&&(r.textFill=t.insideFill||"#fff",!r.textStroke&&t.insideStroke&&(r.textStroke=t.insideStroke),!r.textStroke&&(r.textStroke=n),r.textStrokeWidth==null&&(r.textStrokeWidth=2)):(o&&(r.textFill=a.fill||t.outsideFill||"#000"),!r.textStroke&&t.outsideStroke&&(r.textStroke=t.outsideStroke)),r.text=e.text,r.rich=e.rich,D(e.rich,function(s){Xu(s,s)}),r}function Xu(a,e){e&&(B(e,"fill")&&(a.textFill=e.fill),B(e,"stroke")&&(a.textStroke=e.fill),B(e,"lineWidth")&&(a.textStrokeWidth=e.lineWidth),B(e,"font")&&(a.font=e.font),B(e,"fontStyle")&&(a.fontStyle=e.fontStyle),B(e,"fontWeight")&&(a.fontWeight=e.fontWeight),B(e,"fontSize")&&(a.fontSize=e.fontSize),B(e,"fontFamily")&&(a.fontFamily=e.fontFamily),B(e,"align")&&(a.textAlign=e.align),B(e,"verticalAlign")&&(a.textVerticalAlign=e.verticalAlign),B(e,"lineHeight")&&(a.textLineHeight=e.lineHeight),B(e,"width")&&(a.textWidth=e.width),B(e,"height")&&(a.textHeight=e.height),B(e,"backgroundColor")&&(a.textBackgroundColor=e.backgroundColor),B(e,"padding")&&(a.textPadding=e.padding),B(e,"borderColor")&&(a.textBorderColor=e.borderColor),B(e,"borderWidth")&&(a.textBorderWidth=e.borderWidth),B(e,"borderRadius")&&(a.textBorderRadius=e.borderRadius),B(e,"shadowColor")&&(a.textBoxShadowColor=e.shadowColor),B(e,"shadowBlur")&&(a.textBoxShadowBlur=e.shadowBlur),B(e,"shadowOffsetX")&&(a.textBoxShadowOffsetX=e.shadowOffsetX),B(e,"shadowOffsetY")&&(a.textBoxShadowOffsetY=e.shadowOffsetY),B(e,"textShadowColor")&&(a.textShadowColor=e.textShadowColor),B(e,"textShadowBlur")&&(a.textShadowBlur=e.textShadowBlur),B(e,"textShadowOffsetX")&&(a.textShadowOffsetX=e.textShadowOffsetX),B(e,"textShadowOffsetY")&&(a.textShadowOffsetY=e.textShadowOffsetY))}var Mf={position:["x","y"],scale:["scaleX","scaleY"],origin:["originX","originY"]},Ku=wt(Mf);Dh(Kr,function(a,e){return a[e]=1,a},{});Kr.join(", ");var ii=["","style","shape","extra"],mr=Dt();function ms(a,e,t,r,i){var n=a+"Animation",o=Oh(a,r,i)||{},s=mr(e).userDuring;return o.duration>0&&(o.during=s?W(ob,{el:e,userDuring:s}):null,o.setToFinal=!0,o.scope=a),F(o,t[n]),o}function Ga(a,e,t,r){r=r||{};var i=r.dataIndex,n=r.isInit,o=r.clearStyle,s=t.isAnimationEnabled(),l=mr(a),u=e.style;l.userDuring=e.during;var c={},h={};if(lb(a,e,h),ju("shape",e,h),ju("extra",e,h),!n&&s&&(sb(a,e,c),qu("shape",a,e,c),qu("extra",a,e,c),ub(a,e,u,c)),h.style=u,rb(a,h,o),ib(a,e),s)if(n){var v={};D(ii,function(p){var d=p?e[p]:e;d&&d.enterFrom&&(p&&(v[p]=v[p]||{}),F(p?v[p]:v,d.enterFrom))});var f=ms("enter",a,e,t,i);f.duration>0&&a.animateFrom(v,f)}else ab(a,e,i||0,t,c);Pf(a,e),u?a.dirty():a.markRedraw()}function Pf(a,e){for(var t=mr(a).leaveToProps,r=0;r<ii.length;r++){var i=ii[r],n=i?e[i]:e;n&&n.leaveTo&&(t||(t=mr(a).leaveToProps={}),i&&(t[i]=t[i]||{}),F(i?t[i]:t,n.leaveTo))}}function Ei(a,e,t,r){if(a){var i=a.parent,n=mr(a).leaveToProps;if(n){var o=ms("update",a,e,t,0);o.done=function(){i.remove(a)},a.animateTo(n,o)}else i.remove(a)}}function Ue(a){return a==="all"}function rb(a,e,t){var r=e.style;if(!a.isGroup&&r){if(t){a.useStyle({});for(var i=a.animators,n=0;n<i.length;n++){var o=i[n];o.targetName==="style"&&o.changeTarget(a.style)}}a.setStyle(r)}e.style=null,a.attr(e),e.style=r}function ab(a,e,t,r,i){{var n=ms("update",a,e,r,t);n.duration>0&&a.animateFrom(i,n)}}function ib(a,e){B(e,"silent")&&(a.silent=e.silent),B(e,"ignore")&&(a.ignore=e.ignore),a instanceof dr&&B(e,"invisible")&&(a.invisible=e.invisible),a instanceof ie&&B(e,"autoBatch")&&(a.autoBatch=e.autoBatch)}var ce={},nb={setTransform:function(a,e){return ce.el[a]=e,this},getTransform:function(a){return ce.el[a]},setShape:function(a,e){var t=ce.el,r=t.shape||(t.shape={});return r[a]=e,t.dirtyShape&&t.dirtyShape(),this},getShape:function(a){var e=ce.el.shape;if(e)return e[a]},setStyle:function(a,e){var t=ce.el,r=t.style;return r&&(r[a]=e,t.dirtyStyle&&t.dirtyStyle()),this},getStyle:function(a){var e=ce.el.style;if(e)return e[a]},setExtra:function(a,e){var t=ce.el.extra||(ce.el.extra={});return t[a]=e,this},getExtra:function(a){var e=ce.el.extra;if(e)return e[a]}};function ob(){var a=this,e=a.el;if(e){var t=mr(e).userDuring,r=a.userDuring;if(t!==r){a.el=a.userDuring=null;return}ce.el=e,r(nb)}}function qu(a,e,t,r){var i=t[a];if(i){var n=e[a],o;if(n){var s=t.transition,l=i.transition;if(l)if(!o&&(o=r[a]={}),Ue(l))F(o,n);else for(var u=be(l),c=0;c<u.length;c++){var h=u[c],v=n[h];o[h]=v}else if(Ue(s)||mt(s,a)>=0){!o&&(o=r[a]={});for(var f=wt(n),c=0;c<f.length;c++){var h=f[c],v=n[h];cb(i[h],v)&&(o[h]=v)}}}}}function ju(a,e,t){var r=e[a];if(r)for(var i=t[a]={},n=wt(r),o=0;o<n.length;o++){var s=n[o];i[s]=Id(r[s])}}function sb(a,e,t){for(var r=e.transition,i=Ue(r)?Kr:be(r||[]),n=0;n<i.length;n++){var o=i[n];if(!(o==="style"||o==="shape"||o==="extra")){var s=a[o];t[o]=s}}}function lb(a,e,t){for(var r=0;r<Ku.length;r++){var i=Ku[r],n=Mf[i],o=e[i];o&&(t[n[0]]=o[0],t[n[1]]=o[1])}for(var r=0;r<Kr.length;r++){var s=Kr[r];e[s]!=null&&(t[s]=e[s])}}function ub(a,e,t,r){if(t){var i=a.style,n;if(i){var o=t.transition,s=e.transition;if(o&&!Ue(o)){var l=be(o);!n&&(n=r.style={});for(var u=0;u<l.length;u++){var c=l[u],h=i[c];n[c]=h}}else if(a.getAnimationStyleProps&&(Ue(s)||Ue(o)||mt(s,"style")>=0)){var v=a.getAnimationStyleProps(),f=v?v.style:null;if(f){!n&&(n=r.style={});for(var p=wt(t),u=0;u<p.length;u++){var c=p[u];if(f[c]){var h=i[c];n[c]=h}}}}}}}function cb(a,e){return Ld(a)?a!==e:a!=null&&isFinite(a)}var Rf=Dt(),hb=["percent","easing","shape","style","extra"];function Ef(a){a.stopAnimation("keyframe"),a.attr(Rf(a))}function ni(a,e,t){if(!(!t.isAnimationEnabled()||!e)){if(U(e)){D(e,function(s){ni(a,s,t)});return}var r=e.keyframes,i=e.duration;if(t&&i==null){var n=Oh("enter",t,0);i=n&&n.duration}if(!(!r||!i)){var o=Rf(a);D(ii,function(s){if(!(s&&!a[s])){var l;r.sort(function(u,c){return u.percent-c.percent}),D(r,function(u){var c=a.animators,h=s?u[s]:u;if(h){var v=wt(h);if(s||(v=Yt(v,function(d){return mt(hb,d)<0})),!!v.length){l||(l=a.animate(s,e.loop,!0),l.scope="keyframe");for(var f=0;f<c.length;f++)c[f]!==l&&c[f].targetName===l.targetName&&c[f].stopTracks(v);s&&(o[s]=o[s]||{});var p=s?o[s]:o;D(v,function(d){p[d]=((s?a[s]:a)||{})[d]}),l.whenWithKeys(i*u.percent,h,v,u.easing)}}}),l&&l.delay(e.delay||0).duration(i).start(e.easing)}})}}}var Se="emphasis",De="normal",Ss="blur",xs="select",Ee=[De,Se,Ss,xs],bn={normal:["itemStyle"],emphasis:[Se,"itemStyle"],blur:[Ss,"itemStyle"],select:[xs,"itemStyle"]},_n={normal:["label"],emphasis:[Se,"label"],blur:[Ss,"label"],select:[xs,"label"]},vb=["x","y"],fb="e\0\0",Qt={normal:{},emphasis:{},blur:{},select:{}},pb={cartesian2d:X1,geo:q1,single:J1,polar:tb,calendar:eb};function fo(a){return a instanceof ie}function po(a){return a instanceof dr}function db(a,e){e.copyTransform(a),po(e)&&po(a)&&(e.setStyle(a.style),e.z=a.z,e.z2=a.z2,e.zlevel=a.zlevel,e.invisible=a.invisible,e.ignore=a.ignore,fo(e)&&fo(a)&&e.setShape(a.shape))}var gb=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i,n){this._progressiveEls=null;var o=this._data,s=t.getData(),l=this.group,u=Ju(t,s,r,i);o||l.removeAll(),s.diff(o).add(function(h){wn(i,null,h,u(h,n),t,l,s)}).remove(function(h){var v=o.getItemGraphicEl(h);v&&Ei(v,me(v).option,t)}).update(function(h,v){var f=o.getItemGraphicEl(v);wn(i,f,h,u(h,n),t,l,s)}).execute();var c=t.get("clip",!0)?Ai(t.coordinateSystem,!1,t):null;c?l.setClipPath(c):l.removeClipPath(),this._data=s},e.prototype.incrementalPrepareRender=function(t,r,i){this.group.removeAll(),this._data=null},e.prototype.incrementalRender=function(t,r,i,n,o){var s=r.getData(),l=Ju(r,s,i,n),u=this._progressiveEls=[];function c(f){f.isGroup||(f.incremental=!0,f.ensureState("emphasis").hoverLayer=!0)}for(var h=t.start;h<t.end;h++){var v=wn(null,null,h,l(h,o),r,this.group,s);v&&(v.traverse(c),u.push(v))}},e.prototype.eachRendered=function(t){_i(this._progressiveEls||this.group,t)},e.prototype.filterForExposedEvent=function(t,r,i,n){var o=r.element;if(o==null||i.name===o)return!0;for(;(i=i.__hostTarget||i.parent)&&i!==this.group;)if(i.name===o)return!0;return!1},e.type="custom",e}(bt);function bs(a){var e=a.type,t;if(e==="path"){var r=a.shape,i=r.width!=null&&r.height!=null?{x:r.x||0,y:r.y||0,width:r.width,height:r.height}:null,n=Nf(r);t=Pd(n,null,i,r.layout||"center"),me(t).customPathData=n}else if(e==="image")t=new pe({}),me(t).customImagePath=a.style.image;else if(e==="text")t=new ut({});else if(e==="group")t=new X;else{if(e==="compoundPath")throw new Error('"compoundPath" is not supported yet.');var o=Gh(e);if(!o){var s="";zh(s)}t=new o}return me(t).customGraphicType=e,t.name=a.name,t.z2EmphasisLift=1,t.z2SelectLift=1,t}function _s(a,e,t,r,i,n,o){Ef(e);var s=i&&i.normal.cfg;s&&e.setTextConfig(s),r&&r.transition==null&&(r.transition=vb);var l=r&&r.style;if(l){if(e.type==="text"){var u=l;B(u,"textFill")&&(u.fill=u.textFill),B(u,"textStroke")&&(u.stroke=u.textStroke)}var c=void 0,h=fo(e)?l.decal:null;a&&h&&(h.dirty=!0,c=Si(h,a)),l.__decalPattern=c}if(po(e)&&l){var c=l.__decalPattern;c&&(l.decal=c)}Ga(e,r,n,{dataIndex:t,isInit:o,clearStyle:!0}),ni(e,r.keyframeAnimation,n)}function kf(a,e,t,r,i){var n=e.isGroup?null:e,o=i&&i[a].cfg;if(n){var s=n.ensureState(a);if(r===!1){var l=n.getState(a);l&&(l.style=null)}else s.style=r||null;o&&(s.textConfig=o),gr(n)}}function yb(a,e,t){if(!a.isGroup){var r=a,i=t.currentZ,n=t.currentZLevel;r.z=i,r.zlevel=n;var o=e.z2;o!=null&&(r.z2=o||0);for(var s=0;s<Ee.length;s++)mb(r,e,Ee[s])}}function mb(a,e,t){var r=t===De,i=r?e:oi(e,t),n=i?i.z2:null,o;n!=null&&(o=r?a:a.ensureState(t),o.z2=n||0)}function Ju(a,e,t,r){var i=a.get("renderItem"),n=a.coordinateSystem,o={};n&&(o=n.prepareCustoms?n.prepareCustoms(n):pb[n.type](n));for(var s=ot({getWidth:r.getWidth,getHeight:r.getHeight,getZr:r.getZr,getDevicePixelRatio:r.getDevicePixelRatio,value:x,style:_,ordinalRawValue:b,styleEmphasis:w,visual:T,barLayout:I,currentSeriesIndices:L,font:M},o.api||{}),l={context:{},seriesId:a.id,seriesName:a.name,seriesIndex:a.seriesIndex,coordSys:o.coordSys,dataInsideLength:e.count(),encode:Sb(a.getData())},u,c,h={},v={},f={},p={},d=0;d<Ee.length;d++){var g=Ee[d];f[g]=a.getModel(bn[g]),p[g]=a.getModel(_n[g])}function y(R){return R===u?c||(c=e.getItemModel(R)):e.getItemModel(R)}function m(R,P){return e.hasItemOption?R===u?h[P]||(h[P]=y(R).getModel(bn[P])):y(R).getModel(bn[P]):f[P]}function S(R,P){return e.hasItemOption?R===u?v[P]||(v[P]=y(R).getModel(_n[P])):y(R).getModel(_n[P]):p[P]}return function(R,P){return u=R,c=null,h={},v={},i&&i(ot({dataIndexInside:R,dataIndex:e.getRawIndex(R),actionType:P?P.type:null},l),s)};function x(R,P){return P==null&&(P=u),e.getStore().get(e.getDimensionIndex(R||0),P)}function b(R,P){P==null&&(P=u),R=R||0;var k=e.getDimensionInfo(R);if(!k){var V=e.getDimensionIndex(R);return V>=0?e.getStore().get(V,P):void 0}var N=e.get(k.name,P),z=k&&k.ordinalMeta;return z?z.categories[N]:N}function _(R,P){P==null&&(P=u);var k=e.getItemVisual(P,"style"),V=k&&k.fill,N=k&&k.opacity,z=m(P,De).getItemStyle();V!=null&&(z.fill=V),N!=null&&(z.opacity=N);var H={inheritColor:tt(V)?V:"#000"},Z=S(P,De),Y=yt(Z,null,H,!1,!0);Y.text=Z.getShallow("show")?Mt(a.getFormattedLabel(P,De),Yn(e,P)):null;var Q=js(Z,H,!1);return C(R,z),z=$u(z,Y,Q),R&&A(z,R),z.legacy=!0,z}function w(R,P){P==null&&(P=u);var k=m(P,Se).getItemStyle(),V=S(P,Se),N=yt(V,null,null,!0,!0);N.text=V.getShallow("show")?xr(a.getFormattedLabel(P,Se),a.getFormattedLabel(P,De),Yn(e,P)):null;var z=js(V,null,!0);return C(R,k),k=$u(k,N,z),R&&A(k,R),k.legacy=!0,k}function A(R,P){for(var k in P)B(P,k)&&(R[k]=P[k])}function C(R,P){R&&(R.textFill&&(P.textFill=R.textFill),R.textPosition&&(P.textPosition=R.textPosition))}function T(R,P){if(P==null&&(P=u),B(Uu,R)){var k=e.getItemVisual(P,"style");return k?k[Uu[R]]:null}if(B(U1,R))return e.getItemVisual(P,R)}function I(R){if(n.type==="cartesian2d"){var P=n.getBaseAxis();return Md(ot({axis:P},R))}}function L(){return t.getCurrentSeriesIndices()}function M(R){return Bh(R,t)}}function Sb(a){var e={};return D(a.dimensions,function(t){var r=a.getDimensionInfo(t);if(!r.isExtraCoord){var i=r.coordDim,n=e[i]=e[i]||[];n[r.coordDimIndex]=a.getDimensionIndex(t)}}),e}function wn(a,e,t,r,i,n,o){if(!r){n.remove(e);return}var s=ws(a,e,t,r,i,n);return s&&o.setItemGraphicEl(t,s),s&&xt(s,r.focus,r.blurScope,r.emphasisDisabled),s}function ws(a,e,t,r,i,n){var o=-1,s=e;e&&Vf(e,r,i)&&(o=mt(n.childrenRef(),e),e=null);var l=!e,u=e;u?u.clearStates():(u=bs(r),s&&db(s,u)),r.morph===!1?u.disableMorphing=!0:u.disableMorphing&&(u.disableMorphing=!1),Qt.normal.cfg=Qt.normal.conOpt=Qt.emphasis.cfg=Qt.emphasis.conOpt=Qt.blur.cfg=Qt.blur.conOpt=Qt.select.cfg=Qt.select.conOpt=null,Qt.isLegacy=!1,bb(u,t,r,i,l,Qt),xb(u,t,r,i,l),_s(a,u,t,r,Qt,i,l),B(r,"info")&&(me(u).info=r.info);for(var c=0;c<Ee.length;c++){var h=Ee[c];if(h!==De){var v=oi(r,h),f=As(r,v,h);kf(h,u,v,f,Qt)}}return yb(u,r,i),r.type==="group"&&_b(a,u,t,r,i),o>=0?n.replaceAt(u,o):n.add(u),u}function Vf(a,e,t){var r=me(a),i=e.type,n=e.shape,o=e.style;return t.isUniversalTransitionEnabled()||i!=null&&i!==r.customGraphicType||i==="path"&&Db(n)&&Nf(n)!==r.customPathData||i==="image"&&B(o,"image")&&o.image!==r.customImagePath}function xb(a,e,t,r,i){var n=t.clipPath;if(n===!1)a&&a.getClipPath()&&a.removeClipPath();else if(n){var o=a.getClipPath();o&&Vf(o,n,r)&&(o=null),o||(o=bs(n),a.setClipPath(o)),_s(null,o,e,n,null,r,i)}}function bb(a,e,t,r,i,n){if(!a.isGroup){Qu(t,null,n),Qu(t,Se,n);var o=n.normal.conOpt,s=n.emphasis.conOpt,l=n.blur.conOpt,u=n.select.conOpt;if(o!=null||s!=null||u!=null||l!=null){var c=a.getTextContent();if(o===!1)c&&a.removeTextContent();else{o=n.normal.conOpt=o||{type:"text"},c?c.clearStates():(c=bs(o),a.setTextContent(c)),_s(null,c,e,o,null,r,i);for(var h=o&&o.style,v=0;v<Ee.length;v++){var f=Ee[v];if(f!==De){var p=n[f].conOpt;kf(f,c,p,As(o,p,f),null)}}h?c.dirty():c.markRedraw()}}}}function Qu(a,e,t){var r=e?oi(a,e):a,i=e?As(a,r,Se):a.style,n=a.type,o=r?r.textConfig:null,s=a.textContent,l=s?e?oi(s,e):s:null;if(i&&(t.isLegacy||If(i,n,!!o,!!l))){t.isLegacy=!0;var u=Lf(i,n,!e);!o&&u.textConfig&&(o=u.textConfig),!l&&u.textContent&&(l=u.textContent)}if(!e&&l){var c=l;!c.type&&(c.type="text")}var h=e?t[e]:t.normal;h.cfg=o,h.conOpt=l}function oi(a,e){return e?a?a[e]:null:a}function As(a,e,t){var r=e&&e.style;return r==null&&t===Se&&a&&(r=a.styleEmphasis),r}function _b(a,e,t,r,i){var n=r.children,o=n?n.length:0,s=r.$mergeChildren,l=s==="byName"||r.diffChildrenByName,u=s===!1;if(!(!o&&!l&&!u)){if(l){Ab({api:a,oldChildren:e.children()||[],newChildren:n||[],dataIndex:t,seriesModel:i,group:e});return}u&&e.removeAll();for(var c=0;c<o;c++){var h=n[c],v=e.childAt(c);h?(h.ignore==null&&(h.ignore=!1),ws(a,v,t,h,i,e)):v.ignore=!0}for(var f=e.childCount()-1;f>=c;f--){var p=e.childAt(f);wb(e,p,i)}}}function wb(a,e,t){e&&Ei(e,me(a).option,t)}function Ab(a){new Ar(a.oldChildren,a.newChildren,tc,tc,a).add(ec).update(ec).remove(Tb).execute()}function tc(a,e){var t=a&&a.name;return t??fb+e}function ec(a,e){var t=this.context,r=a!=null?t.newChildren[a]:null,i=e!=null?t.oldChildren[e]:null;ws(t.api,i,t.dataIndex,r,t.seriesModel,t.group)}function Tb(a){var e=this.context,t=e.oldChildren[a];t&&Ei(t,me(t).option,e.seriesModel)}function Nf(a){return a&&(a.pathData||a.d)}function Db(a){return a&&(B(a,"pathData")||B(a,"d"))}function Cb(a){a.registerChartView(gb),a.registerSeriesModel(Y1)}var Ib=function(a){E(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.makeElOption=function(t,r,i,n,o){var s=i.axis;s.dim==="angle"&&(this.animationThreshold=Math.PI/18);var l=s.polar,u=l.getOtherAxis(s),c=u.getExtent(),h=s.dataToCoord(r),v=n.get("type");if(v&&v!=="none"){var f=Fh(n),p=Mb[v](s,l,h,c);p.style=f,t.graphicKey=p.type,t.pointer=p}var d=n.get(["label","margin"]),g=Lb(r,i,n,l,d);Rd(t,i,n,o,g)},e}(Hh);function Lb(a,e,t,r,i){var n=e.axis,o=n.dataToCoord(a),s=r.getAngleAxis().getExtent()[0];s=s/180*Math.PI;var l=r.getRadiusAxis().getExtent(),u,c,h;if(n.dim==="radius"){var v=wr();mi(v,v,s),Ke(v,v,[r.cx,r.cy]),u=Le([o,-i],v);var f=e.getModel("axisLabel").get("rotate")||0,p=$e.innerTextLayout(s,f*Math.PI/180,-1);c=p.textAlign,h=p.textVerticalAlign}else{var d=l[1];u=r.coordToPoint([d+i,o]);var g=r.cx,y=r.cy;c=Math.abs(u[0]-g)/d<.3?"center":u[0]>g?"left":"right",h=Math.abs(u[1]-y)/d<.3?"middle":u[1]>y?"top":"bottom"}return{position:u,align:c,verticalAlign:h}}var Mb={line:function(a,e,t,r){return a.dim==="angle"?{type:"Line",shape:Wh(e.coordToPoint([r[0],t]),e.coordToPoint([r[1],t]))}:{type:"Circle",shape:{cx:e.cx,cy:e.cy,r:t}}},shadow:function(a,e,t,r){var i=Math.max(1,a.getBandWidth()),n=Math.PI/180;return a.dim==="angle"?{type:"Sector",shape:Js(e.cx,e.cy,r[0],r[1],(-t-i/2)*n,(-t+i/2)*n)}:{type:"Sector",shape:Js(e.cx,e.cy,t-i/2,t+i/2,0,Math.PI*2)}}},Pb=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.findAxisModel=function(t){var r,i=this.ecModel;return i.eachComponent(t,function(n){n.getCoordSysModel()===this&&(r=n)},this),r},e.type="polar",e.dependencies=["radiusAxis","angleAxis"],e.defaultOption={z:0,center:["50%","50%"],radius:"80%"},e}(kt),Ts=function(a){E(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.getCoordSysModel=function(){return this.getReferringComponents("polar",fe).models[0]},e.type="polarAxis",e}(kt);ue(Ts,gi);var Rb=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="angleAxis",e}(Ts),Eb=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="radiusAxis",e}(Ts),Ds=function(a){E(e,a);function e(t,r){return a.call(this,"radius",t,r)||this}return e.prototype.pointToData=function(t,r){return this.polar.pointToData(t,r)[this.dim==="radius"?0:1]},e}(de);Ds.prototype.dataToRadius=de.prototype.dataToCoord;Ds.prototype.radiusToData=de.prototype.coordToData;var kb=Dt(),Cs=function(a){E(e,a);function e(t,r){return a.call(this,"angle",t,r||[0,360])||this}return e.prototype.pointToData=function(t,r){return this.polar.pointToData(t,r)[this.dim==="radius"?0:1]},e.prototype.calculateCategoryInterval=function(){var t=this,r=t.getLabelModel(),i=t.scale,n=i.getExtent(),o=i.count();if(n[1]-n[0]<1)return 0;var s=n[0],l=t.dataToCoord(s+1)-t.dataToCoord(s),u=Math.abs(l),c=Zh(s==null?"":s+"",r.getFont(),"center","top"),h=Math.max(c.height,7),v=h/u;isNaN(v)&&(v=1/0);var f=Math.max(0,Math.floor(v)),p=kb(t.model),d=p.lastAutoInterval,g=p.lastTickCount;return d!=null&&g!=null&&Math.abs(d-f)<=1&&Math.abs(g-o)<=1&&d>f?f=d:(p.lastTickCount=o,p.lastAutoInterval=f),f},e}(de);Cs.prototype.dataToAngle=de.prototype.dataToCoord;Cs.prototype.angleToData=de.prototype.coordToData;var zf=["radius","angle"],Vb=function(){function a(e){this.dimensions=zf,this.type="polar",this.cx=0,this.cy=0,this._radiusAxis=new Ds,this._angleAxis=new Cs,this.axisPointerEnabled=!0,this.name=e||"",this._radiusAxis.polar=this._angleAxis.polar=this}return a.prototype.containPoint=function(e){var t=this.pointToCoord(e);return this._radiusAxis.contain(t[0])&&this._angleAxis.contain(t[1])},a.prototype.containData=function(e){return this._radiusAxis.containData(e[0])&&this._angleAxis.containData(e[1])},a.prototype.getAxis=function(e){var t="_"+e+"Axis";return this[t]},a.prototype.getAxes=function(){return[this._radiusAxis,this._angleAxis]},a.prototype.getAxesByScale=function(e){var t=[],r=this._angleAxis,i=this._radiusAxis;return r.scale.type===e&&t.push(r),i.scale.type===e&&t.push(i),t},a.prototype.getAngleAxis=function(){return this._angleAxis},a.prototype.getRadiusAxis=function(){return this._radiusAxis},a.prototype.getOtherAxis=function(e){var t=this._angleAxis;return e===t?this._radiusAxis:t},a.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAngleAxis()},a.prototype.getTooltipAxes=function(e){var t=e!=null&&e!=="auto"?this.getAxis(e):this.getBaseAxis();return{baseAxes:[t],otherAxes:[this.getOtherAxis(t)]}},a.prototype.dataToPoint=function(e,t){return this.coordToPoint([this._radiusAxis.dataToRadius(e[0],t),this._angleAxis.dataToAngle(e[1],t)])},a.prototype.pointToData=function(e,t){var r=this.pointToCoord(e);return[this._radiusAxis.radiusToData(r[0],t),this._angleAxis.angleToData(r[1],t)]},a.prototype.pointToCoord=function(e){var t=e[0]-this.cx,r=e[1]-this.cy,i=this.getAngleAxis(),n=i.getExtent(),o=Math.min(n[0],n[1]),s=Math.max(n[0],n[1]);i.inverse?o=s-360:s=o+360;var l=Math.sqrt(t*t+r*r);t/=l,r/=l;for(var u=Math.atan2(-r,t)/Math.PI*180,c=u<o?1:-1;u<o||u>s;)u+=c*360;return[l,u]},a.prototype.coordToPoint=function(e){var t=e[0],r=e[1]/180*Math.PI,i=Math.cos(r)*t+this.cx,n=-Math.sin(r)*t+this.cy;return[i,n]},a.prototype.getArea=function(){var e=this.getAngleAxis(),t=this.getRadiusAxis(),r=t.getExtent().slice();r[0]>r[1]&&r.reverse();var i=e.getExtent(),n=Math.PI/180,o=1e-4;return{cx:this.cx,cy:this.cy,r0:r[0],r:r[1],startAngle:-i[0]*n,endAngle:-i[1]*n,clockwise:e.inverse,contain:function(s,l){var u=s-this.cx,c=l-this.cy,h=u*u+c*c,v=this.r,f=this.r0;return v!==f&&h-o<=v*v&&h+o>=f*f}}},a.prototype.convertToPixel=function(e,t,r){var i=rc(t);return i===this?this.dataToPoint(r):null},a.prototype.convertFromPixel=function(e,t,r){var i=rc(t);return i===this?this.pointToData(r):null},a}();function rc(a){var e=a.seriesModel,t=a.polarModel;return t&&t.coordinateSystem||e&&e.coordinateSystem}function Nb(a,e,t){var r=e.get("center"),i=t.getWidth(),n=t.getHeight();a.cx=O(r[0],i),a.cy=O(r[1],n);var o=a.getRadiusAxis(),s=Math.min(i,n)/2,l=e.get("radius");l==null?l=[0,"100%"]:U(l)||(l=[0,l]);var u=[O(l[0],s),O(l[1],s)];o.inverse?o.setExtent(u[1],u[0]):o.setExtent(u[0],u[1])}function zb(a,e){var t=this,r=t.getAngleAxis(),i=t.getRadiusAxis();if(r.scale.setExtent(1/0,-1/0),i.scale.setExtent(1/0,-1/0),a.eachSeries(function(s){if(s.coordinateSystem===t){var l=s.getData();D(Qs(l,"radius"),function(u){i.scale.unionExtentFromData(l,u)}),D(Qs(l,"angle"),function(u){r.scale.unionExtentFromData(l,u)})}}),Xa(r.scale,r.model),Xa(i.scale,i.model),r.type==="category"&&!r.onBand){var n=r.getExtent(),o=360/r.scale.count();r.inverse?n[1]+=o:n[1]-=o,r.setExtent(n[0],n[1])}}function Ob(a){return a.mainType==="angleAxis"}function ac(a,e){var t;if(a.type=e.get("type"),a.scale=Eo(e),a.onBand=e.get("boundaryGap")&&a.type==="category",a.inverse=e.get("inverse"),Ob(e)){a.inverse=a.inverse!==e.get("clockwise");var r=e.get("startAngle"),i=(t=e.get("endAngle"))!==null&&t!==void 0?t:r+(a.inverse?-360:360);a.setExtent(r,i)}e.axis=a,a.model=e}var Bb={dimensions:zf,create:function(a,e){var t=[];return a.eachComponent("polar",function(r,i){var n=new Vb(i+"");n.update=zb;var o=n.getRadiusAxis(),s=n.getAngleAxis(),l=r.findAxisModel("radiusAxis"),u=r.findAxisModel("angleAxis");ac(o,l),ac(s,u),Nb(n,r,e),t.push(n),r.coordinateSystem=n,n.model=r}),a.eachSeries(function(r){if(r.get("coordinateSystem")==="polar"){var i=r.getReferringComponents("polar",fe).models[0];r.coordinateSystem=i.coordinateSystem}}),t}},Gb=["axisLine","axisLabel","axisTick","minorTick","splitLine","minorSplitLine","splitArea"];function La(a,e,t){e[1]>e[0]&&(e=e.slice().reverse());var r=a.coordToPoint([e[0],t]),i=a.coordToPoint([e[1],t]);return{x1:r[0],y1:r[1],x2:i[0],y2:i[1]}}function Ma(a){var e=a.getRadiusAxis();return e.inverse?0:1}function ic(a){var e=a[0],t=a[a.length-1];e&&t&&Math.abs(Math.abs(e.coord-t.coord)-360)<1e-4&&a.pop()}var Fb=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.axisPointerClass="PolarAxisPointer",t}return e.prototype.render=function(t,r){if(this.group.removeAll(),!!t.get("show")){var i=t.axis,n=i.polar,o=n.getRadiusAxis().getExtent(),s=i.getTicksCoords(),l=i.getMinorTicksCoords(),u=G(i.getViewLabels(),function(c){c=it(c);var h=i.scale,v=h.type==="ordinal"?h.getRawOrdinalNumber(c.tickValue):c.tickValue;return c.coord=i.dataToCoord(v),c});ic(u),ic(s),D(Gb,function(c){t.get([c,"show"])&&(!i.scale.isBlank()||c==="axisLine")&&Hb[c](this.group,t,n,s,l,o,u)},this)}},e.type="angleAxis",e}(va),Hb={axisLine:function(a,e,t,r,i,n){var o=e.getModel(["axisLine","lineStyle"]),s=t.getAngleAxis(),l=Math.PI/180,u=s.getExtent(),c=Ma(t),h=c?0:1,v,f=Math.abs(u[1]-u[0])===360?"Circle":"Arc";n[h]===0?v=new _r[f]({shape:{cx:t.cx,cy:t.cy,r:n[c],startAngle:-u[0]*l,endAngle:-u[1]*l,clockwise:s.inverse},style:o.getLineStyle(),z2:1,silent:!0}):v=new bh({shape:{cx:t.cx,cy:t.cy,r:n[c],r0:n[h]},style:o.getLineStyle(),z2:1,silent:!0}),v.style.fill=null,a.add(v)},axisTick:function(a,e,t,r,i,n){var o=e.getModel("axisTick"),s=(o.get("inside")?-1:1)*o.get("length"),l=n[Ma(t)],u=G(r,function(c){return new oe({shape:La(t,[l,l+s],c.coord)})});a.add(ne(u,{style:ot(o.getModel("lineStyle").getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])})}))},minorTick:function(a,e,t,r,i,n){if(i.length){for(var o=e.getModel("axisTick"),s=e.getModel("minorTick"),l=(o.get("inside")?-1:1)*s.get("length"),u=n[Ma(t)],c=[],h=0;h<i.length;h++)for(var v=0;v<i[h].length;v++)c.push(new oe({shape:La(t,[u,u+l],i[h][v].coord)}));a.add(ne(c,{style:ot(s.getModel("lineStyle").getLineStyle(),ot(o.getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])}))}))}},axisLabel:function(a,e,t,r,i,n,o){var s=e.getCategories(!0),l=e.getModel("axisLabel"),u=l.get("margin"),c=e.get("triggerEvent");D(o,function(h,v){var f=l,p=h.tickValue,d=n[Ma(t)],g=t.coordToPoint([d+u,h.coord]),y=t.cx,m=t.cy,S=Math.abs(g[0]-y)/d<.3?"center":g[0]>y?"left":"right",x=Math.abs(g[1]-m)/d<.3?"middle":g[1]>m?"top":"bottom";if(s&&s[p]){var b=s[p];Nt(b)&&b.textStyle&&(f=new Ut(b.textStyle,l,l.ecModel))}var _=new ut({silent:$e.isLabelSilent(e),style:yt(f,{x:g[0],y:g[1],fill:f.getTextColor()||e.get(["axisLine","lineStyle","color"]),text:h.formattedLabel,align:S,verticalAlign:x})});if(a.add(_),c){var w=$e.makeAxisEventDataBase(e);w.targetType="axisLabel",w.value=h.rawLabel,ht(_).eventData=w}},this)},splitLine:function(a,e,t,r,i,n){var o=e.getModel("splitLine"),s=o.getModel("lineStyle"),l=s.get("color"),u=0;l=l instanceof Array?l:[l];for(var c=[],h=0;h<r.length;h++){var v=u++%l.length;c[v]=c[v]||[],c[v].push(new oe({shape:La(t,n,r[h].coord)}))}for(var h=0;h<c.length;h++)a.add(ne(c[h],{style:ot({stroke:l[h%l.length]},s.getLineStyle()),silent:!0,z:e.get("z")}))},minorSplitLine:function(a,e,t,r,i,n){if(i.length){for(var o=e.getModel("minorSplitLine"),s=o.getModel("lineStyle"),l=[],u=0;u<i.length;u++)for(var c=0;c<i[u].length;c++)l.push(new oe({shape:La(t,n,i[u][c].coord)}));a.add(ne(l,{style:s.getLineStyle(),silent:!0,z:e.get("z")}))}},splitArea:function(a,e,t,r,i,n){if(r.length){var o=e.getModel("splitArea"),s=o.getModel("areaStyle"),l=s.get("color"),u=0;l=l instanceof Array?l:[l];for(var c=[],h=Math.PI/180,v=-r[0].coord*h,f=Math.min(n[0],n[1]),p=Math.max(n[0],n[1]),d=e.get("clockwise"),g=1,y=r.length;g<=y;g++){var m=g===y?r[0].coord:r[g].coord,S=u++%l.length;c[S]=c[S]||[],c[S].push(new Ye({shape:{cx:t.cx,cy:t.cy,r0:f,r:p,startAngle:v,endAngle:-m*h,clockwise:d},silent:!0})),v=-m*h}for(var g=0;g<c.length;g++)a.add(ne(c[g],{style:ot({fill:l[g%l.length]},s.getAreaStyle()),silent:!0}))}}},Wb=["axisLine","axisTickLabel","axisName"],Zb=["splitLine","splitArea","minorSplitLine"],Ub=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.axisPointerClass="PolarAxisPointer",t}return e.prototype.render=function(t,r){if(this.group.removeAll(),!!t.get("show")){var i=this._axisGroup,n=this._axisGroup=new X;this.group.add(n);var o=t.axis,s=o.polar,l=s.getAngleAxis(),u=o.getTicksCoords(),c=o.getMinorTicksCoords(),h=l.getExtent()[0],v=o.getExtent(),f=$b(s,t,h),p=new $e(t,f);D(Wb,p.add,p),n.add(p.getGroup()),Vo(i,n,t),D(Zb,function(d){t.get([d,"show"])&&!o.scale.isBlank()&&Yb[d](this.group,t,s,h,v,u,c)},this)}},e.type="radiusAxis",e}(va),Yb={splitLine:function(a,e,t,r,i,n){var o=e.getModel("splitLine"),s=o.getModel("lineStyle"),l=s.get("color"),u=0,c=t.getAngleAxis(),h=Math.PI/180,v=c.getExtent(),f=Math.abs(v[1]-v[0])===360?"Circle":"Arc";l=l instanceof Array?l:[l];for(var p=[],d=0;d<n.length;d++){var g=u++%l.length;p[g]=p[g]||[],p[g].push(new _r[f]({shape:{cx:t.cx,cy:t.cy,r:Math.max(n[d].coord,0),startAngle:-v[0]*h,endAngle:-v[1]*h,clockwise:c.inverse}}))}for(var d=0;d<p.length;d++)a.add(ne(p[d],{style:ot({stroke:l[d%l.length],fill:null},s.getLineStyle()),silent:!0}))},minorSplitLine:function(a,e,t,r,i,n,o){if(o.length){for(var s=e.getModel("minorSplitLine"),l=s.getModel("lineStyle"),u=[],c=0;c<o.length;c++)for(var h=0;h<o[c].length;h++)u.push(new la({shape:{cx:t.cx,cy:t.cy,r:o[c][h].coord}}));a.add(ne(u,{style:ot({fill:null},l.getLineStyle()),silent:!0}))}},splitArea:function(a,e,t,r,i,n){if(n.length){var o=e.getModel("splitArea"),s=o.getModel("areaStyle"),l=s.get("color"),u=0;l=l instanceof Array?l:[l];for(var c=[],h=n[0].coord,v=1;v<n.length;v++){var f=u++%l.length;c[f]=c[f]||[],c[f].push(new Ye({shape:{cx:t.cx,cy:t.cy,r0:h,r:n[v].coord,startAngle:0,endAngle:Math.PI*2},silent:!0})),h=n[v].coord}for(var v=0;v<c.length;v++)a.add(ne(c[v],{style:ot({fill:l[v%l.length]},s.getAreaStyle()),silent:!0}))}}};function $b(a,e,t){return{position:[a.cx,a.cy],rotation:t/180*Math.PI,labelDirection:-1,tickDirection:-1,nameDirection:1,labelRotate:e.getModel("axisLabel").get("rotate"),z2:1}}function Of(a){return a.get("stack")||"__ec_stack_"+a.seriesIndex}function Bf(a,e){return e.dim+a.model.componentIndex}function Xb(a,e,t){var r={},i=Kb(Yt(e.getSeriesByType(a),function(n){return!e.isSeriesFiltered(n)&&n.coordinateSystem&&n.coordinateSystem.type==="polar"}));e.eachSeriesByType(a,function(n){if(n.coordinateSystem.type==="polar"){var o=n.getData(),s=n.coordinateSystem,l=s.getBaseAxis(),u=Bf(s,l),c=Of(n),h=i[u][c],v=h.offset,f=h.width,p=s.getOtherAxis(l),d=n.coordinateSystem.cx,g=n.coordinateSystem.cy,y=n.get("barMinHeight")||0,m=n.get("barMinAngle")||0;r[c]=r[c]||[];for(var S=o.mapDimension(p.dim),x=o.mapDimension(l.dim),b=Uh(o,S),_=l.dim!=="radius"||!n.get("roundCap",!0),w=p.model,A=w.get("startValue"),C=p.dataToCoord(A||0),T=0,I=o.count();T<I;T++){var L=o.get(S,T),M=o.get(x,T),R=L>=0?"p":"n",P=C;b&&(r[c][M]||(r[c][M]={p:C,n:C}),P=r[c][M][R]);var k=void 0,V=void 0,N=void 0,z=void 0;if(p.dim==="radius"){var H=p.dataToCoord(L)-C,Z=l.dataToCoord(M);Math.abs(H)<y&&(H=(H<0?-1:1)*y),k=P,V=P+H,N=Z-v,z=N-f,b&&(r[c][M][R]=V)}else{var Y=p.dataToCoord(L,_)-C,Q=l.dataToCoord(M);Math.abs(Y)<m&&(Y=(Y<0?-1:1)*m),k=Q+v,V=k+f,N=P,z=P+Y,b&&(r[c][M][R]=z)}o.setItemLayout(T,{cx:d,cy:g,r0:k,r:V,startAngle:-N*Math.PI/180,endAngle:-z*Math.PI/180,clockwise:N>=z})}}})}function Kb(a){var e={};D(a,function(r,i){var n=r.getData(),o=r.coordinateSystem,s=o.getBaseAxis(),l=Bf(o,s),u=s.getExtent(),c=s.type==="category"?s.getBandWidth():Math.abs(u[1]-u[0])/n.count(),h=e[l]||{bandWidth:c,remainedWidth:c,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},v=h.stacks;e[l]=h;var f=Of(r);v[f]||h.autoWidthCount++,v[f]=v[f]||{width:0,maxWidth:0};var p=O(r.get("barWidth"),c),d=O(r.get("barMaxWidth"),c),g=r.get("barGap"),y=r.get("barCategoryGap");p&&!v[f].width&&(p=Math.min(h.remainedWidth,p),v[f].width=p,h.remainedWidth-=p),d&&(v[f].maxWidth=d),g!=null&&(h.gap=g),y!=null&&(h.categoryGap=y)});var t={};return D(e,function(r,i){t[i]={};var n=r.stacks,o=r.bandWidth,s=O(r.categoryGap,o),l=O(r.gap,1),u=r.remainedWidth,c=r.autoWidthCount,h=(u-s)/(c+(c-1)*l);h=Math.max(h,0),D(n,function(d,g){var y=d.maxWidth;y&&y<h&&(y=Math.min(y,u),d.width&&(y=Math.min(y,d.width)),u-=y,d.width=y,c--)}),h=(u-s)/(c+(c-1)*l),h=Math.max(h,0);var v=0,f;D(n,function(d,g){d.width||(d.width=h),f=d,v+=d.width*(1+l)}),f&&(v-=f.width*l);var p=-v/2;D(n,function(d,g){t[i][g]=t[i][g]||{offset:p,width:d.width},p+=d.width*(1+l)})}),t}var qb={startAngle:90,clockwise:!0,splitNumber:12,axisLabel:{rotate:0}},jb={splitNumber:5},Jb=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="polar",e}(Ot);function Qb(a){K(zo),va.registerAxisPointerClass("PolarAxisPointer",Ib),a.registerCoordinateSystem("polar",Bb),a.registerComponentModel(Pb),a.registerComponentView(Jb),Ka(a,"angle",Rb,qb),Ka(a,"radius",Eb,jb),a.registerComponentView(Fb),a.registerComponentView(Ub),a.registerLayout(q(Xb,"bar"))}function go(a,e){e=e||{};var t=a.coordinateSystem,r=a.axis,i={},n=r.position,o=r.orient,s=t.getRect(),l=[s.x,s.x+s.width,s.y,s.y+s.height],u={horizontal:{top:l[2],bottom:l[3]},vertical:{left:l[0],right:l[1]}};i.position=[o==="vertical"?u.vertical[n]:l[0],o==="horizontal"?u.horizontal[n]:l[3]];var c={horizontal:0,vertical:1};i.rotation=Math.PI/2*c[o];var h={top:-1,bottom:1,right:1,left:-1};i.labelDirection=i.tickDirection=i.nameDirection=h[n],a.get(["axisTick","inside"])&&(i.tickDirection=-i.tickDirection),qt(e.labelInside,a.get(["axisLabel","inside"]))&&(i.labelDirection=-i.labelDirection);var v=e.rotate;return v==null&&(v=a.get(["axisLabel","rotate"])),i.labelRotation=n==="top"?-v:v,i.z2=1,i}var t_=["axisLine","axisTickLabel","axisName"],e_=["splitArea","splitLine"],r_=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.axisPointerClass="SingleAxisPointer",t}return e.prototype.render=function(t,r,i,n){var o=this.group;o.removeAll();var s=this._axisGroup;this._axisGroup=new X;var l=go(t),u=new $e(t,l);D(t_,u.add,u),o.add(this._axisGroup),o.add(u.getGroup()),D(e_,function(c){t.get([c,"show"])&&a_[c](this,this.group,this._axisGroup,t)},this),Vo(s,this._axisGroup,t),a.prototype.render.call(this,t,r,i,n)},e.prototype.remove=function(){Ed(this)},e.type="singleAxis",e}(va),a_={splitLine:function(a,e,t,r){var i=r.axis;if(!i.scale.isBlank()){var n=r.getModel("splitLine"),o=n.getModel("lineStyle"),s=o.get("color");s=s instanceof Array?s:[s];for(var l=o.get("width"),u=r.coordinateSystem.getRect(),c=i.isHorizontal(),h=[],v=0,f=i.getTicksCoords({tickModel:n}),p=[],d=[],g=0;g<f.length;++g){var y=i.toGlobalCoord(f[g].coord);c?(p[0]=y,p[1]=u.y,d[0]=y,d[1]=u.y+u.height):(p[0]=u.x,p[1]=y,d[0]=u.x+u.width,d[1]=y);var m=new oe({shape:{x1:p[0],y1:p[1],x2:d[0],y2:d[1]},silent:!0});kd(m.shape,l);var S=v++%s.length;h[S]=h[S]||[],h[S].push(m)}for(var x=o.getLineStyle(["color"]),g=0;g<h.length;++g)e.add(ne(h[g],{style:ot({stroke:s[g%s.length]},x),silent:!0}))}},splitArea:function(a,e,t,r){Vd(a,t,r,r)}},Fa=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getCoordSysModel=function(){return this},e.type="singleAxis",e.layoutMode="box",e.defaultOption={left:"5%",top:"5%",right:"5%",bottom:"5%",type:"value",position:"bottom",orient:"horizontal",axisLine:{show:!0,lineStyle:{width:1,type:"solid"}},tooltip:{show:!0},axisTick:{show:!0,length:6,lineStyle:{width:1}},axisLabel:{show:!0,interval:"auto"},splitLine:{show:!0,lineStyle:{type:"dashed",opacity:.2}}},e}(kt);ue(Fa,gi.prototype);var i_=function(a){E(e,a);function e(t,r,i,n,o){var s=a.call(this,t,r,i)||this;return s.type=n||"value",s.position=o||"bottom",s}return e.prototype.isHorizontal=function(){var t=this.position;return t==="top"||t==="bottom"},e.prototype.pointToData=function(t,r){return this.coordinateSystem.pointToData(t)[0]},e}(de),Gf=["single"],n_=function(){function a(e,t,r){this.type="single",this.dimension="single",this.dimensions=Gf,this.axisPointerEnabled=!0,this.model=e,this._init(e,t,r)}return a.prototype._init=function(e,t,r){var i=this.dimension,n=new i_(i,Eo(e),[0,0],e.get("type"),e.get("position")),o=n.type==="category";n.onBand=o&&e.get("boundaryGap"),n.inverse=e.get("inverse"),n.orient=e.get("orient"),e.axis=n,n.model=e,n.coordinateSystem=this,this._axis=n},a.prototype.update=function(e,t){e.eachSeries(function(r){if(r.coordinateSystem===this){var i=r.getData();D(i.mapDimensionsAll(this.dimension),function(n){this._axis.scale.unionExtentFromData(i,n)},this),Xa(this._axis.scale,this._axis.model)}},this)},a.prototype.resize=function(e,t){this._rect=Rt({left:e.get("left"),top:e.get("top"),right:e.get("right"),bottom:e.get("bottom"),width:e.get("width"),height:e.get("height")},{width:t.getWidth(),height:t.getHeight()}),this._adjustAxis()},a.prototype.getRect=function(){return this._rect},a.prototype._adjustAxis=function(){var e=this._rect,t=this._axis,r=t.isHorizontal(),i=r?[0,e.width]:[0,e.height],n=t.inverse?1:0;t.setExtent(i[n],i[1-n]),this._updateAxisTransform(t,r?e.x:e.y)},a.prototype._updateAxisTransform=function(e,t){var r=e.getExtent(),i=r[0]+r[1],n=e.isHorizontal();e.toGlobalCoord=n?function(o){return o+t}:function(o){return i-o+t},e.toLocalCoord=n?function(o){return o-t}:function(o){return i-o+t}},a.prototype.getAxis=function(){return this._axis},a.prototype.getBaseAxis=function(){return this._axis},a.prototype.getAxes=function(){return[this._axis]},a.prototype.getTooltipAxes=function(){return{baseAxes:[this.getAxis()],otherAxes:[]}},a.prototype.containPoint=function(e){var t=this.getRect(),r=this.getAxis(),i=r.orient;return i==="horizontal"?r.contain(r.toLocalCoord(e[0]))&&e[1]>=t.y&&e[1]<=t.y+t.height:r.contain(r.toLocalCoord(e[1]))&&e[0]>=t.y&&e[0]<=t.y+t.height},a.prototype.pointToData=function(e){var t=this.getAxis();return[t.coordToData(t.toLocalCoord(e[t.orient==="horizontal"?0:1]))]},a.prototype.dataToPoint=function(e){var t=this.getAxis(),r=this.getRect(),i=[],n=t.orient==="horizontal"?0:1;return e instanceof Array&&(e=e[0]),i[n]=t.toGlobalCoord(t.dataToCoord(+e)),i[1-n]=n===0?r.y+r.height/2:r.x+r.width/2,i},a.prototype.convertToPixel=function(e,t,r){var i=nc(t);return i===this?this.dataToPoint(r):null},a.prototype.convertFromPixel=function(e,t,r){var i=nc(t);return i===this?this.pointToData(r):null},a}();function nc(a){var e=a.seriesModel,t=a.singleAxisModel;return t&&t.coordinateSystem||e&&e.coordinateSystem}function o_(a,e){var t=[];return a.eachComponent("singleAxis",function(r,i){var n=new n_(r,a,e);n.name="single_"+i,n.resize(r,e),r.coordinateSystem=n,t.push(n)}),a.eachSeries(function(r){if(r.get("coordinateSystem")==="singleAxis"){var i=r.getReferringComponents("singleAxis",fe).models[0];r.coordinateSystem=i&&i.coordinateSystem}}),t}var s_={create:o_,dimensions:Gf},oc=["x","y"],l_=["width","height"],u_=function(a){E(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.makeElOption=function(t,r,i,n,o){var s=i.axis,l=s.coordinateSystem,u=An(l,1-si(s)),c=l.dataToPoint(r)[0],h=n.get("type");if(h&&h!=="none"){var v=Fh(n),f=c_[h](s,c,u);f.style=v,t.graphicKey=f.type,t.pointer=f}var p=go(i);Nd(r,t,p,i,n,o)},e.prototype.getHandleTransform=function(t,r,i){var n=go(r,{labelInside:!1});n.labelMargin=i.get(["handle","margin"]);var o=zd(r.axis,t,n);return{x:o[0],y:o[1],rotation:n.rotation+(n.labelDirection<0?Math.PI:0)}},e.prototype.updateHandleTransform=function(t,r,i,n){var o=i.axis,s=o.coordinateSystem,l=si(o),u=An(s,l),c=[t.x,t.y];c[l]+=r[l],c[l]=Math.min(u[1],c[l]),c[l]=Math.max(u[0],c[l]);var h=An(s,1-l),v=(h[1]+h[0])/2,f=[v,v];return f[l]=c[l],{x:c[0],y:c[1],rotation:t.rotation,cursorPoint:f,tooltipOption:{verticalAlign:"middle"}}},e}(Hh),c_={line:function(a,e,t){var r=Wh([e,t[0]],[e,t[1]],si(a));return{type:"Line",subPixelOptimize:!0,shape:r}},shadow:function(a,e,t){var r=a.getBandWidth(),i=t[1]-t[0];return{type:"Rect",shape:Od([e-r/2,t[0]],[r,i],si(a))}}};function si(a){return a.isHorizontal()?0:1}function An(a,e){var t=a.getRect();return[t[oc[e]],t[oc[e]]+t[l_[e]]]}var h_=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="single",e}(Ot);function v_(a){K(zo),va.registerAxisPointerClass("SingleAxisPointer",u_),a.registerComponentView(h_),a.registerComponentView(r_),a.registerComponentModel(Fa),Ka(a,"single",Fa,Fa.defaultOption),a.registerCoordinateSystem("single",s_)}var f_=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r,i){var n=Oo(t);a.prototype.init.apply(this,arguments),sc(t,n)},e.prototype.mergeOption=function(t){a.prototype.mergeOption.apply(this,arguments),sc(this.option,t)},e.prototype.getCellSize=function(){return this.option.cellSize},e.type="calendar",e.defaultOption={z:2,left:80,top:60,cellSize:20,orient:"horizontal",splitLine:{show:!0,lineStyle:{color:"#000",width:1,type:"solid"}},itemStyle:{color:"#fff",borderWidth:1,borderColor:"#ccc"},dayLabel:{show:!0,firstDay:0,position:"start",margin:"50%",color:"#000"},monthLabel:{show:!0,position:"start",margin:5,align:"center",formatter:null,color:"#000"},yearLabel:{show:!0,position:null,margin:30,formatter:null,color:"#ccc",fontFamily:"sans-serif",fontWeight:"bolder",fontSize:20}},e}(kt);function sc(a,e){var t=a.cellSize,r;U(t)?r=t:r=a.cellSize=[t,t],r.length===1&&(r[1]=r[0]);var i=G([0,1],function(n){return Bd(e,n)&&(r[n]="auto"),r[n]!=null&&r[n]!=="auto"});Bo(a,e,{type:"box",ignoreSize:i})}var p_=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i){var n=this.group;n.removeAll();var o=t.coordinateSystem,s=o.getRangeInfo(),l=o.getOrient(),u=r.getLocaleModel();this._renderDayRect(t,s,n),this._renderLines(t,s,l,n),this._renderYearText(t,s,l,n),this._renderMonthText(t,u,l,n),this._renderWeekText(t,u,s,l,n)},e.prototype._renderDayRect=function(t,r,i){for(var n=t.coordinateSystem,o=t.getModel("itemStyle").getItemStyle(),s=n.getCellWidth(),l=n.getCellHeight(),u=r.start.time;u<=r.end.time;u=n.getNextNDay(u,1).time){var c=n.dataToRect([u],!1).tl,h=new pt({shape:{x:c[0],y:c[1],width:s,height:l},cursor:"default",style:o});i.add(h)}},e.prototype._renderLines=function(t,r,i,n){var o=this,s=t.coordinateSystem,l=t.getModel(["splitLine","lineStyle"]).getLineStyle(),u=t.get(["splitLine","show"]),c=l.lineWidth;this._tlpoints=[],this._blpoints=[],this._firstDayOfMonth=[],this._firstDayPoints=[];for(var h=r.start,v=0;h.time<=r.end.time;v++){p(h.formatedDate),v===0&&(h=s.getDateInfo(r.start.y+"-"+r.start.m));var f=h.date;f.setMonth(f.getMonth()+1),h=s.getDateInfo(f)}p(s.getNextNDay(r.end.time,1).formatedDate);function p(d){o._firstDayOfMonth.push(s.getDateInfo(d)),o._firstDayPoints.push(s.dataToRect([d],!1).tl);var g=o._getLinePointsOfOneWeek(t,d,i);o._tlpoints.push(g[0]),o._blpoints.push(g[g.length-1]),u&&o._drawSplitline(g,l,n)}u&&this._drawSplitline(o._getEdgesPoints(o._tlpoints,c,i),l,n),u&&this._drawSplitline(o._getEdgesPoints(o._blpoints,c,i),l,n)},e.prototype._getEdgesPoints=function(t,r,i){var n=[t[0].slice(),t[t.length-1].slice()],o=i==="horizontal"?0:1;return n[0][o]=n[0][o]-r/2,n[1][o]=n[1][o]+r/2,n},e.prototype._drawSplitline=function(t,r,i){var n=new le({z2:20,shape:{points:t},style:r});i.add(n)},e.prototype._getLinePointsOfOneWeek=function(t,r,i){for(var n=t.coordinateSystem,o=n.getDateInfo(r),s=[],l=0;l<7;l++){var u=n.getNextNDay(o.time,l),c=n.dataToRect([u.time],!1);s[2*u.day]=c.tl,s[2*u.day+1]=c[i==="horizontal"?"bl":"tr"]}return s},e.prototype._formatterLabel=function(t,r){return tt(t)&&t?Gd(t,r):st(t)?t(r):r.nameMap},e.prototype._yearTextPositionControl=function(t,r,i,n,o){var s=r[0],l=r[1],u=["center","bottom"];n==="bottom"?(l+=o,u=["center","top"]):n==="left"?s-=o:n==="right"?(s+=o,u=["center","top"]):l-=o;var c=0;return(n==="left"||n==="right")&&(c=Math.PI/2),{rotation:c,x:s,y:l,style:{align:u[0],verticalAlign:u[1]}}},e.prototype._renderYearText=function(t,r,i,n){var o=t.getModel("yearLabel");if(o.get("show")){var s=o.get("margin"),l=o.get("position");l||(l=i!=="horizontal"?"top":"left");var u=[this._tlpoints[this._tlpoints.length-1],this._blpoints[0]],c=(u[0][0]+u[1][0])/2,h=(u[0][1]+u[1][1])/2,v=i==="horizontal"?0:1,f={top:[c,u[v][1]],bottom:[c,u[1-v][1]],left:[u[1-v][0],h],right:[u[v][0],h]},p=r.start.y;+r.end.y>+r.start.y&&(p=p+"-"+r.end.y);var d=o.get("formatter"),g={start:r.start.y,end:r.end.y,nameMap:p},y=this._formatterLabel(d,g),m=new ut({z2:30,style:yt(o,{text:y}),silent:o.get("silent")});m.attr(this._yearTextPositionControl(m,f[l],i,l,s)),n.add(m)}},e.prototype._monthTextPositionControl=function(t,r,i,n,o){var s="left",l="top",u=t[0],c=t[1];return i==="horizontal"?(c=c+o,r&&(s="center"),n==="start"&&(l="bottom")):(u=u+o,r&&(l="middle"),n==="start"&&(s="right")),{x:u,y:c,align:s,verticalAlign:l}},e.prototype._renderMonthText=function(t,r,i,n){var o=t.getModel("monthLabel");if(o.get("show")){var s=o.get("nameMap"),l=o.get("margin"),u=o.get("position"),c=o.get("align"),h=[this._tlpoints,this._blpoints];(!s||tt(s))&&(s&&(r=tl(s)||r),s=r.get(["time","monthAbbr"])||[]);var v=u==="start"?0:1,f=i==="horizontal"?0:1;l=u==="start"?-l:l;for(var p=c==="center",d=o.get("silent"),g=0;g<h[v].length-1;g++){var y=h[v][g].slice(),m=this._firstDayOfMonth[g];if(p){var S=this._firstDayPoints[g];y[f]=(S[f]+h[0][g+1][f])/2}var x=o.get("formatter"),b=s[+m.m-1],_={yyyy:m.y,yy:(m.y+"").slice(2),MM:m.m,M:+m.m,nameMap:b},w=this._formatterLabel(x,_),A=new ut({z2:30,style:F(yt(o,{text:w}),this._monthTextPositionControl(y,p,i,u,l)),silent:d});n.add(A)}}},e.prototype._weekTextPositionControl=function(t,r,i,n,o){var s="center",l="middle",u=t[0],c=t[1],h=i==="start";return r==="horizontal"?(u=u+n+(h?1:-1)*o[0]/2,s=h?"right":"left"):(c=c+n+(h?1:-1)*o[1]/2,l=h?"bottom":"top"),{x:u,y:c,align:s,verticalAlign:l}},e.prototype._renderWeekText=function(t,r,i,n,o){var s=t.getModel("dayLabel");if(s.get("show")){var l=t.coordinateSystem,u=s.get("position"),c=s.get("nameMap"),h=s.get("margin"),v=l.getFirstDayOfWeek();if(!c||tt(c)){c&&(r=tl(c)||r);var f=r.get(["time","dayOfWeekShort"]);c=f||G(r.get(["time","dayOfWeekAbbr"]),function(_){return _[0]})}var p=l.getNextNDay(i.end.time,7-i.lweek).time,d=[l.getCellWidth(),l.getCellHeight()];h=O(h,Math.min(d[1],d[0])),u==="start"&&(p=l.getNextNDay(i.start.time,-(7+i.fweek)).time,h=-h);for(var g=s.get("silent"),y=0;y<7;y++){var m=l.getNextNDay(p,y),S=l.dataToRect([m.time],!1).center,x=y;x=Math.abs((y+v)%7);var b=new ut({z2:30,style:F(yt(s,{text:c[x]}),this._weekTextPositionControl(S,n,u,h,d)),silent:g});o.add(b)}}},e.type="calendar",e}(Ot),Tn=864e5,d_=function(){function a(e,t,r){this.type="calendar",this.dimensions=a.dimensions,this.getDimensionsInfo=a.getDimensionsInfo,this._model=e}return a.getDimensionsInfo=function(){return[{name:"time",type:"time"},"value"]},a.prototype.getRangeInfo=function(){return this._rangeInfo},a.prototype.getModel=function(){return this._model},a.prototype.getRect=function(){return this._rect},a.prototype.getCellWidth=function(){return this._sw},a.prototype.getCellHeight=function(){return this._sh},a.prototype.getOrient=function(){return this._orient},a.prototype.getFirstDayOfWeek=function(){return this._firstDayOfWeek},a.prototype.getDateInfo=function(e){e=Fd(e);var t=e.getFullYear(),r=e.getMonth()+1,i=r<10?"0"+r:""+r,n=e.getDate(),o=n<10?"0"+n:""+n,s=e.getDay();return s=Math.abs((s+7-this.getFirstDayOfWeek())%7),{y:t+"",m:i,d:o,day:s,time:e.getTime(),formatedDate:t+"-"+i+"-"+o,date:e}},a.prototype.getNextNDay=function(e,t){return t=t||0,t===0?this.getDateInfo(e):(e=new Date(this.getDateInfo(e).time),e.setDate(e.getDate()+t),this.getDateInfo(e))},a.prototype.update=function(e,t){this._firstDayOfWeek=+this._model.getModel("dayLabel").get("firstDay"),this._orient=this._model.get("orient"),this._lineWidth=this._model.getModel("itemStyle").getItemStyle().lineWidth||0,this._rangeInfo=this._getRangeInfo(this._initRangeOption());var r=this._rangeInfo.weeks||1,i=["width","height"],n=this._model.getCellSize().slice(),o=this._model.getBoxLayoutParams(),s=this._orient==="horizontal"?[r,7]:[7,r];D([0,1],function(h){c(n,h)&&(o[i[h]]=n[h]*s[h])});var l={width:t.getWidth(),height:t.getHeight()},u=this._rect=Rt(o,l);D([0,1],function(h){c(n,h)||(n[h]=u[i[h]]/s[h])});function c(h,v){return h[v]!=null&&h[v]!=="auto"}this._sw=n[0],this._sh=n[1]},a.prototype.dataToPoint=function(e,t){U(e)&&(e=e[0]),t==null&&(t=!0);var r=this.getDateInfo(e),i=this._rangeInfo,n=r.formatedDate;if(t&&!(r.time>=i.start.time&&r.time<i.end.time+Tn))return[NaN,NaN];var o=r.day,s=this._getRangeInfo([i.start.time,n]).nthWeek;return this._orient==="vertical"?[this._rect.x+o*this._sw+this._sw/2,this._rect.y+s*this._sh+this._sh/2]:[this._rect.x+s*this._sw+this._sw/2,this._rect.y+o*this._sh+this._sh/2]},a.prototype.pointToData=function(e){var t=this.pointToDate(e);return t&&t.time},a.prototype.dataToRect=function(e,t){var r=this.dataToPoint(e,t);return{contentShape:{x:r[0]-(this._sw-this._lineWidth)/2,y:r[1]-(this._sh-this._lineWidth)/2,width:this._sw-this._lineWidth,height:this._sh-this._lineWidth},center:r,tl:[r[0]-this._sw/2,r[1]-this._sh/2],tr:[r[0]+this._sw/2,r[1]-this._sh/2],br:[r[0]+this._sw/2,r[1]+this._sh/2],bl:[r[0]-this._sw/2,r[1]+this._sh/2]}},a.prototype.pointToDate=function(e){var t=Math.floor((e[0]-this._rect.x)/this._sw)+1,r=Math.floor((e[1]-this._rect.y)/this._sh)+1,i=this._rangeInfo.range;return this._orient==="vertical"?this._getDateByWeeksAndDay(r,t-1,i):this._getDateByWeeksAndDay(t,r-1,i)},a.prototype.convertToPixel=function(e,t,r){var i=lc(t);return i===this?i.dataToPoint(r):null},a.prototype.convertFromPixel=function(e,t,r){var i=lc(t);return i===this?i.pointToData(r):null},a.prototype.containPoint=function(e){return console.warn("Not implemented."),!1},a.prototype._initRangeOption=function(){var e=this._model.get("range"),t;if(U(e)&&e.length===1&&(e=e[0]),U(e))t=e;else{var r=e.toString();if(/^\d{4}$/.test(r)&&(t=[r+"-01-01",r+"-12-31"]),/^\d{4}[\/|-]\d{1,2}$/.test(r)){var i=this.getDateInfo(r),n=i.date;n.setMonth(n.getMonth()+1);var o=this.getNextNDay(n,-1);t=[i.formatedDate,o.formatedDate]}/^\d{4}[\/|-]\d{1,2}[\/|-]\d{1,2}$/.test(r)&&(t=[r,r])}if(!t)return e;var s=this._getRangeInfo(t);return s.start.time>s.end.time&&t.reverse(),t},a.prototype._getRangeInfo=function(e){var t=[this.getDateInfo(e[0]),this.getDateInfo(e[1])],r;t[0].time>t[1].time&&(r=!0,t.reverse());var i=Math.floor(t[1].time/Tn)-Math.floor(t[0].time/Tn)+1,n=new Date(t[0].time),o=n.getDate(),s=t[1].date.getDate();n.setDate(o+i-1);var l=n.getDate();if(l!==s)for(var u=n.getTime()-t[1].time>0?1:-1;(l=n.getDate())!==s&&(n.getTime()-t[1].time)*u>0;)i-=u,n.setDate(l-u);var c=Math.floor((i+t[0].day+6)/7),h=r?-c+1:c-1;return r&&t.reverse(),{range:[t[0].formatedDate,t[1].formatedDate],start:t[0],end:t[1],allDay:i,weeks:c,nthWeek:h,fweek:t[0].day,lweek:t[1].day}},a.prototype._getDateByWeeksAndDay=function(e,t,r){var i=this._getRangeInfo(r);if(e>i.weeks||e===0&&t<i.fweek||e===i.weeks&&t>i.lweek)return null;var n=(e-1)*7-i.fweek+t,o=new Date(i.start.time);return o.setDate(+i.start.d+n),this.getDateInfo(o)},a.create=function(e,t){var r=[];return e.eachComponent("calendar",function(i){var n=new a(i);r.push(n),i.coordinateSystem=n}),e.eachSeries(function(i){i.get("coordinateSystem")==="calendar"&&(i.coordinateSystem=r[i.get("calendarIndex")||0])}),r},a.dimensions=["time","value"],a}();function lc(a){var e=a.calendarModel,t=a.seriesModel,r=e?e.coordinateSystem:t?t.coordinateSystem:null;return r}function g_(a){a.registerComponentModel(f_),a.registerComponentView(p_),a.registerCoordinateSystem("calendar",d_)}function y_(a,e){var t=a.existing;if(e.id=a.keyInfo.id,!e.type&&t&&(e.type=t.type),e.parentId==null){var r=e.parentOption;r?e.parentId=r.id:t&&(e.parentId=t.parentId)}e.parentOption=null}function uc(a,e){var t;return D(e,function(r){a[r]!=null&&a[r]!=="auto"&&(t=!0)}),t}function m_(a,e,t){var r=F({},t),i=a[e],n=t.$action||"merge";n==="merge"?i?(ft(i,r,!0),Bo(i,r,{ignoreSize:!0}),Wd(t,i),Pa(t,i),Pa(t,i,"shape"),Pa(t,i,"style"),Pa(t,i,"extra"),t.clipPath=i.clipPath):a[e]=r:n==="replace"?a[e]=r:n==="remove"&&i&&(a[e]=null)}var Ff=["transition","enterFrom","leaveTo"],S_=Ff.concat(["enterAnimation","updateAnimation","leaveAnimation"]);function Pa(a,e,t){if(t&&(!a[t]&&e[t]&&(a[t]={}),a=a[t],e=e[t]),!(!a||!e))for(var r=t?Ff:S_,i=0;i<r.length;i++){var n=r[i];a[n]==null&&e[n]!=null&&(a[n]=e[n])}}function x_(a,e){if(a&&(a.hv=e.hv=[uc(e,["left","right"]),uc(e,["top","bottom"])],a.type==="group")){var t=a,r=e;t.width==null&&(t.width=r.width=0),t.height==null&&(t.height=r.height=0)}}var b_=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.preventAutoZ=!0,t}return e.prototype.mergeOption=function(t,r){var i=this.option.elements;this.option.elements=null,a.prototype.mergeOption.call(this,t,r),this.option.elements=i},e.prototype.optionUpdated=function(t,r){var i=this.option,n=(r?i:t).elements,o=i.elements=r?[]:i.elements,s=[];this._flatten(n,s,null);var l=Hd(o,s,"normalMerge"),u=this._elOptionsToUpdate=[];D(l,function(c,h){var v=c.newOption;v&&(u.push(v),y_(c,v),m_(o,h,v),x_(o[h],v))},this),i.elements=Yt(o,function(c){return c&&delete c.$action,c!=null})},e.prototype._flatten=function(t,r,i){D(t,function(n){if(n){i&&(n.parentOption=i),r.push(n);var o=n.children;o&&o.length&&this._flatten(o,r,n),delete n.children}},this)},e.prototype.useElOptionsToUpdate=function(){var t=this._elOptionsToUpdate;return this._elOptionsToUpdate=null,t},e.type="graphic",e.defaultOption={elements:[]},e}(kt),cc={path:null,compoundPath:null,group:X,image:pe,text:ut},te=Dt(),__=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(){this._elMap=et()},e.prototype.render=function(t,r,i){t!==this._lastGraphicModel&&this._clear(),this._lastGraphicModel=t,this._updateElements(t),this._relocate(t,i)},e.prototype._updateElements=function(t){var r=t.useElOptionsToUpdate();if(r){var i=this._elMap,n=this.group,o=t.get("z"),s=t.get("zlevel");D(r,function(l){var u=ye(l.id,null),c=u!=null?i.get(u):null,h=ye(l.parentId,null),v=h!=null?i.get(h):n,f=l.type,p=l.style;f==="text"&&p&&l.hv&&l.hv[1]&&(p.textVerticalAlign=p.textBaseline=p.verticalAlign=p.align=null);var d=l.textContent,g=l.textConfig;if(p&&If(p,f,!!g,!!d)){var y=Lf(p,f,!0);!g&&y.textConfig&&(g=l.textConfig=y.textConfig),!d&&y.textContent&&(d=y.textContent)}var m=w_(l),S=l.$action||"merge",x=S==="merge",b=S==="replace";if(x){var _=!c,w=c;_?w=hc(u,v,l.type,i):(w&&(te(w).isNew=!1),Ef(w)),w&&(Ga(w,m,t,{isInit:_}),vc(w,l,o,s))}else if(b){Ha(c,l,i,t);var A=hc(u,v,l.type,i);A&&(Ga(A,m,t,{isInit:!0}),vc(A,l,o,s))}else S==="remove"&&(Pf(c,l),Ha(c,l,i,t));var C=i.get(u);if(C&&d)if(x){var T=C.getTextContent();T?T.attr(d):C.setTextContent(new ut(d))}else b&&C.setTextContent(new ut(d));if(C){var I=l.clipPath;if(I){var L=I.type,M=void 0,_=!1;if(x){var R=C.getClipPath();_=!R||te(R).type!==L,M=_?yo(L):R}else b&&(_=!0,M=yo(L));C.setClipPath(M),Ga(M,I,t,{isInit:_}),ni(M,I.keyframeAnimation,t)}var P=te(C);C.setTextConfig(g),P.option=l,A_(C,t,l),xi({el:C,componentModel:t,itemName:C.name,itemTooltipOption:l.tooltip}),ni(C,l.keyframeAnimation,t)}})}},e.prototype._relocate=function(t,r){for(var i=t.option.elements,n=this.group,o=this._elMap,s=r.getWidth(),l=r.getHeight(),u=["x","y"],c=0;c<i.length;c++){var h=i[c],v=ye(h.id,null),f=v!=null?o.get(v):null;if(!(!f||!f.isGroup)){var p=f.parent,d=p===n,g=te(f),y=te(p);g.width=O(g.option.width,d?s:y.width)||0,g.height=O(g.option.height,d?l:y.height)||0}}for(var c=i.length-1;c>=0;c--){var h=i[c],v=ye(h.id,null),f=v!=null?o.get(v):null;if(f){var p=f.parent,y=te(p),m=p===n?{width:s,height:l}:{width:y.width,height:y.height},S={},x=bi(f,h,m,null,{hv:h.hv,boundingMode:h.bounding},S);if(!te(f).isNew&&x){for(var b=h.transition,_={},w=0;w<u.length;w++){var A=u[w],C=S[A];b&&(Ue(b)||mt(b,A)>=0)?_[A]=C:f[A]=C}dt(f,_,t,0)}else f.attr(S)}}},e.prototype._clear=function(){var t=this,r=this._elMap;r.each(function(i){Ha(i,te(i).option,r,t._lastGraphicModel)}),this._elMap=et()},e.prototype.dispose=function(){this._clear()},e.type="graphic",e}(Ot);function yo(a){var e=B(cc,a)?cc[a]:Gh(a),t=new e({});return te(t).type=a,t}function hc(a,e,t,r){var i=yo(t);return e.add(i),r.set(a,i),te(i).id=a,te(i).isNew=!0,i}function Ha(a,e,t,r){var i=a&&a.parent;i&&(a.type==="group"&&a.traverse(function(n){Ha(n,e,t,r)}),Ei(a,e,r),t.removeKey(te(a).id))}function vc(a,e,t,r){a.isGroup||D([["cursor",dr.prototype.cursor],["zlevel",r||0],["z",t||0],["z2",0]],function(i){var n=i[0];B(e,n)?a[n]=Mt(e[n],i[1]):a[n]==null&&(a[n]=i[1])}),D(wt(e),function(i){if(i.indexOf("on")===0){var n=e[i];a[i]=st(n)?n:null}}),B(e,"draggable")&&(a.draggable=e.draggable),e.name!=null&&(a.name=e.name),e.id!=null&&(a.id=e.id)}function w_(a){return a=F({},a),D(["id","parentId","$action","hv","bounding","textContent","clipPath"].concat(Zd),function(e){delete a[e]}),a}function A_(a,e,t){var r=ht(a).eventData;!a.silent&&!a.ignore&&!r&&(r=ht(a).eventData={componentType:"graphic",componentIndex:e.componentIndex,name:a.name}),r&&(r.info=t.info)}function T_(a){a.registerComponentModel(b_),a.registerComponentView(__),a.registerPreprocessor(function(e){var t=e.graphic;U(t)?!t[0]||!t[0].elements?e.graphic=[{elements:t}]:e.graphic=[e.graphic[0]]:t&&!t.elements&&(e.graphic=[{elements:[t]}])})}var fc=["x","y","radius","angle","single"],D_=["cartesian2d","polar","singleAxis"];function C_(a){var e=a.get("coordinateSystem");return mt(D_,e)>=0}function Ce(a){return a+"Axis"}function I_(a,e){var t=et(),r=[],i=et();a.eachComponent({mainType:"dataZoom",query:e},function(c){i.get(c.uid)||s(c)});var n;do n=!1,a.eachComponent("dataZoom",o);while(n);function o(c){!i.get(c.uid)&&l(c)&&(s(c),n=!0)}function s(c){i.set(c.uid,!0),r.push(c),u(c)}function l(c){var h=!1;return c.eachTargetAxis(function(v,f){var p=t.get(v);p&&p[f]&&(h=!0)}),h}function u(c){c.eachTargetAxis(function(h,v){(t.get(h)||t.set(h,[]))[v]=!0})}return r}function Hf(a){var e=a.ecModel,t={infoList:[],infoMap:et()};return a.eachTargetAxis(function(r,i){var n=e.getComponent(Ce(r),i);if(n){var o=n.getCoordSysModel();if(o){var s=o.uid,l=t.infoMap.get(s);l||(l={model:o,axisModels:[]},t.infoList.push(l),t.infoMap.set(s,l)),l.axisModels.push(n)}}}),t}var Dn=function(){function a(){this.indexList=[],this.indexMap=[]}return a.prototype.add=function(e){this.indexMap[e]||(this.indexList.push(e),this.indexMap[e]=!0)},a}(),aa=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._autoThrottle=!0,t._noTarget=!0,t._rangePropMode=["percent","percent"],t}return e.prototype.init=function(t,r,i){var n=pc(t);this.settledOption=n,this.mergeDefaultAndTheme(t,i),this._doInit(n)},e.prototype.mergeOption=function(t){var r=pc(t);ft(this.option,t,!0),ft(this.settledOption,r,!0),this._doInit(r)},e.prototype._doInit=function(t){var r=this.option;this._setDefaultThrottle(t),this._updateRangeUse(t);var i=this.settledOption;D([["start","startValue"],["end","endValue"]],function(n,o){this._rangePropMode[o]==="value"&&(r[n[0]]=i[n[0]]=null)},this),this._resetTarget()},e.prototype._resetTarget=function(){var t=this.get("orient",!0),r=this._targetAxisInfoMap=et(),i=this._fillSpecifiedTargetAxis(r);i?this._orient=t||this._makeAutoOrientByTargetAxis():(this._orient=t||"horizontal",this._fillAutoTargetAxisByOrient(r,this._orient)),this._noTarget=!0,r.each(function(n){n.indexList.length&&(this._noTarget=!1)},this)},e.prototype._fillSpecifiedTargetAxis=function(t){var r=!1;return D(fc,function(i){var n=this.getReferringComponents(Ce(i),Ud);if(n.specified){r=!0;var o=new Dn;D(n.models,function(s){o.add(s.componentIndex)}),t.set(i,o)}},this),r},e.prototype._fillAutoTargetAxisByOrient=function(t,r){var i=this.ecModel,n=!0;if(n){var o=r==="vertical"?"y":"x",s=i.findComponents({mainType:o+"Axis"});l(s,o)}if(n){var s=i.findComponents({mainType:"singleAxis",filter:function(c){return c.get("orient",!0)===r}});l(s,"single")}function l(u,c){var h=u[0];if(h){var v=new Dn;if(v.add(h.componentIndex),t.set(c,v),n=!1,c==="x"||c==="y"){var f=h.getReferringComponents("grid",fe).models[0];f&&D(u,function(p){h.componentIndex!==p.componentIndex&&f===p.getReferringComponents("grid",fe).models[0]&&v.add(p.componentIndex)})}}}n&&D(fc,function(u){if(n){var c=i.findComponents({mainType:Ce(u),filter:function(v){return v.get("type",!0)==="category"}});if(c[0]){var h=new Dn;h.add(c[0].componentIndex),t.set(u,h),n=!1}}},this)},e.prototype._makeAutoOrientByTargetAxis=function(){var t;return this.eachTargetAxis(function(r){!t&&(t=r)},this),t==="y"?"vertical":"horizontal"},e.prototype._setDefaultThrottle=function(t){if(t.hasOwnProperty("throttle")&&(this._autoThrottle=!1),this._autoThrottle){var r=this.ecModel.option;this.option.throttle=r.animation&&r.animationDurationUpdate>0?100:20}},e.prototype._updateRangeUse=function(t){var r=this._rangePropMode,i=this.get("rangeMode");D([["start","startValue"],["end","endValue"]],function(n,o){var s=t[n[0]]!=null,l=t[n[1]]!=null;s&&!l?r[o]="percent":!s&&l?r[o]="value":i?r[o]=i[o]:s&&(r[o]="percent")})},e.prototype.noTarget=function(){return this._noTarget},e.prototype.getFirstTargetAxisModel=function(){var t;return this.eachTargetAxis(function(r,i){t==null&&(t=this.ecModel.getComponent(Ce(r),i))},this),t},e.prototype.eachTargetAxis=function(t,r){this._targetAxisInfoMap.each(function(i,n){D(i.indexList,function(o){t.call(r,n,o)})})},e.prototype.getAxisProxy=function(t,r){var i=this.getAxisModel(t,r);if(i)return i.__dzAxisProxy},e.prototype.getAxisModel=function(t,r){var i=this._targetAxisInfoMap.get(t);if(i&&i.indexMap[r])return this.ecModel.getComponent(Ce(t),r)},e.prototype.setRawRange=function(t){var r=this.option,i=this.settledOption;D([["start","startValue"],["end","endValue"]],function(n){(t[n[0]]!=null||t[n[1]]!=null)&&(r[n[0]]=i[n[0]]=t[n[0]],r[n[1]]=i[n[1]]=t[n[1]])},this),this._updateRangeUse(t)},e.prototype.setCalculatedRange=function(t){var r=this.option;D(["start","startValue","end","endValue"],function(i){r[i]=t[i]})},e.prototype.getPercentRange=function(){var t=this.findRepresentativeAxisProxy();if(t)return t.getDataPercentWindow()},e.prototype.getValueRange=function(t,r){if(t==null&&r==null){var i=this.findRepresentativeAxisProxy();if(i)return i.getDataValueWindow()}else return this.getAxisProxy(t,r).getDataValueWindow()},e.prototype.findRepresentativeAxisProxy=function(t){if(t)return t.__dzAxisProxy;for(var r,i=this._targetAxisInfoMap.keys(),n=0;n<i.length;n++)for(var o=i[n],s=this._targetAxisInfoMap.get(o),l=0;l<s.indexList.length;l++){var u=this.getAxisProxy(o,s.indexList[l]);if(u.hostedBy(this))return u;r||(r=u)}return r},e.prototype.getRangePropMode=function(){return this._rangePropMode.slice()},e.prototype.getOrient=function(){return this._orient},e.type="dataZoom",e.dependencies=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","series","toolbox"],e.defaultOption={z:4,filterMode:"filter",start:0,end:100},e}(kt);function pc(a){var e={};return D(["start","end","startValue","endValue","throttle"],function(t){a.hasOwnProperty(t)&&(e[t]=a[t])}),e}var L_=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="dataZoom.select",e}(aa),Is=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i,n){this.dataZoomModel=t,this.ecModel=r,this.api=i},e.type="dataZoom",e}(Ot),M_=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="dataZoom.select",e}(Is),lr=D,dc=re,P_=function(){function a(e,t,r,i){this._dimName=e,this._axisIndex=t,this.ecModel=i,this._dataZoomModel=r}return a.prototype.hostedBy=function(e){return this._dataZoomModel===e},a.prototype.getDataValueWindow=function(){return this._valueWindow.slice()},a.prototype.getDataPercentWindow=function(){return this._percentWindow.slice()},a.prototype.getTargetSeriesModels=function(){var e=[];return this.ecModel.eachSeries(function(t){if(C_(t)){var r=Ce(this._dimName),i=t.getReferringComponents(r,fe).models[0];i&&this._axisIndex===i.componentIndex&&e.push(t)}},this),e},a.prototype.getAxisModel=function(){return this.ecModel.getComponent(this._dimName+"Axis",this._axisIndex)},a.prototype.getMinMaxSpan=function(){return it(this._minMaxSpan)},a.prototype.calculateDataWindow=function(e){var t=this._dataExtent,r=this.getAxisModel(),i=r.axis.scale,n=this._dataZoomModel.getRangePropMode(),o=[0,100],s=[],l=[],u;lr(["start","end"],function(v,f){var p=e[v],d=e[v+"Value"];n[f]==="percent"?(p==null&&(p=o[f]),d=i.parse(at(p,o,t))):(u=!0,d=d==null?t[f]:i.parse(d),p=at(d,t,o)),l[f]=d==null||isNaN(d)?t[f]:d,s[f]=p==null||isNaN(p)?o[f]:p}),dc(l),dc(s);var c=this._minMaxSpan;u?h(l,s,t,o,!1):h(s,l,o,t,!0);function h(v,f,p,d,g){var y=g?"Span":"ValueSpan";Qe(0,v,p,"all",c["min"+y],c["max"+y]);for(var m=0;m<2;m++)f[m]=at(v[m],p,d,!0),g&&(f[m]=i.parse(f[m]))}return{valueWindow:l,percentWindow:s}},a.prototype.reset=function(e){if(e===this._dataZoomModel){var t=this.getTargetSeriesModels();this._dataExtent=R_(this,this._dimName,t),this._updateMinMaxSpan();var r=this.calculateDataWindow(e.settledOption);this._valueWindow=r.valueWindow,this._percentWindow=r.percentWindow,this._setAxisModel()}},a.prototype.filterData=function(e,t){if(e!==this._dataZoomModel)return;var r=this._dimName,i=this.getTargetSeriesModels(),n=e.get("filterMode"),o=this._valueWindow;if(n==="none")return;lr(i,function(l){var u=l.getData(),c=u.mapDimensionsAll(r);if(c.length){if(n==="weakFilter"){var h=u.getStore(),v=G(c,function(f){return u.getDimensionIndex(f)},u);u.filterSelf(function(f){for(var p,d,g,y=0;y<c.length;y++){var m=h.get(v[y],f),S=!isNaN(m),x=m<o[0],b=m>o[1];if(S&&!x&&!b)return!0;S&&(g=!0),x&&(p=!0),b&&(d=!0)}return g&&p&&d})}else lr(c,function(f){if(n==="empty")l.setData(u=u.map(f,function(d){return s(d)?d:NaN}));else{var p={};p[f]=o,u.selectRange(p)}});lr(c,function(f){u.setApproximateExtent(o,f)})}});function s(l){return l>=o[0]&&l<=o[1]}},a.prototype._updateMinMaxSpan=function(){var e=this._minMaxSpan={},t=this._dataZoomModel,r=this._dataExtent;lr(["min","max"],function(i){var n=t.get(i+"Span"),o=t.get(i+"ValueSpan");o!=null&&(o=this.getAxisModel().axis.scale.parse(o)),o!=null?n=at(r[0]+o,r,[0,100],!0):n!=null&&(o=at(n,[0,100],r,!0)-r[0]),e[i+"Span"]=n,e[i+"ValueSpan"]=o},this)},a.prototype._setAxisModel=function(){var e=this.getAxisModel(),t=this._percentWindow,r=this._valueWindow;if(t){var i=Yd(r,[0,500]);i=Math.min(i,20);var n=e.axis.scale.rawExtentInfo;t[0]!==0&&n.setDeterminedMinMax("min",+r[0].toFixed(i)),t[1]!==100&&n.setDeterminedMinMax("max",+r[1].toFixed(i)),n.freeze()}},a}();function R_(a,e,t){var r=[1/0,-1/0];lr(t,function(o){$d(r,o.getData(),e)});var i=a.getAxisModel(),n=Xd(i.axis.scale,i,r).calculate();return[n.min,n.max]}var E_={getTargetSeries:function(a){function e(i){a.eachComponent("dataZoom",function(n){n.eachTargetAxis(function(o,s){var l=a.getComponent(Ce(o),s);i(o,s,l,n)})})}e(function(i,n,o,s){o.__dzAxisProxy=null});var t=[];e(function(i,n,o,s){o.__dzAxisProxy||(o.__dzAxisProxy=new P_(i,n,s,a),t.push(o.__dzAxisProxy))});var r=et();return D(t,function(i){D(i.getTargetSeriesModels(),function(n){r.set(n.uid,n)})}),r},overallReset:function(a,e){a.eachComponent("dataZoom",function(t){t.eachTargetAxis(function(r,i){t.getAxisProxy(r,i).reset(t)}),t.eachTargetAxis(function(r,i){t.getAxisProxy(r,i).filterData(t,e)})}),a.eachComponent("dataZoom",function(t){var r=t.findRepresentativeAxisProxy();if(r){var i=r.getDataPercentWindow(),n=r.getDataValueWindow();t.setCalculatedRange({start:i[0],end:i[1],startValue:n[0],endValue:n[1]})}})}};function k_(a){a.registerAction("dataZoom",function(e,t){var r=I_(t,e);D(r,function(i){i.setRawRange({start:e.start,end:e.end,startValue:e.startValue,endValue:e.endValue})})})}var gc=!1;function Ls(a){gc||(gc=!0,a.registerProcessor(a.PRIORITY.PROCESSOR.FILTER,E_),k_(a),a.registerSubTypeDefaulter("dataZoom",function(){return"slider"}))}function V_(a){a.registerComponentModel(L_),a.registerComponentView(M_),Ls(a)}var ee=function(){function a(){}return a}(),Wf={};function ur(a,e){Wf[a]=e}function Zf(a){return Wf[a]}var N_=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.optionUpdated=function(){a.prototype.optionUpdated.apply(this,arguments);var t=this.ecModel;D(this.option.feature,function(r,i){var n=Zf(i);n&&(n.getDefaultOption&&(n.defaultOption=n.getDefaultOption(t)),ft(r,n.defaultOption))})},e.type="toolbox",e.layoutMode={type:"box",ignoreSize:!0},e.defaultOption={show:!0,z:6,orient:"horizontal",left:"right",top:"top",backgroundColor:"transparent",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemSize:15,itemGap:8,showTitle:!0,iconStyle:{borderColor:"#666",color:"none"},emphasis:{iconStyle:{borderColor:"#3E98C5"}},tooltip:{show:!1,position:"bottom"}},e}(kt);function z_(a,e,t){var r=e.getBoxLayoutParams(),i=e.get("padding"),n={width:t.getWidth(),height:t.getHeight()},o=Rt(r,n,i);Ze(e.get("orient"),a,e.get("itemGap"),o.width,o.height),bi(a,r,n,i)}function Uf(a,e){var t=Yh(e.get("padding")),r=e.getItemStyle(["color","opacity"]);return r.fill=e.get("backgroundColor"),a=new pt({shape:{x:a.x-t[3],y:a.y-t[0],width:a.width+t[1]+t[3],height:a.height+t[0]+t[2],r:e.get("borderRadius")},style:r,silent:!0,z2:-1}),a}var O_=function(a){E(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.render=function(t,r,i,n){var o=this.group;if(o.removeAll(),!t.get("show"))return;var s=+t.get("itemSize"),l=t.get("orient")==="vertical",u=t.get("feature")||{},c=this._features||(this._features={}),h=[];D(u,function(p,d){h.push(d)}),new Ar(this._featureNames||[],h).add(v).update(v).remove(q(v,null)).execute(),this._featureNames=h;function v(p,d){var g=h[p],y=h[d],m=u[g],S=new Ut(m,t,t.ecModel),x;if(n&&n.newTitle!=null&&n.featureName===g&&(m.title=n.newTitle),g&&!y){if(B_(g))x={onclick:S.option.onclick,featureName:g};else{var b=Zf(g);if(!b)return;x=new b}c[g]=x}else if(x=c[y],!x)return;x.uid=Ah("toolbox-feature"),x.model=S,x.ecModel=r,x.api=i;var _=x instanceof ee;if(!g&&y){_&&x.dispose&&x.dispose(r,i);return}if(!S.get("show")||_&&x.unusable){_&&x.remove&&x.remove(r,i);return}f(S,x,g),S.setIconStatus=function(w,A){var C=this.option,T=this.iconPaths;C.iconStatus=C.iconStatus||{},C.iconStatus[w]=A,T[w]&&(A==="emphasis"?$r:Xr)(T[w])},x instanceof ee&&x.render&&x.render(S,r,i,n)}function f(p,d,g){var y=p.getModel("iconStyle"),m=p.getModel(["emphasis","iconStyle"]),S=d instanceof ee&&d.getIcons?d.getIcons():p.get("icon"),x=p.get("title")||{},b,_;tt(S)?(b={},b[g]=S):b=S,tt(x)?(_={},_[g]=x):_=x;var w=p.iconPaths={};D(b,function(A,C){var T=Go(A,{},{x:-s/2,y:-s/2,width:s,height:s});T.setStyle(y.getItemStyle());var I=T.ensureState("emphasis");I.style=m.getItemStyle();var L=new ut({style:{text:_[C],align:m.get("textAlign"),borderRadius:m.get("textBorderRadius"),padding:m.get("textPadding"),fill:null,font:Bh({fontStyle:m.get("textFontStyle"),fontFamily:m.get("textFontFamily"),fontSize:m.get("textFontSize"),fontWeight:m.get("textFontWeight")},r)},ignore:!0});T.setTextContent(L),xi({el:T,componentModel:t,itemName:C,formatterParamsExtra:{title:_[C]}}),T.__title=_[C],T.on("mouseover",function(){var M=m.getItemStyle(),R=l?t.get("right")==null&&t.get("left")!=="right"?"right":"left":t.get("bottom")==null&&t.get("top")!=="bottom"?"bottom":"top";L.setStyle({fill:m.get("textFill")||M.fill||M.stroke||"#000",backgroundColor:m.get("textBackgroundColor")}),T.setTextConfig({position:m.get("textPosition")||R}),L.ignore=!t.get("showTitle"),i.enterEmphasis(this)}).on("mouseout",function(){p.get(["iconStatus",C])!=="emphasis"&&i.leaveEmphasis(this),L.hide()}),(p.get(["iconStatus",C])==="emphasis"?$r:Xr)(T),o.add(T),T.on("click",W(d.onclick,d,r,i,C)),w[C]=T})}z_(o,t,i),o.add(Uf(o.getBoundingRect(),t)),l||o.eachChild(function(p){var d=p.__title,g=p.ensureState("emphasis"),y=g.textConfig||(g.textConfig={}),m=p.getTextContent(),S=m&&m.ensureState("emphasis");if(S&&!st(S)&&d){var x=S.style||(S.style={}),b=Zh(d,ut.makeFont(x)),_=p.x+o.x,w=p.y+o.y+s,A=!1;w+b.height>i.getHeight()&&(y.position="top",A=!0);var C=A?-5-b.height:s+10;_+b.width/2>i.getWidth()?(y.position=["100%",C],x.align="right"):_-b.width/2<0&&(y.position=[0,C],x.align="left")}})},e.prototype.updateView=function(t,r,i,n){D(this._features,function(o){o instanceof ee&&o.updateView&&o.updateView(o.model,r,i,n)})},e.prototype.remove=function(t,r){D(this._features,function(i){i instanceof ee&&i.remove&&i.remove(t,r)}),this.group.removeAll()},e.prototype.dispose=function(t,r){D(this._features,function(i){i instanceof ee&&i.dispose&&i.dispose(t,r)})},e.type="toolbox",e}(Ot);function B_(a){return a.indexOf("my")===0}var G_=function(a){E(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.onclick=function(t,r){var i=this.model,n=i.get("name")||t.get("title.0.text")||"echarts",o=r.getZr().painter.getType()==="svg",s=o?"svg":i.get("type",!0)||"png",l=r.getConnectedDataURL({type:s,backgroundColor:i.get("backgroundColor",!0)||t.get("backgroundColor")||"#fff",connectedBackgroundColor:i.get("connectedBackgroundColor"),excludeComponents:i.get("excludeComponents"),pixelRatio:i.get("pixelRatio")}),u=$h.browser;if(typeof MouseEvent=="function"&&(u.newEdge||!u.ie&&!u.edge)){var c=document.createElement("a");c.download=n+"."+s,c.target="_blank",c.href=l;var h=new MouseEvent("click",{view:document.defaultView,bubbles:!0,cancelable:!1});c.dispatchEvent(h)}else if(window.navigator.msSaveOrOpenBlob||o){var v=l.split(","),f=v[0].indexOf("base64")>-1,p=o?decodeURIComponent(v[1]):v[1];f&&(p=window.atob(p));var d=n+"."+s;if(window.navigator.msSaveOrOpenBlob){for(var g=p.length,y=new Uint8Array(g);g--;)y[g]=p.charCodeAt(g);var m=new Blob([y]);window.navigator.msSaveOrOpenBlob(m,d)}else{var S=document.createElement("iframe");document.body.appendChild(S);var x=S.contentWindow,b=x.document;b.open("image/svg+xml","replace"),b.write(p),b.close(),x.focus(),b.execCommand("SaveAs",!0,d),document.body.removeChild(S)}}else{var _=i.get("lang"),w='<body style="margin:0;"><img src="'+l+'" style="max-width:100%;" title="'+(_&&_[0]||"")+'" /></body>',A=window.open();A.document.write(w),A.document.title=n}},e.getDefaultOption=function(t){var r={show:!0,icon:"M4.7,22.9L29.3,45.5L54.7,23.4M4.6,43.6L4.6,58L53.8,58L53.8,43.6M29.2,45.1L29.2,0",title:t.getLocaleModel().get(["toolbox","saveAsImage","title"]),type:"png",connectedBackgroundColor:"#fff",name:"",excludeComponents:["toolbox"],lang:t.getLocaleModel().get(["toolbox","saveAsImage","lang"])};return r},e}(ee),yc="__ec_magicType_stack__",F_=[["line","bar"],["stack"]],H_=function(a){E(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.getIcons=function(){var t=this.model,r=t.get("icon"),i={};return D(t.get("type"),function(n){r[n]&&(i[n]=r[n])}),i},e.getDefaultOption=function(t){var r={show:!0,type:[],icon:{line:"M4.1,28.9h7.1l9.3-22l7.4,38l9.7-19.7l3,12.8h14.9M4.1,58h51.4",bar:"M6.7,22.9h10V48h-10V22.9zM24.9,13h10v35h-10V13zM43.2,2h10v46h-10V2zM3.1,58h53.7",stack:"M8.2,38.4l-8.4,4.1l30.6,15.3L60,42.5l-8.1-4.1l-21.5,11L8.2,38.4z M51.9,30l-8.1,4.2l-13.4,6.9l-13.9-6.9L8.2,30l-8.4,4.2l8.4,4.2l22.2,11l21.5-11l8.1-4.2L51.9,30z M51.9,21.7l-8.1,4.2L35.7,30l-5.3,2.8L24.9,30l-8.4-4.1l-8.3-4.2l-8.4,4.2L8.2,30l8.3,4.2l13.9,6.9l13.4-6.9l8.1-4.2l8.1-4.1L51.9,21.7zM30.4,2.2L-0.2,17.5l8.4,4.1l8.3,4.2l8.4,4.2l5.5,2.7l5.3-2.7l8.1-4.2l8.1-4.2l8.1-4.1L30.4,2.2z"},title:t.getLocaleModel().get(["toolbox","magicType","title"]),option:{},seriesIndex:{}};return r},e.prototype.onclick=function(t,r,i){var n=this.model,o=n.get(["seriesIndex",i]);if(mc[i]){var s={series:[]},l=function(h){var v=h.subType,f=h.id,p=mc[i](v,f,h,n);p&&(ot(p,h.option),s.series.push(p));var d=h.coordinateSystem;if(d&&d.type==="cartesian2d"&&(i==="line"||i==="bar")){var g=d.getAxesByScale("ordinal")[0];if(g){var y=g.dim,m=y+"Axis",S=h.getReferringComponents(m,fe).models[0],x=S.componentIndex;s[m]=s[m]||[];for(var b=0;b<=x;b++)s[m][x]=s[m][x]||{};s[m][x].boundaryGap=i==="bar"}}};D(F_,function(h){mt(h,i)>=0&&D(h,function(v){n.setIconStatus(v,"normal")})}),n.setIconStatus(i,"emphasis"),t.eachComponent({mainType:"series",query:o==null?null:{seriesIndex:o}},l);var u,c=i;i==="stack"&&(u=ft({stack:n.option.title.tiled,tiled:n.option.title.stack},n.option.title),n.get(["iconStatus",i])!=="emphasis"&&(c="tiled")),r.dispatchAction({type:"changeMagicType",currentType:c,newOption:s,newTitle:u,featureName:"magicType"})}},e}(ee),mc={line:function(a,e,t,r){if(a==="bar")return ft({id:e,type:"line",data:t.get("data"),stack:t.get("stack"),markPoint:t.get("markPoint"),markLine:t.get("markLine")},r.get(["option","line"])||{},!0)},bar:function(a,e,t,r){if(a==="line")return ft({id:e,type:"bar",data:t.get("data"),stack:t.get("stack"),markPoint:t.get("markPoint"),markLine:t.get("markLine")},r.get(["option","bar"])||{},!0)},stack:function(a,e,t,r){var i=t.get("stack")===yc;if(a==="line"||a==="bar")return r.setIconStatus("stack",i?"normal":"emphasis"),ft({id:e,stack:i?"":yc},r.get(["option","stack"])||{},!0)}};yi({type:"changeMagicType",event:"magicTypeChanged",update:"prepareAndUpdate"},function(a,e){e.mergeOption(a.newOption)});var ki=new Array(60).join("-"),Sr="	";function W_(a){var e={},t=[],r=[];return a.eachRawSeries(function(i){var n=i.coordinateSystem;if(n&&(n.type==="cartesian2d"||n.type==="polar")){var o=n.getBaseAxis();if(o.type==="category"){var s=o.dim+"_"+o.index;e[s]||(e[s]={categoryAxis:o,valueAxis:n.getOtherAxis(o),series:[]},r.push({axisDim:o.dim,axisIndex:o.index})),e[s].series.push(i)}else t.push(i)}else t.push(i)}),{seriesGroupByCategoryAxis:e,other:t,meta:r}}function Z_(a){var e=[];return D(a,function(t,r){var i=t.categoryAxis,n=t.valueAxis,o=n.dim,s=[" "].concat(G(t.series,function(f){return f.name})),l=[i.model.getCategories()];D(t.series,function(f){var p=f.getRawData();l.push(f.getRawData().mapArray(p.mapDimension(o),function(d){return d}))});for(var u=[s.join(Sr)],c=0;c<l[0].length;c++){for(var h=[],v=0;v<l.length;v++)h.push(l[v][c]);u.push(h.join(Sr))}e.push(u.join(`
`))}),e.join(`

`+ki+`

`)}function U_(a){return G(a,function(e){var t=e.getRawData(),r=[e.name],i=[];return t.each(t.dimensions,function(){for(var n=arguments.length,o=arguments[n-1],s=t.getName(o),l=0;l<n-1;l++)i[l]=arguments[l];r.push((s?s+Sr:"")+i.join(Sr))}),r.join(`
`)}).join(`

`+ki+`

`)}function Y_(a){var e=W_(a);return{value:Yt([Z_(e.seriesGroupByCategoryAxis),U_(e.other)],function(t){return!!t.replace(/[\n\t\s]/g,"")}).join(`

`+ki+`

`),meta:e.meta}}function li(a){return a.replace(/^\s\s*/,"").replace(/\s\s*$/,"")}function $_(a){var e=a.slice(0,a.indexOf(`
`));if(e.indexOf(Sr)>=0)return!0}var mo=new RegExp("["+Sr+"]+","g");function X_(a){for(var e=a.split(/\n+/g),t=li(e.shift()).split(mo),r=[],i=G(t,function(l){return{name:l,data:[]}}),n=0;n<e.length;n++){var o=li(e[n]).split(mo);r.push(o.shift());for(var s=0;s<o.length;s++)i[s]&&(i[s].data[n]=o[s])}return{series:i,categories:r}}function K_(a){for(var e=a.split(/\n+/g),t=li(e.shift()),r=[],i=0;i<e.length;i++){var n=li(e[i]);if(n){var o=n.split(mo),s="",l=void 0,u=!1;isNaN(o[0])?(u=!0,s=o[0],o=o.slice(1),r[i]={name:s,value:[]},l=r[i].value):l=r[i]=[];for(var c=0;c<o.length;c++)l.push(+o[c]);l.length===1&&(u?r[i].value=l[0]:r[i]=l[0])}}return{name:t,data:r}}function q_(a,e){var t=a.split(new RegExp(`
*`+ki+`
*`,"g")),r={series:[]};return D(t,function(i,n){if($_(i)){var o=X_(i),s=e[n],l=s.axisDim+"Axis";s&&(r[l]=r[l]||[],r[l][s.axisIndex]={data:o.categories},r.series=r.series.concat(o.series))}else{var o=K_(i);r.series.push(o)}}),r}var j_=function(a){E(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.onclick=function(t,r){setTimeout(function(){r.dispatchAction({type:"hideTip"})});var i=r.getDom(),n=this.model;this._dom&&i.removeChild(this._dom);var o=document.createElement("div");o.style.cssText="position:absolute;top:0;bottom:0;left:0;right:0;padding:5px",o.style.backgroundColor=n.get("backgroundColor")||"#fff";var s=document.createElement("h4"),l=n.get("lang")||[];s.innerHTML=l[0]||n.get("title"),s.style.cssText="margin:10px 20px",s.style.color=n.get("textColor");var u=document.createElement("div"),c=document.createElement("textarea");u.style.cssText="overflow:auto";var h=n.get("optionToContent"),v=n.get("contentToOption"),f=Y_(t);if(st(h)){var p=h(r.getOption());tt(p)?u.innerHTML=p:Kd(p)&&u.appendChild(p)}else{c.readOnly=n.get("readOnly");var d=c.style;d.cssText="display:block;width:100%;height:100%;font-family:monospace;font-size:14px;line-height:1.6rem;resize:none;box-sizing:border-box;outline:none",d.color=n.get("textColor"),d.borderColor=n.get("textareaBorderColor"),d.backgroundColor=n.get("textareaColor"),c.value=f.value,u.appendChild(c)}var g=f.meta,y=document.createElement("div");y.style.cssText="position:absolute;bottom:5px;left:0;right:0";var m="float:right;margin-right:20px;border:none;cursor:pointer;padding:2px 5px;font-size:12px;border-radius:3px",S=document.createElement("div"),x=document.createElement("div");m+=";background-color:"+n.get("buttonColor"),m+=";color:"+n.get("buttonTextColor");var b=this;function _(){i.removeChild(o),b._dom=null}el(S,"click",_),el(x,"click",function(){if(v==null&&h!=null||v!=null&&h==null){_();return}var w;try{st(v)?w=v(u,r.getOption()):w=q_(c.value,g)}catch(A){throw _(),new Error("Data view format error "+A)}w&&r.dispatchAction({type:"changeDataView",newOption:w}),_()}),S.innerHTML=l[1],x.innerHTML=l[2],x.style.cssText=S.style.cssText=m,!n.get("readOnly")&&y.appendChild(x),y.appendChild(S),o.appendChild(s),o.appendChild(u),o.appendChild(y),u.style.height=i.clientHeight-80+"px",i.appendChild(o),this._dom=o},e.prototype.remove=function(t,r){this._dom&&r.getDom().removeChild(this._dom)},e.prototype.dispose=function(t,r){this.remove(t,r)},e.getDefaultOption=function(t){var r={show:!0,readOnly:!1,optionToContent:null,contentToOption:null,icon:"M17.5,17.3H33 M17.5,17.3H33 M45.4,29.5h-28 M11.5,2v56H51V14.8L38.4,2H11.5z M38.4,2.2v12.7H51 M45.4,41.7h-28",title:t.getLocaleModel().get(["toolbox","dataView","title"]),lang:t.getLocaleModel().get(["toolbox","dataView","lang"]),backgroundColor:"#fff",textColor:"#000",textareaColor:"#fff",textareaBorderColor:"#333",buttonColor:"#c23531",buttonTextColor:"#fff"};return r},e}(ee);function J_(a,e){return G(a,function(t,r){var i=e&&e[r];if(Nt(i)&&!U(i)){var n=Nt(t)&&!U(t);n||(t={value:t});var o=i.name!=null&&t.name==null;return t=ot(t,i),o&&delete t.name,t}else return t})}yi({type:"changeDataView",event:"dataViewChanged",update:"prepareAndUpdate"},function(a,e){var t=[];D(a.newOption.series,function(r){var i=e.getSeriesByName(r.name)[0];if(!i)t.push(F({type:"scatter"},r));else{var n=i.get("data");t.push({name:r.name,data:J_(r.data,n)})}}),e.mergeOption(ot({series:t},a.newOption))});var Yf=D,$f=Dt();function Q_(a,e){var t=Ms(a);Yf(e,function(r,i){for(var n=t.length-1;n>=0;n--){var o=t[n];if(o[i])break}if(n<0){var s=a.queryComponents({mainType:"dataZoom",subType:"select",id:i})[0];if(s){var l=s.getPercentRange();t[0][i]={dataZoomId:i,start:l[0],end:l[1]}}}}),t.push(e)}function tw(a){var e=Ms(a),t=e[e.length-1];e.length>1&&e.pop();var r={};return Yf(t,function(i,n){for(var o=e.length-1;o>=0;o--)if(i=e[o][n],i){r[n]=i;break}}),r}function ew(a){$f(a).snapshots=null}function rw(a){return Ms(a).length}function Ms(a){var e=$f(a);return e.snapshots||(e.snapshots=[{}]),e.snapshots}var aw=function(a){E(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.onclick=function(t,r){ew(t),r.dispatchAction({type:"restore",from:this.uid})},e.getDefaultOption=function(t){var r={show:!0,icon:"M3.8,33.4 M47,18.9h9.8V8.7 M56.3,20.1 C52.1,9,40.5,0.6,26.8,2.1C12.6,3.7,1.6,16.2,2.1,30.6 M13,41.1H3.1v10.2 M3.7,39.9c4.2,11.1,15.8,19.5,29.5,18 c14.2-1.6,25.2-14.1,24.7-28.5",title:t.getLocaleModel().get(["toolbox","restore","title"])};return r},e}(ee);yi({type:"restore",event:"restore",update:"prepareAndUpdate"},function(a,e){e.resetOption("recreate")});var iw=["grid","xAxis","yAxis","geo","graph","polar","radiusAxis","angleAxis","bmap"],Ps=function(){function a(e,t,r){var i=this;this._targetInfoList=[];var n=Sc(t,e);D(nw,function(o,s){(!r||!r.include||mt(r.include,s)>=0)&&o(n,i._targetInfoList)})}return a.prototype.setOutputRanges=function(e,t){return this.matchOutputRanges(e,t,function(r,i,n){if((r.coordRanges||(r.coordRanges=[])).push(i),!r.coordRange){r.coordRange=i;var o=Cn[r.brushType](0,n,i);r.__rangeOffset={offset:wc[r.brushType](o.values,r.range,[1,1]),xyMinMax:o.xyMinMax}}}),e},a.prototype.matchOutputRanges=function(e,t,r){D(e,function(i){var n=this.findTargetInfo(i,t);n&&n!==!0&&D(n.coordSyses,function(o){var s=Cn[i.brushType](1,o,i.range,!0);r(i,s.values,o,t)})},this)},a.prototype.setInputRanges=function(e,t){D(e,function(r){var i=this.findTargetInfo(r,t);if(r.range=r.range||[],i&&i!==!0){r.panelId=i.panelId;var n=Cn[r.brushType](0,i.coordSys,r.coordRange),o=r.__rangeOffset;r.range=o?wc[r.brushType](n.values,o.offset,ow(n.xyMinMax,o.xyMinMax)):n.values}},this)},a.prototype.makePanelOpts=function(e,t){return G(this._targetInfoList,function(r){var i=r.getPanelRect();return{panelId:r.panelId,defaultBrushType:t?t(r):null,clipPath:lf(i),isTargetByCursor:cf(i,e,r.coordSysModel),getLinearBrushOtherExtent:uf(i)}})},a.prototype.controlSeries=function(e,t,r){var i=this.findTargetInfo(e,r);return i===!0||i&&mt(i.coordSyses,t.coordinateSystem)>=0},a.prototype.findTargetInfo=function(e,t){for(var r=this._targetInfoList,i=Sc(t,e),n=0;n<r.length;n++){var o=r[n],s=e.panelId;if(s){if(o.panelId===s)return o}else for(var l=0;l<xc.length;l++)if(xc[l](i,o))return o}return!0},a}();function So(a){return a[0]>a[1]&&a.reverse(),a}function Sc(a,e){return Xh(a,e,{includeMainTypes:iw})}var nw={grid:function(a,e){var t=a.xAxisModels,r=a.yAxisModels,i=a.gridModels,n=et(),o={},s={};!t&&!r&&!i||(D(t,function(l){var u=l.axis.grid.model;n.set(u.id,u),o[u.id]=!0}),D(r,function(l){var u=l.axis.grid.model;n.set(u.id,u),s[u.id]=!0}),D(i,function(l){n.set(l.id,l),o[l.id]=!0,s[l.id]=!0}),n.each(function(l){var u=l.coordinateSystem,c=[];D(u.getCartesians(),function(h,v){(mt(t,h.getAxis("x").model)>=0||mt(r,h.getAxis("y").model)>=0)&&c.push(h)}),e.push({panelId:"grid--"+l.id,gridModel:l,coordSysModel:l,coordSys:c[0],coordSyses:c,getPanelRect:bc.grid,xAxisDeclared:o[l.id],yAxisDeclared:s[l.id]})}))},geo:function(a,e){D(a.geoModels,function(t){var r=t.coordinateSystem;e.push({panelId:"geo--"+t.id,geoModel:t,coordSysModel:t,coordSys:r,coordSyses:[r],getPanelRect:bc.geo})})}},xc=[function(a,e){var t=a.xAxisModel,r=a.yAxisModel,i=a.gridModel;return!i&&t&&(i=t.axis.grid.model),!i&&r&&(i=r.axis.grid.model),i&&i===e.gridModel},function(a,e){var t=a.geoModel;return t&&t===e.geoModel}],bc={grid:function(){return this.coordSys.master.getRect().clone()},geo:function(){var a=this.coordSys,e=a.getBoundingRect().clone();return e.applyTransform(vr(a)),e}},Cn={lineX:q(_c,0),lineY:q(_c,1),rect:function(a,e,t,r){var i=a?e.pointToData([t[0][0],t[1][0]],r):e.dataToPoint([t[0][0],t[1][0]],r),n=a?e.pointToData([t[0][1],t[1][1]],r):e.dataToPoint([t[0][1],t[1][1]],r),o=[So([i[0],n[0]]),So([i[1],n[1]])];return{values:o,xyMinMax:o}},polygon:function(a,e,t,r){var i=[[1/0,-1/0],[1/0,-1/0]],n=G(t,function(o){var s=a?e.pointToData(o,r):e.dataToPoint(o,r);return i[0][0]=Math.min(i[0][0],s[0]),i[1][0]=Math.min(i[1][0],s[1]),i[0][1]=Math.max(i[0][1],s[0]),i[1][1]=Math.max(i[1][1],s[1]),s});return{values:n,xyMinMax:i}}};function _c(a,e,t,r){var i=t.getAxis(["x","y"][a]),n=So(G([0,1],function(s){return e?i.coordToData(i.toLocalCoord(r[s]),!0):i.toGlobalCoord(i.dataToCoord(r[s]))})),o=[];return o[a]=n,o[1-a]=[NaN,NaN],{values:n,xyMinMax:o}}var wc={lineX:q(Ac,0),lineY:q(Ac,1),rect:function(a,e,t){return[[a[0][0]-t[0]*e[0][0],a[0][1]-t[0]*e[0][1]],[a[1][0]-t[1]*e[1][0],a[1][1]-t[1]*e[1][1]]]},polygon:function(a,e,t){return G(a,function(r,i){return[r[0]-t[0]*e[i][0],r[1]-t[1]*e[i][1]]})}};function Ac(a,e,t,r){return[e[0]-r[a]*t[0],e[1]-r[a]*t[1]]}function ow(a,e){var t=Tc(a),r=Tc(e),i=[t[0]/r[0],t[1]/r[1]];return isNaN(i[0])&&(i[0]=1),isNaN(i[1])&&(i[1]=1),i}function Tc(a){return a?[a[0][1]-a[0][0],a[1][1]-a[1][0]]:[NaN,NaN]}var xo=D,sw=jd("toolbox-dataZoom_"),lw=function(a){E(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.render=function(t,r,i,n){this._brushController||(this._brushController=new ls(i.getZr()),this._brushController.on("brush",W(this._onBrush,this)).mount()),hw(t,r,this,n,i),cw(t,r)},e.prototype.onclick=function(t,r,i){uw[i].call(this)},e.prototype.remove=function(t,r){this._brushController&&this._brushController.unmount()},e.prototype.dispose=function(t,r){this._brushController&&this._brushController.dispose()},e.prototype._onBrush=function(t){var r=t.areas;if(!t.isEnd||!r.length)return;var i={},n=this.ecModel;this._brushController.updateCovers([]);var o=new Ps(Rs(this.model),n,{include:["grid"]});o.matchOutputRanges(r,n,function(u,c,h){if(h.type==="cartesian2d"){var v=u.brushType;v==="rect"?(s("x",h,c[0]),s("y",h,c[1])):s({lineX:"x",lineY:"y"}[v],h,c)}}),Q_(n,i),this._dispatchZoomAction(i);function s(u,c,h){var v=c.getAxis(u),f=v.model,p=l(u,f,n),d=p.findRepresentativeAxisProxy(f).getMinMaxSpan();(d.minValueSpan!=null||d.maxValueSpan!=null)&&(h=Qe(0,h.slice(),v.scale.getExtent(),0,d.minValueSpan,d.maxValueSpan)),p&&(i[p.id]={dataZoomId:p.id,startValue:h[0],endValue:h[1]})}function l(u,c,h){var v;return h.eachComponent({mainType:"dataZoom",subType:"select"},function(f){var p=f.getAxisModel(u,c.componentIndex);p&&(v=f)}),v}},e.prototype._dispatchZoomAction=function(t){var r=[];xo(t,function(i,n){r.push(it(i))}),r.length&&this.api.dispatchAction({type:"dataZoom",from:this.uid,batch:r})},e.getDefaultOption=function(t){var r={show:!0,filterMode:"filter",icon:{zoom:"M0,13.5h26.9 M13.5,26.9V0 M32.1,13.5H58V58H13.5 V32.1",back:"M22,1.4L9.9,13.5l12.3,12.3 M10.3,13.5H54.9v44.6 H10.3v-26"},title:t.getLocaleModel().get(["toolbox","dataZoom","title"]),brushStyle:{borderWidth:0,color:"rgba(210,219,238,0.2)"}};return r},e}(ee),uw={zoom:function(){var a=!this._isZoomActive;this.api.dispatchAction({type:"takeGlobalCursor",key:"dataZoomSelect",dataZoomSelectActive:a})},back:function(){this._dispatchZoomAction(tw(this.ecModel))}};function Rs(a){var e={xAxisIndex:a.get("xAxisIndex",!0),yAxisIndex:a.get("yAxisIndex",!0),xAxisId:a.get("xAxisId",!0),yAxisId:a.get("yAxisId",!0)};return e.xAxisIndex==null&&e.xAxisId==null&&(e.xAxisIndex="all"),e.yAxisIndex==null&&e.yAxisId==null&&(e.yAxisIndex="all"),e}function cw(a,e){a.setIconStatus("back",rw(e)>1?"emphasis":"normal")}function hw(a,e,t,r,i){var n=t._isZoomActive;r&&r.type==="takeGlobalCursor"&&(n=r.key==="dataZoomSelect"?r.dataZoomSelectActive:!1),t._isZoomActive=n,a.setIconStatus("zoom",n?"emphasis":"normal");var o=new Ps(Rs(a),e,{include:["grid"]}),s=o.makePanelOpts(i,function(l){return l.xAxisDeclared&&!l.yAxisDeclared?"lineX":!l.xAxisDeclared&&l.yAxisDeclared?"lineY":"rect"});t._brushController.setPanels(s).enableBrush(n&&s.length?{brushType:"auto",brushStyle:a.getModel("brushStyle").getItemStyle()}:!1)}qd("dataZoom",function(a){var e=a.getComponent("toolbox",0),t=["feature","dataZoom"];if(!e||e.get(t)==null)return;var r=e.getModel(t),i=[],n=Rs(r),o=Xh(a,n);xo(o.xAxisModels,function(l){return s(l,"xAxis","xAxisIndex")}),xo(o.yAxisModels,function(l){return s(l,"yAxis","yAxisIndex")});function s(l,u,c){var h=l.componentIndex,v={type:"select",$fromToolbox:!0,filterMode:r.get("filterMode",!0)||"filter",id:sw+u+h};v[c]=h,i.push(v)}return i});function vw(a){a.registerComponentModel(N_),a.registerComponentView(O_),ur("saveAsImage",G_),ur("magicType",H_),ur("dataView",j_),ur("dataZoom",lw),ur("restore",aw),K(V_)}var fw=["rect","polygon","keep","clear"];function pw(a,e){var t=be(a?a.brush:[]);if(t.length){var r=[];D(t,function(l){var u=l.hasOwnProperty("toolbox")?l.toolbox:[];u instanceof Array&&(r=r.concat(u))});var i=a&&a.toolbox;U(i)&&(i=i[0]),i||(i={feature:{}},a.toolbox=[i]);var n=i.feature||(i.feature={}),o=n.brush||(n.brush={}),s=o.type||(o.type=[]);s.push.apply(s,r),dw(s),e&&!s.length&&s.push.apply(s,fw)}}function dw(a){var e={};D(a,function(t){e[t]=1}),a.length=0,D(e,function(t,r){a.push(r)})}var Dc=D;function Cc(a){if(a){for(var e in a)if(a.hasOwnProperty(e))return!0}}function bo(a,e,t){var r={};return Dc(e,function(n){var o=r[n]=i();Dc(a[n],function(s,l){if(Tt.isValidType(l)){var u={type:l,visual:s};t&&t(u,n),o[l]=new Tt(u),l==="opacity"&&(u=it(u),u.type="colorAlpha",o.__hidden.__alphaForOpacity=new Tt(u))}})}),r;function i(){var n=function(){};n.prototype.__hidden=n.prototype;var o=new n;return o}}function Xf(a,e,t){var r;D(t,function(i){e.hasOwnProperty(i)&&Cc(e[i])&&(r=!0)}),r&&D(t,function(i){e.hasOwnProperty(i)&&Cc(e[i])?a[i]=it(e[i]):delete a[i]})}function gw(a,e,t,r,i,n){var o={};D(a,function(h){var v=Tt.prepareVisualTypes(e[h]);o[h]=v});var s;function l(h){return Kh(t,s,h)}function u(h,v){qh(t,s,h,v)}t.each(c);function c(h,v){s=h;var f=t.getRawDataItem(s);if(!(f&&f.visualMap===!1))for(var p=r.call(i,h),d=e[p],g=o[p],y=0,m=g.length;y<m;y++){var S=g[y];d[S]&&d[S].applyVisual(h,l,u)}}}function yw(a,e,t,r){var i={};return D(a,function(n){var o=Tt.prepareVisualTypes(e[n]);i[n]=o}),{progress:function(o,s){var l;r!=null&&(l=s.getDimensionIndex(r));function u(b){return Kh(s,h,b)}function c(b,_){qh(s,h,b,_)}for(var h,v=s.getStore();(h=o.next())!=null;){var f=s.getRawDataItem(h);if(!(f&&f.visualMap===!1))for(var p=r!=null?v.get(l,h):h,d=t(p),g=e[d],y=i[d],m=0,S=y.length;m<S;m++){var x=y[m];g[x]&&g[x].applyVisual(p,u,c)}}}}}function mw(a){var e=a.brushType,t={point:function(r){return Ic[e].point(r,t,a)},rect:function(r){return Ic[e].rect(r,t,a)}};return t}var Ic={lineX:Lc(0),lineY:Lc(1),rect:{point:function(a,e,t){return a&&t.boundingRect.contain(a[0],a[1])},rect:function(a,e,t){return a&&t.boundingRect.intersect(a)}},polygon:{point:function(a,e,t){return a&&t.boundingRect.contain(a[0],a[1])&&Dr(t.range,a[0],a[1])},rect:function(a,e,t){var r=t.range;if(!a||r.length<=1)return!1;var i=a.x,n=a.y,o=a.width,s=a.height,l=r[0];if(Dr(r,i,n)||Dr(r,i+o,n)||Dr(r,i,n+s)||Dr(r,i+o,n+s)||Pt.create(a).contain(l[0],l[1])||Sa(i,n,i+o,n,r)||Sa(i,n,i,n+s,r)||Sa(i+o,n,i+o,n+s,r)||Sa(i,n+s,i+o,n+s,r))return!0}}};function Lc(a){var e=["x","y"],t=["width","height"];return{point:function(r,i,n){if(r){var o=n.range,s=r[a];return Rr(s,o)}},rect:function(r,i,n){if(r){var o=n.range,s=[r[e[a]],r[e[a]]+r[t[a]]];return s[1]<s[0]&&s.reverse(),Rr(s[0],o)||Rr(s[1],o)||Rr(o[0],s)||Rr(o[1],s)}}}}function Rr(a,e){return e[0]<=a&&a<=e[1]}var Mc=["inBrush","outOfBrush"],In="__ecBrushSelect",_o="__ecInBrushSelectEvent";function Kf(a){a.eachComponent({mainType:"brush"},function(e){var t=e.brushTargetManager=new Ps(e.option,a);t.setInputRanges(e.areas,a)})}function Sw(a,e,t){var r=[],i,n;a.eachComponent({mainType:"brush"},function(o){t&&t.type==="takeGlobalCursor"&&o.setBrushOption(t.key==="brush"?t.brushOption:{brushType:!1})}),Kf(a),a.eachComponent({mainType:"brush"},function(o,s){var l={brushId:o.id,brushIndex:s,brushName:o.name,areas:it(o.areas),selected:[]};r.push(l);var u=o.option,c=u.brushLink,h=[],v=[],f=[],p=!1;s||(i=u.throttleType,n=u.throttleDelay);var d=G(o.areas,function(b){var _=ww[b.brushType],w=ot({boundingRect:_?_(b):void 0},b);return w.selectors=mw(w),w}),g=bo(o.option,Mc,function(b){b.mappingMethod="fixed"});U(c)&&D(c,function(b){h[b]=1});function y(b){return c==="all"||!!h[b]}function m(b){return!!b.length}a.eachSeries(function(b,_){var w=f[_]=[];b.subType==="parallel"?S(b,_):x(b,_,w)});function S(b,_){var w=b.coordinateSystem;p=p||w.hasAxisBrushed(),y(_)&&w.eachActiveState(b.getData(),function(A,C){A==="active"&&(v[C]=1)})}function x(b,_,w){if(!(!b.brushSelector||_w(o,_))&&(D(d,function(C){o.brushTargetManager.controlSeries(C,b,a)&&w.push(C),p=p||m(w)}),y(_)&&m(w))){var A=b.getData();A.each(function(C){Pc(b,w,A,C)&&(v[C]=1)})}}a.eachSeries(function(b,_){var w={seriesId:b.id,seriesIndex:_,seriesName:b.name,dataIndex:[]};l.selected.push(w);var A=f[_],C=b.getData(),T=y(_)?function(I){return v[I]?(w.dataIndex.push(C.getRawIndex(I)),"inBrush"):"outOfBrush"}:function(I){return Pc(b,A,C,I)?(w.dataIndex.push(C.getRawIndex(I)),"inBrush"):"outOfBrush"};(y(_)?p:m(A))&&gw(Mc,g,C,T)})}),xw(e,i,n,r,t)}function xw(a,e,t,r,i){if(i){var n=a.getZr();if(!n[_o]){n[In]||(n[In]=bw);var o=wi(n,In,t,e);o(a,r)}}}function bw(a,e){if(!a.isDisposed()){var t=a.getZr();t[_o]=!0,a.dispatchAction({type:"brushSelect",batch:e}),t[_o]=!1}}function Pc(a,e,t,r){for(var i=0,n=e.length;i<n;i++){var o=e[i];if(a.brushSelector(r,t,o.selectors,o))return!0}}function _w(a,e){var t=a.option.seriesIndex;return t!=null&&t!=="all"&&(U(t)?mt(t,e)<0:e!==t)}var ww={rect:function(a){return Rc(a.range)},polygon:function(a){for(var e,t=a.range,r=0,i=t.length;r<i;r++){e=e||[[1/0,-1/0],[1/0,-1/0]];var n=t[r];n[0]<e[0][0]&&(e[0][0]=n[0]),n[0]>e[0][1]&&(e[0][1]=n[0]),n[1]<e[1][0]&&(e[1][0]=n[1]),n[1]>e[1][1]&&(e[1][1]=n[1])}return e&&Rc(e)}};function Rc(a){return new Pt(a[0][0],a[1][0],a[0][1]-a[0][0],a[1][1]-a[1][0])}var Aw=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r){this.ecModel=t,this.api=r,this.model,(this._brushController=new ls(r.getZr())).on("brush",W(this._onBrush,this)).mount()},e.prototype.render=function(t,r,i,n){this.model=t,this._updateController(t,r,i,n)},e.prototype.updateTransform=function(t,r,i,n){Kf(r),this._updateController(t,r,i,n)},e.prototype.updateVisual=function(t,r,i,n){this.updateTransform(t,r,i,n)},e.prototype.updateView=function(t,r,i,n){this._updateController(t,r,i,n)},e.prototype._updateController=function(t,r,i,n){(!n||n.$from!==t.id)&&this._brushController.setPanels(t.brushTargetManager.makePanelOpts(i)).enableBrush(t.brushOption).updateCovers(t.areas.slice())},e.prototype.dispose=function(){this._brushController.dispose()},e.prototype._onBrush=function(t){var r=this.model.id,i=this.model.brushTargetManager.setOutputRanges(t.areas,this.ecModel);(!t.isEnd||t.removeOnClick)&&this.api.dispatchAction({type:"brush",brushId:r,areas:it(i),$from:r}),t.isEnd&&this.api.dispatchAction({type:"brushEnd",brushId:r,areas:it(i),$from:r})},e.type="brush",e}(Ot),Tw="#ddd",Dw=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.areas=[],t.brushOption={},t}return e.prototype.optionUpdated=function(t,r){var i=this.option;!r&&Xf(i,t,["inBrush","outOfBrush"]);var n=i.inBrush=i.inBrush||{};i.outOfBrush=i.outOfBrush||{color:Tw},n.hasOwnProperty("liftZ")||(n.liftZ=5)},e.prototype.setAreas=function(t){t&&(this.areas=G(t,function(r){return Ec(this.option,r)},this))},e.prototype.setBrushOption=function(t){this.brushOption=Ec(this.option,t),this.brushType=this.brushOption.brushType},e.type="brush",e.dependencies=["geo","grid","xAxis","yAxis","parallel","series"],e.defaultOption={seriesIndex:"all",brushType:"rect",brushMode:"single",transformable:!0,brushStyle:{borderWidth:1,color:"rgba(210,219,238,0.3)",borderColor:"#D2DBEE"},throttleType:"fixRate",throttleDelay:0,removeOnClick:!0,z:1e4},e}(kt);function Ec(a,e){return ft({brushType:a.brushType,brushMode:a.brushMode,transformable:a.transformable,brushStyle:new Ut(a.brushStyle).getItemStyle(),removeOnClick:a.removeOnClick,z:a.z},e,!0)}var Cw=["rect","polygon","lineX","lineY","keep","clear"],Iw=function(a){E(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.render=function(t,r,i){var n,o,s;r.eachComponent({mainType:"brush"},function(l){n=l.brushType,o=l.brushOption.brushMode||"single",s=s||!!l.areas.length}),this._brushType=n,this._brushMode=o,D(t.get("type",!0),function(l){t.setIconStatus(l,(l==="keep"?o==="multiple":l==="clear"?s:l===n)?"emphasis":"normal")})},e.prototype.updateView=function(t,r,i){this.render(t,r,i)},e.prototype.getIcons=function(){var t=this.model,r=t.get("icon",!0),i={};return D(t.get("type",!0),function(n){r[n]&&(i[n]=r[n])}),i},e.prototype.onclick=function(t,r,i){var n=this._brushType,o=this._brushMode;i==="clear"?(r.dispatchAction({type:"axisAreaSelect",intervals:[]}),r.dispatchAction({type:"brush",command:"clear",areas:[]})):r.dispatchAction({type:"takeGlobalCursor",key:"brush",brushOption:{brushType:i==="keep"?n:n===i?!1:i,brushMode:i==="keep"?o==="multiple"?"single":"multiple":o}})},e.getDefaultOption=function(t){var r={show:!0,type:Cw.slice(),icon:{rect:"M7.3,34.7 M0.4,10V-0.2h9.8 M89.6,10V-0.2h-9.8 M0.4,60v10.2h9.8 M89.6,60v10.2h-9.8 M12.3,22.4V10.5h13.1 M33.6,10.5h7.8 M49.1,10.5h7.8 M77.5,22.4V10.5h-13 M12.3,31.1v8.2 M77.7,31.1v8.2 M12.3,47.6v11.9h13.1 M33.6,59.5h7.6 M49.1,59.5 h7.7 M77.5,47.6v11.9h-13",polygon:"M55.2,34.9c1.7,0,3.1,1.4,3.1,3.1s-1.4,3.1-3.1,3.1 s-3.1-1.4-3.1-3.1S53.5,34.9,55.2,34.9z M50.4,51c1.7,0,3.1,1.4,3.1,3.1c0,1.7-1.4,3.1-3.1,3.1c-1.7,0-3.1-1.4-3.1-3.1 C47.3,52.4,48.7,51,50.4,51z M55.6,37.1l1.5-7.8 M60.1,13.5l1.6-8.7l-7.8,4 M59,19l-1,5.3 M24,16.1l6.4,4.9l6.4-3.3 M48.5,11.6 l-5.9,3.1 M19.1,12.8L9.7,5.1l1.1,7.7 M13.4,29.8l1,7.3l6.6,1.6 M11.6,18.4l1,6.1 M32.8,41.9 M26.6,40.4 M27.3,40.2l6.1,1.6 M49.9,52.1l-5.6-7.6l-4.9-1.2",lineX:"M15.2,30 M19.7,15.6V1.9H29 M34.8,1.9H40.4 M55.3,15.6V1.9H45.9 M19.7,44.4V58.1H29 M34.8,58.1H40.4 M55.3,44.4 V58.1H45.9 M12.5,20.3l-9.4,9.6l9.6,9.8 M3.1,29.9h16.5 M62.5,20.3l9.4,9.6L62.3,39.7 M71.9,29.9H55.4",lineY:"M38.8,7.7 M52.7,12h13.2v9 M65.9,26.6V32 M52.7,46.3h13.2v-9 M24.9,12H11.8v9 M11.8,26.6V32 M24.9,46.3H11.8v-9 M48.2,5.1l-9.3-9l-9.4,9.2 M38.9-3.9V12 M48.2,53.3l-9.3,9l-9.4-9.2 M38.9,62.3V46.4",keep:"M4,10.5V1h10.3 M20.7,1h6.1 M33,1h6.1 M55.4,10.5V1H45.2 M4,17.3v6.6 M55.6,17.3v6.6 M4,30.5V40h10.3 M20.7,40 h6.1 M33,40h6.1 M55.4,30.5V40H45.2 M21,18.9h62.9v48.6H21V18.9z",clear:"M22,14.7l30.9,31 M52.9,14.7L22,45.7 M4.7,16.8V4.2h13.1 M26,4.2h7.8 M41.6,4.2h7.8 M70.3,16.8V4.2H57.2 M4.7,25.9v8.6 M70.3,25.9v8.6 M4.7,43.2v12.6h13.1 M26,55.8h7.8 M41.6,55.8h7.8 M70.3,43.2v12.6H57.2"},title:t.getLocaleModel().get(["toolbox","brush","title"])};return r},e}(ee);function Lw(a){a.registerComponentView(Aw),a.registerComponentModel(Dw),a.registerPreprocessor(pw),a.registerVisual(a.PRIORITY.VISUAL.BRUSH,Sw),a.registerAction({type:"brush",event:"brush",update:"updateVisual"},function(e,t){t.eachComponent({mainType:"brush",query:e},function(r){r.setAreas(e.areas)})}),a.registerAction({type:"brushSelect",event:"brushSelected",update:"none"},Pe),a.registerAction({type:"brushEnd",event:"brushEnd",update:"none"},Pe),ur("brush",Iw)}var kc=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.layoutMode="box",t}return e.prototype.init=function(t,r,i){this.mergeDefaultAndTheme(t,i),this._initData()},e.prototype.mergeOption=function(t){a.prototype.mergeOption.apply(this,arguments),this._initData()},e.prototype.setCurrentIndex=function(t){t==null&&(t=this.option.currentIndex);var r=this._data.count();this.option.loop?t=(t%r+r)%r:(t>=r&&(t=r-1),t<0&&(t=0)),this.option.currentIndex=t},e.prototype.getCurrentIndex=function(){return this.option.currentIndex},e.prototype.isIndexMax=function(){return this.getCurrentIndex()>=this._data.count()-1},e.prototype.setPlayState=function(t){this.option.autoPlay=!!t},e.prototype.getPlayState=function(){return!!this.option.autoPlay},e.prototype._initData=function(){var t=this.option,r=t.data||[],i=t.axisType,n=this._names=[],o;i==="category"?(o=[],D(r,function(u,c){var h=ye(Jd(u),""),v;Nt(u)?(v=it(u),v.value=c):v=c,o.push(v),n.push(h)})):o=r;var s={category:"ordinal",time:"time",value:"number"}[i]||"number",l=this._data=new Ft([{name:"value",type:s}],this);l.initData(o,n)},e.prototype.getData=function(){return this._data},e.prototype.getCategories=function(){if(this.get("axisType")==="category")return this._names.slice()},e.type="timeline",e.defaultOption={z:4,show:!0,axisType:"time",realtime:!0,left:"20%",top:null,right:"20%",bottom:0,width:null,height:40,padding:5,controlPosition:"left",autoPlay:!1,rewind:!1,loop:!0,playInterval:2e3,currentIndex:0,itemStyle:{},label:{color:"#000"},data:[]},e}(kt),qf=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="timeline.slider",e.defaultOption=Je(kc.defaultOption,{backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,orient:"horizontal",inverse:!1,tooltip:{trigger:"item"},symbol:"circle",symbolSize:12,lineStyle:{show:!0,width:2,color:"#DAE1F5"},label:{position:"auto",show:!0,interval:"auto",rotate:0,color:"#A4B1D7"},itemStyle:{color:"#A4B1D7",borderWidth:1},checkpointStyle:{symbol:"circle",symbolSize:15,color:"#316bf3",borderColor:"#fff",borderWidth:2,shadowBlur:2,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0, 0, 0, 0.3)",animation:!0,animationDuration:300,animationEasing:"quinticInOut"},controlStyle:{show:!0,showPlayBtn:!0,showPrevBtn:!0,showNextBtn:!0,itemSize:24,itemGap:12,position:"left",playIcon:"path://M31.6,53C17.5,53,6,41.5,6,27.4S17.5,1.8,31.6,1.8C45.7,1.8,57.2,13.3,57.2,27.4S45.7,53,31.6,53z M31.6,3.3 C18.4,3.3,7.5,14.1,7.5,27.4c0,13.3,10.8,24.1,24.1,24.1C44.9,51.5,55.7,40.7,55.7,27.4C55.7,14.1,44.9,3.3,31.6,3.3z M24.9,21.3 c0-2.2,1.6-3.1,3.5-2l10.5,6.1c1.899,1.1,1.899,2.9,0,4l-10.5,6.1c-1.9,1.1-3.5,0.2-3.5-2V21.3z",stopIcon:"path://M30.9,53.2C16.8,53.2,5.3,41.7,5.3,27.6S16.8,2,30.9,2C45,2,56.4,13.5,56.4,27.6S45,53.2,30.9,53.2z M30.9,3.5C17.6,3.5,6.8,14.4,6.8,27.6c0,13.3,10.8,24.1,24.101,24.1C44.2,51.7,55,40.9,55,27.6C54.9,14.4,44.1,3.5,30.9,3.5z M36.9,35.8c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H36c0.5,0,0.9,0.4,0.9,1V35.8z M27.8,35.8 c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H27c0.5,0,0.9,0.4,0.9,1L27.8,35.8L27.8,35.8z",nextIcon:"M2,18.5A1.52,1.52,0,0,1,.92,18a1.49,1.49,0,0,1,0-2.12L7.81,9.36,1,3.11A1.5,1.5,0,1,1,3,.89l8,7.34a1.48,1.48,0,0,1,.49,1.09,1.51,1.51,0,0,1-.46,1.1L3,18.08A1.5,1.5,0,0,1,2,18.5Z",prevIcon:"M10,.5A1.52,1.52,0,0,1,11.08,1a1.49,1.49,0,0,1,0,2.12L4.19,9.64,11,15.89a1.5,1.5,0,1,1-2,2.22L1,10.77A1.48,1.48,0,0,1,.5,9.68,1.51,1.51,0,0,1,1,8.58L9,.92A1.5,1.5,0,0,1,10,.5Z",prevBtnSize:18,nextBtnSize:18,color:"#A4B1D7",borderColor:"#A4B1D7",borderWidth:1},emphasis:{label:{show:!0,color:"#6f778d"},itemStyle:{color:"#316BF3"},controlStyle:{color:"#316BF3",borderColor:"#316BF3",borderWidth:2}},progress:{lineStyle:{color:"#316BF3"},itemStyle:{color:"#316BF3"},label:{color:"#6f778d"}},data:[]}),e}(kc);ue(qf,Fo.prototype);var Mw=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="timeline",e}(Ot),Pw=function(a){E(e,a);function e(t,r,i,n){var o=a.call(this,t,r,i)||this;return o.type=n||"value",o}return e.prototype.getLabelModel=function(){return this.model.getModel("label")},e.prototype.isHorizontal=function(){return this.model.get("orient")==="horizontal"},e}(de),Ln=Math.PI,Vc=Dt(),Rw=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r){this.api=r},e.prototype.render=function(t,r,i){if(this.model=t,this.api=i,this.ecModel=r,this.group.removeAll(),t.get("show",!0)){var n=this._layout(t,i),o=this._createGroup("_mainGroup"),s=this._createGroup("_labelGroup"),l=this._axis=this._createAxis(n,t);t.formatTooltip=function(u){var c=l.scale.getLabel({value:u});return Zt("nameValue",{noName:!0,value:c})},D(["AxisLine","AxisTick","Control","CurrentPointer"],function(u){this["_render"+u](n,o,l,t)},this),this._renderAxisLabel(n,s,l,t),this._position(n,t)}this._doPlayStop(),this._updateTicksStatus()},e.prototype.remove=function(){this._clearTimer(),this.group.removeAll()},e.prototype.dispose=function(){this._clearTimer()},e.prototype._layout=function(t,r){var i=t.get(["label","position"]),n=t.get("orient"),o=kw(t,r),s;i==null||i==="auto"?s=n==="horizontal"?o.y+o.height/2<r.getHeight()/2?"-":"+":o.x+o.width/2<r.getWidth()/2?"+":"-":tt(i)?s={horizontal:{top:"-",bottom:"+"},vertical:{left:"-",right:"+"}}[n][i]:s=i;var l={horizontal:"center",vertical:s>=0||s==="+"?"left":"right"},u={horizontal:s>=0||s==="+"?"top":"bottom",vertical:"middle"},c={horizontal:0,vertical:Ln/2},h=n==="vertical"?o.height:o.width,v=t.getModel("controlStyle"),f=v.get("show",!0),p=f?v.get("itemSize"):0,d=f?v.get("itemGap"):0,g=p+d,y=t.get(["label","rotate"])||0;y=y*Ln/180;var m,S,x,b=v.get("position",!0),_=f&&v.get("showPlayBtn",!0),w=f&&v.get("showPrevBtn",!0),A=f&&v.get("showNextBtn",!0),C=0,T=h;b==="left"||b==="bottom"?(_&&(m=[0,0],C+=g),w&&(S=[C,0],C+=g),A&&(x=[T-p,0],T-=g)):(_&&(m=[T-p,0],T-=g),w&&(S=[0,0],C+=g),A&&(x=[T-p,0],T-=g));var I=[C,T];return t.get("inverse")&&I.reverse(),{viewRect:o,mainLength:h,orient:n,rotation:c[n],labelRotation:y,labelPosOpt:s,labelAlign:t.get(["label","align"])||l[n],labelBaseline:t.get(["label","verticalAlign"])||t.get(["label","baseline"])||u[n],playPosition:m,prevBtnPosition:S,nextBtnPosition:x,axisExtent:I,controlSize:p,controlGap:d}},e.prototype._position=function(t,r){var i=this._mainGroup,n=this._labelGroup,o=t.viewRect;if(t.orient==="vertical"){var s=wr(),l=o.x,u=o.y+o.height;Ke(s,s,[-l,-u]),mi(s,s,-Ln/2),Ke(s,s,[l,u]),o=o.clone(),o.applyTransform(s)}var c=m(o),h=m(i.getBoundingRect()),v=m(n.getBoundingRect()),f=[i.x,i.y],p=[n.x,n.y];p[0]=f[0]=c[0][0];var d=t.labelPosOpt;if(d==null||tt(d)){var g=d==="+"?0:1;S(f,h,c,1,g),S(p,v,c,1,1-g)}else{var g=d>=0?0:1;S(f,h,c,1,g),p[1]=f[1]+d}i.setPosition(f),n.setPosition(p),i.rotation=n.rotation=t.rotation,y(i),y(n);function y(x){x.originX=c[0][0]-x.x,x.originY=c[1][0]-x.y}function m(x){return[[x.x,x.x+x.width],[x.y,x.y+x.height]]}function S(x,b,_,w,A){x[w]+=_[w][A]-b[w][A]}},e.prototype._createAxis=function(t,r){var i=r.getData(),n=r.get("axisType"),o=Ew(r,n);o.getTicks=function(){return i.mapArray(["value"],function(u){return{value:u}})};var s=i.getDataExtent("value");o.setExtent(s[0],s[1]),o.calcNiceTicks();var l=new Pw("value",o,t.axisExtent,n);return l.model=r,l},e.prototype._createGroup=function(t){var r=this[t]=new X;return this.group.add(r),r},e.prototype._renderAxisLine=function(t,r,i,n){var o=i.getExtent();if(n.get(["lineStyle","show"])){var s=new oe({shape:{x1:o[0],y1:0,x2:o[1],y2:0},style:F({lineCap:"round"},n.getModel("lineStyle").getLineStyle()),silent:!0,z2:1});r.add(s);var l=this._progressLine=new oe({shape:{x1:o[0],x2:this._currentPointer?this._currentPointer.x:o[0],y1:0,y2:0},style:ot({lineCap:"round",lineWidth:s.style.lineWidth},n.getModel(["progress","lineStyle"]).getLineStyle()),silent:!0,z2:1});r.add(l)}},e.prototype._renderAxisTick=function(t,r,i,n){var o=this,s=n.getData(),l=i.scale.getTicks();this._tickSymbols=[],D(l,function(u){var c=i.dataToCoord(u.value),h=s.getItemModel(u.value),v=h.getModel("itemStyle"),f=h.getModel(["emphasis","itemStyle"]),p=h.getModel(["progress","itemStyle"]),d={x:c,y:0,onclick:W(o._changeTimeline,o,u.value)},g=Nc(h,v,r,d);g.ensureState("emphasis").style=f.getItemStyle(),g.ensureState("progress").style=p.getItemStyle(),fr(g);var y=ht(g);h.get("tooltip")?(y.dataIndex=u.value,y.dataModel=n):y.dataIndex=y.dataModel=null,o._tickSymbols.push(g)})},e.prototype._renderAxisLabel=function(t,r,i,n){var o=this,s=i.getLabelModel();if(s.get("show")){var l=n.getData(),u=i.getViewLabels();this._tickLabels=[],D(u,function(c){var h=c.tickValue,v=l.getItemModel(h),f=v.getModel("label"),p=v.getModel(["emphasis","label"]),d=v.getModel(["progress","label"]),g=i.dataToCoord(c.tickValue),y=new ut({x:g,y:0,rotation:t.labelRotation-t.rotation,onclick:W(o._changeTimeline,o,h),silent:!1,style:yt(f,{text:c.formattedLabel,align:t.labelAlign,verticalAlign:t.labelBaseline})});y.ensureState("emphasis").style=yt(p),y.ensureState("progress").style=yt(d),r.add(y),fr(y),Vc(y).dataIndex=h,o._tickLabels.push(y)})}},e.prototype._renderControl=function(t,r,i,n){var o=t.controlSize,s=t.rotation,l=n.getModel("controlStyle").getItemStyle(),u=n.getModel(["emphasis","controlStyle"]).getItemStyle(),c=n.getPlayState(),h=n.get("inverse",!0);v(t.nextBtnPosition,"next",W(this._changeTimeline,this,h?"-":"+")),v(t.prevBtnPosition,"prev",W(this._changeTimeline,this,h?"+":"-")),v(t.playPosition,c?"stop":"play",W(this._handlePlayClick,this,!c),!0);function v(f,p,d,g){if(f){var y=$n(Mt(n.get(["controlStyle",p+"BtnSize"]),o),o),m=[0,-y/2,y,y],S=Vw(n,p+"Icon",m,{x:f[0],y:f[1],originX:o/2,originY:0,rotation:g?-s:0,rectHover:!0,style:l,onclick:d});S.ensureState("emphasis").style=u,r.add(S),fr(S)}}},e.prototype._renderCurrentPointer=function(t,r,i,n){var o=n.getData(),s=n.getCurrentIndex(),l=o.getItemModel(s).getModel("checkpointStyle"),u=this,c={onCreate:function(h){h.draggable=!0,h.drift=W(u._handlePointerDrag,u),h.ondragend=W(u._handlePointerDragend,u),zc(h,u._progressLine,s,i,n,!0)},onUpdate:function(h){zc(h,u._progressLine,s,i,n)}};this._currentPointer=Nc(l,l,this._mainGroup,{},this._currentPointer,c)},e.prototype._handlePlayClick=function(t){this._clearTimer(),this.api.dispatchAction({type:"timelinePlayChange",playState:t,from:this.uid})},e.prototype._handlePointerDrag=function(t,r,i){this._clearTimer(),this._pointerChangeTimeline([i.offsetX,i.offsetY])},e.prototype._handlePointerDragend=function(t){this._pointerChangeTimeline([t.offsetX,t.offsetY],!0)},e.prototype._pointerChangeTimeline=function(t,r){var i=this._toAxisCoord(t)[0],n=this._axis,o=re(n.getExtent().slice());i>o[1]&&(i=o[1]),i<o[0]&&(i=o[0]),this._currentPointer.x=i,this._currentPointer.markRedraw();var s=this._progressLine;s&&(s.shape.x2=i,s.dirty());var l=this._findNearestTick(i),u=this.model;(r||l!==u.getCurrentIndex()&&u.get("realtime"))&&this._changeTimeline(l)},e.prototype._doPlayStop=function(){var t=this;this._clearTimer(),this.model.getPlayState()&&(this._timer=setTimeout(function(){var r=t.model;t._changeTimeline(r.getCurrentIndex()+(r.get("rewind",!0)?-1:1))},this.model.get("playInterval")))},e.prototype._toAxisCoord=function(t){var r=this._mainGroup.getLocalTransform();return Le(t,r,!0)},e.prototype._findNearestTick=function(t){var r=this.model.getData(),i=1/0,n,o=this._axis;return r.each(["value"],function(s,l){var u=o.dataToCoord(s),c=Math.abs(u-t);c<i&&(i=c,n=l)}),n},e.prototype._clearTimer=function(){this._timer&&(clearTimeout(this._timer),this._timer=null)},e.prototype._changeTimeline=function(t){var r=this.model.getCurrentIndex();t==="+"?t=r+1:t==="-"&&(t=r-1),this.api.dispatchAction({type:"timelineChange",currentIndex:t,from:this.uid})},e.prototype._updateTicksStatus=function(){var t=this.model.getCurrentIndex(),r=this._tickSymbols,i=this._tickLabels;if(r)for(var n=0;n<r.length;n++)r&&r[n]&&r[n].toggleState("progress",n<t);if(i)for(var n=0;n<i.length;n++)i&&i[n]&&i[n].toggleState("progress",Vc(i[n]).dataIndex<=t)},e.type="timeline.slider",e}(Mw);function Ew(a,e){if(e=e||a.get("type"),e)switch(e){case"category":return new tg({ordinalMeta:a.getCategories(),extent:[1/0,-1/0]});case"time":return new Qd({locale:a.ecModel.getLocaleModel(),useUTC:a.ecModel.get("useUTC")});default:return new Bn}}function kw(a,e){return Rt(a.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()},a.get("padding"))}function Vw(a,e,t,r){var i=r.style,n=Go(a.get(["controlStyle",e]),r,new Pt(t[0],t[1],t[2],t[3]));return i&&n.setStyle(i),n}function Nc(a,e,t,r,i,n){var o=e.get("color");if(i)i.setColor(o),t.add(i),n&&n.onUpdate(i);else{var s=a.get("symbol");i=Et(s,-1,-1,2,2,o),i.setStyle("strokeNoScale",!0),t.add(i),n&&n.onCreate(i)}var l=e.getItemStyle(["color"]);i.setStyle(l),r=ft({rectHover:!0,z2:100},r,!0);var u=sa(a.get("symbolSize"));r.scaleX=u[0]/2,r.scaleY=u[1]/2;var c=ha(a.get("symbolOffset"),u);c&&(r.x=(r.x||0)+c[0],r.y=(r.y||0)+c[1]);var h=a.get("symbolRotate");return r.rotation=(h||0)*Math.PI/180||0,i.attr(r),i.updateTransform(),i}function zc(a,e,t,r,i,n){if(!a.dragging){var o=i.getModel("checkpointStyle"),s=r.dataToCoord(i.getData().get("value",t));if(n||!o.get("animation",!0))a.attr({x:s,y:0}),e&&e.attr({shape:{x2:s}});else{var l={duration:o.get("animationDuration",!0),easing:o.get("animationEasing",!0)};a.stopAnimation(null,!0),a.animateTo({x:s,y:0},l),e&&e.animateTo({shape:{x2:s}},l)}}}function Nw(a){a.registerAction({type:"timelineChange",event:"timelineChanged",update:"prepareAndUpdate"},function(e,t,r){var i=t.getComponent("timeline");return i&&e.currentIndex!=null&&(i.setCurrentIndex(e.currentIndex),!i.get("loop",!0)&&i.isIndexMax()&&i.getPlayState()&&(i.setPlayState(!1),r.dispatchAction({type:"timelinePlayChange",playState:!1,from:e.from}))),t.resetOption("timeline",{replaceMerge:i.get("replaceMerge",!0)}),ot({currentIndex:i.option.currentIndex},e)}),a.registerAction({type:"timelinePlayChange",event:"timelinePlayChanged",update:"update"},function(e,t){var r=t.getComponent("timeline");r&&e.playState!=null&&r.setPlayState(e.playState)})}function zw(a){var e=a&&a.timeline;U(e)||(e=e?[e]:[]),D(e,function(t){t&&Ow(t)})}function Ow(a){var e=a.type,t={number:"value",time:"time"};if(t[e]&&(a.axisType=t[e],delete a.type),Oc(a),We(a,"controlPosition")){var r=a.controlStyle||(a.controlStyle={});We(r,"position")||(r.position=a.controlPosition),r.position==="none"&&!We(r,"show")&&(r.show=!1,delete r.position),delete a.controlPosition}D(a.data||[],function(i){Nt(i)&&!U(i)&&(!We(i,"value")&&We(i,"name")&&(i.value=i.name),Oc(i))})}function Oc(a){var e=a.itemStyle||(a.itemStyle={}),t=e.emphasis||(e.emphasis={}),r=a.label||a.label||{},i=r.normal||(r.normal={}),n={normal:1,emphasis:1};D(r,function(o,s){!n[s]&&!We(i,s)&&(i[s]=o)}),t.label&&!We(r,"emphasis")&&(r.emphasis=t.label,delete t.label)}function We(a,e){return a.hasOwnProperty(e)}function Bw(a){a.registerComponentModel(qf),a.registerComponentView(Rw),a.registerSubTypeDefaulter("timeline",function(){return"slider"}),Nw(a),a.registerPreprocessor(zw)}function Es(a,e){if(!a)return!1;for(var t=U(a)?a:[a],r=0;r<t.length;r++)if(t[r]&&t[r][e])return!0;return!1}function Ra(a){oa(a,"label",["show"])}var Ea=Dt(),we=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.createdBySelf=!1,t}return e.prototype.init=function(t,r,i){this.mergeDefaultAndTheme(t,i),this._mergeOption(t,i,!1,!0)},e.prototype.isAnimationEnabled=function(){if($h.node)return!1;var t=this.__hostSeries;return this.getShallow("animation")&&t&&t.isAnimationEnabled()},e.prototype.mergeOption=function(t,r){this._mergeOption(t,r,!1,!1)},e.prototype._mergeOption=function(t,r,i,n){var o=this.mainType;i||r.eachSeries(function(s){var l=s.get(this.mainType,!0),u=Ea(s)[o];if(!l||!l.data){Ea(s)[o]=null;return}u?u._mergeOption(l,r,!0):(n&&Ra(l),D(l.data,function(c){c instanceof Array?(Ra(c[0]),Ra(c[1])):Ra(c)}),u=this.createMarkerModelFromSeries(l,this,r),F(u,{mainType:this.mainType,seriesIndex:s.seriesIndex,name:s.name,createdBySelf:!0}),u.__hostSeries=s),Ea(s)[o]=u},this)},e.prototype.formatTooltip=function(t,r,i){var n=this.getData(),o=this.getRawValue(t),s=n.getName(t);return Zt("section",{header:this.name,blocks:[Zt("nameValue",{name:s,value:o,noName:!s,noValue:o==null})]})},e.prototype.getData=function(){return this._data},e.prototype.setData=function(t){this._data=t},e.prototype.getDataParams=function(t,r){var i=Fo.prototype.getDataParams.call(this,t,r),n=this.__hostSeries;return n&&(i.seriesId=n.id,i.seriesName=n.name,i.seriesType=n.subType),i},e.getMarkerModelFromSeries=function(t,r){return Ea(t)[r]},e.type="marker",e.dependencies=["series","grid","polar","geo"],e}(kt);ue(we,Fo.prototype);var Gw=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.createMarkerModelFromSeries=function(t,r,i){return new e(t,r,i)},e.type="markPoint",e.defaultOption={z:5,symbol:"pin",symbolSize:50,tooltip:{trigger:"item"},label:{show:!0,position:"inside"},itemStyle:{borderWidth:2},emphasis:{label:{show:!0}}},e}(we);function wo(a){return!(isNaN(parseFloat(a.x))&&isNaN(parseFloat(a.y)))}function Fw(a){return!isNaN(parseFloat(a.x))&&!isNaN(parseFloat(a.y))}function ka(a,e,t,r,i,n){var o=[],s=Uh(e,r),l=s?e.getCalculationInfo("stackResultDimension"):r,u=ks(e,l,a),c=e.indicesOfNearest(l,u)[0];o[i]=e.get(t,c),o[n]=e.get(l,c);var h=e.get(r,c),v=eg(e.get(r,c));return v=Math.min(v,20),v>=0&&(o[n]=+o[n].toFixed(v)),[o,h]}var Mn={min:q(ka,"min"),max:q(ka,"max"),average:q(ka,"average"),median:q(ka,"median")};function ia(a,e){if(e){var t=a.getData(),r=a.coordinateSystem,i=r&&r.dimensions;if(!Fw(e)&&!U(e.coord)&&U(i)){var n=jf(e,t,r,a);if(e=it(e),e.type&&Mn[e.type]&&n.baseAxis&&n.valueAxis){var o=mt(i,n.baseAxis.dim),s=mt(i,n.valueAxis.dim),l=Mn[e.type](t,n.baseDataDim,n.valueDataDim,o,s);e.coord=l[0],e.value=l[1]}else e.coord=[e.xAxis!=null?e.xAxis:e.radiusAxis,e.yAxis!=null?e.yAxis:e.angleAxis]}if(e.coord==null||!U(i))e.coord=[];else for(var u=e.coord,c=0;c<2;c++)Mn[u[c]]&&(u[c]=ks(t,t.mapDimension(i[c]),u[c]));return e}}function jf(a,e,t,r){var i={};return a.valueIndex!=null||a.valueDim!=null?(i.valueDataDim=a.valueIndex!=null?e.getDimension(a.valueIndex):a.valueDim,i.valueAxis=t.getAxis(Hw(r,i.valueDataDim)),i.baseAxis=t.getOtherAxis(i.valueAxis),i.baseDataDim=e.mapDimension(i.baseAxis.dim)):(i.baseAxis=r.getBaseAxis(),i.valueAxis=t.getOtherAxis(i.baseAxis),i.baseDataDim=e.mapDimension(i.baseAxis.dim),i.valueDataDim=e.mapDimension(i.valueAxis.dim)),i}function Hw(a,e){var t=a.getData().getDimensionInfo(e);return t&&t.coordDim}function na(a,e){return a&&a.containData&&e.coord&&!wo(e)?a.containData(e.coord):!0}function Ww(a,e,t){return a&&a.containZone&&e.coord&&t.coord&&!wo(e)&&!wo(t)?a.containZone(e.coord,t.coord):!0}function Jf(a,e){return a?function(t,r,i,n){var o=n<2?t.coord&&t.coord[n]:t.value;return qa(o,e[n])}:function(t,r,i,n){return qa(t.value,e[n])}}function ks(a,e,t){if(t==="average"){var r=0,i=0;return a.each(e,function(n,o){isNaN(n)||(r+=n,i++)}),r/i}else return t==="median"?a.getMedian(e):a.getDataExtent(e)[t==="max"?1:0]}var Pn=Dt(),Vs=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(){this.markerGroupMap=et()},e.prototype.render=function(t,r,i){var n=this,o=this.markerGroupMap;o.each(function(s){Pn(s).keep=!1}),r.eachSeries(function(s){var l=we.getMarkerModelFromSeries(s,n.type);l&&n.renderSeries(s,l,r,i)}),o.each(function(s){!Pn(s).keep&&n.group.remove(s.group)})},e.prototype.markKeep=function(t){Pn(t).keep=!0},e.prototype.toggleBlurSeries=function(t,r){var i=this;D(t,function(n){var o=we.getMarkerModelFromSeries(n,i.type);if(o){var s=o.getData();s.eachItemGraphicEl(function(l){l&&(r?rg(l):ag(l))})}})},e.type="marker",e}(Ot);function Bc(a,e,t){var r=e.coordinateSystem;a.each(function(i){var n=a.getItemModel(i),o,s=O(n.get("x"),t.getWidth()),l=O(n.get("y"),t.getHeight());if(!isNaN(s)&&!isNaN(l))o=[s,l];else if(e.getMarkerPosition)o=e.getMarkerPosition(a.getValues(a.dimensions,i));else if(r){var u=a.get(r.dimensions[0],i),c=a.get(r.dimensions[1],i);o=r.dataToPoint([u,c])}isNaN(s)||(o[0]=s),isNaN(l)||(o[1]=l),a.setItemLayout(i,o)})}var Zw=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.updateTransform=function(t,r,i){r.eachSeries(function(n){var o=we.getMarkerModelFromSeries(n,"markPoint");o&&(Bc(o.getData(),n,i),this.markerGroupMap.get(n.id).updateLayout())},this)},e.prototype.renderSeries=function(t,r,i,n){var o=t.coordinateSystem,s=t.id,l=t.getData(),u=this.markerGroupMap,c=u.get(s)||u.set(s,new di),h=Uw(o,t,r);r.setData(h),Bc(r.getData(),t,n),h.each(function(v){var f=h.getItemModel(v),p=f.getShallow("symbol"),d=f.getShallow("symbolSize"),g=f.getShallow("symbolRotate"),y=f.getShallow("symbolOffset"),m=f.getShallow("symbolKeepAspect");if(st(p)||st(d)||st(g)||st(y)){var S=r.getRawValue(v),x=r.getDataParams(v);st(p)&&(p=p(S,x)),st(d)&&(d=d(S,x)),st(g)&&(g=g(S,x)),st(y)&&(y=y(S,x))}var b=f.getModel("itemStyle").getItemStyle(),_=Di(l,"color");b.fill||(b.fill=_),h.setItemVisual(v,{symbol:p,symbolSize:d,symbolRotate:g,symbolOffset:y,symbolKeepAspect:m,style:b})}),c.updateData(h),this.group.add(c.group),h.eachItemGraphicEl(function(v){v.traverse(function(f){ht(f).dataModel=r})}),this.markKeep(c),c.group.silent=r.get("silent")||t.get("silent")},e.type="markPoint",e}(Vs);function Uw(a,e,t){var r;a?r=G(a&&a.dimensions,function(s){var l=e.getData().getDimensionInfo(e.getData().mapDimension(s))||{};return F(F({},l),{name:s,ordinalMeta:null})}):r=[{name:"value",type:"float"}];var i=new Ft(r,t),n=G(t.get("data"),q(ia,e));a&&(n=Yt(n,q(na,a)));var o=Jf(!!a,r);return i.initData(n,null,o),i}function Yw(a){a.registerComponentModel(Gw),a.registerComponentView(Zw),a.registerPreprocessor(function(e){Es(e.series,"markPoint")&&(e.markPoint=e.markPoint||{})})}var $w=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.createMarkerModelFromSeries=function(t,r,i){return new e(t,r,i)},e.type="markLine",e.defaultOption={z:5,symbol:["circle","arrow"],symbolSize:[8,16],symbolOffset:0,precision:2,tooltip:{trigger:"item"},label:{show:!0,position:"end",distance:5},lineStyle:{type:"dashed"},emphasis:{label:{show:!0},lineStyle:{width:3}},animationEasing:"linear"},e}(we),Va=Dt(),Xw=function(a,e,t,r){var i=a.getData(),n;if(U(r))n=r;else{var o=r.type;if(o==="min"||o==="max"||o==="average"||o==="median"||r.xAxis!=null||r.yAxis!=null){var s=void 0,l=void 0;if(r.yAxis!=null||r.xAxis!=null)s=e.getAxis(r.yAxis!=null?"y":"x"),l=qt(r.yAxis,r.xAxis);else{var u=jf(r,i,e,a);s=u.valueAxis;var c=ig(i,u.valueDataDim);l=ks(i,c,o)}var h=s.dim==="x"?0:1,v=1-h,f=it(r),p={coord:[]};f.type=null,f.coord=[],f.coord[v]=-1/0,p.coord[v]=1/0;var d=t.get("precision");d>=0&&jt(l)&&(l=+l.toFixed(Math.min(d,20))),f.coord[h]=p.coord[h]=l,n=[f,p,{type:o,valueIndex:r.valueIndex,value:l}]}else n=[]}var g=[ia(a,n[0]),ia(a,n[1]),F({},n[2])];return g[2].type=g[2].type||null,ft(g[2],g[0]),ft(g[2],g[1]),g};function ui(a){return!isNaN(a)&&!isFinite(a)}function Gc(a,e,t,r){var i=1-a,n=r.dimensions[a];return ui(e[i])&&ui(t[i])&&e[a]===t[a]&&r.getAxis(n).containData(e[a])}function Kw(a,e){if(a.type==="cartesian2d"){var t=e[0].coord,r=e[1].coord;if(t&&r&&(Gc(1,t,r,a)||Gc(0,t,r,a)))return!0}return na(a,e[0])&&na(a,e[1])}function Rn(a,e,t,r,i){var n=r.coordinateSystem,o=a.getItemModel(e),s,l=O(o.get("x"),i.getWidth()),u=O(o.get("y"),i.getHeight());if(!isNaN(l)&&!isNaN(u))s=[l,u];else{if(r.getMarkerPosition)s=r.getMarkerPosition(a.getValues(a.dimensions,e));else{var c=n.dimensions,h=a.get(c[0],e),v=a.get(c[1],e);s=n.dataToPoint([h,v])}if(Ti(n,"cartesian2d")){var f=n.getAxis("x"),p=n.getAxis("y"),c=n.dimensions;ui(a.get(c[0],e))?s[0]=f.toGlobalCoord(f.getExtent()[t?0:1]):ui(a.get(c[1],e))&&(s[1]=p.toGlobalCoord(p.getExtent()[t?0:1]))}isNaN(l)||(s[0]=l),isNaN(u)||(s[1]=u)}a.setItemLayout(e,s)}var qw=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.updateTransform=function(t,r,i){r.eachSeries(function(n){var o=we.getMarkerModelFromSeries(n,"markLine");if(o){var s=o.getData(),l=Va(o).from,u=Va(o).to;l.each(function(c){Rn(l,c,!0,n,i),Rn(u,c,!1,n,i)}),s.each(function(c){s.setItemLayout(c,[l.getItemLayout(c),u.getItemLayout(c)])}),this.markerGroupMap.get(n.id).updateLayout()}},this)},e.prototype.renderSeries=function(t,r,i,n){var o=t.coordinateSystem,s=t.id,l=t.getData(),u=this.markerGroupMap,c=u.get(s)||u.set(s,new ss);this.group.add(c.group);var h=jw(o,t,r),v=h.from,f=h.to,p=h.line;Va(r).from=v,Va(r).to=f,r.setData(p);var d=r.get("symbol"),g=r.get("symbolSize"),y=r.get("symbolRotate"),m=r.get("symbolOffset");U(d)||(d=[d,d]),U(g)||(g=[g,g]),U(y)||(y=[y,y]),U(m)||(m=[m,m]),h.from.each(function(x){S(v,x,!0),S(f,x,!1)}),p.each(function(x){var b=p.getItemModel(x).getModel("lineStyle").getLineStyle();p.setItemLayout(x,[v.getItemLayout(x),f.getItemLayout(x)]),b.stroke==null&&(b.stroke=v.getItemVisual(x,"style").fill),p.setItemVisual(x,{fromSymbolKeepAspect:v.getItemVisual(x,"symbolKeepAspect"),fromSymbolOffset:v.getItemVisual(x,"symbolOffset"),fromSymbolRotate:v.getItemVisual(x,"symbolRotate"),fromSymbolSize:v.getItemVisual(x,"symbolSize"),fromSymbol:v.getItemVisual(x,"symbol"),toSymbolKeepAspect:f.getItemVisual(x,"symbolKeepAspect"),toSymbolOffset:f.getItemVisual(x,"symbolOffset"),toSymbolRotate:f.getItemVisual(x,"symbolRotate"),toSymbolSize:f.getItemVisual(x,"symbolSize"),toSymbol:f.getItemVisual(x,"symbol"),style:b})}),c.updateData(p),h.line.eachItemGraphicEl(function(x){ht(x).dataModel=r,x.traverse(function(b){ht(b).dataModel=r})});function S(x,b,_){var w=x.getItemModel(b);Rn(x,b,_,t,n);var A=w.getModel("itemStyle").getItemStyle();A.fill==null&&(A.fill=Di(l,"color")),x.setItemVisual(b,{symbolKeepAspect:w.get("symbolKeepAspect"),symbolOffset:Mt(w.get("symbolOffset",!0),m[_?0:1]),symbolRotate:Mt(w.get("symbolRotate",!0),y[_?0:1]),symbolSize:Mt(w.get("symbolSize"),g[_?0:1]),symbol:Mt(w.get("symbol",!0),d[_?0:1]),style:A})}this.markKeep(c),c.group.silent=r.get("silent")||t.get("silent")},e.type="markLine",e}(Vs);function jw(a,e,t){var r;a?r=G(a&&a.dimensions,function(u){var c=e.getData().getDimensionInfo(e.getData().mapDimension(u))||{};return F(F({},c),{name:u,ordinalMeta:null})}):r=[{name:"value",type:"float"}];var i=new Ft(r,t),n=new Ft(r,t),o=new Ft([],t),s=G(t.get("data"),q(Xw,e,a,t));a&&(s=Yt(s,q(Kw,a)));var l=Jf(!!a,r);return i.initData(G(s,function(u){return u[0]}),null,l),n.initData(G(s,function(u){return u[1]}),null,l),o.initData(G(s,function(u){return u[2]})),o.hasItemOption=!0,{from:i,to:n,line:o}}function Jw(a){a.registerComponentModel($w),a.registerComponentView(qw),a.registerPreprocessor(function(e){Es(e.series,"markLine")&&(e.markLine=e.markLine||{})})}var Qw=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.createMarkerModelFromSeries=function(t,r,i){return new e(t,r,i)},e.type="markArea",e.defaultOption={z:1,tooltip:{trigger:"item"},animation:!1,label:{show:!0,position:"top"},itemStyle:{borderWidth:0},emphasis:{label:{show:!0,position:"top"}}},e}(we),Na=Dt(),tA=function(a,e,t,r){var i=r[0],n=r[1];if(!(!i||!n)){var o=ia(a,i),s=ia(a,n),l=o.coord,u=s.coord;l[0]=qt(l[0],-1/0),l[1]=qt(l[1],-1/0),u[0]=qt(u[0],1/0),u[1]=qt(u[1],1/0);var c=Po([{},o,s]);return c.coord=[o.coord,s.coord],c.x0=o.x,c.y0=o.y,c.x1=s.x,c.y1=s.y,c}};function ci(a){return!isNaN(a)&&!isFinite(a)}function Fc(a,e,t,r){var i=1-a;return ci(e[i])&&ci(t[i])}function eA(a,e){var t=e.coord[0],r=e.coord[1],i={coord:t,x:e.x0,y:e.y0},n={coord:r,x:e.x1,y:e.y1};return Ti(a,"cartesian2d")?t&&r&&(Fc(1,t,r)||Fc(0,t,r))?!0:Ww(a,i,n):na(a,i)||na(a,n)}function Hc(a,e,t,r,i){var n=r.coordinateSystem,o=a.getItemModel(e),s,l=O(o.get(t[0]),i.getWidth()),u=O(o.get(t[1]),i.getHeight());if(!isNaN(l)&&!isNaN(u))s=[l,u];else{if(r.getMarkerPosition){var c=a.getValues(["x0","y0"],e),h=a.getValues(["x1","y1"],e),v=n.clampData(c),f=n.clampData(h),p=[];t[0]==="x0"?p[0]=v[0]>f[0]?h[0]:c[0]:p[0]=v[0]>f[0]?c[0]:h[0],t[1]==="y0"?p[1]=v[1]>f[1]?h[1]:c[1]:p[1]=v[1]>f[1]?c[1]:h[1],s=r.getMarkerPosition(p,t,!0)}else{var d=a.get(t[0],e),g=a.get(t[1],e),y=[d,g];n.clampData&&n.clampData(y,y),s=n.dataToPoint(y,!0)}if(Ti(n,"cartesian2d")){var m=n.getAxis("x"),S=n.getAxis("y"),d=a.get(t[0],e),g=a.get(t[1],e);ci(d)?s[0]=m.toGlobalCoord(m.getExtent()[t[0]==="x0"?0:1]):ci(g)&&(s[1]=S.toGlobalCoord(S.getExtent()[t[1]==="y0"?0:1]))}isNaN(l)||(s[0]=l),isNaN(u)||(s[1]=u)}return s}var Wc=[["x0","y0"],["x1","y0"],["x1","y1"],["x0","y1"]],rA=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.updateTransform=function(t,r,i){r.eachSeries(function(n){var o=we.getMarkerModelFromSeries(n,"markArea");if(o){var s=o.getData();s.each(function(l){var u=G(Wc,function(h){return Hc(s,l,h,n,i)});s.setItemLayout(l,u);var c=s.getItemGraphicEl(l);c.setShape("points",u)})}},this)},e.prototype.renderSeries=function(t,r,i,n){var o=t.coordinateSystem,s=t.id,l=t.getData(),u=this.markerGroupMap,c=u.get(s)||u.set(s,{group:new X});this.group.add(c.group),this.markKeep(c);var h=aA(o,t,r);r.setData(h),h.each(function(v){var f=G(Wc,function(A){return Hc(h,v,A,t,n)}),p=o.getAxis("x").scale,d=o.getAxis("y").scale,g=p.getExtent(),y=d.getExtent(),m=[p.parse(h.get("x0",v)),p.parse(h.get("x1",v))],S=[d.parse(h.get("y0",v)),d.parse(h.get("y1",v))];re(m),re(S);var x=!(g[0]>m[1]||g[1]<m[0]||y[0]>S[1]||y[1]<S[0]),b=!x;h.setItemLayout(v,{points:f,allClipped:b});var _=h.getItemModel(v).getModel("itemStyle").getItemStyle(),w=Di(l,"color");_.fill||(_.fill=w,tt(_.fill)&&(_.fill=$a(_.fill,.4))),_.stroke||(_.stroke=w),h.setItemVisual(v,"style",_)}),h.diff(Na(c).data).add(function(v){var f=h.getItemLayout(v);if(!f.allClipped){var p=new se({shape:{points:f.points}});h.setItemGraphicEl(v,p),c.group.add(p)}}).update(function(v,f){var p=Na(c).data.getItemGraphicEl(f),d=h.getItemLayout(v);d.allClipped?p&&c.group.remove(p):(p?dt(p,{shape:{points:d.points}},r,v):p=new se({shape:{points:d.points}}),h.setItemGraphicEl(v,p),c.group.add(p))}).remove(function(v){var f=Na(c).data.getItemGraphicEl(v);c.group.remove(f)}).execute(),h.eachItemGraphicEl(function(v,f){var p=h.getItemModel(f),d=h.getItemVisual(f,"style");v.useStyle(h.getItemVisual(f,"style")),Wt(v,zt(p),{labelFetcher:r,labelDataIndex:f,defaultText:h.getName(f)||"",inheritColor:tt(d.fill)?$a(d.fill,1):"#000"}),Ht(v,p),xt(v,null,null,p.get(["emphasis","disabled"])),ht(v).dataModel=r}),Na(c).data=h,c.group.silent=r.get("silent")||t.get("silent")},e.type="markArea",e}(Vs);function aA(a,e,t){var r,i,n=["x0","y0","x1","y1"];if(a){var o=G(a&&a.dimensions,function(u){var c=e.getData(),h=c.getDimensionInfo(c.mapDimension(u))||{};return F(F({},h),{name:u,ordinalMeta:null})});i=G(n,function(u,c){return{name:u,type:o[c%2].type}}),r=new Ft(i,t)}else i=[{name:"value",type:"float"}],r=new Ft(i,t);var s=G(t.get("data"),q(tA,e,a,t));a&&(s=Yt(s,q(eA,a)));var l=a?function(u,c,h,v){var f=u.coord[Math.floor(v/2)][v%2];return qa(f,i[v])}:function(u,c,h,v){return qa(u.value,i[v])};return r.initData(s,null,l),r.hasItemOption=!0,r}function iA(a){a.registerComponentModel(Qw),a.registerComponentView(rA),a.registerPreprocessor(function(e){Es(e.series,"markArea")&&(e.markArea=e.markArea||{})})}var nA=function(a,e){if(e==="all")return{type:"all",title:a.getLocaleModel().get(["legend","selector","all"])};if(e==="inverse")return{type:"inverse",title:a.getLocaleModel().get(["legend","selector","inverse"])}},Ao=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.layoutMode={type:"box",ignoreSize:!0},t}return e.prototype.init=function(t,r,i){this.mergeDefaultAndTheme(t,i),t.selected=t.selected||{},this._updateSelector(t)},e.prototype.mergeOption=function(t,r){a.prototype.mergeOption.call(this,t,r),this._updateSelector(t)},e.prototype._updateSelector=function(t){var r=t.selector,i=this.ecModel;r===!0&&(r=t.selector=["all","inverse"]),U(r)&&D(r,function(n,o){tt(n)&&(n={type:n}),r[o]=ft(n,nA(i,n.type))})},e.prototype.optionUpdated=function(){this._updateData(this.ecModel);var t=this._data;if(t[0]&&this.get("selectedMode")==="single"){for(var r=!1,i=0;i<t.length;i++){var n=t[i].get("name");if(this.isSelected(n)){this.select(n),r=!0;break}}!r&&this.select(t[0].get("name"))}},e.prototype._updateData=function(t){var r=[],i=[];t.eachRawSeries(function(l){var u=l.name;i.push(u);var c;if(l.legendVisualProvider){var h=l.legendVisualProvider,v=h.getAllNames();t.isSeriesFiltered(l)||(i=i.concat(v)),v.length?r=r.concat(v):c=!0}else c=!0;c&&ng(l)&&r.push(l.name)}),this._availableNames=i;var n=this.get("data")||r,o=et(),s=G(n,function(l){return(tt(l)||jt(l))&&(l={name:l}),o.get(l.name)?null:(o.set(l.name,!0),new Ut(l,this,this.ecModel))},this);this._data=Yt(s,function(l){return!!l})},e.prototype.getData=function(){return this._data},e.prototype.select=function(t){var r=this.option.selected,i=this.get("selectedMode");if(i==="single"){var n=this._data;D(n,function(o){r[o.get("name")]=!1})}r[t]=!0},e.prototype.unSelect=function(t){this.get("selectedMode")!=="single"&&(this.option.selected[t]=!1)},e.prototype.toggleSelected=function(t){var r=this.option.selected;r.hasOwnProperty(t)||(r[t]=!0),this[r[t]?"unSelect":"select"](t)},e.prototype.allSelect=function(){var t=this._data,r=this.option.selected;D(t,function(i){r[i.get("name",!0)]=!0})},e.prototype.inverseSelect=function(){var t=this._data,r=this.option.selected;D(t,function(i){var n=i.get("name",!0);r.hasOwnProperty(n)||(r[n]=!0),r[n]=!r[n]})},e.prototype.isSelected=function(t){var r=this.option.selected;return!(r.hasOwnProperty(t)&&!r[t])&&mt(this._availableNames,t)>=0},e.prototype.getOrient=function(){return this.get("orient")==="vertical"?{index:1,name:"vertical"}:{index:0,name:"horizontal"}},e.type="legend.plain",e.dependencies=["series"],e.defaultOption={z:4,show:!0,orient:"horizontal",left:"center",top:0,align:"auto",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemGap:10,itemWidth:25,itemHeight:14,symbolRotate:"inherit",symbolKeepAspect:!0,inactiveColor:"#ccc",inactiveBorderColor:"#ccc",inactiveBorderWidth:"auto",itemStyle:{color:"inherit",opacity:"inherit",borderColor:"inherit",borderWidth:"auto",borderCap:"inherit",borderJoin:"inherit",borderDashOffset:"inherit",borderMiterLimit:"inherit"},lineStyle:{width:"auto",color:"inherit",inactiveColor:"#ccc",inactiveWidth:2,opacity:"inherit",type:"inherit",cap:"inherit",join:"inherit",dashOffset:"inherit",miterLimit:"inherit"},textStyle:{color:"#333"},selectedMode:!0,selector:!1,selectorLabel:{show:!0,borderRadius:10,padding:[3,5,3,5],fontSize:12,fontFamily:"sans-serif",color:"#666",borderWidth:1,borderColor:"#666"},emphasis:{selectorLabel:{show:!0,color:"#eee",backgroundColor:"#666"}},selectorPosition:"auto",selectorItemGap:7,selectorButtonGap:10,tooltip:{show:!1}},e}(kt),nr=q,To=D,za=X,Qf=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.newlineDisabled=!1,t}return e.prototype.init=function(){this.group.add(this._contentGroup=new za),this.group.add(this._selectorGroup=new za),this._isFirstRender=!0},e.prototype.getContentGroup=function(){return this._contentGroup},e.prototype.getSelectorGroup=function(){return this._selectorGroup},e.prototype.render=function(t,r,i){var n=this._isFirstRender;if(this._isFirstRender=!1,this.resetInner(),!!t.get("show",!0)){var o=t.get("align"),s=t.get("orient");(!o||o==="auto")&&(o=t.get("left")==="right"&&s==="vertical"?"right":"left");var l=t.get("selector",!0),u=t.get("selectorPosition",!0);l&&(!u||u==="auto")&&(u=s==="horizontal"?"end":"start"),this.renderInner(o,t,r,i,l,s,u);var c=t.getBoxLayoutParams(),h={width:i.getWidth(),height:i.getHeight()},v=t.get("padding"),f=Rt(c,h,v),p=this.layoutInner(t,o,f,n,l,u),d=Rt(ot({width:p.width,height:p.height},c),h,v);this.group.x=d.x-p.x,this.group.y=d.y-p.y,this.group.markRedraw(),this.group.add(this._backgroundEl=Uf(p,t))}},e.prototype.resetInner=function(){this.getContentGroup().removeAll(),this._backgroundEl&&this.group.remove(this._backgroundEl),this.getSelectorGroup().removeAll()},e.prototype.renderInner=function(t,r,i,n,o,s,l){var u=this.getContentGroup(),c=et(),h=r.get("selectedMode"),v=[];i.eachRawSeries(function(f){!f.get("legendHoverLink")&&v.push(f.id)}),To(r.getData(),function(f,p){var d=f.get("name");if(!this.newlineDisabled&&(d===""||d===`
`)){var g=new za;g.newline=!0,u.add(g);return}var y=i.getSeriesByName(d)[0];if(!c.get(d))if(y){var m=y.getData(),S=m.getVisual("legendLineStyle")||{},x=m.getVisual("legendIcon"),b=m.getVisual("style"),_=this._createItem(y,d,p,f,r,t,S,b,x,h,n);_.on("click",nr(Zc,d,null,n,v)).on("mouseover",nr(Do,y.name,null,n,v)).on("mouseout",nr(Co,y.name,null,n,v)),i.ssr&&_.eachChild(function(w){var A=ht(w);A.seriesIndex=y.seriesIndex,A.dataIndex=p,A.ssrType="legend"}),c.set(d,!0)}else i.eachRawSeries(function(w){if(!c.get(d)&&w.legendVisualProvider){var A=w.legendVisualProvider;if(!A.containName(d))return;var C=A.indexOfName(d),T=A.getItemVisual(C,"style"),I=A.getItemVisual(C,"legendIcon"),L=Eh(T.fill);L&&L[3]===0&&(L[3]=.2,T=F(F({},T),{fill:Oa(L,"rgba")}));var M=this._createItem(w,d,p,f,r,t,{},T,I,h,n);M.on("click",nr(Zc,null,d,n,v)).on("mouseover",nr(Do,null,d,n,v)).on("mouseout",nr(Co,null,d,n,v)),i.ssr&&M.eachChild(function(R){var P=ht(R);P.seriesIndex=w.seriesIndex,P.dataIndex=p,P.ssrType="legend"}),c.set(d,!0)}},this)},this),o&&this._createSelector(o,r,n,s,l)},e.prototype._createSelector=function(t,r,i,n,o){var s=this.getSelectorGroup();To(t,function(u){var c=u.type,h=new ut({style:{x:0,y:0,align:"center",verticalAlign:"middle"},onclick:function(){i.dispatchAction({type:c==="all"?"legendAllSelect":"legendInverseSelect",legendId:r.id})}});s.add(h);var v=r.getModel("selectorLabel"),f=r.getModel(["emphasis","selectorLabel"]);Wt(h,{normal:v,emphasis:f},{defaultText:u.title}),fr(h)})},e.prototype._createItem=function(t,r,i,n,o,s,l,u,c,h,v){var f=t.visualDrawType,p=o.get("itemWidth"),d=o.get("itemHeight"),g=o.isSelected(r),y=n.get("symbolRotate"),m=n.get("symbolKeepAspect"),S=n.get("icon");c=S||c||"roundRect";var x=oA(c,n,l,u,f,g,v),b=new za,_=n.getModel("textStyle");if(st(t.getLegendIcon)&&(!S||S==="inherit"))b.add(t.getLegendIcon({itemWidth:p,itemHeight:d,icon:c,iconRotate:y,itemStyle:x.itemStyle,lineStyle:x.lineStyle,symbolKeepAspect:m}));else{var w=S==="inherit"&&t.getData().getVisual("symbol")?y==="inherit"?t.getData().getVisual("symbolRotate"):y:0;b.add(sA({itemWidth:p,itemHeight:d,icon:c,iconRotate:w,itemStyle:x.itemStyle,lineStyle:x.lineStyle,symbolKeepAspect:m}))}var A=s==="left"?p+5:-5,C=s,T=o.get("formatter"),I=r;tt(T)&&T?I=T.replace("{name}",r??""):st(T)&&(I=T(r));var L=g?_.getTextColor():n.get("inactiveColor");b.add(new ut({style:yt(_,{text:I,x:A,y:d/2,fill:L,align:C,verticalAlign:"middle"},{inheritColor:L})}));var M=new pt({shape:b.getBoundingRect(),style:{fill:"transparent"}}),R=n.getModel("tooltip");return R.get("show")&&xi({el:M,componentModel:o,itemName:r,itemTooltipOption:R.option}),b.add(M),b.eachChild(function(P){P.silent=!0}),M.silent=!h,this.getContentGroup().add(b),fr(b),b.__legendDataIndex=i,b},e.prototype.layoutInner=function(t,r,i,n,o,s){var l=this.getContentGroup(),u=this.getSelectorGroup();Ze(t.get("orient"),l,t.get("itemGap"),i.width,i.height);var c=l.getBoundingRect(),h=[-c.x,-c.y];if(u.markRedraw(),l.markRedraw(),o){Ze("horizontal",u,t.get("selectorItemGap",!0));var v=u.getBoundingRect(),f=[-v.x,-v.y],p=t.get("selectorButtonGap",!0),d=t.getOrient().index,g=d===0?"width":"height",y=d===0?"height":"width",m=d===0?"y":"x";s==="end"?f[d]+=c[g]+p:h[d]+=v[g]+p,f[1-d]+=c[y]/2-v[y]/2,u.x=f[0],u.y=f[1],l.x=h[0],l.y=h[1];var S={x:0,y:0};return S[g]=c[g]+p+v[g],S[y]=Math.max(c[y],v[y]),S[m]=Math.min(0,v[m]+f[1-d]),S}else return l.x=h[0],l.y=h[1],this.group.getBoundingRect()},e.prototype.remove=function(){this.getContentGroup().removeAll(),this._isFirstRender=!0},e.type="legend.plain",e}(Ot);function oA(a,e,t,r,i,n,o){function s(g,y){g.lineWidth==="auto"&&(g.lineWidth=y.lineWidth>0?2:0),To(g,function(m,S){g[S]==="inherit"&&(g[S]=y[S])})}var l=e.getModel("itemStyle"),u=l.getItemStyle(),c=a.lastIndexOf("empty",0)===0?"fill":"stroke",h=l.getShallow("decal");u.decal=!h||h==="inherit"?r.decal:Si(h,o),u.fill==="inherit"&&(u.fill=r[i]),u.stroke==="inherit"&&(u.stroke=r[c]),u.opacity==="inherit"&&(u.opacity=(i==="fill"?r:t).opacity),s(u,r);var v=e.getModel("lineStyle"),f=v.getLineStyle();if(s(f,t),u.fill==="auto"&&(u.fill=r.fill),u.stroke==="auto"&&(u.stroke=r.fill),f.stroke==="auto"&&(f.stroke=r.fill),!n){var p=e.get("inactiveBorderWidth"),d=u[c];u.lineWidth=p==="auto"?r.lineWidth>0&&d?2:0:u.lineWidth,u.fill=e.get("inactiveColor"),u.stroke=e.get("inactiveBorderColor"),f.stroke=v.get("inactiveColor"),f.lineWidth=v.get("inactiveWidth")}return{itemStyle:u,lineStyle:f}}function sA(a){var e=a.icon||"roundRect",t=Et(e,0,0,a.itemWidth,a.itemHeight,a.itemStyle.fill,a.symbolKeepAspect);return t.setStyle(a.itemStyle),t.rotation=(a.iconRotate||0)*Math.PI/180,t.setOrigin([a.itemWidth/2,a.itemHeight/2]),e.indexOf("empty")>-1&&(t.style.stroke=t.style.fill,t.style.fill="#fff",t.style.lineWidth=2),t}function Zc(a,e,t,r){Co(a,e,t,r),t.dispatchAction({type:"legendToggleSelect",name:a??e}),Do(a,e,t,r)}function tp(a){for(var e=a.getZr().storage.getDisplayList(),t,r=0,i=e.length;r<i&&!(t=e[r].states.emphasis);)r++;return t&&t.hoverLayer}function Do(a,e,t,r){tp(t)||t.dispatchAction({type:"highlight",seriesName:a,name:e,excludeSeriesId:r})}function Co(a,e,t,r){tp(t)||t.dispatchAction({type:"downplay",seriesName:a,name:e,excludeSeriesId:r})}function lA(a){var e=a.findComponents({mainType:"legend"});e&&e.length&&a.filterSeries(function(t){for(var r=0;r<e.length;r++)if(!e[r].isSelected(t.name))return!1;return!0})}function Er(a,e,t){var r=a==="allSelect"||a==="inverseSelect",i={},n=[];t.eachComponent({mainType:"legend",query:e},function(s){r?s[a]():s[a](e.name),Uc(s,i),n.push(s.componentIndex)});var o={};return t.eachComponent("legend",function(s){D(i,function(l,u){s[l?"select":"unSelect"](u)}),Uc(s,o)}),r?{selected:o,legendIndex:n}:{name:e.name,selected:o}}function Uc(a,e){var t=e||{};return D(a.getData(),function(r){var i=r.get("name");if(!(i===`
`||i==="")){var n=a.isSelected(i);B(t,i)?t[i]=t[i]&&n:t[i]=n}}),t}function uA(a){a.registerAction("legendToggleSelect","legendselectchanged",q(Er,"toggleSelected")),a.registerAction("legendAllSelect","legendselectall",q(Er,"allSelect")),a.registerAction("legendInverseSelect","legendinverseselect",q(Er,"inverseSelect")),a.registerAction("legendSelect","legendselected",q(Er,"select")),a.registerAction("legendUnSelect","legendunselected",q(Er,"unSelect"))}function ep(a){a.registerComponentModel(Ao),a.registerComponentView(Qf),a.registerProcessor(a.PRIORITY.PROCESSOR.SERIES_FILTER,lA),a.registerSubTypeDefaulter("legend",function(){return"plain"}),uA(a)}var cA=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.setScrollDataIndex=function(t){this.option.scrollDataIndex=t},e.prototype.init=function(t,r,i){var n=Oo(t);a.prototype.init.call(this,t,r,i),Yc(this,t,n)},e.prototype.mergeOption=function(t,r){a.prototype.mergeOption.call(this,t,r),Yc(this,this.option,t)},e.type="legend.scroll",e.defaultOption=Je(Ao.defaultOption,{scrollDataIndex:0,pageButtonItemGap:5,pageButtonGap:null,pageButtonPosition:"end",pageFormatter:"{current}/{total}",pageIcons:{horizontal:["M0,0L12,-10L12,10z","M0,0L-12,-10L-12,10z"],vertical:["M0,0L20,0L10,-20z","M0,0L20,0L10,20z"]},pageIconColor:"#2f4554",pageIconInactiveColor:"#aaa",pageIconSize:15,pageTextStyle:{color:"#333"},animationDurationUpdate:800}),e}(Ao);function Yc(a,e,t){var r=a.getOrient(),i=[1,1];i[r.index]=0,Bo(e,t,{type:"box",ignoreSize:!!i})}var $c=X,En=["width","height"],kn=["x","y"],hA=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.newlineDisabled=!0,t._currentIndex=0,t}return e.prototype.init=function(){a.prototype.init.call(this),this.group.add(this._containerGroup=new $c),this._containerGroup.add(this.getContentGroup()),this.group.add(this._controllerGroup=new $c)},e.prototype.resetInner=function(){a.prototype.resetInner.call(this),this._controllerGroup.removeAll(),this._containerGroup.removeClipPath(),this._containerGroup.__rectSize=null},e.prototype.renderInner=function(t,r,i,n,o,s,l){var u=this;a.prototype.renderInner.call(this,t,r,i,n,o,s,l);var c=this._controllerGroup,h=r.get("pageIconSize",!0),v=U(h)?h:[h,h];p("pagePrev",0);var f=r.getModel("pageTextStyle");c.add(new ut({name:"pageText",style:{text:"xx/xx",fill:f.getTextColor(),font:f.getFont(),verticalAlign:"middle",align:"center"},silent:!0})),p("pageNext",1);function p(d,g){var y=d+"DataIndex",m=Go(r.get("pageIcons",!0)[r.getOrient().name][g],{onclick:W(u._pageGo,u,y,r,n)},{x:-v[0]/2,y:-v[1]/2,width:v[0],height:v[1]});m.name=d,c.add(m)}},e.prototype.layoutInner=function(t,r,i,n,o,s){var l=this.getSelectorGroup(),u=t.getOrient().index,c=En[u],h=kn[u],v=En[1-u],f=kn[1-u];o&&Ze("horizontal",l,t.get("selectorItemGap",!0));var p=t.get("selectorButtonGap",!0),d=l.getBoundingRect(),g=[-d.x,-d.y],y=it(i);o&&(y[c]=i[c]-d[c]-p);var m=this._layoutContentAndController(t,n,y,u,c,v,f,h);if(o){if(s==="end")g[u]+=m[c]+p;else{var S=d[c]+p;g[u]-=S,m[h]-=S}m[c]+=d[c]+p,g[1-u]+=m[f]+m[v]/2-d[v]/2,m[v]=Math.max(m[v],d[v]),m[f]=Math.min(m[f],d[f]+g[1-u]),l.x=g[0],l.y=g[1],l.markRedraw()}return m},e.prototype._layoutContentAndController=function(t,r,i,n,o,s,l,u){var c=this.getContentGroup(),h=this._containerGroup,v=this._controllerGroup;Ze(t.get("orient"),c,t.get("itemGap"),n?i.width:null,n?null:i.height),Ze("horizontal",v,t.get("pageButtonItemGap",!0));var f=c.getBoundingRect(),p=v.getBoundingRect(),d=this._showController=f[o]>i[o],g=[-f.x,-f.y];r||(g[n]=c[u]);var y=[0,0],m=[-p.x,-p.y],S=Mt(t.get("pageButtonGap",!0),t.get("itemGap",!0));if(d){var x=t.get("pageButtonPosition",!0);x==="end"?m[n]+=i[o]-p[o]:y[n]+=p[o]+S}m[1-n]+=f[s]/2-p[s]/2,c.setPosition(g),h.setPosition(y),v.setPosition(m);var b={x:0,y:0};if(b[o]=d?i[o]:f[o],b[s]=Math.max(f[s],p[s]),b[l]=Math.min(0,p[l]+m[1-n]),h.__rectSize=i[o],d){var _={x:0,y:0};_[o]=Math.max(i[o]-p[o]-S,0),_[s]=b[s],h.setClipPath(new pt({shape:_})),h.__rectSize=_[o]}else v.eachChild(function(A){A.attr({invisible:!0,silent:!0})});var w=this._getPageInfo(t);return w.pageIndex!=null&&dt(c,{x:w.contentPosition[0],y:w.contentPosition[1]},d?t:null),this._updatePageInfoView(t,w),b},e.prototype._pageGo=function(t,r,i){var n=this._getPageInfo(r)[t];n!=null&&i.dispatchAction({type:"legendScroll",scrollDataIndex:n,legendId:r.id})},e.prototype._updatePageInfoView=function(t,r){var i=this._controllerGroup;D(["pagePrev","pageNext"],function(c){var h=c+"DataIndex",v=r[h]!=null,f=i.childOfName(c);f&&(f.setStyle("fill",v?t.get("pageIconColor",!0):t.get("pageIconInactiveColor",!0)),f.cursor=v?"pointer":"default")});var n=i.childOfName("pageText"),o=t.get("pageFormatter"),s=r.pageIndex,l=s!=null?s+1:0,u=r.pageCount;n&&o&&n.setStyle("text",tt(o)?o.replace("{current}",l==null?"":l+"").replace("{total}",u==null?"":u+""):o({current:l,total:u}))},e.prototype._getPageInfo=function(t){var r=t.get("scrollDataIndex",!0),i=this.getContentGroup(),n=this._containerGroup.__rectSize,o=t.getOrient().index,s=En[o],l=kn[o],u=this._findTargetItemIndex(r),c=i.children(),h=c[u],v=c.length,f=v?1:0,p={contentPosition:[i.x,i.y],pageCount:f,pageIndex:f-1,pagePrevDataIndex:null,pageNextDataIndex:null};if(!h)return p;var d=x(h);p.contentPosition[o]=-d.s;for(var g=u+1,y=d,m=d,S=null;g<=v;++g)S=x(c[g]),(!S&&m.e>y.s+n||S&&!b(S,y.s))&&(m.i>y.i?y=m:y=S,y&&(p.pageNextDataIndex==null&&(p.pageNextDataIndex=y.i),++p.pageCount)),m=S;for(var g=u-1,y=d,m=d,S=null;g>=-1;--g)S=x(c[g]),(!S||!b(m,S.s))&&y.i<m.i&&(m=y,p.pagePrevDataIndex==null&&(p.pagePrevDataIndex=y.i),++p.pageCount,++p.pageIndex),y=S;return p;function x(_){if(_){var w=_.getBoundingRect(),A=w[l]+_[l];return{s:A,e:A+w[s],i:_.__legendDataIndex}}}function b(_,w){return _.e>=w&&_.s<=w+n}},e.prototype._findTargetItemIndex=function(t){if(!this._showController)return 0;var r,i=this.getContentGroup(),n;return i.eachChild(function(o,s){var l=o.__legendDataIndex;n==null&&l!=null&&(n=s),l===t&&(r=s)}),r??n},e.type="legend.scroll",e}(Qf);function vA(a){a.registerAction("legendScroll","legendscroll",function(e,t){var r=e.scrollDataIndex;r!=null&&t.eachComponent({mainType:"legend",subType:"scroll",query:e},function(i){i.setScrollDataIndex(r)})})}function fA(a){K(ep),a.registerComponentModel(cA),a.registerComponentView(hA),vA(a)}function pA(a){K(ep),K(fA)}var dA=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="dataZoom.inside",e.defaultOption=Je(aa.defaultOption,{disabled:!1,zoomLock:!1,zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!1,preventDefaultMouseMove:!0}),e}(aa),Ns=Dt();function gA(a,e,t){Ns(a).coordSysRecordMap.each(function(r){var i=r.dataZoomInfoMap.get(e.uid);i&&(i.getRange=t)})}function yA(a,e){for(var t=Ns(a).coordSysRecordMap,r=t.keys(),i=0;i<r.length;i++){var n=r[i],o=t.get(n),s=o.dataZoomInfoMap;if(s){var l=e.uid,u=s.get(l);u&&(s.removeKey(l),s.keys().length||rp(t,o))}}}function rp(a,e){if(e){a.removeKey(e.model.uid);var t=e.controller;t&&t.dispose()}}function mA(a,e){var t={model:e,containsPoint:q(xA,e),dispatchAction:q(SA,a),dataZoomInfoMap:null,controller:null},r=t.controller=new da(a.getZr());return D(["pan","zoom","scrollMove"],function(i){r.on(i,function(n){var o=[];t.dataZoomInfoMap.each(function(s){if(n.isAvailableBehavior(s.model.option)){var l=(s.getRange||{})[i],u=l&&l(s.dzReferCoordSysInfo,t.model.mainType,t.controller,n);!s.model.get("disabled",!0)&&u&&o.push({dataZoomId:s.model.id,start:u[0],end:u[1]})}}),o.length&&t.dispatchAction(o)})}),t}function SA(a,e){a.isDisposed()||a.dispatchAction({type:"dataZoom",animation:{easing:"cubicOut",duration:100},batch:e})}function xA(a,e,t,r){return a.coordinateSystem.containPoint([t,r])}function bA(a){var e,t="type_",r={type_true:2,type_move:1,type_false:0,type_undefined:-1},i=!0;return a.each(function(n){var o=n.model,s=o.get("disabled",!0)?!1:o.get("zoomLock",!0)?"move":!0;r[t+s]>r[t+e]&&(e=s),i=i&&o.get("preventDefaultMouseMove",!0)}),{controlType:e,opt:{zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!0,preventDefaultMouseMove:!!i}}}function _A(a){a.registerProcessor(a.PRIORITY.PROCESSOR.FILTER,function(e,t){var r=Ns(t),i=r.coordSysRecordMap||(r.coordSysRecordMap=et());i.each(function(n){n.dataZoomInfoMap=null}),e.eachComponent({mainType:"dataZoom",subType:"inside"},function(n){var o=Hf(n);D(o.infoList,function(s){var l=s.model.uid,u=i.get(l)||i.set(l,mA(t,s.model)),c=u.dataZoomInfoMap||(u.dataZoomInfoMap=et());c.set(n.uid,{dzReferCoordSysInfo:s,model:n,getRange:null})})}),i.each(function(n){var o=n.controller,s,l=n.dataZoomInfoMap;if(l){var u=l.keys()[0];u!=null&&(s=l.get(u))}if(!s){rp(i,n);return}var c=bA(l);o.enable(c.controlType,c.opt),o.setPointerChecker(n.containsPoint),wi(n,"dispatchAction",s.model.get("throttle",!0),"fixRate")})})}var wA=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type="dataZoom.inside",t}return e.prototype.render=function(t,r,i){if(a.prototype.render.apply(this,arguments),t.noTarget()){this._clear();return}this.range=t.getPercentRange(),gA(i,t,{pan:W(Vn.pan,this),zoom:W(Vn.zoom,this),scrollMove:W(Vn.scrollMove,this)})},e.prototype.dispose=function(){this._clear(),a.prototype.dispose.apply(this,arguments)},e.prototype._clear=function(){yA(this.api,this.dataZoomModel),this.range=null},e.type="dataZoom.inside",e}(Is),Vn={zoom:function(a,e,t,r){var i=this.range,n=i.slice(),o=a.axisModels[0];if(o){var s=Nn[e](null,[r.originX,r.originY],o,t,a),l=(s.signal>0?s.pixelStart+s.pixelLength-s.pixel:s.pixel-s.pixelStart)/s.pixelLength*(n[1]-n[0])+n[0],u=Math.max(1/r.scale,0);n[0]=(n[0]-l)*u+l,n[1]=(n[1]-l)*u+l;var c=this.dataZoomModel.findRepresentativeAxisProxy().getMinMaxSpan();if(Qe(0,n,[0,100],0,c.minSpan,c.maxSpan),this.range=n,i[0]!==n[0]||i[1]!==n[1])return n}},pan:Xc(function(a,e,t,r,i,n){var o=Nn[r]([n.oldX,n.oldY],[n.newX,n.newY],e,i,t);return o.signal*(a[1]-a[0])*o.pixel/o.pixelLength}),scrollMove:Xc(function(a,e,t,r,i,n){var o=Nn[r]([0,0],[n.scrollDelta,n.scrollDelta],e,i,t);return o.signal*(a[1]-a[0])*n.scrollDelta})};function Xc(a){return function(e,t,r,i){var n=this.range,o=n.slice(),s=e.axisModels[0];if(s){var l=a(o,s,e,t,r,i);if(Qe(l,o,[0,100],"all"),this.range=o,n[0]!==o[0]||n[1]!==o[1])return o}}}var Nn={grid:function(a,e,t,r,i){var n=t.axis,o={},s=i.model.coordinateSystem.getRect();return a=a||[0,0],n.dim==="x"?(o.pixel=e[0]-a[0],o.pixelLength=s.width,o.pixelStart=s.x,o.signal=n.inverse?1:-1):(o.pixel=e[1]-a[1],o.pixelLength=s.height,o.pixelStart=s.y,o.signal=n.inverse?-1:1),o},polar:function(a,e,t,r,i){var n=t.axis,o={},s=i.model.coordinateSystem,l=s.getRadiusAxis().getExtent(),u=s.getAngleAxis().getExtent();return a=a?s.pointToCoord(a):[0,0],e=s.pointToCoord(e),t.mainType==="radiusAxis"?(o.pixel=e[0]-a[0],o.pixelLength=l[1]-l[0],o.pixelStart=l[0],o.signal=n.inverse?1:-1):(o.pixel=e[1]-a[1],o.pixelLength=u[1]-u[0],o.pixelStart=u[0],o.signal=n.inverse?-1:1),o},singleAxis:function(a,e,t,r,i){var n=t.axis,o=i.model.coordinateSystem.getRect(),s={};return a=a||[0,0],n.orient==="horizontal"?(s.pixel=e[0]-a[0],s.pixelLength=o.width,s.pixelStart=o.x,s.signal=n.inverse?1:-1):(s.pixel=e[1]-a[1],s.pixelLength=o.height,s.pixelStart=o.y,s.signal=n.inverse?-1:1),s}};function ap(a){Ls(a),a.registerComponentModel(dA),a.registerComponentView(wA),_A(a)}var AA=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="dataZoom.slider",e.layoutMode="box",e.defaultOption=Je(aa.defaultOption,{show:!0,right:"ph",top:"ph",width:"ph",height:"ph",left:null,bottom:null,borderColor:"#d2dbee",borderRadius:3,backgroundColor:"rgba(47,69,84,0)",dataBackground:{lineStyle:{color:"#d2dbee",width:.5},areaStyle:{color:"#d2dbee",opacity:.2}},selectedDataBackground:{lineStyle:{color:"#8fb0f7",width:.5},areaStyle:{color:"#8fb0f7",opacity:.2}},fillerColor:"rgba(135,175,274,0.2)",handleIcon:"path://M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z",handleSize:"100%",handleStyle:{color:"#fff",borderColor:"#ACB8D1"},moveHandleSize:7,moveHandleIcon:"path://M-320.9-50L-320.9-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-348-41-339-50-320.9-50z M-212.3-50L-212.3-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-239.4-41-230.4-50-212.3-50z M-103.7-50L-103.7-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-130.9-41-121.8-50-103.7-50z",moveHandleStyle:{color:"#D2DBEE",opacity:.7},showDetail:!0,showDataShadow:"auto",realtime:!0,zoomLock:!1,textStyle:{color:"#6E7079"},brushSelect:!0,brushStyle:{color:"rgba(135,175,274,0.15)"},emphasis:{handleLabel:{show:!0},handleStyle:{borderColor:"#8FB0F7"},moveHandleStyle:{color:"#8FB0F7"}}}),e}(aa),kr=pt,Kc=7,TA=1,zn=30,DA=7,Vr="horizontal",qc="vertical",CA=5,IA=["line","bar","candlestick","scatter"],LA={easing:"cubicOut",duration:100,delay:0},MA=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._displayables={},t}return e.prototype.init=function(t,r){this.api=r,this._onBrush=W(this._onBrush,this),this._onBrushEnd=W(this._onBrushEnd,this)},e.prototype.render=function(t,r,i,n){if(a.prototype.render.apply(this,arguments),wi(this,"_dispatchZoomAction",t.get("throttle"),"fixRate"),this._orient=t.getOrient(),t.get("show")===!1){this.group.removeAll();return}if(t.noTarget()){this._clear(),this.group.removeAll();return}(!n||n.type!=="dataZoom"||n.from!==this.uid)&&this._buildView(),this._updateView()},e.prototype.dispose=function(){this._clear(),a.prototype.dispose.apply(this,arguments)},e.prototype._clear=function(){Nh(this,"_dispatchZoomAction");var t=this.api.getZr();t.off("mousemove",this._onBrush),t.off("mouseup",this._onBrushEnd)},e.prototype._buildView=function(){var t=this.group;t.removeAll(),this._brushing=!1,this._displayables.brushRect=null,this._resetLocation(),this._resetInterval();var r=this._displayables.sliderGroup=new X;this._renderBackground(),this._renderHandle(),this._renderDataShadow(),t.add(r),this._positionGroup()},e.prototype._resetLocation=function(){var t=this.dataZoomModel,r=this.api,i=t.get("brushSelect"),n=i?DA:0,o=this._findCoordRect(),s={width:r.getWidth(),height:r.getHeight()},l=this._orient===Vr?{right:s.width-o.x-o.width,top:s.height-zn-Kc-n,width:o.width,height:zn}:{right:Kc,top:o.y,width:zn,height:o.height},u=Oo(t.option);D(["right","top","width","height"],function(h){u[h]==="ph"&&(u[h]=l[h])});var c=Rt(u,s);this._location={x:c.x,y:c.y},this._size=[c.width,c.height],this._orient===qc&&this._size.reverse()},e.prototype._positionGroup=function(){var t=this.group,r=this._location,i=this._orient,n=this.dataZoomModel.getFirstTargetAxisModel(),o=n&&n.get("inverse"),s=this._displayables.sliderGroup,l=(this._dataShadowInfo||{}).otherAxisInverse;s.attr(i===Vr&&!o?{scaleY:l?1:-1,scaleX:1}:i===Vr&&o?{scaleY:l?1:-1,scaleX:-1}:i===qc&&!o?{scaleY:l?-1:1,scaleX:1,rotation:Math.PI/2}:{scaleY:l?-1:1,scaleX:-1,rotation:Math.PI/2});var u=t.getBoundingRect([s]);t.x=r.x-u.x,t.y=r.y-u.y,t.markRedraw()},e.prototype._getViewExtent=function(){return[0,this._size[0]]},e.prototype._renderBackground=function(){var t=this.dataZoomModel,r=this._size,i=this._displayables.sliderGroup,n=t.get("brushSelect");i.add(new kr({silent:!0,shape:{x:0,y:0,width:r[0],height:r[1]},style:{fill:t.get("backgroundColor")},z2:-40}));var o=new kr({shape:{x:0,y:0,width:r[0],height:r[1]},style:{fill:"transparent"},z2:0,onclick:W(this._onClickPanel,this)}),s=this.api.getZr();n?(o.on("mousedown",this._onBrushStart,this),o.cursor="crosshair",s.on("mousemove",this._onBrush),s.on("mouseup",this._onBrushEnd)):(s.off("mousemove",this._onBrush),s.off("mouseup",this._onBrushEnd)),i.add(o)},e.prototype._renderDataShadow=function(){var t=this._dataShadowInfo=this._prepareDataShadowInfo();if(this._displayables.dataShadowSegs=[],!t)return;var r=this._size,i=this._shadowSize||[],n=t.series,o=n.getRawData(),s=n.getShadowDim&&n.getShadowDim(),l=s&&o.getDimensionInfo(s)?n.getShadowDim():t.otherDim;if(l==null)return;var u=this._shadowPolygonPts,c=this._shadowPolylinePts;if(o!==this._shadowData||l!==this._shadowDim||r[0]!==i[0]||r[1]!==i[1]){var h=o.getDataExtent(l),v=(h[1]-h[0])*.3;h=[h[0]-v,h[1]+v];var f=[0,r[1]],p=[0,r[0]],d=[[r[0],0],[0,0]],g=[],y=p[1]/(o.count()-1),m=0,S=Math.round(o.count()/r[0]),x;o.each([l],function(C,T){if(S>0&&T%S){m+=y;return}var I=C==null||isNaN(C)||C==="",L=I?0:at(C,h,f,!0);I&&!x&&T?(d.push([d[d.length-1][0],0]),g.push([g[g.length-1][0],0])):!I&&x&&(d.push([m,0]),g.push([m,0])),d.push([m,L]),g.push([m,L]),m+=y,x=I}),u=this._shadowPolygonPts=d,c=this._shadowPolylinePts=g}this._shadowData=o,this._shadowDim=l,this._shadowSize=[r[0],r[1]];var b=this.dataZoomModel;function _(C){var T=b.getModel(C?"selectedDataBackground":"dataBackground"),I=new X,L=new se({shape:{points:u},segmentIgnoreThreshold:1,style:T.getModel("areaStyle").getAreaStyle(),silent:!0,z2:-20}),M=new le({shape:{points:c},segmentIgnoreThreshold:1,style:T.getModel("lineStyle").getLineStyle(),silent:!0,z2:-19});return I.add(L),I.add(M),I}for(var w=0;w<3;w++){var A=_(w===1);this._displayables.sliderGroup.add(A),this._displayables.dataShadowSegs.push(A)}},e.prototype._prepareDataShadowInfo=function(){var t=this.dataZoomModel,r=t.get("showDataShadow");if(r!==!1){var i,n=this.ecModel;return t.eachTargetAxis(function(o,s){var l=t.getAxisProxy(o,s).getTargetSeriesModels();D(l,function(u){if(!i&&!(r!==!0&&mt(IA,u.get("type"))<0)){var c=n.getComponent(Ce(o),s).axis,h=PA(o),v,f=u.coordinateSystem;h!=null&&f.getOtherAxis&&(v=f.getOtherAxis(c).inverse),h=u.getData().mapDimension(h),i={thisAxis:c,series:u,thisDim:o,otherDim:h,otherAxisInverse:v}}},this)},this),i}},e.prototype._renderHandle=function(){var t=this.group,r=this._displayables,i=r.handles=[null,null],n=r.handleLabels=[null,null],o=this._displayables.sliderGroup,s=this._size,l=this.dataZoomModel,u=this.api,c=l.get("borderRadius")||0,h=l.get("brushSelect"),v=r.filler=new kr({silent:h,style:{fill:l.get("fillerColor")},textConfig:{position:"inside"}});o.add(v),o.add(new kr({silent:!0,subPixelOptimize:!0,shape:{x:0,y:0,width:s[0],height:s[1],r:c},style:{stroke:l.get("dataBackgroundColor")||l.get("borderColor"),lineWidth:TA,fill:"rgba(0,0,0,0)"}})),D([0,1],function(S){var x=l.get("handleIcon");!og[x]&&x.indexOf("path://")<0&&x.indexOf("image://")<0&&(x="path://"+x);var b=Et(x,-1,0,2,2,null,!0);b.attr({cursor:jc(this._orient),draggable:!0,drift:W(this._onDragMove,this,S),ondragend:W(this._onDragEnd,this),onmouseover:W(this._showDataInfo,this,!0),onmouseout:W(this._showDataInfo,this,!1),z2:5});var _=b.getBoundingRect(),w=l.get("handleSize");this._handleHeight=O(w,this._size[1]),this._handleWidth=_.width/_.height*this._handleHeight,b.setStyle(l.getModel("handleStyle").getItemStyle()),b.style.strokeNoScale=!0,b.rectHover=!0,b.ensureState("emphasis").style=l.getModel(["emphasis","handleStyle"]).getItemStyle(),fr(b);var A=l.get("handleColor");A!=null&&(b.style.fill=A),o.add(i[S]=b);var C=l.getModel("textStyle"),T=l.get("handleLabel")||{},I=T.show||!1;t.add(n[S]=new ut({silent:!0,invisible:!I,style:yt(C,{x:0,y:0,text:"",verticalAlign:"middle",align:"center",fill:C.getTextColor(),font:C.getFont()}),z2:10}))},this);var f=v;if(h){var p=O(l.get("moveHandleSize"),s[1]),d=r.moveHandle=new pt({style:l.getModel("moveHandleStyle").getItemStyle(),silent:!0,shape:{r:[0,0,2,2],y:s[1]-.5,height:p}}),g=p*.8,y=r.moveHandleIcon=Et(l.get("moveHandleIcon"),-g/2,-g/2,g,g,"#fff",!0);y.silent=!0,y.y=s[1]+p/2-.5,d.ensureState("emphasis").style=l.getModel(["emphasis","moveHandleStyle"]).getItemStyle();var m=Math.min(s[1]/2,Math.max(p,10));f=r.moveZone=new pt({invisible:!0,shape:{y:s[1]-m,height:p+m}}),f.on("mouseover",function(){u.enterEmphasis(d)}).on("mouseout",function(){u.leaveEmphasis(d)}),o.add(d),o.add(y),o.add(f)}f.attr({draggable:!0,cursor:jc(this._orient),drift:W(this._onDragMove,this,"all"),ondragstart:W(this._showDataInfo,this,!0),ondragend:W(this._onDragEnd,this),onmouseover:W(this._showDataInfo,this,!0),onmouseout:W(this._showDataInfo,this,!1)})},e.prototype._resetInterval=function(){var t=this._range=this.dataZoomModel.getPercentRange(),r=this._getViewExtent();this._handleEnds=[at(t[0],[0,100],r,!0),at(t[1],[0,100],r,!0)]},e.prototype._updateInterval=function(t,r){var i=this.dataZoomModel,n=this._handleEnds,o=this._getViewExtent(),s=i.findRepresentativeAxisProxy().getMinMaxSpan(),l=[0,100];Qe(r,n,o,i.get("zoomLock")?"all":t,s.minSpan!=null?at(s.minSpan,l,o,!0):null,s.maxSpan!=null?at(s.maxSpan,l,o,!0):null);var u=this._range,c=this._range=re([at(n[0],o,l,!0),at(n[1],o,l,!0)]);return!u||u[0]!==c[0]||u[1]!==c[1]},e.prototype._updateView=function(t){var r=this._displayables,i=this._handleEnds,n=re(i.slice()),o=this._size;D([0,1],function(f){var p=r.handles[f],d=this._handleHeight;p.attr({scaleX:d/2,scaleY:d/2,x:i[f]+(f?-1:1),y:o[1]/2-d/2})},this),r.filler.setShape({x:n[0],y:0,width:n[1]-n[0],height:o[1]});var s={x:n[0],width:n[1]-n[0]};r.moveHandle&&(r.moveHandle.setShape(s),r.moveZone.setShape(s),r.moveZone.getBoundingRect(),r.moveHandleIcon&&r.moveHandleIcon.attr("x",s.x+s.width/2));for(var l=r.dataShadowSegs,u=[0,n[0],n[1],o[0]],c=0;c<l.length;c++){var h=l[c],v=h.getClipPath();v||(v=new pt,h.setClipPath(v)),v.setShape({x:u[c],y:0,width:u[c+1]-u[c],height:o[1]})}this._updateDataInfo(t)},e.prototype._updateDataInfo=function(t){var r=this.dataZoomModel,i=this._displayables,n=i.handleLabels,o=this._orient,s=["",""];if(r.get("showDetail")){var l=r.findRepresentativeAxisProxy();if(l){var u=l.getAxisModel().axis,c=this._range,h=t?l.calculateDataWindow({start:c[0],end:c[1]}).valueWindow:l.getDataValueWindow();s=[this._formatLabel(h[0],u),this._formatLabel(h[1],u)]}}var v=re(this._handleEnds.slice());f.call(this,0),f.call(this,1);function f(p){var d=vr(i.handles[p].parent,this.group),g=ko(p===0?"right":"left",d),y=this._handleWidth/2+CA,m=Le([v[p]+(p===0?-y:y),this._size[1]/2],d);n[p].setStyle({x:m[0],y:m[1],verticalAlign:o===Vr?"middle":g,align:o===Vr?g:"center",text:s[p]})}},e.prototype._formatLabel=function(t,r){var i=this.dataZoomModel,n=i.get("labelFormatter"),o=i.get("labelPrecision");(o==null||o==="auto")&&(o=r.getPixelPrecision());var s=t==null||isNaN(t)?"":r.type==="category"||r.type==="time"?r.scale.getLabel({value:Math.round(t)}):t.toFixed(Math.min(o,20));return st(n)?n(t,s):tt(n)?n.replace("{value}",s):s},e.prototype._showDataInfo=function(t){var r=this.dataZoomModel.get("handleLabel")||{},i=r.show||!1,n=this.dataZoomModel.getModel(["emphasis","handleLabel"]),o=n.get("show")||!1,s=t||this._dragging?o:i,l=this._displayables,u=l.handleLabels;u[0].attr("invisible",!s),u[1].attr("invisible",!s),l.moveHandle&&this.api[s?"enterEmphasis":"leaveEmphasis"](l.moveHandle,1)},e.prototype._onDragMove=function(t,r,i,n){this._dragging=!0,Xe(n.event);var o=this._displayables.sliderGroup.getLocalTransform(),s=Le([r,i],o,!0),l=this._updateInterval(t,s[0]),u=this.dataZoomModel.get("realtime");this._updateView(!u),l&&u&&this._dispatchZoomAction(!0)},e.prototype._onDragEnd=function(){this._dragging=!1,this._showDataInfo(!1);var t=this.dataZoomModel.get("realtime");!t&&this._dispatchZoomAction(!1)},e.prototype._onClickPanel=function(t){var r=this._size,i=this._displayables.sliderGroup.transformCoordToLocal(t.offsetX,t.offsetY);if(!(i[0]<0||i[0]>r[0]||i[1]<0||i[1]>r[1])){var n=this._handleEnds,o=(n[0]+n[1])/2,s=this._updateInterval("all",i[0]-o);this._updateView(),s&&this._dispatchZoomAction(!1)}},e.prototype._onBrushStart=function(t){var r=t.offsetX,i=t.offsetY;this._brushStart=new Za(r,i),this._brushing=!0,this._brushStartTime=+new Date},e.prototype._onBrushEnd=function(t){if(this._brushing){var r=this._displayables.brushRect;if(this._brushing=!1,!!r){r.attr("ignore",!0);var i=r.shape,n=+new Date;if(!(n-this._brushStartTime<200&&Math.abs(i.width)<5)){var o=this._getViewExtent(),s=[0,100];this._range=re([at(i.x,o,s,!0),at(i.x+i.width,o,s,!0)]),this._handleEnds=[i.x,i.x+i.width],this._updateView(),this._dispatchZoomAction(!1)}}}},e.prototype._onBrush=function(t){this._brushing&&(Xe(t.event),this._updateBrushRect(t.offsetX,t.offsetY))},e.prototype._updateBrushRect=function(t,r){var i=this._displayables,n=this.dataZoomModel,o=i.brushRect;o||(o=i.brushRect=new kr({silent:!0,style:n.getModel("brushStyle").getItemStyle()}),i.sliderGroup.add(o)),o.attr("ignore",!1);var s=this._brushStart,l=this._displayables.sliderGroup,u=l.transformCoordToLocal(t,r),c=l.transformCoordToLocal(s.x,s.y),h=this._size;u[0]=Math.max(Math.min(h[0],u[0]),0),o.setShape({x:c[0],y:0,width:u[0]-c[0],height:h[1]})},e.prototype._dispatchZoomAction=function(t){var r=this._range;this.api.dispatchAction({type:"dataZoom",from:this.uid,dataZoomId:this.dataZoomModel.id,animation:t?LA:null,start:r[0],end:r[1]})},e.prototype._findCoordRect=function(){var t,r=Hf(this.dataZoomModel).infoList;if(!t&&r.length){var i=r[0].model.coordinateSystem;t=i.getRect&&i.getRect()}if(!t){var n=this.api.getWidth(),o=this.api.getHeight();t={x:n*.2,y:o*.2,width:n*.6,height:o*.6}}return t},e.type="dataZoom.slider",e}(Is);function PA(a){var e={x:"y",y:"x",radius:"angle",angle:"radius"};return e[a]}function jc(a){return a==="vertical"?"ns-resize":"ew-resize"}function ip(a){a.registerComponentModel(AA),a.registerComponentView(MA),Ls(a)}function RA(a){K(ap),K(ip)}var np={get:function(a,e,t){var r=it((EA[a]||{})[e]);return t&&U(r)?r[r.length-1]:r}},EA={color:{active:["#006edd","#e0ffff"],inactive:["rgba(0,0,0,0)"]},colorHue:{active:[0,360],inactive:[0,0]},colorSaturation:{active:[.3,1],inactive:[0,0]},colorLightness:{active:[.9,.5],inactive:[0,0]},colorAlpha:{active:[.3,1],inactive:[0,0]},opacity:{active:[.3,1],inactive:[0,0]},symbol:{active:["circle","roundRect","diamond"],inactive:["none"]},symbolSize:{active:[10,50],inactive:[0,0]}},Jc=Tt.mapVisual,kA=Tt.eachVisual,VA=U,Qc=D,NA=re,zA=at,hi=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.stateList=["inRange","outOfRange"],t.replacableOptionKeys=["inRange","outOfRange","target","controller","color"],t.layoutMode={type:"box",ignoreSize:!0},t.dataBound=[-1/0,1/0],t.targetVisuals={},t.controllerVisuals={},t}return e.prototype.init=function(t,r,i){this.mergeDefaultAndTheme(t,i)},e.prototype.optionUpdated=function(t,r){var i=this.option;!r&&Xf(i,t,this.replacableOptionKeys),this.textStyleModel=this.getModel("textStyle"),this.resetItemSize(),this.completeVisualOption()},e.prototype.resetVisual=function(t){var r=this.stateList;t=W(t,this),this.controllerVisuals=bo(this.option.controller,r,t),this.targetVisuals=bo(this.option.target,r,t)},e.prototype.getItemSymbol=function(){return null},e.prototype.getTargetSeriesIndices=function(){var t=this.option.seriesIndex,r=[];return t==null||t==="all"?this.ecModel.eachSeries(function(i,n){r.push(n)}):r=be(t),r},e.prototype.eachTargetSeries=function(t,r){D(this.getTargetSeriesIndices(),function(i){var n=this.ecModel.getSeriesByIndex(i);n&&t.call(r,n)},this)},e.prototype.isTargetSeries=function(t){var r=!1;return this.eachTargetSeries(function(i){i===t&&(r=!0)}),r},e.prototype.formatValueText=function(t,r,i){var n=this.option,o=n.precision,s=this.dataBound,l=n.formatter,u;i=i||["<",">"],U(t)&&(t=t.slice(),u=!0);var c=r?t:u?[h(t[0]),h(t[1])]:h(t);if(tt(l))return l.replace("{value}",u?c[0]:c).replace("{value2}",u?c[1]:c);if(st(l))return u?l(t[0],t[1]):l(t);if(u)return t[0]===s[0]?i[0]+" "+c[1]:t[1]===s[1]?i[1]+" "+c[0]:c[0]+" - "+c[1];return c;function h(v){return v===s[0]?"min":v===s[1]?"max":(+v).toFixed(Math.min(o,20))}},e.prototype.resetExtent=function(){var t=this.option,r=NA([t.min,t.max]);this._dataExtent=r},e.prototype.getDataDimensionIndex=function(t){var r=this.option.dimension;if(r!=null)return t.getDimensionIndex(r);for(var i=t.dimensions,n=i.length-1;n>=0;n--){var o=i[n],s=t.getDimensionInfo(o);if(!s.isCalculationCoord)return s.storeDimIndex}},e.prototype.getExtent=function(){return this._dataExtent.slice()},e.prototype.completeVisualOption=function(){var t=this.ecModel,r=this.option,i={inRange:r.inRange,outOfRange:r.outOfRange},n=r.target||(r.target={}),o=r.controller||(r.controller={});ft(n,i),ft(o,i);var s=this.isCategory();l.call(this,n),l.call(this,o),u.call(this,n,"inRange","outOfRange"),c.call(this,o);function l(h){VA(r.color)&&!h.inRange&&(h.inRange={color:r.color.slice().reverse()}),h.inRange=h.inRange||{color:t.get("gradientColor")}}function u(h,v,f){var p=h[v],d=h[f];p&&!d&&(d=h[f]={},Qc(p,function(g,y){if(Tt.isValidType(y)){var m=np.get(y,"inactive",s);m!=null&&(d[y]=m,y==="color"&&!d.hasOwnProperty("opacity")&&!d.hasOwnProperty("colorAlpha")&&(d.opacity=[0,0]))}}))}function c(h){var v=(h.inRange||{}).symbol||(h.outOfRange||{}).symbol,f=(h.inRange||{}).symbolSize||(h.outOfRange||{}).symbolSize,p=this.get("inactiveColor"),d=this.getItemSymbol(),g=d||"roundRect";Qc(this.stateList,function(y){var m=this.itemSize,S=h[y];S||(S=h[y]={color:s?p:[p]}),S.symbol==null&&(S.symbol=v&&it(v)||(s?g:[g])),S.symbolSize==null&&(S.symbolSize=f&&it(f)||(s?m[0]:[m[0],m[0]])),S.symbol=Jc(S.symbol,function(_){return _==="none"?g:_});var x=S.symbolSize;if(x!=null){var b=-1/0;kA(x,function(_){_>b&&(b=_)}),S.symbolSize=Jc(x,function(_){return zA(_,[0,b],[0,m[0]],!0)})}},this)}},e.prototype.resetItemSize=function(){this.itemSize=[parseFloat(this.get("itemWidth")),parseFloat(this.get("itemHeight"))]},e.prototype.isCategory=function(){return!!this.option.categories},e.prototype.setSelected=function(t){},e.prototype.getSelected=function(){return null},e.prototype.getValueState=function(t){return null},e.prototype.getVisualMeta=function(t){return null},e.type="visualMap",e.dependencies=["series"],e.defaultOption={show:!0,z:4,seriesIndex:"all",min:0,max:200,left:0,right:null,top:null,bottom:0,itemWidth:null,itemHeight:null,inverse:!1,orient:"vertical",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",contentColor:"#5793f3",inactiveColor:"#aaa",borderWidth:0,padding:5,textGap:10,precision:0,textStyle:{color:"#333"}},e}(kt),th=[20,140],OA=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.optionUpdated=function(t,r){a.prototype.optionUpdated.apply(this,arguments),this.resetExtent(),this.resetVisual(function(i){i.mappingMethod="linear",i.dataExtent=this.getExtent()}),this._resetRange()},e.prototype.resetItemSize=function(){a.prototype.resetItemSize.apply(this,arguments);var t=this.itemSize;(t[0]==null||isNaN(t[0]))&&(t[0]=th[0]),(t[1]==null||isNaN(t[1]))&&(t[1]=th[1])},e.prototype._resetRange=function(){var t=this.getExtent(),r=this.option.range;!r||r.auto?(t.auto=1,this.option.range=t):U(r)&&(r[0]>r[1]&&r.reverse(),r[0]=Math.max(r[0],t[0]),r[1]=Math.min(r[1],t[1]))},e.prototype.completeVisualOption=function(){a.prototype.completeVisualOption.apply(this,arguments),D(this.stateList,function(t){var r=this.option.controller[t].symbolSize;r&&r[0]!==r[1]&&(r[0]=r[1]/3)},this)},e.prototype.setSelected=function(t){this.option.range=t.slice(),this._resetRange()},e.prototype.getSelected=function(){var t=this.getExtent(),r=re((this.get("range")||[]).slice());return r[0]>t[1]&&(r[0]=t[1]),r[1]>t[1]&&(r[1]=t[1]),r[0]<t[0]&&(r[0]=t[0]),r[1]<t[0]&&(r[1]=t[0]),r},e.prototype.getValueState=function(t){var r=this.option.range,i=this.getExtent();return(r[0]<=i[0]||r[0]<=t)&&(r[1]>=i[1]||t<=r[1])?"inRange":"outOfRange"},e.prototype.findTargetDataIndices=function(t){var r=[];return this.eachTargetSeries(function(i){var n=[],o=i.getData();o.each(this.getDataDimensionIndex(o),function(s,l){t[0]<=s&&s<=t[1]&&n.push(l)},this),r.push({seriesId:i.id,dataIndex:n})},this),r},e.prototype.getVisualMeta=function(t){var r=eh(this,"outOfRange",this.getExtent()),i=eh(this,"inRange",this.option.range.slice()),n=[];function o(f,p){n.push({value:f,color:t(f,p)})}for(var s=0,l=0,u=i.length,c=r.length;l<c&&(!i.length||r[l]<=i[0]);l++)r[l]<i[s]&&o(r[l],"outOfRange");for(var h=1;s<u;s++,h=0)h&&n.length&&o(i[s],"outOfRange"),o(i[s],"inRange");for(var h=1;l<c;l++)(!i.length||i[i.length-1]<r[l])&&(h&&(n.length&&o(n[n.length-1].value,"outOfRange"),h=0),o(r[l],"outOfRange"));var v=n.length;return{stops:n,outerColors:[v?n[0].color:"transparent",v?n[v-1].color:"transparent"]}},e.type="visualMap.continuous",e.defaultOption=Je(hi.defaultOption,{align:"auto",calculable:!1,hoverLink:!0,realtime:!0,handleIcon:"path://M-11.39,9.77h0a3.5,3.5,0,0,1-3.5,3.5h-22a3.5,3.5,0,0,1-3.5-3.5h0a3.5,3.5,0,0,1,3.5-3.5h22A3.5,3.5,0,0,1-11.39,9.77Z",handleSize:"120%",handleStyle:{borderColor:"#fff",borderWidth:1},indicatorIcon:"circle",indicatorSize:"50%",indicatorStyle:{borderColor:"#fff",borderWidth:2,shadowBlur:2,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0,0,0,0.2)"}}),e}(hi);function eh(a,e,t){if(t[0]===t[1])return t.slice();for(var r=200,i=(t[1]-t[0])/r,n=t[0],o=[],s=0;s<=r&&n<t[1];s++)o.push(n),n+=i;return o.push(t[1]),o}var op=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.autoPositionValues={left:1,right:1,top:1,bottom:1},t}return e.prototype.init=function(t,r){this.ecModel=t,this.api=r},e.prototype.render=function(t,r,i,n){if(this.visualMapModel=t,t.get("show")===!1){this.group.removeAll();return}this.doRender(t,r,i,n)},e.prototype.renderBackground=function(t){var r=this.visualMapModel,i=Yh(r.get("padding")||0),n=t.getBoundingRect();t.add(new pt({z2:-1,silent:!0,shape:{x:n.x-i[3],y:n.y-i[0],width:n.width+i[3]+i[1],height:n.height+i[0]+i[2]},style:{fill:r.get("backgroundColor"),stroke:r.get("borderColor"),lineWidth:r.get("borderWidth")}}))},e.prototype.getControllerVisual=function(t,r,i){i=i||{};var n=i.forceState,o=this.visualMapModel,s={};if(r==="color"){var l=o.get("contentColor");s.color=l}function u(f){return s[f]}function c(f,p){s[f]=p}var h=o.controllerVisuals[n||o.getValueState(t)],v=Tt.prepareVisualTypes(h);return D(v,function(f){var p=h[f];i.convertOpacityToAlpha&&f==="opacity"&&(f="colorAlpha",p=h.__alphaForOpacity),Tt.dependsOn(f,r)&&p&&p.applyVisual(t,u,c)}),s[r]},e.prototype.positionGroup=function(t){var r=this.visualMapModel,i=this.api;bi(t,r.getBoxLayoutParams(),{width:i.getWidth(),height:i.getHeight()})},e.prototype.doRender=function(t,r,i,n){},e.type="visualMap",e}(Ot),rh=[["left","right","width"],["top","bottom","height"]];function sp(a,e,t){var r=a.option,i=r.align;if(i!=null&&i!=="auto")return i;for(var n={width:e.getWidth(),height:e.getHeight()},o=r.orient==="horizontal"?1:0,s=rh[o],l=[0,null,10],u={},c=0;c<3;c++)u[rh[1-o][c]]=l[c],u[s[c]]=c===2?t[0]:r[s[c]];var h=[["x","width",3],["y","height",0]][o],v=Rt(u,n,r.padding);return s[(v.margin[h[2]]||0)+v[h[0]]+v[h[1]]*.5<n[h[1]]*.5?0:1]}function Wa(a,e){return D(a||[],function(t){t.dataIndex!=null&&(t.dataIndexInside=t.dataIndex,t.dataIndex=null),t.highlightKey="visualMap"+(e?e.componentIndex:"")}),a}var he=at,BA=D,ah=Math.min,On=Math.max,GA=12,FA=6,HA=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._shapes={},t._dataInterval=[],t._handleEnds=[],t._hoverLinkDataIndices=[],t}return e.prototype.init=function(t,r){a.prototype.init.call(this,t,r),this._hoverLinkFromSeriesMouseOver=W(this._hoverLinkFromSeriesMouseOver,this),this._hideIndicator=W(this._hideIndicator,this)},e.prototype.doRender=function(t,r,i,n){(!n||n.type!=="selectDataRange"||n.from!==this.uid)&&this._buildView()},e.prototype._buildView=function(){this.group.removeAll();var t=this.visualMapModel,r=this.group;this._orient=t.get("orient"),this._useHandle=t.get("calculable"),this._resetInterval(),this._renderBar(r);var i=t.get("text");this._renderEndsText(r,i,0),this._renderEndsText(r,i,1),this._updateView(!0),this.renderBackground(r),this._updateView(),this._enableHoverLinkToSeries(),this._enableHoverLinkFromSeries(),this.positionGroup(r)},e.prototype._renderEndsText=function(t,r,i){if(r){var n=r[1-i];n=n!=null?n+"":"";var o=this.visualMapModel,s=o.get("textGap"),l=o.itemSize,u=this._shapes.mainGroup,c=this._applyTransform([l[0]/2,i===0?-s:l[1]+s],u),h=this._applyTransform(i===0?"bottom":"top",u),v=this._orient,f=this.visualMapModel.textStyleModel;this.group.add(new ut({style:yt(f,{x:c[0],y:c[1],verticalAlign:v==="horizontal"?"middle":h,align:v==="horizontal"?h:"center",text:n})}))}},e.prototype._renderBar=function(t){var r=this.visualMapModel,i=this._shapes,n=r.itemSize,o=this._orient,s=this._useHandle,l=sp(r,this.api,n),u=i.mainGroup=this._createBarGroup(l),c=new X;u.add(c),c.add(i.outOfRange=ih()),c.add(i.inRange=ih(null,s?oh(this._orient):null,W(this._dragHandle,this,"all",!1),W(this._dragHandle,this,"all",!0))),c.setClipPath(new pt({shape:{x:0,y:0,width:n[0],height:n[1],r:3}}));var h=r.textStyleModel.getTextRect("国"),v=On(h.width,h.height);s&&(i.handleThumbs=[],i.handleLabels=[],i.handleLabelPoints=[],this._createHandle(r,u,0,n,v,o),this._createHandle(r,u,1,n,v,o)),this._createIndicator(r,u,n,v,o),t.add(u)},e.prototype._createHandle=function(t,r,i,n,o,s){var l=W(this._dragHandle,this,i,!1),u=W(this._dragHandle,this,i,!0),c=$n(t.get("handleSize"),n[0]),h=Et(t.get("handleIcon"),-c/2,-c/2,c,c,null,!0),v=oh(this._orient);h.attr({cursor:v,draggable:!0,drift:l,ondragend:u,onmousemove:function(y){Xe(y.event)}}),h.x=n[0]/2,h.useStyle(t.getModel("handleStyle").getItemStyle()),h.setStyle({strokeNoScale:!0,strokeFirst:!0}),h.style.lineWidth*=2,h.ensureState("emphasis").style=t.getModel(["emphasis","handleStyle"]).getItemStyle(),Nr(h,!0),r.add(h);var f=this.visualMapModel.textStyleModel,p=new ut({cursor:v,draggable:!0,drift:l,onmousemove:function(y){Xe(y.event)},ondragend:u,style:yt(f,{x:0,y:0,text:""})});p.ensureState("blur").style={opacity:.1},p.stateTransition={duration:200},this.group.add(p);var d=[c,0],g=this._shapes;g.handleThumbs[i]=h,g.handleLabelPoints[i]=d,g.handleLabels[i]=p},e.prototype._createIndicator=function(t,r,i,n,o){var s=$n(t.get("indicatorSize"),i[0]),l=Et(t.get("indicatorIcon"),-s/2,-s/2,s,s,null,!0);l.attr({cursor:"move",invisible:!0,silent:!0,x:i[0]/2});var u=t.getModel("indicatorStyle").getItemStyle();if(l instanceof pe){var c=l.style;l.useStyle(F({image:c.image,x:c.x,y:c.y,width:c.width,height:c.height},u))}else l.useStyle(u);r.add(l);var h=this.visualMapModel.textStyleModel,v=new ut({silent:!0,invisible:!0,style:yt(h,{x:0,y:0,text:""})});this.group.add(v);var f=[(o==="horizontal"?n/2:FA)+i[0]/2,0],p=this._shapes;p.indicator=l,p.indicatorLabel=v,p.indicatorLabelPoint=f,this._firstShowIndicator=!0},e.prototype._dragHandle=function(t,r,i,n){if(this._useHandle){if(this._dragging=!r,!r){var o=this._applyTransform([i,n],this._shapes.mainGroup,!0);this._updateInterval(t,o[1]),this._hideIndicator(),this._updateView()}r===!this.visualMapModel.get("realtime")&&this.api.dispatchAction({type:"selectDataRange",from:this.uid,visualMapId:this.visualMapModel.id,selected:this._dataInterval.slice()}),r?!this._hovering&&this._clearHoverLinkToSeries():nh(this.visualMapModel)&&this._doHoverLinkToSeries(this._handleEnds[t],!1)}},e.prototype._resetInterval=function(){var t=this.visualMapModel,r=this._dataInterval=t.getSelected(),i=t.getExtent(),n=[0,t.itemSize[1]];this._handleEnds=[he(r[0],i,n,!0),he(r[1],i,n,!0)]},e.prototype._updateInterval=function(t,r){r=r||0;var i=this.visualMapModel,n=this._handleEnds,o=[0,i.itemSize[1]];Qe(r,n,o,t,0);var s=i.getExtent();this._dataInterval=[he(n[0],o,s,!0),he(n[1],o,s,!0)]},e.prototype._updateView=function(t){var r=this.visualMapModel,i=r.getExtent(),n=this._shapes,o=[0,r.itemSize[1]],s=t?o:this._handleEnds,l=this._createBarVisual(this._dataInterval,i,s,"inRange"),u=this._createBarVisual(i,i,o,"outOfRange");n.inRange.setStyle({fill:l.barColor}).setShape("points",l.barPoints),n.outOfRange.setStyle({fill:u.barColor}).setShape("points",u.barPoints),this._updateHandle(s,l)},e.prototype._createBarVisual=function(t,r,i,n){var o={forceState:n,convertOpacityToAlpha:!0},s=this._makeColorGradient(t,o),l=[this.getControllerVisual(t[0],"symbolSize",o),this.getControllerVisual(t[1],"symbolSize",o)],u=this._createBarPoints(i,l);return{barColor:new Mo(0,0,0,1,s),barPoints:u,handlesColor:[s[0].color,s[s.length-1].color]}},e.prototype._makeColorGradient=function(t,r){var i=100,n=[],o=(t[1]-t[0])/i;n.push({color:this.getControllerVisual(t[0],"color",r),offset:0});for(var s=1;s<i;s++){var l=t[0]+o*s;if(l>t[1])break;n.push({color:this.getControllerVisual(l,"color",r),offset:s/i})}return n.push({color:this.getControllerVisual(t[1],"color",r),offset:1}),n},e.prototype._createBarPoints=function(t,r){var i=this.visualMapModel.itemSize;return[[i[0]-r[0],t[0]],[i[0],t[0]],[i[0],t[1]],[i[0]-r[1],t[1]]]},e.prototype._createBarGroup=function(t){var r=this._orient,i=this.visualMapModel.get("inverse");return new X(r==="horizontal"&&!i?{scaleX:t==="bottom"?1:-1,rotation:Math.PI/2}:r==="horizontal"&&i?{scaleX:t==="bottom"?-1:1,rotation:-Math.PI/2}:r==="vertical"&&!i?{scaleX:t==="left"?1:-1,scaleY:-1}:{scaleX:t==="left"?1:-1})},e.prototype._updateHandle=function(t,r){if(this._useHandle){var i=this._shapes,n=this.visualMapModel,o=i.handleThumbs,s=i.handleLabels,l=n.itemSize,u=n.getExtent(),c=this._applyTransform("left",i.mainGroup);BA([0,1],function(h){var v=o[h];v.setStyle("fill",r.handlesColor[h]),v.y=t[h];var f=he(t[h],[0,l[1]],u,!0),p=this.getControllerVisual(f,"symbolSize");v.scaleX=v.scaleY=p/l[0],v.x=l[0]-p/2;var d=Le(i.handleLabelPoints[h],vr(v,this.group));if(this._orient==="horizontal"){var g=c==="left"||c==="top"?(l[0]-p)/2:(l[0]-p)/-2;d[1]+=g}s[h].setStyle({x:d[0],y:d[1],text:n.formatValueText(this._dataInterval[h]),verticalAlign:"middle",align:this._orient==="vertical"?this._applyTransform("left",i.mainGroup):"center"})},this)}},e.prototype._showIndicator=function(t,r,i,n){var o=this.visualMapModel,s=o.getExtent(),l=o.itemSize,u=[0,l[1]],c=this._shapes,h=c.indicator;if(h){h.attr("invisible",!1);var v={convertOpacityToAlpha:!0},f=this.getControllerVisual(t,"color",v),p=this.getControllerVisual(t,"symbolSize"),d=he(t,s,u,!0),g=l[0]-p/2,y={x:h.x,y:h.y};h.y=d,h.x=g;var m=Le(c.indicatorLabelPoint,vr(h,this.group)),S=c.indicatorLabel;S.attr("invisible",!1);var x=this._applyTransform("left",c.mainGroup),b=this._orient,_=b==="horizontal";S.setStyle({text:(i||"")+o.formatValueText(r),verticalAlign:_?x:"middle",align:_?"center":x});var w={x:g,y:d,style:{fill:f}},A={style:{x:m[0],y:m[1]}};if(o.ecModel.isAnimationEnabled()&&!this._firstShowIndicator){var C={duration:100,easing:"cubicInOut",additive:!0};h.x=y.x,h.y=y.y,h.animateTo(w,C),S.animateTo(A,C)}else h.attr(w),S.attr(A);this._firstShowIndicator=!1;var T=this._shapes.handleLabels;if(T)for(var I=0;I<T.length;I++)this.api.enterBlur(T[I])}},e.prototype._enableHoverLinkToSeries=function(){var t=this;this._shapes.mainGroup.on("mousemove",function(r){if(t._hovering=!0,!t._dragging){var i=t.visualMapModel.itemSize,n=t._applyTransform([r.offsetX,r.offsetY],t._shapes.mainGroup,!0,!0);n[1]=ah(On(0,n[1]),i[1]),t._doHoverLinkToSeries(n[1],0<=n[0]&&n[0]<=i[0])}}).on("mouseout",function(){t._hovering=!1,!t._dragging&&t._clearHoverLinkToSeries()})},e.prototype._enableHoverLinkFromSeries=function(){var t=this.api.getZr();this.visualMapModel.option.hoverLink?(t.on("mouseover",this._hoverLinkFromSeriesMouseOver,this),t.on("mouseout",this._hideIndicator,this)):this._clearHoverLinkFromSeries()},e.prototype._doHoverLinkToSeries=function(t,r){var i=this.visualMapModel,n=i.itemSize;if(i.option.hoverLink){var o=[0,n[1]],s=i.getExtent();t=ah(On(o[0],t),o[1]);var l=WA(i,s,o),u=[t-l,t+l],c=he(t,o,s,!0),h=[he(u[0],o,s,!0),he(u[1],o,s,!0)];u[0]<o[0]&&(h[0]=-1/0),u[1]>o[1]&&(h[1]=1/0),r&&(h[0]===-1/0?this._showIndicator(c,h[1],"< ",l):h[1]===1/0?this._showIndicator(c,h[0],"> ",l):this._showIndicator(c,c,"≈ ",l));var v=this._hoverLinkDataIndices,f=[];(r||nh(i))&&(f=this._hoverLinkDataIndices=i.findTargetDataIndices(h));var p=sg(v,f);this._dispatchHighDown("downplay",Wa(p[0],i)),this._dispatchHighDown("highlight",Wa(p[1],i))}},e.prototype._hoverLinkFromSeriesMouseOver=function(t){var r;if(Ch(t.target,function(l){var u=ht(l);if(u.dataIndex!=null)return r=u,!0},!0),!!r){var i=this.ecModel.getSeriesByIndex(r.seriesIndex),n=this.visualMapModel;if(n.isTargetSeries(i)){var o=i.getData(r.dataType),s=o.getStore().get(n.getDataDimensionIndex(o),r.dataIndex);isNaN(s)||this._showIndicator(s,s)}}},e.prototype._hideIndicator=function(){var t=this._shapes;t.indicator&&t.indicator.attr("invisible",!0),t.indicatorLabel&&t.indicatorLabel.attr("invisible",!0);var r=this._shapes.handleLabels;if(r)for(var i=0;i<r.length;i++)this.api.leaveBlur(r[i])},e.prototype._clearHoverLinkToSeries=function(){this._hideIndicator();var t=this._hoverLinkDataIndices;this._dispatchHighDown("downplay",Wa(t,this.visualMapModel)),t.length=0},e.prototype._clearHoverLinkFromSeries=function(){this._hideIndicator();var t=this.api.getZr();t.off("mouseover",this._hoverLinkFromSeriesMouseOver),t.off("mouseout",this._hideIndicator)},e.prototype._applyTransform=function(t,r,i,n){var o=vr(r,n?null:this.group);return U(t)?Le(t,o,i):ko(t,o,i)},e.prototype._dispatchHighDown=function(t,r){r&&r.length&&this.api.dispatchAction({type:t,batch:r})},e.prototype.dispose=function(){this._clearHoverLinkFromSeries(),this._clearHoverLinkToSeries()},e.type="visualMap.continuous",e}(op);function ih(a,e,t,r){return new se({shape:{points:a},draggable:!!t,cursor:e,drift:t,onmousemove:function(i){Xe(i.event)},ondragend:r})}function WA(a,e,t){var r=GA/2,i=a.get("hoverLinkDataSize");return i&&(r=he(i,e,t,!0)/2),r}function nh(a){var e=a.get("hoverLinkOnHandle");return!!(e??a.get("realtime"))}function oh(a){return a==="vertical"?"ns-resize":"ew-resize"}var ZA={type:"selectDataRange",event:"dataRangeSelected",update:"update"},UA=function(a,e){e.eachComponent({mainType:"visualMap",query:a},function(t){t.setSelected(a.selected)})},YA=[{createOnAllSeries:!0,reset:function(a,e){var t=[];return e.eachComponent("visualMap",function(r){var i=a.pipelineContext;!r.isTargetSeries(a)||i&&i.large||t.push(yw(r.stateList,r.targetVisuals,W(r.getValueState,r),r.getDataDimensionIndex(a.getData())))}),t}},{createOnAllSeries:!0,reset:function(a,e){var t=a.getData(),r=[];e.eachComponent("visualMap",function(i){if(i.isTargetSeries(a)){var n=i.getVisualMeta(W($A,null,a,i))||{stops:[],outerColors:[]},o=i.getDataDimensionIndex(t);o>=0&&(n.dimension=o,r.push(n))}}),a.getData().setVisual("visualMeta",r)}}];function $A(a,e,t,r){for(var i=e.targetVisuals[r],n=Tt.prepareVisualTypes(i),o={color:Di(a.getData(),"color")},s=0,l=n.length;s<l;s++){var u=n[s],c=i[u==="opacity"?"__alphaForOpacity":u];c&&c.applyVisual(t,h,v)}return o.color;function h(f){return o[f]}function v(f,p){o[f]=p}}var sh=D;function XA(a){var e=a&&a.visualMap;U(e)||(e=e?[e]:[]),sh(e,function(t){if(t){or(t,"splitList")&&!or(t,"pieces")&&(t.pieces=t.splitList,delete t.splitList);var r=t.pieces;r&&U(r)&&sh(r,function(i){Nt(i)&&(or(i,"start")&&!or(i,"min")&&(i.min=i.start),or(i,"end")&&!or(i,"max")&&(i.max=i.end))})}})}function or(a,e){return a&&a.hasOwnProperty&&a.hasOwnProperty(e)}var lh=!1;function lp(a){lh||(lh=!0,a.registerSubTypeDefaulter("visualMap",function(e){return!e.categories&&(!(e.pieces?e.pieces.length>0:e.splitNumber>0)||e.calculable)?"continuous":"piecewise"}),a.registerAction(ZA,UA),D(YA,function(e){a.registerVisual(a.PRIORITY.VISUAL.COMPONENT,e)}),a.registerPreprocessor(XA))}function up(a){a.registerComponentModel(OA),a.registerComponentView(HA),lp(a)}var KA=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._pieceList=[],t}return e.prototype.optionUpdated=function(t,r){a.prototype.optionUpdated.apply(this,arguments),this.resetExtent();var i=this._mode=this._determineMode();this._pieceList=[],qA[this._mode].call(this,this._pieceList),this._resetSelected(t,r);var n=this.option.categories;this.resetVisual(function(o,s){i==="categories"?(o.mappingMethod="category",o.categories=it(n)):(o.dataExtent=this.getExtent(),o.mappingMethod="piecewise",o.pieceList=G(this._pieceList,function(l){return l=it(l),s!=="inRange"&&(l.visual=null),l}))})},e.prototype.completeVisualOption=function(){var t=this.option,r={},i=Tt.listVisualTypes(),n=this.isCategory();D(t.pieces,function(s){D(i,function(l){s.hasOwnProperty(l)&&(r[l]=1)})}),D(r,function(s,l){var u=!1;D(this.stateList,function(c){u=u||o(t,c,l)||o(t.target,c,l)},this),!u&&D(this.stateList,function(c){(t[c]||(t[c]={}))[l]=np.get(l,c==="inRange"?"active":"inactive",n)})},this);function o(s,l,u){return s&&s[l]&&s[l].hasOwnProperty(u)}a.prototype.completeVisualOption.apply(this,arguments)},e.prototype._resetSelected=function(t,r){var i=this.option,n=this._pieceList,o=(r?i:t).selected||{};if(i.selected=o,D(n,function(l,u){var c=this.getSelectedMapKey(l);o.hasOwnProperty(c)||(o[c]=!0)},this),i.selectedMode==="single"){var s=!1;D(n,function(l,u){var c=this.getSelectedMapKey(l);o[c]&&(s?o[c]=!1:s=!0)},this)}},e.prototype.getItemSymbol=function(){return this.get("itemSymbol")},e.prototype.getSelectedMapKey=function(t){return this._mode==="categories"?t.value+"":t.index+""},e.prototype.getPieceList=function(){return this._pieceList},e.prototype._determineMode=function(){var t=this.option;return t.pieces&&t.pieces.length>0?"pieces":this.option.categories?"categories":"splitNumber"},e.prototype.setSelected=function(t){this.option.selected=it(t)},e.prototype.getValueState=function(t){var r=Tt.findPieceIndex(t,this._pieceList);return r!=null&&this.option.selected[this.getSelectedMapKey(this._pieceList[r])]?"inRange":"outOfRange"},e.prototype.findTargetDataIndices=function(t){var r=[],i=this._pieceList;return this.eachTargetSeries(function(n){var o=[],s=n.getData();s.each(this.getDataDimensionIndex(s),function(l,u){var c=Tt.findPieceIndex(l,i);c===t&&o.push(u)},this),r.push({seriesId:n.id,dataIndex:o})},this),r},e.prototype.getRepresentValue=function(t){var r;if(this.isCategory())r=t.value;else if(t.value!=null)r=t.value;else{var i=t.interval||[];r=i[0]===-1/0&&i[1]===1/0?0:(i[0]+i[1])/2}return r},e.prototype.getVisualMeta=function(t){if(this.isCategory())return;var r=[],i=["",""],n=this;function o(c,h){var v=n.getRepresentValue({interval:c});h||(h=n.getValueState(v));var f=t(v,h);c[0]===-1/0?i[0]=f:c[1]===1/0?i[1]=f:r.push({value:c[0],color:f},{value:c[1],color:f})}var s=this._pieceList.slice();if(!s.length)s.push({interval:[-1/0,1/0]});else{var l=s[0].interval[0];l!==-1/0&&s.unshift({interval:[-1/0,l]}),l=s[s.length-1].interval[1],l!==1/0&&s.push({interval:[l,1/0]})}var u=-1/0;return D(s,function(c){var h=c.interval;h&&(h[0]>u&&o([u,h[0]],"outOfRange"),o(h.slice()),u=h[1])},this),{stops:r,outerColors:i}},e.type="visualMap.piecewise",e.defaultOption=Je(hi.defaultOption,{selected:null,minOpen:!1,maxOpen:!1,align:"auto",itemWidth:20,itemHeight:14,itemSymbol:"roundRect",pieces:null,categories:null,splitNumber:5,selectedMode:"multiple",itemGap:10,hoverLink:!0}),e}(hi),qA={splitNumber:function(a){var e=this.option,t=Math.min(e.precision,20),r=this.getExtent(),i=e.splitNumber;i=Math.max(parseInt(i,10),1),e.splitNumber=i;for(var n=(r[1]-r[0])/i;+n.toFixed(t)!==n&&t<5;)t++;e.precision=t,n=+n.toFixed(t),e.minOpen&&a.push({interval:[-1/0,r[0]],close:[0,0]});for(var o=0,s=r[0];o<i;s+=n,o++){var l=o===i-1?r[1]:s+n;a.push({interval:[s,l],close:[1,1]})}e.maxOpen&&a.push({interval:[r[1],1/0],close:[0,0]}),rl(a),D(a,function(u,c){u.index=c,u.text=this.formatValueText(u.interval)},this)},categories:function(a){var e=this.option;D(e.categories,function(t){a.push({text:this.formatValueText(t,!0),value:t})},this),uh(e,a)},pieces:function(a){var e=this.option;D(e.pieces,function(t,r){Nt(t)||(t={value:t});var i={text:"",index:r};if(t.label!=null&&(i.text=t.label),t.hasOwnProperty("value")){var n=i.value=t.value;i.interval=[n,n],i.close=[1,1]}else{for(var o=i.interval=[],s=i.close=[0,0],l=[1,0,1],u=[-1/0,1/0],c=[],h=0;h<2;h++){for(var v=[["gte","gt","min"],["lte","lt","max"]][h],f=0;f<3&&o[h]==null;f++)o[h]=t[v[f]],s[h]=l[f],c[h]=f===2;o[h]==null&&(o[h]=u[h])}c[0]&&o[1]===1/0&&(s[0]=0),c[1]&&o[0]===-1/0&&(s[1]=0),o[0]===o[1]&&s[0]&&s[1]&&(i.value=o[0])}i.visual=Tt.retrieveVisuals(t),a.push(i)},this),uh(e,a),rl(a),D(a,function(t){var r=t.close,i=[["<","≤"][r[1]],[">","≥"][r[0]]];t.text=t.text||this.formatValueText(t.value!=null?t.value:t.interval,!1,i)},this)}};function uh(a,e){var t=a.inverse;(a.orient==="vertical"?!t:t)&&e.reverse()}var jA=function(a){E(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.doRender=function(){var t=this.group;t.removeAll();var r=this.visualMapModel,i=r.get("textGap"),n=r.textStyleModel,o=n.getFont(),s=n.getTextColor(),l=this._getItemAlign(),u=r.itemSize,c=this._getViewData(),h=c.endsText,v=qt(r.get("showLabel",!0),!h),f=!r.get("selectedMode");h&&this._renderEndsText(t,h[0],u,v,l),D(c.viewPieceList,function(p){var d=p.piece,g=new X;g.onclick=W(this._onItemClick,this,d),this._enableHoverLink(g,p.indexInModelPieceList);var y=r.getRepresentValue(d);if(this._createItemSymbol(g,y,[0,0,u[0],u[1]],f),v){var m=this.visualMapModel.getValueState(y);g.add(new ut({style:{x:l==="right"?-i:u[0]+i,y:u[1]/2,text:d.text,verticalAlign:"middle",align:l,font:o,fill:s,opacity:m==="outOfRange"?.5:1},silent:f}))}t.add(g)},this),h&&this._renderEndsText(t,h[1],u,v,l),Ze(r.get("orient"),t,r.get("itemGap")),this.renderBackground(t),this.positionGroup(t)},e.prototype._enableHoverLink=function(t,r){var i=this;t.on("mouseover",function(){return n("highlight")}).on("mouseout",function(){return n("downplay")});var n=function(o){var s=i.visualMapModel;s.option.hoverLink&&i.api.dispatchAction({type:o,batch:Wa(s.findTargetDataIndices(r),s)})}},e.prototype._getItemAlign=function(){var t=this.visualMapModel,r=t.option;if(r.orient==="vertical")return sp(t,this.api,t.itemSize);var i=r.align;return(!i||i==="auto")&&(i="left"),i},e.prototype._renderEndsText=function(t,r,i,n,o){if(r){var s=new X,l=this.visualMapModel.textStyleModel;s.add(new ut({style:yt(l,{x:n?o==="right"?i[0]:0:i[0]/2,y:i[1]/2,verticalAlign:"middle",align:n?o:"center",text:r})})),t.add(s)}},e.prototype._getViewData=function(){var t=this.visualMapModel,r=G(t.getPieceList(),function(s,l){return{piece:s,indexInModelPieceList:l}}),i=t.get("text"),n=t.get("orient"),o=t.get("inverse");return(n==="horizontal"?o:!o)?r.reverse():i&&(i=i.slice().reverse()),{viewPieceList:r,endsText:i}},e.prototype._createItemSymbol=function(t,r,i,n){var o=Et(this.getControllerVisual(r,"symbol"),i[0],i[1],i[2],i[3],this.getControllerVisual(r,"color"));o.silent=n,t.add(o)},e.prototype._onItemClick=function(t){var r=this.visualMapModel,i=r.option,n=i.selectedMode;if(n){var o=it(i.selected),s=r.getSelectedMapKey(t);n==="single"||n===!0?(o[s]=!0,D(o,function(l,u){o[u]=u===s})):o[s]=!o[s],this.api.dispatchAction({type:"selectDataRange",from:this.uid,visualMapId:this.visualMapModel.id,selected:o})}},e.type="visualMap.piecewise",e}(op);function cp(a){a.registerComponentModel(KA),a.registerComponentView(jA),lp(a)}function JA(a){K(up),K(cp)}var QA={label:{enabled:!0},decal:{show:!1}},ch=Dt(),tT={};function eT(a,e){var t=a.getModel("aria");if(!t.get("enabled"))return;var r=it(QA);ft(r.label,a.getLocaleModel().get("aria"),!1),ft(t.option,r,!1),i(),n();function i(){var u=t.getModel("decal"),c=u.get("show");if(c){var h=et();a.eachSeries(function(v){if(!v.isColorBySeries()){var f=h.get(v.type);f||(f={},h.set(v.type,f)),ch(v).scope=f}}),a.eachRawSeries(function(v){if(a.isSeriesFiltered(v))return;if(st(v.enableAriaDecal)){v.enableAriaDecal();return}var f=v.getData();if(v.isColorBySeries()){var m=Fn(v.ecModel,v.name,tT,a.getSeriesCount()),S=f.getVisual("decal");f.setVisual("decal",x(S,m))}else{var p=v.getRawData(),d={},g=ch(v).scope;f.each(function(b){var _=f.getRawIndex(b);d[_]=b});var y=p.count();p.each(function(b){var _=d[b],w=p.getName(b)||b+"",A=Fn(v.ecModel,w,g,y),C=f.getItemVisual(_,"decal");f.setItemVisual(_,"decal",x(C,A))})}function x(b,_){var w=b?F(F({},_),b):_;return w.dirty=!0,w}})}}function n(){var u=e.getZr().dom;if(u){var c=a.getLocaleModel().get("aria"),h=t.getModel("label");if(h.option=ot(h.option,c),!!h.get("enabled")){if(u.setAttribute("role","img"),h.get("description")){u.setAttribute("aria-label",h.get("description"));return}var v=a.getSeriesCount(),f=h.get(["data","maxCount"])||10,p=h.get(["series","maxCount"])||10,d=Math.min(v,p),g;if(!(v<1)){var y=s();if(y){var m=h.get(["general","withTitle"]);g=o(m,{title:y})}else g=h.get(["general","withoutTitle"]);var S=[],x=v>1?h.get(["series","multiple","prefix"]):h.get(["series","single","prefix"]);g+=o(x,{seriesCount:v}),a.eachSeries(function(A,C){if(C<d){var T=void 0,I=A.get("name"),L=I?"withName":"withoutName";T=v>1?h.get(["series","multiple",L]):h.get(["series","single",L]),T=o(T,{seriesId:A.seriesIndex,seriesName:A.get("name"),seriesType:l(A.subType)});var M=A.getData();if(M.count()>f){var R=h.get(["data","partialData"]);T+=o(R,{displayCnt:f})}else T+=h.get(["data","allData"]);for(var P=h.get(["data","separator","middle"]),k=h.get(["data","separator","end"]),V=h.get(["data","excludeDimensionId"]),N=[],z=0;z<M.count();z++)if(z<f){var H=M.getName(z),Z=V?Yt(M.getValues(z),function(Q,j){return mt(V,j)===-1}):M.getValues(z),Y=h.get(["data",H?"withName":"withoutName"]);N.push(o(Y,{name:H,value:Z.join(P)}))}T+=N.join(P)+k,S.push(T)}});var b=h.getModel(["series","multiple","separator"]),_=b.get("middle"),w=b.get("end");g+=S.join(_)+w,u.setAttribute("aria-label",g)}}}}function o(u,c){if(!tt(u))return u;var h=u;return D(c,function(v,f){h=h.replace(new RegExp("\\{\\s*"+f+"\\s*\\}","g"),v)}),h}function s(){var u=a.get("title");return u&&u.length&&(u=u[0]),u&&u.text}function l(u){var c=a.getLocaleModel().get(["series","typeNames"]);return c[u]||c.chart}}function rT(a){if(!(!a||!a.aria)){var e=a.aria;e.show!=null&&(e.enabled=e.show),e.label=e.label||{},D(["description","general","series","data"],function(t){e[t]!=null&&(e.label[t]=e[t])})}}function aT(a){a.registerPreprocessor(rT),a.registerVisual(a.PRIORITY.VISUAL.ARIA,eT)}K([lg]);K([jg]);K([ug,cg,sy,fy,Ay,cm,Om,b0,W0,K0,iS,JS,wx,kx,qx,t1,c1,y1,I1,k1,Z1,Cb]);K(hg);K(Qb);K(Tv);K(v_);K(hf);K(g_);K(T_);K(vw);K(vg);K(zo);K(Lw);K(fg);K(Bw);K(Yw);K(Jw);K(iA);K(pA);K(RA);K(ap);K(ip);K(JA);K(up);K(cp);K(aT);K(pg);K(dg);K(gg);K(yg);
