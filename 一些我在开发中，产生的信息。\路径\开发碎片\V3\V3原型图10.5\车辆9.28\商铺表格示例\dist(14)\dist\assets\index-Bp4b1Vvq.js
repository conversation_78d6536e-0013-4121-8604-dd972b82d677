import{g as m}from"./_commonjsHelpers-BbMlrU8H.js";var M={exports:{}},L=M.exports,w;function y(){return w||(w=1,function(I,P){(function(f,v){I.exports=v()})(L,function(){function f(n){var s=[];return n.AMapUI&&s.push(v(n.AMapUI)),n.Loca&&s.push(U(n.Loca)),Promise.all(s)}function v(n){return new Promise(function(s,e){var a=[];if(n.plugins)for(var i=0;i<n.plugins.length;i+=1)u.AMapUI.plugins.indexOf(n.plugins[i])==-1&&a.push(n.plugins[i]);if(t.AMapUI===o.failed)e("前次请求 AMapUI 失败");else if(t.AMapUI===o.notload){t.AMapUI=o.loading,u.AMapUI.version=n.version||u.AMapUI.version,i=u.AMapUI.version;var d=document.body||document.head,r=document.createElement("script");r.type="text/javascript",r.src="https://webapi.amap.com/ui/"+i+"/main.js",r.onerror=function(p){t.AMapUI=o.failed,e("请求 AMapUI 失败")},r.onload=function(){if(t.AMapUI=o.loaded,a.length)window.AMapUI.loadUI(a,function(){for(var p=0,c=a.length;p<c;p++){var A=a[p].split("/").slice(-1)[0];window.AMapUI[A]=arguments[p]}for(s();l.AMapUI.length;)l.AMapUI.splice(0,1)[0]()});else for(s();l.AMapUI.length;)l.AMapUI.splice(0,1)[0]()},d.appendChild(r)}else t.AMapUI===o.loaded?n.version&&n.version!==u.AMapUI.version?e("不允许多个版本 AMapUI 混用"):a.length?window.AMapUI.loadUI(a,function(){for(var p=0,c=a.length;p<c;p++){var A=a[p].split("/").slice(-1)[0];window.AMapUI[A]=arguments[p]}s()}):s():n.version&&n.version!==u.AMapUI.version?e("不允许多个版本 AMapUI 混用"):l.AMapUI.push(function(p){p?e(p):a.length?window.AMapUI.loadUI(a,function(){for(var c=0,A=a.length;c<A;c++){var b=a[c].split("/").slice(-1)[0];window.AMapUI[b]=arguments[c]}s()}):s()})})}function U(n){return new Promise(function(s,e){if(t.Loca===o.failed)e("前次请求 Loca 失败");else if(t.Loca===o.notload){t.Loca=o.loading,u.Loca.version=n.version||u.Loca.version;var a=u.Loca.version,i=u.AMap.version.startsWith("2"),d=a.startsWith("2");if(i&&!d||!i&&d)e("JSAPI 与 Loca 版本不对应！！");else{i=u.key,d=document.body||document.head;var r=document.createElement("script");r.type="text/javascript",r.src="https://webapi.amap.com/loca?v="+a+"&key="+i,r.onerror=function(p){t.Loca=o.failed,e("请求 AMapUI 失败")},r.onload=function(){for(t.Loca=o.loaded,s();l.Loca.length;)l.Loca.splice(0,1)[0]()},d.appendChild(r)}}else t.Loca===o.loaded?n.version&&n.version!==u.Loca.version?e("不允许多个版本 Loca 混用"):s():n.version&&n.version!==u.Loca.version?e("不允许多个版本 Loca 混用"):l.Loca.push(function(p){p?e(p):e()})})}if(!window)throw Error("AMap JSAPI can only be used in Browser.");var o;(function(n){n.notload="notload",n.loading="loading",n.loaded="loaded",n.failed="failed"})(o||(o={}));var u={key:"",AMap:{version:"1.4.15",plugins:[]},AMapUI:{version:"1.1",plugins:[]},Loca:{version:"1.3.2"}},t={AMap:o.notload,AMapUI:o.notload,Loca:o.notload},l={AMap:[],AMapUI:[],Loca:[]},g=[],h=function(n){typeof n=="function"&&(t.AMap===o.loaded?n(window.AMap):g.push(n))};return{load:function(n){return new Promise(function(s,e){if(t.AMap==o.failed)e("");else if(t.AMap==o.notload){var a=n.key,i=n.version,d=n.plugins;a?(window.AMap&&location.host!=="lbs.amap.com"&&e("禁止多种API加载方式混用"),u.key=a,u.AMap.version=i||u.AMap.version,u.AMap.plugins=d||u.AMap.plugins,t.AMap=o.loading,i=document.body||document.head,window.___onAPILoaded=function(p){if(delete window.___onAPILoaded,p)t.AMap=o.failed,e(p);else for(t.AMap=o.loaded,f(n).then(function(){s(window.AMap)}).catch(e);g.length;)g.splice(0,1)[0]()},d=document.createElement("script"),d.type="text/javascript",d.src="https://webapi.amap.com/maps?callback=___onAPILoaded&v="+u.AMap.version+"&key="+a+"&plugin="+u.AMap.plugins.join(","),d.onerror=function(p){t.AMap=o.failed,e(p)},i.appendChild(d)):e("请填写key")}else if(t.AMap==o.loaded)if(n.key&&n.key!==u.key)e("多个不一致的 key");else if(n.version&&n.version!==u.AMap.version)e("不允许多个版本 JSAPI 混用");else{if(a=[],n.plugins)for(i=0;i<n.plugins.length;i+=1)u.AMap.plugins.indexOf(n.plugins[i])==-1&&a.push(n.plugins[i]);a.length?window.AMap.plugin(a,function(){f(n).then(function(){s(window.AMap)}).catch(e)}):f(n).then(function(){s(window.AMap)}).catch(e)}else if(n.key&&n.key!==u.key)e("多个不一致的 key");else if(n.version&&n.version!==u.AMap.version)e("不允许多个版本 JSAPI 混用");else{var r=[];if(n.plugins)for(i=0;i<n.plugins.length;i+=1)u.AMap.plugins.indexOf(n.plugins[i])==-1&&r.push(n.plugins[i]);h(function(){r.length?window.AMap.plugin(r,function(){f(n).then(function(){s(window.AMap)}).catch(e)}):f(n).then(function(){s(window.AMap)}).catch(e)})}})},reset:function(){delete window.AMap,delete window.AMapUI,delete window.Loca,u={key:"",AMap:{version:"1.4.15",plugins:[]},AMapUI:{version:"1.1",plugins:[]},Loca:{version:"1.3.2"}},t={AMap:o.notload,AMapUI:o.notload,Loca:o.notload},l={AMap:[],AMapUI:[],Loca:[]}}}})}(M)),M.exports}var x=y();const k=m(x);export{k as A};
