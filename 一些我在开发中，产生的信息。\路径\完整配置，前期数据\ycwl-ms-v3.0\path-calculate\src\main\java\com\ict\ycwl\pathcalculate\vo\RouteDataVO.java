package com.ict.ycwl.pathcalculate.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 大区（含车牌号）返回类
 */
@Data
@NoArgsConstructor
public class RouteDataVO {

    /**
     * 路线id
     */
    Long routeId;

    /**
     * 路线名称
     */
    String routeName;

    /**
     * 大区id
     */
    Long areaId;

    /**
     * 路线坐标点串
     */
    List<Map<String,Double>> polyline;

    /**
     * 运营里程
     */
    String distance;

    /**
     * 工作时长
     */
    String workTime;

    /**
     * 载货量
     */
    String cargoWeight;

    /**
     * 中转站ID
     */
    Long transitDepotId;

    /**
     * 凸包坐标点串
     */
    List<Map<String, Double>> convex;
}
