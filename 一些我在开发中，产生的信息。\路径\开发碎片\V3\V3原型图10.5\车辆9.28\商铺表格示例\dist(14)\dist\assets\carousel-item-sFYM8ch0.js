import{aw as Ne,as as ie,ak as Se,f as ue,i as Te,r as _,k as P,u as e,m as j,z as ce,A as Me,x as $e,l as Ae,L as fe,d as J,j as ve,o as A,c as R,a as K,p as se,w as X,X as q,t as O,M as U,b as Y,a5 as G,ad as ne,K as W,s as de,n as me,N as Oe,T as Be,S as He,y as Le,P as Pe,aa as Re,a7 as Ve,v as ze,O as De}from"./index-C0QCllTd.js";import{E as oe,l as je,m as Ue,_ as he}from"./base-kpSIrADU.js";import{b as We}from"./index-m25zEilF.js";import{f as Fe}from"./index-DOdSMika.js";import{d as Ke}from"./_commonjsHelpers-BbMlrU8H.js";var Xe="Expected a function";function re(o,g,t){var a=!0,d=!0;if(typeof o!="function")throw new TypeError(Xe);return Ne(t)&&(a="leading"in t?!!t.leading:a,d="trailing"in t?!!t.trailing:d),Ke(o,g,{leading:a,maxWait:g,trailing:d})}const Ye=(o,g,t)=>Fe(o.subTree).filter(u=>{var n;return Se(u)&&((n=u.type)==null?void 0:n.name)===g&&!!u.component}).map(u=>u.component.uid).map(u=>t[u]).filter(u=>!!u),qe=(o,g)=>{const t={},a=ie([]);return{children:a,addChild:n=>{t[n.uid]=n,a.value=Ye(o,g,t)},removeChild:n=>{delete t[n],a.value=a.value.filter(m=>m.uid!==n)}}},Ge=ue({initialIndex:{type:Number,default:0},height:{type:String,default:""},trigger:{type:String,values:["hover","click"],default:"hover"},autoplay:{type:Boolean,default:!0},interval:{type:Number,default:3e3},indicatorPosition:{type:String,values:["","none","outside"],default:""},arrow:{type:String,values:["always","hover","never"],default:"hover"},type:{type:String,values:["","card"],default:""},loop:{type:Boolean,default:!0},direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},pauseOnHover:{type:Boolean,default:!0}}),Je={change:(o,g)=>[o,g].every(Te)},pe=Symbol("carouselContextKey"),le=300,Qe=(o,g,t)=>{const{children:a,addChild:d,removeChild:u}=qe(fe(),"ElCarouselItem"),n=_(-1),m=_(null),C=_(!1),I=_(),w=_(0),N=P(()=>o.arrow!=="never"&&!e(p)),T=P(()=>a.value.some(s=>s.props.label.toString().length>0)),k=P(()=>o.type==="card"),p=P(()=>o.direction==="vertical"),B=P(()=>o.height!=="auto"?{height:o.height}:{height:`${w.value}px`,overflow:"hidden"}),E=re(s=>{l(s)},le,{trailing:!0}),S=re(s=>{V(s)},le);function M(){m.value&&(clearInterval(m.value),m.value=null)}function H(){o.interval<=0||!o.autoplay||m.value||(m.value=setInterval(()=>i(),o.interval))}const i=()=>{n.value<a.value.length-1?n.value=n.value+1:o.loop&&(n.value=0)};function l(s){if(Ae(s)){const z=a.value.filter(F=>F.props.name===s);z.length>0&&(s=a.value.indexOf(z[0]))}if(s=Number(s),Number.isNaN(s)||s!==Math.floor(s))return;const h=a.value.length,$=n.value;s<0?n.value=o.loop?h-1:0:s>=h?n.value=o.loop?0:h-1:n.value=s,$===n.value&&v($),x()}function v(s){a.value.forEach((h,$)=>{h.translateItem($,n.value,s)})}function r(s,h){var $,z,F,ee;const D=e(a),te=D.length;if(te===0||!s.states.inStage)return!1;const Ie=h+1,we=h-1,ae=te-1,be=D[ae].states.active,_e=D[0].states.active,ke=(z=($=D[Ie])==null?void 0:$.states)==null?void 0:z.active,Ee=(ee=(F=D[we])==null?void 0:F.states)==null?void 0:ee.active;return h===ae&&_e||ke?"left":h===0&&be||Ee?"right":!1}function b(){C.value=!0,o.pauseOnHover&&M()}function L(){C.value=!1,H()}function c(s){e(p)||a.value.forEach((h,$)=>{s===r(h,$)&&(h.states.hover=!0)})}function f(){e(p)||a.value.forEach(s=>{s.states.hover=!1})}function y(s){n.value=s}function V(s){o.trigger==="hover"&&s!==n.value&&(n.value=s)}function Q(){l(n.value-1)}function ye(){l(n.value+1)}function x(){M(),H()}function Ce(s){o.height==="auto"&&(w.value=s)}j(()=>n.value,(s,h)=>{v(h),h>-1&&g("change",s,h)}),j(()=>o.autoplay,s=>{s?H():M()}),j(()=>o.loop,()=>{l(n.value)}),j(()=>o.interval,()=>{x()}),j(()=>a.value,()=>{a.value.length>0&&l(o.initialIndex)});const Z=ie();return ce(()=>{Z.value=We(I.value,()=>{v()}),H()}),Me(()=>{M(),I.value&&Z.value&&Z.value.stop()}),$e(pe,{root:I,isCardType:k,isVertical:p,items:a,loop:o.loop,addItem:d,removeItem:u,setActiveItem:l,setContainerHeight:Ce}),{root:I,activeIndex:n,arrowDisplay:N,hasLabel:T,hover:C,isCardType:k,items:a,isVertical:p,containerStyle:B,handleButtonEnter:c,handleButtonLeave:f,handleIndicatorClick:y,handleMouseEnter:b,handleMouseLeave:L,setActiveItem:l,prev:Q,next:ye,throttledArrowClick:E,throttledIndicatorHover:S}},Ze=["onMouseenter","onClick"],xe={key:0},et="ElCarousel",tt=J({name:et}),at=J({...tt,props:Ge,emits:Je,setup(o,{expose:g,emit:t}){const a=o,{root:d,activeIndex:u,arrowDisplay:n,hasLabel:m,hover:C,isCardType:I,items:w,isVertical:N,containerStyle:T,handleButtonEnter:k,handleButtonLeave:p,handleIndicatorClick:B,handleMouseEnter:E,handleMouseLeave:S,setActiveItem:M,prev:H,next:i,throttledArrowClick:l,throttledIndicatorHover:v}=Qe(a,t),r=ve("carousel"),b=P(()=>{const c=[r.b(),r.m(a.direction)];return e(I)&&c.push(r.m("card")),c}),L=P(()=>{const c=[r.e("indicators"),r.em("indicators",a.direction)];return e(m)&&c.push(r.em("indicators","labels")),a.indicatorPosition==="outside"&&c.push(r.em("indicators","outside")),e(N)&&c.push(r.em("indicators","right")),c});return g({setActiveItem:M,prev:H,next:i}),(c,f)=>(A(),R("div",{ref_key:"root",ref:d,class:O(e(b)),onMouseenter:f[6]||(f[6]=U((...y)=>e(E)&&e(E)(...y),["stop"])),onMouseleave:f[7]||(f[7]=U((...y)=>e(S)&&e(S)(...y),["stop"]))},[K("div",{class:O(e(r).e("container")),style:me(e(T))},[e(n)?(A(),se(ne,{key:0,name:"carousel-arrow-left",persisted:""},{default:X(()=>[q(K("button",{type:"button",class:O([e(r).e("arrow"),e(r).em("arrow","left")]),onMouseenter:f[0]||(f[0]=y=>e(k)("left")),onMouseleave:f[1]||(f[1]=(...y)=>e(p)&&e(p)(...y)),onClick:f[2]||(f[2]=U(y=>e(l)(e(u)-1),["stop"]))},[Y(e(oe),null,{default:X(()=>[Y(e(je))]),_:1})],34),[[G,(c.arrow==="always"||e(C))&&(a.loop||e(u)>0)]])]),_:1})):W("v-if",!0),e(n)?(A(),se(ne,{key:1,name:"carousel-arrow-right",persisted:""},{default:X(()=>[q(K("button",{type:"button",class:O([e(r).e("arrow"),e(r).em("arrow","right")]),onMouseenter:f[3]||(f[3]=y=>e(k)("right")),onMouseleave:f[4]||(f[4]=(...y)=>e(p)&&e(p)(...y)),onClick:f[5]||(f[5]=U(y=>e(l)(e(u)+1),["stop"]))},[Y(e(oe),null,{default:X(()=>[Y(e(Ue))]),_:1})],34),[[G,(c.arrow==="always"||e(C))&&(a.loop||e(u)<e(w).length-1)]])]),_:1})):W("v-if",!0),de(c.$slots,"default")],6),c.indicatorPosition!=="none"?(A(),R("ul",{key:0,class:O(e(L))},[(A(!0),R(Oe,null,Be(e(w),(y,V)=>(A(),R("li",{key:V,class:O([e(r).e("indicator"),e(r).em("indicator",c.direction),e(r).is("active",V===e(u))]),onMouseenter:Q=>e(v)(V),onClick:U(Q=>e(B)(V),["stop"])},[K("button",{class:O(e(r).e("button"))},[e(m)?(A(),R("span",xe,He(y.props.label),1)):W("v-if",!0)],2)],42,Ze))),128))],2)):W("v-if",!0)],34))}});var st=he(at,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/carousel/src/carousel.vue"]]);const nt=ue({name:{type:String,default:""},label:{type:[String,Number],default:""}}),ot=(o,g)=>{const t=Le(pe),a=fe(),d=.83,u=_(),n=_(!1),m=_(0),C=_(1),I=_(!1),w=_(!1),N=_(!1),T=_(!1),{isCardType:k,isVertical:p}=t;function B(i,l,v){const r=v-1,b=l-1,L=l+1,c=v/2;return l===0&&i===r?-1:l===r&&i===0?v:i<b&&l-i>=c?v+1:i>L&&i-l>=c?-2:i}function E(i,l){var v,r;const b=e(p)?((v=t.root.value)==null?void 0:v.offsetHeight)||0:((r=t.root.value)==null?void 0:r.offsetWidth)||0;return N.value?b*((2-d)*(i-l)+1)/4:i<l?-(1+d)*b/4:(3+d)*b/4}function S(i,l,v){const r=t.root.value;return r?((v?r.offsetHeight:r.offsetWidth)||0)*(i-l):0}const M=(i,l,v)=>{var r;const b=e(k),L=(r=t.items.value.length)!=null?r:Number.NaN,c=i===l;!b&&!Ve(v)&&(T.value=c||i===v),!c&&L>2&&t.loop&&(i=B(i,l,L));const f=e(p);I.value=c,b?(N.value=Math.round(Math.abs(i-l))<=1,m.value=E(i,l),C.value=e(I)?1:d):m.value=S(i,l,f),w.value=!0,c&&u.value&&t.setContainerHeight(u.value.offsetHeight)};function H(){if(t&&e(k)){const i=t.items.value.findIndex(({uid:l})=>l===a.uid);t.setActiveItem(i)}}return ce(()=>{t.addItem({props:o,states:Pe({hover:n,translate:m,scale:C,active:I,ready:w,inStage:N,animating:T}),uid:a.uid,translateItem:M})}),Re(()=>{t.removeItem(a.uid)}),{carouselItemRef:u,active:I,animating:T,hover:n,inStage:N,isVertical:p,translate:m,isCardType:k,scale:C,ready:w,handleItemClick:H}},rt=J({name:"ElCarouselItem"}),lt=J({...rt,props:nt,setup(o){const g=o,t=ve("carousel"),{carouselItemRef:a,active:d,animating:u,hover:n,inStage:m,isVertical:C,translate:I,isCardType:w,scale:N,ready:T,handleItemClick:k}=ot(g),p=P(()=>{const E=`${`translate${e(C)?"Y":"X"}`}(${e(I)}px)`,S=`scale(${e(N)})`;return{transform:[E,S].join(" ")}});return(B,E)=>q((A(),R("div",{ref_key:"carouselItemRef",ref:a,class:O([e(t).e("item"),e(t).is("active",e(d)),e(t).is("in-stage",e(m)),e(t).is("hover",e(n)),e(t).is("animating",e(u)),{[e(t).em("item","card")]:e(w),[e(t).em("item","card-vertical")]:e(w)&&e(C)}]),style:me(e(p)),onClick:E[0]||(E[0]=(...S)=>e(k)&&e(k)(...S))},[e(w)?q((A(),R("div",{key:0,class:O(e(t).e("mask"))},null,2)),[[G,!e(d)]]):W("v-if",!0),de(B.$slots,"default")],6)),[[G,e(T)]])}});var ge=he(lt,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/carousel/src/carousel-item.vue"]]);const dt=ze(st,{CarouselItem:ge}),mt=De(ge);export{dt as E,mt as a,re as t};
