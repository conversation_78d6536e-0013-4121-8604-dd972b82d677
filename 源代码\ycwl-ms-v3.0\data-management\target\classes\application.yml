server:
  port: 8085
spring:
  application:
    name: datamanagement
  cloud:
    nacos:
      server-addr: localhost:8848 # nacos地址
  mvc:
    servlet:
      load-on-startup: 1
mybatis:
  type-aliases-package: com.ict.datamanagement.domain.entity
  configuration:
    map-underscore-to-camel-case: true
logging:
  level:
    cn.itcast: debug
knife4j:
  enable: false

mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.ict.datamanagement.domain.entity
  global-config:
    db-config:
      id-type: auto
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
route:
  version-limit: 3   
file:
  DOWNLOAD_PATH: E:\www\wwwroot\ycwl\ycwl-ms\data-management\data\Download\ 
  UPLOAD_PATH: E:\www\wwwroot\ycwl\ycwl-ms\data-management\data\uploadFile\
  DOWNLOAD_NULL_FROM_PATH: E:\www\wwwroot\ycwl\ycwl-ms\data-management\data\nullFrom\