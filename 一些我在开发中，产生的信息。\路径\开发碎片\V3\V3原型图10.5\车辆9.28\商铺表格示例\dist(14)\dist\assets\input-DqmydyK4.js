import{ba as Z,bb as Q,aC as nt,bc as O,bd as ue,be as Ke,k as f,b5 as st,L as De,r as F,as as q,m as J,i as ze,f as rt,ag as lt,h as se,l as re,d as _e,av as it,a2 as ut,j as Ve,G as z,z as ct,C as dt,X as pt,a5 as ft,o as m,c as S,K as y,N as le,t as g,u as o,s as G,a as V,p as E,w as B,q as Y,J as ie,b as vt,M as mt,ay as yt,S as X,n as ht,al as Ne,v as gt}from"./index-C0QCllTd.js";import{p as bt,q as wt,i as xt,r as St}from"./_initCloneObject-BmTtMqsv.js";import{u as Ct,m as Et,b as It}from"./index-m25zEilF.js";import{N as je,i as Fe,k as kt,L as Pt,c as Tt,K as zt,ah as Vt,al as Nt,am as Ft,E as L,Y as Ot,_ as Mt}from"./base-kpSIrADU.js";const At=()=>je&&/firefox/i.test(window.navigator.userAgent);var ce=Z(Q,"WeakMap"),$t=bt(Object.keys,Object),Rt=Object.prototype,Bt=Rt.hasOwnProperty;function Lt(t){if(!wt(t))return $t(t);var s=[];for(var n in Object(t))Bt.call(t,n)&&n!="constructor"&&s.push(n);return s}function Kt(t){return xt(t)?St(t):Lt(t)}function Dt(t,s){for(var n=-1,a=s.length,l=t.length;++n<a;)t[l+n]=s[n];return t}function _t(t,s){for(var n=-1,a=t==null?0:t.length,l=0,i=[];++n<a;){var c=t[n];s(c,n,t)&&(i[l++]=c)}return i}function jt(){return[]}var Ht=Object.prototype,Wt=Ht.propertyIsEnumerable,Oe=Object.getOwnPropertySymbols,Ut=Oe?function(t){return t==null?[]:(t=Object(t),_t(Oe(t),function(s){return Wt.call(t,s)}))}:jt;function Gt(t,s,n){var a=s(t);return nt(t)?a:Dt(a,n(t))}function Ea(t){return Gt(t,Kt,Ut)}var de=Z(Q,"DataView"),pe=Z(Q,"Promise"),fe=Z(Q,"Set"),Me="[object Map]",Yt="[object Object]",Ae="[object Promise]",$e="[object Set]",Re="[object WeakMap]",Be="[object DataView]",Xt=O(de),qt=O(ue),Jt=O(pe),Zt=O(fe),Qt=O(ce),N=Ke;(de&&N(new de(new ArrayBuffer(1)))!=Be||ue&&N(new ue)!=Me||pe&&N(pe.resolve())!=Ae||fe&&N(new fe)!=$e||ce&&N(new ce)!=Re)&&(N=function(t){var s=Ke(t),n=s==Yt?t.constructor:void 0,a=n?O(n):"";if(a)switch(a){case Xt:return Be;case qt:return Me;case Jt:return Ae;case Zt:return $e;case Qt:return Re}return s});function ea(t){return t==null}class ta extends Error{constructor(s){super(s),this.name="ElementPlusError"}}function Ia(t,s){throw new ta(`[${t}] ${s}`)}function ka(t,s){}const ve="update:modelValue",Pa="change",Ta="input",aa=t=>/([\uAC00-\uD7AF\u3130-\u318F])+/gi.test(t),oa=["class","style"],na=/^on[A-Z]/,sa=(t={})=>{const{excludeListeners:s=!1,excludeKeys:n}=t,a=f(()=>((n==null?void 0:n.value)||[]).concat(oa)),l=De();return l?f(()=>{var i;return st(Object.entries((i=l.proxy)==null?void 0:i.$attrs).filter(([c])=>!a.value.includes(c)&&!(s&&na.test(c))))}):f(()=>({}))};function ra(t){const s=F();function n(){if(t.value==null)return;const{selectionStart:l,selectionEnd:i,value:c}=t.value;if(l==null||i==null)return;const w=c.slice(0,Math.max(0,l)),d=c.slice(Math.max(0,i));s.value={selectionStart:l,selectionEnd:i,value:c,beforeTxt:w,afterTxt:d}}function a(){if(t.value==null||s.value==null)return;const{value:l}=t.value,{beforeTxt:i,afterTxt:c,selectionStart:w}=s.value;if(i==null||c==null||w==null)return;let d=l.length;if(l.endsWith(c))d=l.length-c.length;else if(l.startsWith(i))d=i.length;else{const b=i[w-1],v=l.indexOf(b,w-1);v!==-1&&(d=v+1)}t.value.setSelectionRange(d,d)}return[n,a]}function la(t,{afterFocus:s,afterBlur:n}={}){const a=De(),{emit:l}=a,i=q(),c=F(!1),w=v=>{c.value||(c.value=!0,l("focus",v),s==null||s())},d=v=>{var p;v.relatedTarget&&((p=i.value)!=null&&p.contains(v.relatedTarget))||(c.value=!1,l("blur",v),n==null||n())},b=()=>{var v;(v=t.value)==null||v.focus()};return J(i,v=>{v&&v.setAttribute("tabindex","-1")}),Ct(i,"click",b),{wrapperRef:i,isFocused:c,handleFocus:w,handleBlur:d}}let x;const ia=`
  height:0 !important;
  visibility:hidden !important;
  ${At()?"":"overflow:hidden !important;"}
  position:absolute !important;
  z-index:-1000 !important;
  top:0 !important;
  right:0 !important;
`,ua=["letter-spacing","line-height","padding-top","padding-bottom","font-family","font-weight","font-size","text-rendering","text-transform","width","text-indent","padding-left","padding-right","border-width","box-sizing"];function ca(t){const s=window.getComputedStyle(t),n=s.getPropertyValue("box-sizing"),a=Number.parseFloat(s.getPropertyValue("padding-bottom"))+Number.parseFloat(s.getPropertyValue("padding-top")),l=Number.parseFloat(s.getPropertyValue("border-bottom-width"))+Number.parseFloat(s.getPropertyValue("border-top-width"));return{contextStyle:ua.map(c=>`${c}:${s.getPropertyValue(c)}`).join(";"),paddingSize:a,borderSize:l,boxSizing:n}}function Le(t,s=1,n){var a;x||(x=document.createElement("textarea"),document.body.appendChild(x));const{paddingSize:l,borderSize:i,boxSizing:c,contextStyle:w}=ca(t);x.setAttribute("style",`${w};${ia}`),x.value=t.value||t.placeholder||"";let d=x.scrollHeight;const b={};c==="border-box"?d=d+i:c==="content-box"&&(d=d-l),x.value="";const v=x.scrollHeight-l;if(ze(s)){let p=v*s;c==="border-box"&&(p=p+l+i),d=Math.max(p,d),b.minHeight=`${p}px`}if(ze(n)){let p=v*n;c==="border-box"&&(p=p+l+i),d=Math.min(p,d)}return b.height=`${d}px`,(a=x.parentNode)==null||a.removeChild(x),x=void 0,b}const da=rt({id:{type:String,default:void 0},size:lt,disabled:Boolean,modelValue:{type:se([String,Number,Object]),default:""},type:{type:String,default:"text"},resize:{type:String,values:["none","both","horizontal","vertical"]},autosize:{type:se([Boolean,Object]),default:!1},autocomplete:{type:String,default:"off"},formatter:{type:Function},parser:{type:Function},placeholder:{type:String},form:{type:String},readonly:{type:Boolean,default:!1},clearable:{type:Boolean,default:!1},showPassword:{type:Boolean,default:!1},showWordLimit:{type:Boolean,default:!1},suffixIcon:{type:Fe},prefixIcon:{type:Fe},containerRole:{type:String,default:void 0},label:{type:String,default:void 0},tabindex:{type:[String,Number],default:0},validateEvent:{type:Boolean,default:!0},inputStyle:{type:se([Object,Array,String]),default:()=>Et({})},autofocus:{type:Boolean,default:!1}}),pa={[ve]:t=>re(t),input:t=>re(t),change:t=>re(t),focus:t=>t instanceof FocusEvent,blur:t=>t instanceof FocusEvent,clear:()=>!0,mouseleave:t=>t instanceof MouseEvent,mouseenter:t=>t instanceof MouseEvent,keydown:t=>t instanceof Event,compositionstart:t=>t instanceof CompositionEvent,compositionupdate:t=>t instanceof CompositionEvent,compositionend:t=>t instanceof CompositionEvent},fa=["role"],va=["id","type","disabled","formatter","parser","readonly","autocomplete","tabindex","aria-label","placeholder","form","autofocus"],ma=["id","tabindex","disabled","readonly","autocomplete","aria-label","placeholder","form","autofocus"],ya=_e({name:"ElInput",inheritAttrs:!1}),ha=_e({...ya,props:da,emits:pa,setup(t,{expose:s,emit:n}){const a=t,l=it(),i=ut(),c=f(()=>{const e={};return a.containerRole==="combobox"&&(e["aria-haspopup"]=l["aria-haspopup"],e["aria-owns"]=l["aria-owns"],e["aria-expanded"]=l["aria-expanded"]),e}),w=f(()=>[a.type==="textarea"?ye.b():u.b(),u.m(He.value),u.is("disabled",k.value),u.is("exceed",Ye.value),{[u.b("group")]:i.prepend||i.append,[u.bm("group","append")]:i.append,[u.bm("group","prepend")]:i.prepend,[u.m("prefix")]:i.prefix||a.prefixIcon,[u.m("suffix")]:i.suffix||a.suffixIcon||a.clearable||a.showPassword,[u.bm("suffix","password-clear")]:W.value&&ae.value},l.class]),d=f(()=>[u.e("wrapper"),u.is("focus",te.value)]),b=sa({excludeKeys:f(()=>Object.keys(c.value))}),{form:v,formItem:p}=kt(),{inputId:me}=Pt(a,{formItemContext:p}),He=Tt(),k=zt(),u=Ve("input"),ye=Ve("textarea"),K=q(),C=q(),ee=F(!1),M=F(!1),D=F(!1),he=F(),_=q(a.inputStyle),P=f(()=>K.value||C.value),{wrapperRef:We,isFocused:te,handleFocus:j,handleBlur:H}=la(P,{afterBlur(){var e;a.validateEvent&&((e=p==null?void 0:p.validate)==null||e.call(p,"blur").catch(r=>void 0))}}),ge=f(()=>{var e;return(e=v==null?void 0:v.statusIcon)!=null?e:!1}),A=f(()=>(p==null?void 0:p.validateState)||""),be=f(()=>A.value&&Vt[A.value]),Ue=f(()=>D.value?Nt:Ft),Ge=f(()=>[l.style,a.inputStyle]),we=f(()=>[a.inputStyle,_.value,{resize:a.resize}]),I=f(()=>ea(a.modelValue)?"":String(a.modelValue)),W=f(()=>a.clearable&&!k.value&&!a.readonly&&!!I.value&&(te.value||ee.value)),ae=f(()=>a.showPassword&&!k.value&&!a.readonly&&!!I.value&&(!!I.value||te.value)),T=f(()=>a.showWordLimit&&!!b.value.maxlength&&(a.type==="text"||a.type==="textarea")&&!k.value&&!a.readonly&&!a.showPassword),oe=f(()=>I.value.length),Ye=f(()=>!!T.value&&oe.value>Number(b.value.maxlength)),Xe=f(()=>!!i.suffix||!!a.suffixIcon||W.value||a.showPassword||T.value||!!A.value&&ge.value),[qe,Je]=ra(K);It(C,e=>{if(Ze(),!T.value||a.resize!=="both")return;const r=e[0],{width:h}=r.contentRect;he.value={right:`calc(100% - ${h+15+6}px)`}});const $=()=>{const{type:e,autosize:r}=a;if(!(!je||e!=="textarea"||!C.value))if(r){const h=Ne(r)?r.minRows:void 0,U=Ne(r)?r.maxRows:void 0,Te=Le(C.value,h,U);_.value={overflowY:"hidden",...Te},z(()=>{C.value.offsetHeight,_.value=Te})}else _.value={minHeight:Le(C.value).minHeight}},Ze=(e=>{let r=!1;return()=>{var h;if(r||!a.autosize)return;((h=C.value)==null?void 0:h.offsetParent)===null||(e(),r=!0)}})($),R=()=>{const e=P.value,r=a.formatter?a.formatter(I.value):I.value;!e||e.value===r||(e.value=r)},ne=async e=>{qe();let{value:r}=e.target;if(a.formatter&&(r=a.parser?a.parser(r):r),!M.value){if(r===I.value){R();return}n(ve,r),n("input",r),await z(),R(),Je()}},xe=e=>{n("change",e.target.value)},Se=e=>{n("compositionstart",e),M.value=!0},Ce=e=>{var r;n("compositionupdate",e);const h=(r=e.target)==null?void 0:r.value,U=h[h.length-1]||"";M.value=!aa(U)},Ee=e=>{n("compositionend",e),M.value&&(M.value=!1,ne(e))},Qe=()=>{D.value=!D.value,Ie()},Ie=async()=>{var e;await z(),(e=P.value)==null||e.focus()},et=()=>{var e;return(e=P.value)==null?void 0:e.blur()},tt=e=>{ee.value=!1,n("mouseleave",e)},at=e=>{ee.value=!0,n("mouseenter",e)},ke=e=>{n("keydown",e)},ot=()=>{var e;(e=P.value)==null||e.select()},Pe=()=>{n(ve,""),n("change",""),n("clear"),n("input","")};return J(()=>a.modelValue,()=>{var e;z(()=>$()),a.validateEvent&&((e=p==null?void 0:p.validate)==null||e.call(p,"change").catch(r=>void 0))}),J(I,()=>R()),J(()=>a.type,async()=>{await z(),R(),$()}),ct(()=>{!a.formatter&&a.parser,R(),z($)}),s({input:K,textarea:C,ref:P,textareaStyle:we,autosize:dt(a,"autosize"),focus:Ie,blur:et,select:ot,clear:Pe,resizeTextarea:$}),(e,r)=>pt((m(),S("div",ie(o(c),{class:o(w),style:o(Ge),role:e.containerRole,onMouseenter:at,onMouseleave:tt}),[y(" input "),e.type!=="textarea"?(m(),S(le,{key:0},[y(" prepend slot "),e.$slots.prepend?(m(),S("div",{key:0,class:g(o(u).be("group","prepend"))},[G(e.$slots,"prepend")],2)):y("v-if",!0),V("div",{ref_key:"wrapperRef",ref:We,class:g(o(d))},[y(" prefix slot "),e.$slots.prefix||e.prefixIcon?(m(),S("span",{key:0,class:g(o(u).e("prefix"))},[V("span",{class:g(o(u).e("prefix-inner"))},[G(e.$slots,"prefix"),e.prefixIcon?(m(),E(o(L),{key:0,class:g(o(u).e("icon"))},{default:B(()=>[(m(),E(Y(e.prefixIcon)))]),_:1},8,["class"])):y("v-if",!0)],2)],2)):y("v-if",!0),V("input",ie({id:o(me),ref_key:"input",ref:K,class:o(u).e("inner")},o(b),{type:e.showPassword?D.value?"text":"password":e.type,disabled:o(k),formatter:e.formatter,parser:e.parser,readonly:e.readonly,autocomplete:e.autocomplete,tabindex:e.tabindex,"aria-label":e.label,placeholder:e.placeholder,style:e.inputStyle,form:a.form,autofocus:a.autofocus,onCompositionstart:Se,onCompositionupdate:Ce,onCompositionend:Ee,onInput:ne,onFocus:r[0]||(r[0]=(...h)=>o(j)&&o(j)(...h)),onBlur:r[1]||(r[1]=(...h)=>o(H)&&o(H)(...h)),onChange:xe,onKeydown:ke}),null,16,va),y(" suffix slot "),o(Xe)?(m(),S("span",{key:1,class:g(o(u).e("suffix"))},[V("span",{class:g(o(u).e("suffix-inner"))},[!o(W)||!o(ae)||!o(T)?(m(),S(le,{key:0},[G(e.$slots,"suffix"),e.suffixIcon?(m(),E(o(L),{key:0,class:g(o(u).e("icon"))},{default:B(()=>[(m(),E(Y(e.suffixIcon)))]),_:1},8,["class"])):y("v-if",!0)],64)):y("v-if",!0),o(W)?(m(),E(o(L),{key:1,class:g([o(u).e("icon"),o(u).e("clear")]),onMousedown:mt(o(yt),["prevent"]),onClick:Pe},{default:B(()=>[vt(o(Ot))]),_:1},8,["class","onMousedown"])):y("v-if",!0),o(ae)?(m(),E(o(L),{key:2,class:g([o(u).e("icon"),o(u).e("password")]),onClick:Qe},{default:B(()=>[(m(),E(Y(o(Ue))))]),_:1},8,["class"])):y("v-if",!0),o(T)?(m(),S("span",{key:3,class:g(o(u).e("count"))},[V("span",{class:g(o(u).e("count-inner"))},X(o(oe))+" / "+X(o(b).maxlength),3)],2)):y("v-if",!0),o(A)&&o(be)&&o(ge)?(m(),E(o(L),{key:4,class:g([o(u).e("icon"),o(u).e("validateIcon"),o(u).is("loading",o(A)==="validating")])},{default:B(()=>[(m(),E(Y(o(be))))]),_:1},8,["class"])):y("v-if",!0)],2)],2)):y("v-if",!0)],2),y(" append slot "),e.$slots.append?(m(),S("div",{key:1,class:g(o(u).be("group","append"))},[G(e.$slots,"append")],2)):y("v-if",!0)],64)):(m(),S(le,{key:1},[y(" textarea "),V("textarea",ie({id:o(me),ref_key:"textarea",ref:C,class:o(ye).e("inner")},o(b),{tabindex:e.tabindex,disabled:o(k),readonly:e.readonly,autocomplete:e.autocomplete,style:o(we),"aria-label":e.label,placeholder:e.placeholder,form:a.form,autofocus:a.autofocus,onCompositionstart:Se,onCompositionupdate:Ce,onCompositionend:Ee,onInput:ne,onFocus:r[2]||(r[2]=(...h)=>o(j)&&o(j)(...h)),onBlur:r[3]||(r[3]=(...h)=>o(H)&&o(H)(...h)),onChange:xe,onKeydown:ke}),null,16,ma),o(T)?(m(),S("span",{key:0,style:ht(he.value),class:g(o(u).e("count"))},X(o(oe))+" / "+X(o(b).maxlength),7)):y("v-if",!0)],64))],16,fa)),[[ft,e.type!=="hidden"]])}});var ga=Mt(ha,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/input/src/input.vue"]]);const za=gt(ga);export{Pa as C,za as E,Ta as I,ve as U,Dt as a,Gt as b,N as c,ka as d,Ea as e,aa as f,Ut as g,ea as i,Kt as k,jt as s,Ia as t,sa as u};
