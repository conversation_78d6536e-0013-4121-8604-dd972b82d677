package com.ict.datamanagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ict.datamanagement.domain.entity.Group;
import com.ict.datamanagement.domain.vo.GroupVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【group】的数据库操作Service
* @createDate 2024-04-22 13:45:36
*/
public interface GroupService extends IService<Group> {

    List<GroupVO> getOptionalData();

    GroupVO getGroupVO(Group group);
}
