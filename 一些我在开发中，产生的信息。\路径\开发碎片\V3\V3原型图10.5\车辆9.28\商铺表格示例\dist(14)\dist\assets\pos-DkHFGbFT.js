import{v as Le,w as Ve,x as Qe,n as We,E as Ce,h as Xe,y as Ye,A as Ze,o as He,r as et,l as tt,m as lt}from"./base-kpSIrADU.js";/* empty css             */import{a as de,E as pe,b as Ne}from"./table-column-DZpqkK6R.js";import{E as ce}from"./input-DqmydyK4.js";import{a as me,E as fe,b as ot}from"./select-BOcQ2ynX.js";import"./scrollbar-BNeK4Yi-.js";import"./checkbox-DWZ5xHlw.js";import{E as Y}from"./button-IGKrEYb9.js";import{a as ge,E as _e}from"./form-item-Bd-FvCZ5.js";import{d as Z,r as c,P as ee,c as I,b as t,w as l,u as n,Q as X,o as b,a,R as V,p as T,m as at,N as ae,K as J,T as xe,S as M,Y as nt,k as oe,z as st,X as K,ab as it,M as he,G as rt,W as ut}from"./index-C0QCllTd.js";import{E as te}from"./overlay-D06mCCGK.js";import{a as F,r as O}from"./index-m25zEilF.js";import{_ as le}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{g as dt,E as pt,a as ct}from"./progress-BWKU0l_-.js";import{u as mt}from"./pick-BIEvBaQG.js";import{p as ft}from"./pickMap-CANvhVXt.js";import"./_commonjsHelpers-BbMlrU8H.js";import"./merge-B3fxVp56.js";import"./_initCloneObject-BmTtMqsv.js";import"./flatten-BP0fiJV-.js";import"./castArray-CSO3s-vM.js";import"./index-DOdSMika.js";import"./getMapKey-C0z490Cj.js";import"./modifyUserAgent-Ct74EQNt.js";import"./index-Bp4b1Vvq.js";const gt={class:"add"},_t={class:"btns"},vt=Z({__name:"addPost",emits:["add"],setup(h,{expose:D,emit:x}){const v=c(!1);D({addPostOpen:v});const U=x,g=ee({pickupName:"",status:"",longitude:"",latitude:"",type:"",pickupAddress:""}),f=c(),C=ee({pickupName:[{required:!0,message:"不能为空",trigger:"blur"}],status:[{required:!0,message:"不能为空",trigger:"blur"}],longitude:[{required:!0,message:"不能为空",trigger:"blur"}],latitude:[{required:!0,message:"不能为空",trigger:"blur"}],type:[{required:!0,message:"不能为空",trigger:"blur"}],pickupAddress:[{required:!0,message:"不能为空",trigger:"blur"}]}),s=()=>{f.value.resetFields(),v.value=!1},z=()=>{f.value.validate(P=>{P?(U("add",g),v.value=!1):F({type:"warning",message:"请输入正确的信息"})})};return(P,_)=>{const m=ce,y=ge,u=me,p=fe,N=_e,w=Y,A=te;return b(),I("div",gt,[t(A,{modelValue:n(v),"onUpdate:modelValue":_[6]||(_[6]=d=>X(v)?v.value=d:null),modal:!1,title:"添加选址",class:"flex"},{default:l(()=>[t(N,{"label-width":"auto",width:"100%",inline:!0,class:"areaForm",model:g,ref_key:"formRef",ref:f,rules:C},{default:l(()=>[t(y,{label:"名称",prop:"pickupName"},{default:l(()=>[t(m,{placeholder:"请输入",modelValue:g.pickupName,"onUpdate:modelValue":_[0]||(_[0]=d=>g.pickupName=d)},null,8,["modelValue"])]),_:1}),t(y,{label:"状态",prop:"status"},{default:l(()=>[t(p,{placeholder:"请选择",modelValue:g.status,"onUpdate:modelValue":_[1]||(_[1]=d=>g.status=d)},{default:l(()=>[t(u,{label:"启用",value:2}),t(u,{label:"禁用",value:1})]),_:1},8,["modelValue"])]),_:1}),t(y,{label:"经度",prop:"longitude"},{default:l(()=>[t(m,{placeholder:"请输入",modelValue:g.longitude,"onUpdate:modelValue":_[2]||(_[2]=d=>g.longitude=d)},null,8,["modelValue"])]),_:1}),t(y,{label:"纬度",prop:"latitude"},{default:l(()=>[t(m,{placeholder:"请输入",modelValue:g.latitude,"onUpdate:modelValue":_[3]||(_[3]=d=>g.latitude=d)},null,8,["modelValue"])]),_:1}),t(y,{label:"取货地类型",prop:"type"},{default:l(()=>[t(p,{placeholder:"请选择",modelValue:g.type,"onUpdate:modelValue":_[4]||(_[4]=d=>g.type=d)},{default:l(()=>[t(u,{label:"邮局",value:"邮局"}),t(u,{label:"烟站",value:"烟站"}),t(u,{label:"村委会",value:"村委会"}),t(u,{label:"其他",value:"其他"})]),_:1},8,["modelValue"])]),_:1}),t(y,{label:"详细地址",prop:"pickupAddress"},{default:l(()=>[t(m,{placeholder:"请输入",modelValue:g.pickupAddress,"onUpdate:modelValue":_[5]||(_[5]=d=>g.pickupAddress=d)},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"]),a("div",_t,[t(w,{type:"primary",onClick:s},{default:l(()=>_[7]||(_[7]=[V("取消")])),_:1}),t(w,{type:"primary",style:{"margin-left":"100px"},onClick:z},{default:l(()=>_[8]||(_[8]=[V("确定")])),_:1})])]),_:1},8,["modelValue"])])}}}),yt=le(vt,[["__scopeId","data-v-375262f8"]]),kt={class:"dialog-footer"},bt=Z({__name:"updateTip",setup(h,{expose:D}){const x=c(!1);D({confirmDialogVis:x});function v(){x.value=!1}function U(){x.value=!1}return(g,f)=>{const C=Y,s=te;return b(),T(s,{modelValue:n(x),"onUpdate:modelValue":f[0]||(f[0]=z=>X(x)?x.value=z:null),title:"提示",class:"transform",width:"40%","align-center":"","close-on-click-modal":!1},{footer:l(()=>[a("div",kt,[t(C,{onClick:U,type:"primary"},{default:l(()=>f[1]||(f[1]=[V("取消")])),_:1}),t(C,{type:"primary",onClick:v},{default:l(()=>f[2]||(f[2]=[V("确定")])),_:1})])]),default:l(()=>[f[3]||(f[3]=a("div",{class:"content"},"选择之后会将所有关联的商户由已分配设置为未分配",-1))]),_:1},8,["modelValue"])}}}),ht={class:"change"},wt={class:"flex-center"},Lt={class:"btns"},Vt=Z({__name:"changePost",props:{row:{type:Object,default:{}}},emits:["change"],setup(h,{expose:D,emit:x}){const v=c(!1);D({changePostOpen:v});const U=x,g=c([]),f=c(),C=h,s=c(JSON.parse(JSON.stringify(C.row)));at(v,u=>{u?(g.value=[],s.value=JSON.parse(JSON.stringify(C.row))):s.value={}},{deep:!0,immediate:!0});const z=c(),P=()=>{v.value=!1};function _(u){s.value.stores=s.value.stores.filter(p=>p.storeId!==u),g.value.push(u)}function m(u){u!==3&&(f.value.confirmDialogVis=!0)}const y=()=>{s.value.storeIds=g.value,delete s.value.stores,console.log(s.value),console.log(C.row),U("change",s.value),v.value=!1};return(u,p)=>{const N=ce,w=ge,A=me,d=fe,L=ot,r=_e,S=Y,E=te;return b(),I(ae,null,[a("div",ht,[t(E,{modelValue:n(v),"onUpdate:modelValue":p[6]||(p[6]=R=>X(v)?v.value=R:null),width:"60%",height:"70%",modal:!1,title:"修改选址"},{footer:l(()=>[a("div",Lt,[t(S,{type:"primary",onClick:P},{default:l(()=>p[8]||(p[8]=[V("取消")])),_:1}),t(S,{type:"primary",style:{"margin-left":"100px"},onClick:y},{default:l(()=>p[9]||(p[9]=[V("确定")])),_:1})])]),default:l(()=>[a("div",wt,[t(r,{"label-width":"auto",width:"100%",class:"areaForm",model:n(s),ref_key:"formRef",ref:z},{default:l(()=>[t(w,{label:"名称",prop:"pickupName"},{default:l(()=>[t(N,{placeholder:"请输入",modelValue:n(s).pickupName,"onUpdate:modelValue":p[0]||(p[0]=R=>n(s).pickupName=R)},null,8,["modelValue"])]),_:1}),t(w,{label:"状态",prop:"status"},{default:l(()=>[t(d,{placeholder:"请选择",modelValue:n(s).status,"onUpdate:modelValue":p[1]||(p[1]=R=>n(s).status=R),onChange:m},{default:l(()=>[t(A,{label:"启用(已分配)",value:3}),t(A,{label:"启用(未分配)",value:2}),t(A,{label:"禁用",value:1})]),_:1},8,["modelValue"])]),_:1}),t(w,{label:"经度",prop:"longitude"},{default:l(()=>[t(N,{placeholder:"请输入",modelValue:n(s).longitude,"onUpdate:modelValue":p[2]||(p[2]=R=>n(s).longitude=R)},null,8,["modelValue"])]),_:1}),t(w,{label:"纬度",prop:"latitude"},{default:l(()=>[t(N,{placeholder:"请输入",modelValue:n(s).latitude,"onUpdate:modelValue":p[3]||(p[3]=R=>n(s).latitude=R)},null,8,["modelValue"])]),_:1}),t(w,{label:"取货地类型",prop:"type"},{default:l(()=>[t(d,{placeholder:"请选择",modelValue:n(s).type,"onUpdate:modelValue":p[4]||(p[4]=R=>n(s).type=R)},{default:l(()=>[t(A,{label:"邮局",value:"邮局"}),t(A,{label:"烟站",value:"烟站"}),t(A,{label:"村委会",value:"村委会"}),t(A,{label:"其他",value:"其他"})]),_:1},8,["modelValue"])]),_:1}),t(w,{label:"详细地址",prop:"pickupAddress"},{default:l(()=>[t(N,{placeholder:"请输入",modelValue:n(s).pickupAddress,"onUpdate:modelValue":p[5]||(p[5]=R=>n(s).pickupAddress=R)},null,8,["modelValue"])]),_:1}),t(w,{label:"关联商户",prop:"storeIds"},{default:l(()=>[a("div",null,[n(s).stores&&n(s).stores.length<=0?(b(),T(L,{key:0},{default:l(()=>p[7]||(p[7]=[V("无")])),_:1})):J("",!0),(b(!0),I(ae,null,xe(n(s).stores,(R,Q)=>(b(),T(L,{closable:"",key:R.storeId,onClose:j=>_(R.storeId)},{default:l(()=>[V(M(R.storeName),1)]),_:2},1032,["onClose"]))),128))])]),_:1})]),_:1},8,["model"])])]),_:1},8,["modelValue"])]),t(bt,{ref_key:"tip",ref:f},null,512)],64)}}}),Ct=le(Vt,[["__scopeId","data-v-d2adeedb"]]);function Nt(){return O.get({url:"/pickup/pickupLocation/pickupLocationExport",responseType:"blob"})}function xt(h,D){return O.post({url:"/pickup/pickupLocation/pickupLocationImport",data:h,headers:{accept:"*/*","Content-Type":"multipart/form-data"},onUploadProgress:D.onUploadProgress,signal:D.signal})}function Ut(h){return O.get({url:"/datamanagement/getImportLogs",params:h})}function St(h){return O.get({url:"/pickup/pickupLocation/pickupLocationList",params:h})}function Dt(h){return O.put({url:"/pickup/pickupLocation/pickupLocationUpdate",params:h,paramsSerializer:D=>new URLSearchParams(D).toString()})}function Rt(h){return O.post({url:"/pickup/pickupLocation/pickupLocationAdd",params:h})}function Et(h){return O.get({url:"/datamanagement/downloadLogs",headers:{"Content-Type":"application/x-download"},responseType:"blob",params:h})}function zt(h){return O.delete({url:"/datamanagement/deleteImportLogs",params:h})}function At(h){return O.post({url:"/pickup/pickupUser/downloadNullFrom",headers:{"Content-Type":"application/x-download"},responseType:"blob",params:h})}function Ft(){return O.patch({url:"/pickup/pickupLocation/recalculate"})}function Tt(h){return O.patch({url:"/pickup/pickupLocation/recalculate",params:h,paramsSerializer:D=>new URLSearchParams(D).toString()})}function Pt(){return O.get({url:"/pickup/pickupLocation/toBeaSsignedList"})}const ve=nt("location",()=>{async function h(){return await Pt()}async function D(m){return await Rt(m)}async function x(m){return(await St(m)).data}async function v(m){return await Dt(m)}async function U(m){return(await Ut(m)).data}async function g(){return await Nt()}async function f(m){return await At(m)}async function C(m){return await Et(m)}async function s(m){return await zt(m)}async function z(m,y){return await xt(m,y)}async function P(m){return await Tt(m)}async function _(){return await Ft()}return{addLocationData:D,getUnassignedListData:h,getLocationListData:x,updateLocationData:v,importLog:U,downloadNullForm:f,uploadFile:z,editLocationData:P,downloadLocationLog:C,deleteLocationLog:s,exportLocationData:g,calculate:_}}),It={class:"download"},Ot={class:"dialog-footer"},$t=Z({__name:"uploadTable",setup(h,{expose:D}){const x=c([]),v=c(["csv","xls","xlsx"]),U=c([{fileName:"",fileSize:""}]),g=ve(),f=c(!1),C=c(!1),s=c(),z=c(0),P=new AbortController;D({uploadVisible:f});function _(){s.value.clearFiles(),f.value=!1}function m(){var L;(L=s.value)==null||L.submit(),f.value=!1}async function y(){g.downloadNullForm({code:1}).then(L=>{let r=document.createElement("a");r.download="选址空白表格模版.xlsx",r.style.display="none";let S=URL.createObjectURL(L);r.href=S,document.body.appendChild(r),r.click(),URL.revokeObjectURL(S),document.body.removeChild(r)})}function u(){s.value.clearFiles()}function p(){U.value=[{}],z.value<100&&P.abort()}function N(){var L;z.value==100?F.error("已上传成功,请勿重复上传"):(L=s.value)==null||L.submit()}function w(L){if(L.type!=""||L.type!=null||L.type!=null){const r=L.name.replace(/.+\./,"").toLowerCase();return L.size/1024/1024<20?v.value.includes(r)?(x.value[0]=L,U.value=[{fileName:x.value[0].name,fileSize:(x.value[0].size/1024).toFixed(2)+"kb"}],!0):(F.error("上传文件格式不正确!"),!1):(F.error("上传文件大小不能超过 20MB!"),!1)}}function A(L){s.value.clearFiles();const r=L[0];r.uid=dt(),s.value.handleStart(r),x.value[0]=r}function d(L){const r={signal:P.signal,onUploadProgress:E=>{z.value=Number((E.loaded/E.total*100).toFixed(1))-1}};let S=new FormData;S.append("File",L.file),S.append("Authorization",localStorage.getItem("token")),g.uploadFile(S,r).then(E=>{if(E.message==="系统异常"){F.error("系统异常"),s.value.clearFiles();return}if(E.includes("导入失败")){F.error("导入失败"),s.value.clearFiles();return}E.includes("导入成功")&&(z.value=100,F({message:"上传成功",type:"success"}),C.value=!0,f.value=!1)})}return(L,r)=>{const S=Ce,E=Y,R=pt,Q=te,j=de,ne=ct,se=pe;return b(),I(ae,null,[t(Q,{modelValue:n(f),"onUpdate:modelValue":r[0]||(r[0]=H=>X(f)?f.value=H:null),title:"导入表格",class:"transform",width:"50%","align-center":"","close-on-click-modal":!1,onClose:_},{footer:l(()=>[a("div",Ot,[t(E,{onClick:_,type:"primary"},{default:l(()=>r[5]||(r[5]=[V("取消")])),_:1}),t(E,{type:"primary",onClick:m},{default:l(()=>r[6]||(r[6]=[V(" 确定 ")])),_:1})])]),default:l(()=>[t(R,{ref_key:"uploadRef",ref:s,class:"upload-demo",drag:"",action:"",limit:1,"file-list":n(x),"http-request":d,multiple:!1,"on-exceed":A,"before-upload":w,"auto-upload":!1},{tip:l(()=>[a("div",It,[t(E,{type:"primary",icon:n(Le),class:"button",onClick:y},{default:l(()=>r[2]||(r[2]=[V("点击此处下载空格表格")])),_:1},8,["icon"])])]),default:l(()=>[t(S,{class:"el-icon--upload"},{default:l(()=>[t(n(Ve))]),_:1}),r[3]||(r[3]=a("div",{class:"el-upload__text"},[V("拖拽文件到此处"),a("em",null,"点击上传")],-1)),r[4]||(r[4]=a("div",{class:"el-upload__text",style:{width:"100%"}},[a("span",{style:{color:"red"}},"* 一次只能导入一个文件,仅支持xls, xlsx和csv格式")],-1))]),_:1},8,["file-list"])]),_:1},8,["modelValue"]),t(Q,{modelValue:n(C),"onUpdate:modelValue":r[1]||(r[1]=H=>X(C)?C.value=H:null),title:"上传文件",class:"transform",width:"50%","align-center":"","close-on-click-modal":!1,onClose:u},{default:l(()=>[t(se,{ref:"tableRef",data:n(U),"cell-style":{textAlign:"center"},"header-cell-style":{height:"1vh","text-align":"center"},size:"small","row-style":{height:"4vh"},style:{"font-size":"1vw"}},{default:l(()=>[t(j,{label:"文件名","min-width":"1%",prop:"fileName"}),t(j,{label:"大小","min-width":"1%",prop:"fileSize"}),n(U)[0].fileName?(b(),T(j,{key:0,label:"状态","min-width":"1%"},{default:l(()=>[t(ne,{"text-inside":!0,"stroke-width":26,percentage:n(z)},null,8,["percentage"])]),_:1})):J("",!0),n(U)[0].fileName?(b(),T(j,{key:1,label:"操作","min-width":"1%"},{default:l(()=>[t(E,{size:"small",type:"primary",onClick:p,icon:n(Qe)},{default:l(()=>r[7]||(r[7]=[V(" 删除 ")])),_:1},8,["icon"]),t(E,{size:"small",type:"primary",onClick:N,icon:n(We)},{default:l(()=>r[8]||(r[8]=[V(" 重传 ")])),_:1},8,["icon"])]),_:1})):J("",!0)]),_:1},8,["data"])]),_:1},8,["modelValue"])],64)}}}),Mt=le($t,[["__scopeId","data-v-9456a403"]]),jt={class:"pageDivide"},Bt=Z({__name:"uploadNote",setup(h,{expose:D}){const x=ve(),v=c(!1),U=c(),g=c(),f=c({}),C=ee({pageNum:1,pageSize:6});D({noteVisible:v,onOpenDialog:s});function s(){C.pageNum=1,y()}function z(){f.value=null}function P(u,p){x.downloadLocationLog({fileName:u,importTime:p}).then(N=>{let w=document.createElement("a");w.download=u,w.style.display="none";let A=URL.createObjectURL(N);w.href=A,document.body.appendChild(w),w.click(),URL.revokeObjectURL(A),document.body.removeChild(w)})}function _(u){x.deleteLocationLog({logsId:u}).then(()=>{F.success("删除成功"),y()})}function m(u=1){C.pageNum=u,y()}function y(){x.importLog({...C,type:"3"}).then(u=>{f.value=u,C.pageNum=u.current,g.value=u.records})}return(u,p)=>{const N=de,w=Y,A=pe,d=Ne,L=te;return b(),T(L,{modelValue:n(v),"onUpdate:modelValue":p[0]||(p[0]=r=>X(v)?v.value=r:null),title:"导入日志",class:"transform",width:"50%","align-center":"","close-on-click-modal":!1,onClose:z},{default:l(()=>[p[1]||(p[1]=a("div",{style:{"font-size":"20px"}},"最近6个月的导入记录",-1)),n(g)?(b(),T(A,{key:0,ref_key:"tableRef",ref:U,data:n(g),"cell-style":{textAlign:"center"},"header-cell-style":{height:"2vh","text-align":"center"},size:"small","row-style":{height:"4.3vh"},style:{"font-size":"1vw"}},{default:l(()=>[t(N,{label:"文件名","min-width":"1%",prop:"fileName"}),t(N,{label:"大小","min-width":"1%",prop:"fileSize"}),t(N,{label:"导入时间","min-width":"1%",prop:"importTime"}),t(N,{label:"用户","min-width":"1%",prop:"userName"}),t(N,{label:"状态","min-width":"1%",prop:"status"}),t(N,{label:"操作","min-width":"2%"},{default:l(r=>[r.row.status.includes("全部导入成功")?J("",!0):(b(),T(w,{key:0,size:"small",type:"primary",onClick:S=>P(r.row.fileName,r.row.importTime),icon:n(Le)},null,8,["onClick","icon"])),t(w,{size:"small",type:"primary",onClick:S=>_(Number(r.row.fileId)),icon:n(Xe)},null,8,["onClick","icon"])]),_:1})]),_:1},8,["data"])):J("",!0),a("div",jt,[n(f)?(b(),T(d,{key:0,layout:"prev, pager, next","current-page":n(C).pageNum,"page-size":n(C).pageSize,total:Number(n(f).total),onCurrentChange:m},null,8,["current-page","page-size","total"])):J("",!0)])]),_:1},8,["modelValue"])}}}),qt=le(Bt,[["__scopeId","data-v-ce6e0e74"]]),Gt={class:"pos"},Jt={class:"section"},Kt={class:"box"},Qt={class:"searchContent"},Wt={class:"range"},Xt={class:"search"},Yt={key:0,class:"searchView"},Zt={class:"content"},Ht={class:"group"},el={class:"btns"},tl={class:"butContent"},ll={class:"table"},ol={key:0},al={key:1},nl={key:2},sl={key:3},il={class:"divide"},rl={class:"pageDivide"},ul={class:"mapSection"},dl={class:"map"},pl={class:"mapButton"},cl={class:"waitfor"},ml={class:"merchant-container"},fl={class:"carousel-header"},gl={class:"carousel-wrapper"},_l={class:"merchant-group"},vl={class:"cardOk"},yl=["checked","onClick"],kl={class:"cardInfo"},bl={class:"distance"},we=4,hl=Z({__name:"pos",setup(h){const D=c(),x=mt(),v=c([]),U=ve(),g=c(),f=c(),C=c(),s=c(),z=c(),P=c({}),_=c(),m=c(),y=c(),u=ee({pageNum:1,pageSize:6}),p=c(""),N=c(!1),w=c(!1),A=c(),d=ee({pageNum:1,pageSize:6,pickupAddress:"",pickupName:"",status:"",type:""}),L={type:"取货柜类型",pickupName:"取货柜地址",pickupAddress:"地址",status:"颜色"},r=c(!0),S=c([]),E=c(0),R=oe(()=>S.value.length),Q=oe(()=>{const o=[];for(let e=0;e<S.value.length;e+=we)o.push(S.value.slice(e,e+we));return o}),j=oe({get:()=>S.value.every(o=>o.selected),set:o=>{S.value.forEach(e=>e.selected=o)}}),ne=o=>{const e=S.value.find(i=>i.id===o);e&&(e.selected=!e.selected)};function se(o){if(N.value){F.error("搜索状态中");return}U.addLocationData(o).then(e=>{e.msg?(F.success(e.msg),$()):F.error("失败")})}const H=o=>{D.value.getMap()&&(o.status==0||o.status==2||o.color==1||o.color==5||D.value.getMap().setZoomAndCenter(16,[o.longitude,o.latitude]))};function Ue(o){U.updateLocationData(o).then(e=>{if(e.code==200){if(F.success(e.msg),N.value){$(4);return}$()}else if(!e.code){F.error("失败");return}})}const Se=({row:o})=>{if(o.status===1)return"red";if(o.status===2)return"gray";if(o.status===3)return"gree"},De=oe(()=>Q.value[E.value]||[]),Re=()=>{E.value<Q.value.length-1&&E.value++},Ee=()=>{E.value>0&&E.value--};function ze(o){P.value=o[0]}function Ae(o=1){if(N.value){d.pageNum=o,$(4);return}u.pageNum=o,$()}function Fe(o){if(d.pageNum=1,u.pageNum=1,N.value=!0,$(4),o===1){y.value.style="background-color: rgb(121, 64, 73)";return}else if(o===2){y.value.style="background-color: #5378bc";return}else if(o===3){y.value.style="background-color: rgb(78, 138, 56)";return}}function Te(){rt(()=>{g.value.onOpenDialog(),g.value.noteVisible=!0})}function Pe(){const o=S.value.filter(e=>e.selected).map(e=>e.id);U.editLocationData({ids:o}).then(e=>{e.code==200&&(F.success(e.msg),$(2),$()),console.log(e),(!e.code||e.code==="ERR_BAD_RESPONSE")&&F.error("计算失败")})}async function $(o=0){o==2?U.getUnassignedListData().then(e=>{e.data&&(S.value=e.data.map(i=>(i.selected=!1,i)))}):o==4?(ye(),U.getLocationListData({...d}).then(e=>{N.value=!0,s.value=e,u.pageNum=e.current,_.value=e.records})):(ye(),U.getLocationListData({...u}).then(e=>{s.value=e,u.pageNum=e.current,_.value=e.records}))}async function ye(){try{r.value=!0,x.getMap().then(o=>{v.value=o.pickupUsers.map(e=>{const i={lnglat:[e.longitude,e.latitude],info:{name:e.storeName,address:e.pickupContainers?e.pickupContainers:"无",distance:e.deliveryDistance}};return e.color===3?i.type="B":e.color===4?i.type="C":e.color===2&&(i.type="D"),e.pickupContainers&&(i.info.dian=o.pickupLocationVos.filter(B=>B.pickupName===e.pickupContainers).map(B=>[B.longitude,B.latitude])),i}),v.value=[...v.value,...o.pickupLocationVos.map(e=>e.status==1?{lnglat:[e.longitude,e.latitude],type:"E",info:{name:e.pickupName}}:e.status==3?{lnglat:[e.longitude,e.latitude],type:"F",info:{name:e.pickupName,stores:e.stores.length>0?e.stores.map(i=>i.storeName).join(","):"无",posList:o.pickupUsers.filter(i=>i.pickupContainers==e.pickupName).map(i=>[i.longitude,i.latitude])}}:{lnglat:[e.longitude,e.latitude],type:"A",info:{name:e.pickupName,stores:e.stores.length>0?e.stores.map(i=>i.storeName).join(","):"无",posList:o.pickupUsers.filter(i=>i.pickupContainers==e.pickupName).map(i=>[i.longitude,i.latitude])}})],r.value=!1})}catch(o){console.error("获取数据失败",o)}}function ke(){const o=Object.entries(d).filter(([e,i])=>i!==""&&i!==null&&i!==void 0&&e!=="pageNum"&&e!=="pageSize").map(([e,i])=>`${L[e]||e}:${i}`).join("; ");p.value=o,w.value=!w.value}st(()=>{$(2),$()});function Ie(){N.value=!1,Object.keys(d).forEach(o=>{o!=="pageNum"&&o!=="pageSize"&&(d[o]="")}),y.value.style="",d.status="",d.pageNum=1,u.pageNum=1,$()}function Oe(){d.status||(d.status=0),U.getLocationListData({...d}).then(o=>{s.value=o,s.value.records.length==0&&F.error("没有查询到数据"),N.value=!0,u.pageNum=o.current,_.value=o.records})}function $e(){C.value.addPostOpen=!0}function Me(){U.exportLocationData().then(o=>{const e=window.URL.createObjectURL(o),i=document.createElement("a");i.style.display="none",i.href=e,i.setAttribute("download","定点商户信息表.xlsx"),document.body.appendChild(i),i.click(),document.body.removeChild(i)})}function je(){var o,e;if(((o=m.value)==null?void 0:o.getSelectionRows().length)>1){F.error("只能选择一条");return}else if(((e=m.value)==null?void 0:e.getSelectionRows().length)==0){F.error("没有选择");return}z.value.changePostOpen=!0}function Be(){f.value.uploadVisible=!0}function qe(o){const e=u.pageNum,i=u.pageSize;return o+1+(e-1)*i}return(o,e)=>{const i=me,B=fe,ie=ce,re=ge,Ge=_e,q=Y,G=de,Je=pe,Ke=Ne,be=Ce,W=ut("op");return b(),I("div",Gt,[a("div",Jt,[a("div",Kt,[a("div",Qt,[a("div",{class:"circle",ref_key:"circle",ref:y},null,512),a("div",Wt,[t(B,{modelValue:n(d).status,"onUpdate:modelValue":e[0]||(e[0]=k=>n(d).status=k),placeholder:"全部",style:{width:"140px"},onChange:Fe},{default:l(()=>[t(i,{label:"禁用",value:1},{default:l(()=>e[7]||(e[7]=[a("div",{class:"shopType"},[a("div",{class:"ci",style:{"background-color":"#794049"}}),a("span",null,"禁用")],-1)])),_:1}),t(i,{label:"启用(已分配)",value:3},{default:l(()=>e[8]||(e[8]=[a("div",{class:"shopType"},[a("div",{class:"ci",style:{"background-color":"#4e8a38"}}),a("span",null,"启用(已分配)")],-1)])),_:1}),t(i,{label:"启用(未分配)",value:2},{default:l(()=>e[9]||(e[9]=[a("div",{class:"shopType"},[a("div",{class:"ci",style:{"background-color":"#5378bc"}}),a("span",null,"启用(未分配)")],-1)])),_:1}),t(i,{label:"全部",value:0},{default:l(()=>e[10]||(e[10]=[a("div",{class:"shopType"},[a("div",{class:"ci",style:{"background-color":"transparent"}}),a("span",null,"全部")],-1)])),_:1})]),_:1},8,["modelValue"])]),a("div",Xt,[t(ie,{placeholder:"请点击搜索","model-value":p.value,"onUpdate:modelValue":e[1]||(e[1]=k=>p.value=k),onClick:ke},null,8,["model-value"]),w.value?(b(),I("div",Yt,[a("div",Zt,[a("div",Ht,[a("div",{class:"closeBold",onClick:ke},"x"),t(Ge,{"label-width":"auto",model:n(d),ref_key:"searchModal",ref:A,class:"searchForm"},{default:l(()=>[t(re,{label:"名称",prop:"pickupName"},{default:l(()=>[t(ie,{modelValue:n(d).pickupName,"onUpdate:modelValue":e[2]||(e[2]=k=>n(d).pickupName=k),placeholder:"请输入"},null,8,["modelValue"])]),_:1}),t(re,{label:"取货地类型",prop:"type"},{default:l(()=>[t(B,{placeholder:"请选择",modelValue:n(d).type,"onUpdate:modelValue":e[3]||(e[3]=k=>n(d).type=k)},{default:l(()=>[t(i,{label:"邮局",value:"邮局"}),t(i,{label:"烟站",value:"烟站"}),t(i,{label:"村委会",value:"村委会"}),t(i,{label:"其他",value:"其他"})]),_:1},8,["modelValue"])]),_:1}),t(re,{label:"详细地址",prop:"pickupAddress"},{default:l(()=>[t(ie,{modelValue:n(d).pickupAddress,"onUpdate:modelValue":e[4]||(e[4]=k=>n(d).pickupAddress=k),placeholder:"请输入"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"]),a("div",el,[t(q,{onClick:Ie,type:"primary"},{default:l(()=>e[11]||(e[11]=[V("清空")])),_:1}),t(q,{style:{"margin-left":"10px"},onClick:Oe,type:"primary"},{default:l(()=>e[12]||(e[12]=[V("搜索")])),_:1})])])])])):J("",!0)])]),a("div",tl,[K((b(),T(q,{icon:n(Ve),type:"primary",onClick:$e},{default:l(()=>e[13]||(e[13]=[V("添加选址")])),_:1},8,["icon"])),[[W,"pickup:location:add"]]),K((b(),T(q,{icon:n(Ye),type:"primary",style:{"margin-left":"2vw"},onClick:je},{default:l(()=>e[14]||(e[14]=[V("修改信息")])),_:1},8,["icon"])),[[W,"pickup:location:update"]]),K((b(),T(q,{icon:n(Ze),type:"primary",style:{"margin-left":"2vw"},onClick:Be},{default:l(()=>e[15]||(e[15]=[V("导入表格")])),_:1},8,["icon"])),[[W,"pickup:location:importForm"]]),K((b(),T(q,{icon:n(He),type:"primary",style:{"margin-left":"2vw"},onClick:Te},{default:l(()=>e[16]||(e[16]=[V("导入日志")])),_:1},8,["icon"])),[[W,"pickup:location:importLogs"]]),K((b(),T(q,{icon:n(et),type:"primary",style:{"margin-left":"2vw"},onClick:Me},{default:l(()=>e[17]||(e[17]=[V("导出表格")])),_:1},8,["icon"])),[[W,"pickup:location:exportForm"]])]),a("div",ll,[t(Je,{data:_.value,ref_key:"tableRef",ref:m,"cell-style":{textAlign:"center"},onRowClick:H,"header-cell-style":{height:"4vh","text-align":"center"},size:"small","row-style":{height:"3.9vh"},"row-class-name":Se,style:{"font-size":"0.8vw",width:"100%"},onSelectionChange:ze},{default:l(()=>[t(G,{type:"selection"}),t(G,{label:"序号",type:"index",index:qe}),t(G,{prop:"pickupName",label:"名称","show-overflow-tooltip":!0}),t(G,{prop:"pickupAddress",label:"详细地址","show-overflow-tooltip":!0}),t(G,{label:"经纬度","show-overflow-tooltip":!0},{default:l(k=>[V(M("经度:"+k.row.longitude+",纬度:"+k.row.latitude),1)]),_:1}),t(G,{prop:"type",label:"取货地类型"}),t(G,{label:"关联商户","show-overflow-tooltip":!0},{default:l(k=>[V(M(k.row.stores.length>0?k.row.stores.map(ue=>ue.storeName).join(","):"无"),1)]),_:1}),t(G,{prop:"status",label:"状态"},{default:l(k=>[k.row.status===1?(b(),I("span",ol,"禁用")):k.row.status===2?(b(),I("span",al,"启用(未分配)")):k.row.status===3?(b(),I("span",nl,"启用(已分配)")):(b(),I("span",sl,"全部"))]),_:1})]),_:1},8,["data"]),a("div",il,[a("div",rl,[s.value?(b(),T(Ke,{key:0,layout:"prev, pager, next","current-page":n(u).pageNum,"page-size":n(u).pageSize,total:Number(s.value.total),onCurrentChange:Ae},null,8,["current-page","page-size","total"])):J("",!0)])])])]),a("div",ul,[a("div",dl,[t(ft,{ref_key:"mapRef",ref:D,list:v.value,mapType:0,loading:r.value},null,8,["list","loading"])]),a("div",pl,[K((b(),T(q,{type:"primary",onClick:Pe},{default:l(()=>e[18]||(e[18]=[V("重新计算")])),_:1})),[[W,"pickup:location:recalculate"]])])])]),a("div",cl,[a("div",ml,[a("div",fl,[K(a("input",{type:"checkbox","onUpdate:modelValue":e[5]||(e[5]=k=>j.value=k),onClick:e[6]||(e[6]=he(()=>{},["stop"]))},null,512),[[it,j.value]]),a("span",null," 待分配商户（"+M(R.value)+"）： ",1)]),a("div",gl,[t(be,{class:"arrow left",onClick:Ee},{default:l(()=>[t(n(tt))]),_:1}),a("div",_l,[(b(!0),I(ae,null,xe(De.value,(k,ue)=>(b(),I("div",{key:ue,class:"merchant-card"},[a("div",vl,[a("input",{type:"checkbox",checked:k.selected,onClick:he(wl=>ne(k.id),["stop"])},null,8,yl)]),a("div",kl,[a("p",null,"客户编码："+M(k.customerCode),1),a("p",null,"客户名称："+M(k.contactName),1),a("p",null,"客户地址："+M(k.storeAddress),1),a("p",null,[V(" 最近选址："+M(k.recentlySited)+" 距离 ",1),a("span",bl,M(k.dist),1),e[19]||(e[19]=V(" km "))])])]))),128))]),t(be,{class:"arrow right",onClick:Re},{default:l(()=>[t(n(lt))]),_:1})])])]),t(yt,{ref_key:"add",ref:C,onAdd:se},null,512),t(Ct,{ref_key:"change",ref:z,onChange:Ue,row:P.value},null,8,["row"]),t(qt,{ref_key:"uploadNoteRef",ref:g},null,512),t(Mt,{ref_key:"uploadTableRef",ref:f},null,512)])}}}),Ql=le(hl,[["__scopeId","data-v-03132934"]]);export{Ql as default};
