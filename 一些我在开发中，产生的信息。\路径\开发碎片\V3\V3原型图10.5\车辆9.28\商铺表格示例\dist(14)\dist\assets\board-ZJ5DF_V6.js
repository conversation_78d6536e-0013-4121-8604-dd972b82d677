import{Y as v,r as o}from"./index-C0QCllTd.js";import{r as n,a as i}from"./index-m25zEilF.js";function w(e){return n.get({url:"/guestbook/feedback/list",params:e})}function y(){return n.get({url:"/guestbook/feedback/getConditionsData"})}function C(e){return n.post({url:"/guestbook/feedback/add",data:e,timeout:1e3*60})}function D(e){return n.delete({url:`/guestbook/feedback/delete/${e}`,headers:{"Content-Type":"application/x-www-form-urlencoded"}})}function I(e){return n.get({url:`/guestbook/feedback/details/${e}`})}function N(e){return n.post({url:"/guestbook/feedback/addReply",data:e,timeout:1e3*60})}function h(e){return n.get({url:`/guestbook/feedback/getSingleConditionsData/${e}`})}function S(){return n.get({url:"/guestbook/feedback/getUnhandledAmount"})}const M=v("board",()=>{const e=o(!1),s=o({currentPageNum:1,dataCurrentPage:[],pageSize:6,totalCount:0,totalPageNum:0});async function l(t){e.value=!0;const a=await w(t);s.value=a.data,e.value=!1}const u=o();async function g(){e.value=!0;const t=await y();u.value=t.data,e.value=!1}async function f(t){const a=await C(t);a.code===200&&i.success(a.msg)}async function m(t){const a=await D(t);a.code===200&&i.success(a.msg)}const c=o();async function b(t){const a=await I(t);c.value=a.data}async function k(t){return await N(t)}const r=o({contactName:"",customerManagerId:"",customerManagerName:"",areaId:"",areaName:"",routeId:"",routeName:""});async function p(t){const a=await h(t);r.value=a.data}const d=o();async function A(){const t=await S();d.value=t.data}return{loading:e,boardData:s,getBoardData:l,cond:u,getCondAction:g,addFeedbackAction:f,removeFeedbackAction:m,detail:c,getDetailData:b,postInfoAddAction:k,singleCondData:r,singleCondDataAction:p,UnhandledAmountAction:A,UnhandledAmountData:d}});export{M as u};
