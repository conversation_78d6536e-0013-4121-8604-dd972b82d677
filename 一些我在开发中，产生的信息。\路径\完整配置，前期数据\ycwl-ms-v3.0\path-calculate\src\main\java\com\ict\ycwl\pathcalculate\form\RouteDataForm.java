package com.ict.ycwl.pathcalculate.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("获取路线详细数据请求表单")
public class RouteDataForm {

    /**
     * 路线名称
     */
    @ApiModelProperty(value = "路线名称",dataType = "String",required = true,example = "南雄市中转站-粤F QB54321-星期四-2024.2.1")
    @NotBlank(message = "路线名称不能为空")
    private String routeName;

    /**
     *所属中转站id
     */
    @ApiModelProperty(value = "中转站ID",dataType = "Long",required = true,example = "3")
    @NotNull(message = "中转站ID不能为空")
    Long transitDepotId;

}
