<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.datamanagement.mapper.AccumulationMapper">

    <delete id="deleteAll">
        delete from accumulation
    </delete>

    <select id="selectCheckList" resultType="com.ict.datamanagement.domain.dto.store.CheckInPointName">
        select accumulation_id,accumulation_name,accumulation_address from accumulation where area_name=#{areaName} and is_delete=0
    </select>

    <select id="selectAccIdByAccName" resultType="java.lang.Long">
        select accumulation_id from accumulation where accumulation_address=#{accumulationName} and is_delete=0 limit 1
    </select>
</mapper>
