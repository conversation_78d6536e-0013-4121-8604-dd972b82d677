import{Y as p,r as l}from"./index-C0QCllTd.js";import{r}from"./index-m25zEilF.js";function u(t){return r.get({url:"datamanagement/deliveryList",params:t})}function w(){return r.post({url:"datamanagement/getSelect"})}function L(){return r.post({url:"datamanagement/addDeliveryDownBox"})}function h(t){return r.post({url:"datamanagement/updateDelivery",params:t})}function x(t){return r.delete({url:"datamanagement/deleteDelivery",params:t})}function I(t){return r.post({url:"datamanagement/addDelivery",params:t})}const B=p("delivery",()=>{const t=l([]),i=l([]),o=l(),d=l();async function c(e){const a=await u(e);t.value.findIndex(n=>n.deliveryId===a.data.records[0].deliveryId)===-1&&t.value.push(a.data.records[0])}async function y(e){let a=1,n=!0;for(i.value=[];n;)try{const s=await u({pageNum:a,pageSize:e});i.value=[...i.value,...s.data.records],n=s.data.records.length===e,a++}catch(s){console.error("请求数据时出错:",s),n=!1}}async function v(e){return await h(e)}async function D(e){return await x(e)}async function f(e){return await I(e)}async function m(){const e=await w();o.value=e.data}async function g(){const e=await L();d.value=e.data}return{getDeliveryData:c,getDeliverySelectList:m,getTotalDelivery:y,addDelivery:f,updateDelivery:v,deleteDelivery:D,getDeliveryAddDownBox:g,deliveryList:t,deliveryTotalList:i,selectList:o,info:d}});export{B as u};
