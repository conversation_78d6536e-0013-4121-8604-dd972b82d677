package com.ict.datamanagement.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ict.datamanagement.domain.dto.store.StoreListRequest;
import com.baomidou.mybatisplus.extension.service.IService;
import com.ict.datamanagement.domain.entity.Store;
import com.ict.datamanagement.domain.vo.StoreVO;

/**
* <AUTHOR>
* @description 针对表【store】的数据库操作Service
* @createDate 2024-04-16 23:16:01
*/
public interface StoreService extends IService<Store> {

    QueryWrapper<Store> getQueryWrapper(StoreListRequest request);

    StoreVO getStoreVO(Store store);

}
