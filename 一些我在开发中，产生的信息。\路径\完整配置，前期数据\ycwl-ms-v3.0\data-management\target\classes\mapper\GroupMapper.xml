<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.datamanagement.mapper.GroupMapper">

    <resultMap id="BaseResultMap" type="com.ict.datamanagement.domain.entity.Group">
            <id property="groupId" column="group_id" jdbcType="BIGINT"/>
            <result property="groupName" column="group_name" jdbcType="VARCHAR"/>
            <result property="areaId" column="area_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        group_id,group_name,area_id
    </sql>

    <select id="selectGroupList"  parameterType="Long" resultMap="BaseResultMap">
        SELECT  <include refid="Base_Column_List"></include>  FROM `group`
    </select>
</mapper>
