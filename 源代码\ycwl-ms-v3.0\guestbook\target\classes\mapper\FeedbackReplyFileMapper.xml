<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.guestbook.mapper.FeedbackReplyFileMapper">

    <select id="selectReplyFile" parameterType="Long" resultType="String">
        SELECT reply_file_path FROM feedback_reply_file WHERE reply_id = #{replyId}
    </select>


    <insert id="insertReplyFile">
        INSERT INTO feedback_reply_file VALUES
        <foreach collection="fileList" item="file" separator=",">
            (null,#{file.replyFilePath},#{file.replyFileRealPath},#{file.replyId})
        </foreach>
    </insert>

    <delete id="deleteReplyFileByReplyIds">
        DELETE FROM feedback_reply_file WHERE reply_id IN
        <foreach collection="replyIdList" separator="," item="replyId" open="(" close=")">
            #{replyId}
        </foreach>
    </delete>

    <select id="selectReplyFileRealPath" parameterType="list" resultType="String">
        SELECT reply_file_real_path FROM feedback_reply_file WHERE reply_id IN
        <foreach collection="replyIdList" separator="," item="replyId" open="(" close=")">
            #{replyId}
        </foreach>
    </select>
</mapper>