server:
  port: 8086
spring:
  application:
    name: guestbook
  cloud:
    nacos:
      server-addr: localhost:8848 # nacos地址
  mvc:
    servlet:
      load-on-startup: 1
mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.ict.ycwl.guestbook.pojo
  global-config:
    db-config:
      id-type: auto
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
file:
  ACCESS_PATH: E:\www\wwwroot\ycwl\ycwl-ms\guestbook\data\uploadFile\