package com.ict.datamanagement.common;

public class ResultUtils<T> {

    /**
     * 成功
     * @param data
     * @return
     * @param <T>
     */
    public static <T> BaseResponse<T> success(T data){return new BaseResponse<>(StatusCode.SUCCESS,data);}

    /**
     * 成功
     * @return
     * @param <T>
     */
    public static <T> BaseResponse<T> success(){return new BaseResponse<>(StatusCode.SUCCESS);}

    /**
     * 失败
     * @param statusCode
     * @return
     * @param <T>
     */
    public static <T> BaseResponse<T> error(StatusCode statusCode){return new BaseResponse<>(statusCode);}

    /**
     * 失败
     * @param statusCode
     * @return
     * @param <T>
     */
    public static <T> BaseResponse<T> error(int code,String message){return new BaseResponse<>(code,message);}
}
