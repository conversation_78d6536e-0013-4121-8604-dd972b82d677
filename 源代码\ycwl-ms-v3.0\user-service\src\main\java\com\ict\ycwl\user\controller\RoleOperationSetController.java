package com.ict.ycwl.user.controller;

import com.ict.ycwl.common.web.AjaxResult;
import com.ict.ycwl.user.service.RoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "用户管理API")
@RestController
@RequestMapping("/user")
public class RoleOperationSetController {


    @Autowired
    private RoleService roleService;

    @ApiOperation("角色权限点设置接口")
    @PostMapping("/set")
    public AjaxResult setOperation(@RequestParam("role_id") Long roleId,
                                   @ApiParam(value = "以英文逗号串起来的权限点id字符串",required = true,example = "1,2,3")
                                   @RequestParam("idList") String idList){
        return roleService.setRoleOperation(roleId,idList);
    }

    @ApiOperation("获取权限点列表接口")
    @GetMapping("/get/operations")
    public AjaxResult getOperation(){
        return roleService.getOperations();
    }

    @ApiOperation("获取该角色已有权限接口")
    @GetMapping("/get/role/operations")
    public AjaxResult getRoleOperations(@RequestParam("role_id") Long roleId)
    {
        return roleService.getRoleOperations(roleId);
    }

    @ApiOperation("获取角色名字与id接口")
    @GetMapping("/get/roles")
    public AjaxResult getRoles(){
        return roleService.getRoles();
    }

}
