
package com.ict.ycwl.pathcalculate.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.ict.ycwl.pathcalculate.form.AddRouteFrom;
import com.ict.ycwl.pathcalculate.pojo.Route;
import com.ict.ycwl.pathcalculate.vo.GroupDataVO;
import com.ict.ycwl.pathcalculate.vo.TransitDepotRouteVO;
import com.ict.ycwl.pathcalculate.vo.RouteVO;
import com.ict.ycwl.pathcalculate.vo.details.AccumulationDetailsVO;
import com.ict.ycwl.pathcalculate.vo.details.TransitDepotDetailsVO;
import com.ict.ycwl.pathcalculate.vo.RouteDataVO;
import com.ict.ycwl.pathcalculate.vo.details.StoreDetailsVO;
import java.math.BigDecimal;
import java.util.List;

/**
 * 路径计算service
 */
public interface RouteService extends IService<Route> {

    /**
     * 获取路线详情，包含大区、路线
     *
     * @return TransitDepotDetailsVO
     */
    List<GroupDataVO> getRouteDetails();

    /**
     * 获取聚集区下商户信息
     *
     * @return StoreDetailsVO
     */
    List<AccumulationDetailsVO> getAccumulationDetails(Long routeId);

    /**
     * 获取聚集区下商户信息
     *
     * @return StoreDetailsVO
     */
    List<StoreDetailsVO> getStoreDetails(Long accumulationId);

    /**
     * 获取地图数据（外层）
     *
     * @return RouteVO
     */
    List<RouteVO> getMapData();


    /* *
     * 获取大区历史路径列表数据
     *
     * @return TransitDepotRouteVO
     */
    List<TransitDepotRouteVO> getTransitDepotRouteData();

    /**
     * 获取路径详细数据
     *
     * @return RouteDataVO
     */
    RouteDataVO getRouteData(Long transitDepotId, String routeName);

    /**
     * 添加路线数据（替换）
     *
     * @param from 请求参数
     */
    String addRoute(List<AddRouteFrom> from);

    /**
     * 获取路线版本号
     * @return
     */
    List<String> getRouteVersion(String date,Long transitDepotId);

    BigDecimal getWorkTime(Long routeId,String polylineStr);

    RouteDataVO getRouteDataVO(Route route);
}
