## 1.安装npmp

npm install -g pnpm

## 2.安装vite：

npm i

## 3.如果出现：解决 npm或pnpm : 无法加载文件 C:\Users\<USER>\AppData\Roaming\npm\cnpm.ps1，因为在此系统上禁止运行脚本

参考即可解决：[解决 npm或pnpm : 无法加载文件 C:\Users\<USER>\AppData\Roaming\npm\cnpm.ps1，因为在此系统上禁止运行脚本_所在位置 行:1 字符: 1 + pnpm install + ~~~~ + categoryinf-CSDN博客](https://blog.csdn.net/weixin_46212682/article/details/121784020)

## 4.如果出现：![image-20240702174857705](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20240702174857705.png)

### 修改：

![image-20240702175003002](C:\Users\<USER>\AppData\Roaming\Typora\typora-user-images\image-20240702175003002.png)