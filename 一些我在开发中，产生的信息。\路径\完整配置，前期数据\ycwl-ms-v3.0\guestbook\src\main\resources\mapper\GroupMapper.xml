<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.guestbook.mapper.GroupMapper">

    <resultMap id="GroupAndUserMap" type="com.ict.ycwl.guestbook.api.vo.GroupVo">
        <id property="groupId" column="group_id"></id>
        <result property="groupName" column="group_name"></result>
        <collection property="userList" fetchType="eager"
                    select="com.ict.ycwl.guestbook.mapper.UserMapper.selectUserByGroupId"
                    column="group_id"></collection>
    </resultMap>

    <select id="selectGroupByAreaId" resultMap="GroupAndUserMap" parameterType="Long">
        SELECT group_id,group_name FROM `group`  WHERE area_id = #{areaId}
    </select>

</mapper>