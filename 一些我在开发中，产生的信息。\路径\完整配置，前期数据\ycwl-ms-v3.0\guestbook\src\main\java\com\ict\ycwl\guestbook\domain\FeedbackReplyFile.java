package com.ict.ycwl.guestbook.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FeedbackReplyFile {

    @TableId(type = IdType.ASSIGN_ID)
    private Long replyFileId;

    private String replyFilePath;

    private String replyFileRealPath;

    private Long replyId;

}
