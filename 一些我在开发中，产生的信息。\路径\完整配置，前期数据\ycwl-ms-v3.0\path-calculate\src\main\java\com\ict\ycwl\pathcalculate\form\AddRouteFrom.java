package com.ict.ycwl.pathcalculate.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("添加路线表单")
public class AddRouteFrom {

    /**
     * 大区ID
     */
    @ApiModelProperty(value = "大区ID",dataType = "Long",required = true,example = "1")
    @NotNull(message = "大区ID不能为空")
    private Long areaId;

    /**
     * 路线名称
     */
    @ApiModelProperty(value = "路线名称",dataType = "String",required = true,example = "浈江区-粤F QB12345-星期一-2024.2.1")
    @NotBlank(message = "路线名称不能为空")
    private String routeName;

    /**
     * 路线坐标点串
     */
    @ApiModelProperty(value = "路线坐标串",dataType = "Map",required = true)
    private List<Map<String,Double>> polyline;

    /**
     * 路线坐标点串
     */
    @ApiModelProperty(value = "凸包坐标点串",dataType = "Map",required = true)
    private List<Map<String,Double>> convex;

    /**
     * 运营里程
     */
    @ApiModelProperty(value = "运营里程",dataType = "String",required = true)
    @NotBlank(message = "运营里程不能为空")
    private String distance;

    /**
     * 载货量
     */
    @ApiModelProperty(value = "载货量",dataType = "String",required = true)
    @NotBlank(message = "载货量不能为空")
    private String cargoWeight;

    /**
     * 中转站ID
     */
    @ApiModelProperty(value = "中转站ID",dataType = "Long",required = true,example = "3")
    @NotNull(message = "中转站ID不能为空")
    private Long transitDepotId;
}
