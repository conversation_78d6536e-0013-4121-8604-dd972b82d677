import{u as h,_,a as g}from"./base-kpSIrADU.js";import{d as c,j as u,o as n,c as r,a as t,u as s,f as k,H as v,k as f,s as p,t as i,n as $,S as C,K as w,b as B,v as N}from"./index-C0QCllTd.js";const V={viewBox:"0 0 79 86",version:"1.1",xmlns:"http://www.w3.org/2000/svg","xmlns:xlink":"http://www.w3.org/1999/xlink"},x=["id"],E=["stop-color"],R=["stop-color"],S=["id"],G=["stop-color"],b=["stop-color"],I=["id"],z={id:"Illustrations",stroke:"none","stroke-width":"1",fill:"none","fill-rule":"evenodd"},D={id:"B-type",transform:"translate(-1268.000000, -535.000000)"},M={id:"Group-2",transform:"translate(1268.000000, 535.000000)"},P=["fill"],j=["fill"],H={id:"Group-Copy",transform:"translate(34.500000, 31.500000) scale(-1, 1) rotate(-25.000000) translate(-34.500000, -31.500000) translate(7.000000, 10.000000)"},K=["fill"],L=["fill"],O=["fill"],T=["fill"],U=["fill"],Z={id:"Rectangle-Copy-17",transform:"translate(53.000000, 45.000000)"},q=["fill","xlink:href"],A=["fill","mask"],F=["fill"],J=c({name:"ImgEmpty"}),Q=c({...J,setup(d){const e=u("empty"),l=h();return(a,m)=>(n(),r("svg",V,[t("defs",null,[t("linearGradient",{id:`linearGradient-1-${s(l)}`,x1:"38.8503086%",y1:"0%",x2:"61.1496914%",y2:"100%"},[t("stop",{"stop-color":`var(${s(e).cssVarBlockName("fill-color-1")})`,offset:"0%"},null,8,E),t("stop",{"stop-color":`var(${s(e).cssVarBlockName("fill-color-4")})`,offset:"100%"},null,8,R)],8,x),t("linearGradient",{id:`linearGradient-2-${s(l)}`,x1:"0%",y1:"9.5%",x2:"100%",y2:"90.5%"},[t("stop",{"stop-color":`var(${s(e).cssVarBlockName("fill-color-1")})`,offset:"0%"},null,8,G),t("stop",{"stop-color":`var(${s(e).cssVarBlockName("fill-color-6")})`,offset:"100%"},null,8,b)],8,S),t("rect",{id:`path-3-${s(l)}`,x:"0",y:"0",width:"17",height:"36"},null,8,I)]),t("g",z,[t("g",D,[t("g",M,[t("path",{id:"Oval-Copy-2",d:"M39.5,86 C61.3152476,86 79,83.9106622 79,81.3333333 C79,78.7560045 57.3152476,78 35.5,78 C13.6847524,78 0,78.7560045 0,81.3333333 C0,83.9106622 17.6847524,86 39.5,86 Z",fill:`var(${s(e).cssVarBlockName("fill-color-3")})`},null,8,P),t("polygon",{id:"Rectangle-Copy-14",fill:`var(${s(e).cssVarBlockName("fill-color-7")})`,transform:"translate(27.500000, 51.500000) scale(1, -1) translate(-27.500000, -51.500000) ",points:"13 58 53 58 42 45 2 45"},null,8,j),t("g",H,[t("polygon",{id:"Rectangle-Copy-10",fill:`var(${s(e).cssVarBlockName("fill-color-7")})`,transform:"translate(11.500000, 5.000000) scale(1, -1) translate(-11.500000, -5.000000) ",points:"2.84078316e-14 3 18 3 23 7 5 7"},null,8,K),t("polygon",{id:"Rectangle-Copy-11",fill:`var(${s(e).cssVarBlockName("fill-color-5")})`,points:"-3.69149156e-15 7 38 7 38 43 -3.69149156e-15 43"},null,8,L),t("rect",{id:"Rectangle-Copy-12",fill:`url(#linearGradient-1-${s(l)})`,transform:"translate(46.500000, 25.000000) scale(-1, 1) translate(-46.500000, -25.000000) ",x:"38",y:"7",width:"17",height:"36"},null,8,O),t("polygon",{id:"Rectangle-Copy-13",fill:`var(${s(e).cssVarBlockName("fill-color-2")})`,transform:"translate(39.500000, 3.500000) scale(-1, 1) translate(-39.500000, -3.500000) ",points:"24 7 41 7 55 -3.63806207e-12 38 -3.63806207e-12"},null,8,T)]),t("rect",{id:"Rectangle-Copy-15",fill:`url(#linearGradient-2-${s(l)})`,x:"13",y:"45",width:"40",height:"36"},null,8,U),t("g",Z,[t("use",{id:"Mask",fill:`var(${s(e).cssVarBlockName("fill-color-8")})`,transform:"translate(8.500000, 18.000000) scale(-1, 1) translate(-8.500000, -18.000000) ","xlink:href":`#path-3-${s(l)}`},null,8,q),t("polygon",{id:"Rectangle-Copy",fill:`var(${s(e).cssVarBlockName("fill-color-9")})`,mask:`url(#mask-4-${s(l)})`,transform:"translate(12.000000, 9.000000) scale(-1, 1) translate(-12.000000, -9.000000) ",points:"7 0 24 0 20 18 7 16.5"},null,8,A)]),t("polygon",{id:"Rectangle-Copy-18",fill:`var(${s(e).cssVarBlockName("fill-color-2")})`,transform:"translate(66.000000, 51.500000) scale(-1, 1) translate(-66.000000, -51.500000) ",points:"62 45 79 45 70 58 53 58"},null,8,F)])])])]))}});var W=_(Q,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/empty/src/img-empty.vue"]]);const X=k({image:{type:String,default:""},imageSize:Number,description:{type:String,default:""}}),Y=["src"],t0={key:1},s0=c({name:"ElEmpty"}),e0=c({...s0,props:X,setup(d){const e=d,{t:l}=v(),a=u("empty"),m=f(()=>e.description||l("el.table.emptyText")),y=f(()=>({width:g(e.imageSize)}));return(o,o0)=>(n(),r("div",{class:i(s(a).b())},[t("div",{class:i(s(a).e("image")),style:$(s(y))},[o.image?(n(),r("img",{key:0,src:o.image,ondragstart:"return false"},null,8,Y)):p(o.$slots,"image",{key:1},()=>[B(W)])],6),t("div",{class:i(s(a).e("description"))},[o.$slots.description?p(o.$slots,"description",{key:0}):(n(),r("p",t0,C(s(m)),1))],2),o.$slots.default?(n(),r("div",{key:0,class:i(s(a).e("bottom"))},[p(o.$slots,"default")],2)):w("v-if",!0)],2))}});var l0=_(e0,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/empty/src/empty.vue"]]);const r0=N(l0);export{r0 as E};
