package com.ict.ycwl.pathcalculate.service.Impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ict.ycwl.pathcalculate.pojo.RouteDetail;
import com.ict.ycwl.pathcalculate.service.RouteDetailService;
import com.ict.ycwl.pathcalculate.mapper.RouteDetailMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【route_detail】的数据库操作Service实现
* @createDate 2024-06-04 10:44:28
*/
@Service
public class RouteDetailServiceImpl extends ServiceImpl<RouteDetailMapper, RouteDetail>
    implements RouteDetailService{

}




