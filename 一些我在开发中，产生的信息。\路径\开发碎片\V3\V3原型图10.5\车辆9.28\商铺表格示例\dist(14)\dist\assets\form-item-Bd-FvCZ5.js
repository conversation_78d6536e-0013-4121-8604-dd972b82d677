import{c as Ze,ac as Oe,_ as He,ad as ge,u as Ot,ae as jt,a as $e}from"./base-kpSIrADU.js";import{aN as Ie,aR as ke,aw as qt,aC as Et,aS as Tt,f as je,g as Qe,h as ve,a0 as St,l as me,ah as Xe,r as V,k as T,d as H,j as qe,m as ne,x as et,P as tt,a4 as rt,o as ye,c as nt,s as ee,t as K,u as E,a1 as it,y as ie,z as at,A as st,af as _t,b as ae,N as Pt,G as ot,a2 as $t,aH as de,w as ce,p as It,q as Mt,n as Me,R as Rt,S as Re,K as Ne,a as Le,az as Nt,v as Lt,O as Bt}from"./index-C0QCllTd.js";import{c as he}from"./castArray-CSO3s-vM.js";import{k as ft,g as lt,s as Vt,a as Wt,b as Ct,c as Ee,e as Dt,d as Ut,t as zt}from"./input-DqmydyK4.js";import{b as Gt}from"./index-m25zEilF.js";import{c as oe,k as Te,g as Kt,b as ut,d as Jt,n as se,e as dt,f as Yt,h as Zt,j as Ht,l as kt,S as Qt}from"./_initCloneObject-BmTtMqsv.js";function Xt(r,e){for(var t=-1,n=r==null?0:r.length;++t<n&&e(r[t],t,r)!==!1;);return r}function er(r,e){return r&&oe(e,ft(e),r)}function tr(r,e){return r&&oe(e,Te(e),r)}function rr(r,e){return oe(r,lt(r),e)}var nr=Object.getOwnPropertySymbols,ct=nr?function(r){for(var e=[];r;)Wt(e,lt(r)),r=Kt(r);return e}:Vt;function ir(r,e){return oe(r,ct(r),e)}function ar(r){return Ct(r,Te,ct)}var sr=Object.prototype,or=sr.hasOwnProperty;function fr(r){var e=r.length,t=new r.constructor(e);return e&&typeof r[0]=="string"&&or.call(r,"index")&&(t.index=r.index,t.input=r.input),t}function lr(r,e){var t=e?ut(r.buffer):r.buffer;return new r.constructor(t,r.byteOffset,r.byteLength)}var ur=/\w*$/;function dr(r){var e=new r.constructor(r.source,ur.exec(r));return e.lastIndex=r.lastIndex,e}var Be=Ie?Ie.prototype:void 0,Ve=Be?Be.valueOf:void 0;function cr(r){return Ve?Object(Ve.call(r)):{}}var pr="[object Boolean]",gr="[object Date]",vr="[object Map]",mr="[object Number]",yr="[object RegExp]",hr="[object Set]",br="[object String]",wr="[object Symbol]",Fr="[object ArrayBuffer]",xr="[object DataView]",Ar="[object Float32Array]",Or="[object Float64Array]",jr="[object Int8Array]",qr="[object Int16Array]",Er="[object Int32Array]",Tr="[object Uint8Array]",Sr="[object Uint8ClampedArray]",_r="[object Uint16Array]",Pr="[object Uint32Array]";function $r(r,e,t){var n=r.constructor;switch(e){case Fr:return ut(r);case pr:case gr:return new n(+r);case xr:return lr(r,t);case Ar:case Or:case jr:case qr:case Er:case Tr:case Sr:case _r:case Pr:return Jt(r,t);case vr:return new n;case mr:case br:return new n(r);case yr:return dr(r);case hr:return new n;case wr:return cr(r)}}var Ir="[object Map]";function Mr(r){return ke(r)&&Ee(r)==Ir}var We=se&&se.isMap,Rr=We?dt(We):Mr,Nr="[object Set]";function Lr(r){return ke(r)&&Ee(r)==Nr}var Ce=se&&se.isSet,Br=Ce?dt(Ce):Lr,Vr=1,Wr=2,Cr=4,pt="[object Arguments]",Dr="[object Array]",Ur="[object Boolean]",zr="[object Date]",Gr="[object Error]",gt="[object Function]",Kr="[object GeneratorFunction]",Jr="[object Map]",Yr="[object Number]",vt="[object Object]",Zr="[object RegExp]",Hr="[object Set]",kr="[object String]",Qr="[object Symbol]",Xr="[object WeakMap]",en="[object ArrayBuffer]",tn="[object DataView]",rn="[object Float32Array]",nn="[object Float64Array]",an="[object Int8Array]",sn="[object Int16Array]",on="[object Int32Array]",fn="[object Uint8Array]",ln="[object Uint8ClampedArray]",un="[object Uint16Array]",dn="[object Uint32Array]",A={};A[pt]=A[Dr]=A[en]=A[tn]=A[Ur]=A[zr]=A[rn]=A[nn]=A[an]=A[sn]=A[on]=A[Jr]=A[Yr]=A[vt]=A[Zr]=A[Hr]=A[kr]=A[Qr]=A[fn]=A[ln]=A[un]=A[dn]=!0;A[Gr]=A[gt]=A[Xr]=!1;function te(r,e,t,n,i,s){var a,o=e&Vr,u=e&Wr,w=e&Cr;if(a!==void 0)return a;if(!qt(r))return r;var c=Et(r);if(c){if(a=fr(r),!o)return Yt(r,a)}else{var g=Ee(r),b=g==gt||g==Kr;if(Zt(r))return Ht(r,o);if(g==vt||g==pt||b&&!i){if(a=u||b?{}:kt(r),!o)return u?ir(r,tr(a,r)):rr(r,er(a,r))}else{if(!A[g])return i?r:{};a=$r(r,g,o)}}s||(s=new Qt);var j=s.get(r);if(j)return j;s.set(r,a),Br(r)?r.forEach(function(v){a.add(te(v,e,t,v,r,s))}):Rr(r)&&r.forEach(function(v,f){a.set(f,te(v,e,t,f,r,s))});var q=w?u?ar:Dt:u?Te:ft,d=c?void 0:q(r);return Xt(d||r,function(v,f){d&&(f=v,v=r[f]),Tt(a,f,te(v,e,t,f,r,s))}),a}var cn=4;function De(r){return te(r,cn)}const pn=je({size:{type:String,values:Qe},disabled:Boolean}),gn=je({...pn,model:Object,rules:{type:ve(Object)},labelPosition:{type:String,values:["left","right","top"],default:"right"},requireAsteriskPosition:{type:String,values:["left","right"],default:"left"},labelWidth:{type:[String,Number],default:""},labelSuffix:{type:String,default:""},inline:Boolean,inlineMessage:Boolean,statusIcon:Boolean,showMessage:{type:Boolean,default:!0},validateOnRuleChange:{type:Boolean,default:!0},hideRequiredAsterisk:Boolean,scrollToError:Boolean,scrollIntoViewOptions:{type:[Object,Boolean]}}),vn={validate:(r,e,t)=>(St(r)||me(r))&&Xe(e)&&me(t)};function mn(){const r=V([]),e=T(()=>{if(!r.value.length)return"0";const s=Math.max(...r.value);return s?`${s}px`:""});function t(s){const a=r.value.indexOf(s);return a===-1&&e.value,a}function n(s,a){if(s&&a){const o=t(a);r.value.splice(o,1,s)}else s&&r.value.push(s)}function i(s){const a=t(s);a>-1&&r.value.splice(a,1)}return{autoLabelWidth:e,registerLabelWidth:n,deregisterLabelWidth:i}}const Q=(r,e)=>{const t=he(e);return t.length>0?r.filter(n=>n.prop&&t.includes(n.prop)):r},yn="ElForm",hn=H({name:yn}),bn=H({...hn,props:gn,emits:vn,setup(r,{expose:e,emit:t}){const n=r,i=[],s=Ze(),a=qe("form"),o=T(()=>{const{labelPosition:m,inline:l}=n;return[a.b(),a.m(s.value||"default"),{[a.m(`label-${m}`)]:m,[a.m("inline")]:l}]}),u=m=>{i.push(m)},w=m=>{m.prop&&i.splice(i.indexOf(m),1)},c=(m=[])=>{n.model&&Q(i,m).forEach(l=>l.resetField())},g=(m=[])=>{Q(i,m).forEach(l=>l.clearValidate())},b=T(()=>!!n.model),j=m=>{if(i.length===0)return[];const l=Q(i,m);return l.length?l:[]},q=async m=>v(void 0,m),d=async(m=[])=>{if(!b.value)return!1;const l=j(m);if(l.length===0)return!0;let h={};for(const x of l)try{await x.validate("")}catch(O){h={...h,...O}}return Object.keys(h).length===0?!0:Promise.reject(h)},v=async(m=[],l)=>{const h=!it(l);try{const x=await d(m);return x===!0&&(l==null||l(x)),x}catch(x){if(x instanceof Error)throw x;const O=x;return n.scrollToError&&f(Object.keys(O)[0]),l==null||l(!1,O),h&&Promise.reject(O)}},f=m=>{var l;const h=Q(i,m)[0];h&&((l=h.$el)==null||l.scrollIntoView(n.scrollIntoViewOptions))};return ne(()=>n.rules,()=>{n.validateOnRuleChange&&q().catch(m=>Ut())},{deep:!0}),et(Oe,tt({...rt(n),emit:t,resetFields:c,clearValidate:g,validateField:v,addField:u,removeField:w,...mn()})),e({validate:q,validateField:v,resetFields:c,clearValidate:g,scrollToField:f}),(m,l)=>(ye(),nt("form",{class:K(E(o))},[ee(m.$slots,"default")],2))}});var wn=He(bn,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/form/src/form.vue"]]);function W(){return W=Object.assign?Object.assign.bind():function(r){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(r[n]=t[n])}return r},W.apply(this,arguments)}function Fn(r,e){r.prototype=Object.create(e.prototype),r.prototype.constructor=r,Z(r,e)}function be(r){return be=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},be(r)}function Z(r,e){return Z=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,i){return n.__proto__=i,n},Z(r,e)}function xn(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function re(r,e,t){return xn()?re=Reflect.construct.bind():re=function(i,s,a){var o=[null];o.push.apply(o,s);var u=Function.bind.apply(i,o),w=new u;return a&&Z(w,a.prototype),w},re.apply(null,arguments)}function An(r){return Function.toString.call(r).indexOf("[native code]")!==-1}function we(r){var e=typeof Map=="function"?new Map:void 0;return we=function(n){if(n===null||!An(n))return n;if(typeof n!="function")throw new TypeError("Super expression must either be null or a function");if(typeof e<"u"){if(e.has(n))return e.get(n);e.set(n,i)}function i(){return re(n,arguments,be(this).constructor)}return i.prototype=Object.create(n.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),Z(i,n)},we(r)}var On=/%[sdj%]/g,jn=function(){};function Fe(r){if(!r||!r.length)return null;var e={};return r.forEach(function(t){var n=t.field;e[n]=e[n]||[],e[n].push(t)}),e}function I(r){for(var e=arguments.length,t=new Array(e>1?e-1:0),n=1;n<e;n++)t[n-1]=arguments[n];var i=0,s=t.length;if(typeof r=="function")return r.apply(null,t);if(typeof r=="string"){var a=r.replace(On,function(o){if(o==="%%")return"%";if(i>=s)return o;switch(o){case"%s":return String(t[i++]);case"%d":return Number(t[i++]);case"%j":try{return JSON.stringify(t[i++])}catch{return"[Circular]"}break;default:return o}});return a}return r}function qn(r){return r==="string"||r==="url"||r==="hex"||r==="email"||r==="date"||r==="pattern"}function S(r,e){return!!(r==null||e==="array"&&Array.isArray(r)&&!r.length||qn(e)&&typeof r=="string"&&!r)}function En(r,e,t){var n=[],i=0,s=r.length;function a(o){n.push.apply(n,o||[]),i++,i===s&&t(n)}r.forEach(function(o){e(o,a)})}function Ue(r,e,t){var n=0,i=r.length;function s(a){if(a&&a.length){t(a);return}var o=n;n=n+1,o<i?e(r[o],s):t([])}s([])}function Tn(r){var e=[];return Object.keys(r).forEach(function(t){e.push.apply(e,r[t]||[])}),e}var ze=function(r){Fn(e,r);function e(t,n){var i;return i=r.call(this,"Async Validation Error")||this,i.errors=t,i.fields=n,i}return e}(we(Error));function Sn(r,e,t,n,i){if(e.first){var s=new Promise(function(b,j){var q=function(f){return n(f),f.length?j(new ze(f,Fe(f))):b(i)},d=Tn(r);Ue(d,t,q)});return s.catch(function(b){return b}),s}var a=e.firstFields===!0?Object.keys(r):e.firstFields||[],o=Object.keys(r),u=o.length,w=0,c=[],g=new Promise(function(b,j){var q=function(v){if(c.push.apply(c,v),w++,w===u)return n(c),c.length?j(new ze(c,Fe(c))):b(i)};o.length||(n(c),b(i)),o.forEach(function(d){var v=r[d];a.indexOf(d)!==-1?Ue(v,t,q):En(v,t,q)})});return g.catch(function(b){return b}),g}function _n(r){return!!(r&&r.message!==void 0)}function Pn(r,e){for(var t=r,n=0;n<e.length;n++){if(t==null)return t;t=t[e[n]]}return t}function Ge(r,e){return function(t){var n;return r.fullFields?n=Pn(e,r.fullFields):n=e[t.field||r.fullField],_n(t)?(t.field=t.field||r.fullField,t.fieldValue=n,t):{message:typeof t=="function"?t():t,fieldValue:n,field:t.field||r.fullField}}}function Ke(r,e){if(e){for(var t in e)if(e.hasOwnProperty(t)){var n=e[t];typeof n=="object"&&typeof r[t]=="object"?r[t]=W({},r[t],n):r[t]=n}}return r}var mt=function(e,t,n,i,s,a){e.required&&(!n.hasOwnProperty(e.field)||S(t,a||e.type))&&i.push(I(s.messages.required,e.fullField))},$n=function(e,t,n,i,s){(/^\s+$/.test(t)||t==="")&&i.push(I(s.messages.whitespace,e.fullField))},X,In=function(){if(X)return X;var r="[a-fA-F\\d:]",e=function(h){return h&&h.includeBoundaries?"(?:(?<=\\s|^)(?="+r+")|(?<="+r+")(?=\\s|$))":""},t="(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}",n="[a-fA-F\\d]{1,4}",i=(`
(?:
(?:`+n+":){7}(?:"+n+`|:)|                                    // 1:2:3:4:5:6:7::  1:2:3:4:5:6:7:8
(?:`+n+":){6}(?:"+t+"|:"+n+`|:)|                             // 1:2:3:4:5:6::    1:2:3:4:5:6::8   1:2:3:4:5:6::8  1:2:3:4:5:6::1.2.3.4
(?:`+n+":){5}(?::"+t+"|(?::"+n+`){1,2}|:)|                   // 1:2:3:4:5::      1:2:3:4:5::7:8   1:2:3:4:5::8    1:2:3:4:5::7:1.2.3.4
(?:`+n+":){4}(?:(?::"+n+"){0,1}:"+t+"|(?::"+n+`){1,3}|:)| // 1:2:3:4::        1:2:3:4::6:7:8   1:2:3:4::8      1:2:3:4::6:7:1.2.3.4
(?:`+n+":){3}(?:(?::"+n+"){0,2}:"+t+"|(?::"+n+`){1,4}|:)| // 1:2:3::          1:2:3::5:6:7:8   1:2:3::8        1:2:3::5:6:7:1.2.3.4
(?:`+n+":){2}(?:(?::"+n+"){0,3}:"+t+"|(?::"+n+`){1,5}|:)| // 1:2::            1:2::4:5:6:7:8   1:2::8          1:2::4:5:6:7:1.2.3.4
(?:`+n+":){1}(?:(?::"+n+"){0,4}:"+t+"|(?::"+n+`){1,6}|:)| // 1::              1::3:4:5:6:7:8   1::8            1::3:4:5:6:7:1.2.3.4
(?::(?:(?::`+n+"){0,5}:"+t+"|(?::"+n+`){1,7}|:))             // ::2:3:4:5:6:7:8  ::2:3:4:5:6:7:8  ::8             ::1.2.3.4
)(?:%[0-9a-zA-Z]{1,})?                                             // %eth0            %1
`).replace(/\s*\/\/.*$/gm,"").replace(/\n/g,"").trim(),s=new RegExp("(?:^"+t+"$)|(?:^"+i+"$)"),a=new RegExp("^"+t+"$"),o=new RegExp("^"+i+"$"),u=function(h){return h&&h.exact?s:new RegExp("(?:"+e(h)+t+e(h)+")|(?:"+e(h)+i+e(h)+")","g")};u.v4=function(l){return l&&l.exact?a:new RegExp(""+e(l)+t+e(l),"g")},u.v6=function(l){return l&&l.exact?o:new RegExp(""+e(l)+i+e(l),"g")};var w="(?:(?:[a-z]+:)?//)",c="(?:\\S+(?::\\S*)?@)?",g=u.v4().source,b=u.v6().source,j="(?:(?:[a-z\\u00a1-\\uffff0-9][-_]*)*[a-z\\u00a1-\\uffff0-9]+)",q="(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*",d="(?:\\.(?:[a-z\\u00a1-\\uffff]{2,}))",v="(?::\\d{2,5})?",f='(?:[/?#][^\\s"]*)?',m="(?:"+w+"|www\\.)"+c+"(?:localhost|"+g+"|"+b+"|"+j+q+d+")"+v+f;return X=new RegExp("(?:^"+m+"$)","i"),X},Je={email:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+\.)+[a-zA-Z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}))$/,hex:/^#?([a-f0-9]{6}|[a-f0-9]{3})$/i},J={integer:function(e){return J.number(e)&&parseInt(e,10)===e},float:function(e){return J.number(e)&&!J.integer(e)},array:function(e){return Array.isArray(e)},regexp:function(e){if(e instanceof RegExp)return!0;try{return!!new RegExp(e)}catch{return!1}},date:function(e){return typeof e.getTime=="function"&&typeof e.getMonth=="function"&&typeof e.getYear=="function"&&!isNaN(e.getTime())},number:function(e){return isNaN(e)?!1:typeof e=="number"},object:function(e){return typeof e=="object"&&!J.array(e)},method:function(e){return typeof e=="function"},email:function(e){return typeof e=="string"&&e.length<=320&&!!e.match(Je.email)},url:function(e){return typeof e=="string"&&e.length<=2048&&!!e.match(In())},hex:function(e){return typeof e=="string"&&!!e.match(Je.hex)}},Mn=function(e,t,n,i,s){if(e.required&&t===void 0){mt(e,t,n,i,s);return}var a=["integer","float","array","regexp","object","method","email","number","date","url","hex"],o=e.type;a.indexOf(o)>-1?J[o](t)||i.push(I(s.messages.types[o],e.fullField,e.type)):o&&typeof t!==e.type&&i.push(I(s.messages.types[o],e.fullField,e.type))},Rn=function(e,t,n,i,s){var a=typeof e.len=="number",o=typeof e.min=="number",u=typeof e.max=="number",w=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,c=t,g=null,b=typeof t=="number",j=typeof t=="string",q=Array.isArray(t);if(b?g="number":j?g="string":q&&(g="array"),!g)return!1;q&&(c=t.length),j&&(c=t.replace(w,"_").length),a?c!==e.len&&i.push(I(s.messages[g].len,e.fullField,e.len)):o&&!u&&c<e.min?i.push(I(s.messages[g].min,e.fullField,e.min)):u&&!o&&c>e.max?i.push(I(s.messages[g].max,e.fullField,e.max)):o&&u&&(c<e.min||c>e.max)&&i.push(I(s.messages[g].range,e.fullField,e.min,e.max))},z="enum",Nn=function(e,t,n,i,s){e[z]=Array.isArray(e[z])?e[z]:[],e[z].indexOf(t)===-1&&i.push(I(s.messages[z],e.fullField,e[z].join(", ")))},Ln=function(e,t,n,i,s){if(e.pattern){if(e.pattern instanceof RegExp)e.pattern.lastIndex=0,e.pattern.test(t)||i.push(I(s.messages.pattern.mismatch,e.fullField,t,e.pattern));else if(typeof e.pattern=="string"){var a=new RegExp(e.pattern);a.test(t)||i.push(I(s.messages.pattern.mismatch,e.fullField,t,e.pattern))}}},y={required:mt,whitespace:$n,type:Mn,range:Rn,enum:Nn,pattern:Ln},Bn=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(S(t,"string")&&!e.required)return n();y.required(e,t,i,a,s,"string"),S(t,"string")||(y.type(e,t,i,a,s),y.range(e,t,i,a,s),y.pattern(e,t,i,a,s),e.whitespace===!0&&y.whitespace(e,t,i,a,s))}n(a)},Vn=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(S(t)&&!e.required)return n();y.required(e,t,i,a,s),t!==void 0&&y.type(e,t,i,a,s)}n(a)},Wn=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(t===""&&(t=void 0),S(t)&&!e.required)return n();y.required(e,t,i,a,s),t!==void 0&&(y.type(e,t,i,a,s),y.range(e,t,i,a,s))}n(a)},Cn=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(S(t)&&!e.required)return n();y.required(e,t,i,a,s),t!==void 0&&y.type(e,t,i,a,s)}n(a)},Dn=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(S(t)&&!e.required)return n();y.required(e,t,i,a,s),S(t)||y.type(e,t,i,a,s)}n(a)},Un=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(S(t)&&!e.required)return n();y.required(e,t,i,a,s),t!==void 0&&(y.type(e,t,i,a,s),y.range(e,t,i,a,s))}n(a)},zn=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(S(t)&&!e.required)return n();y.required(e,t,i,a,s),t!==void 0&&(y.type(e,t,i,a,s),y.range(e,t,i,a,s))}n(a)},Gn=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(t==null&&!e.required)return n();y.required(e,t,i,a,s,"array"),t!=null&&(y.type(e,t,i,a,s),y.range(e,t,i,a,s))}n(a)},Kn=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(S(t)&&!e.required)return n();y.required(e,t,i,a,s),t!==void 0&&y.type(e,t,i,a,s)}n(a)},Jn="enum",Yn=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(S(t)&&!e.required)return n();y.required(e,t,i,a,s),t!==void 0&&y[Jn](e,t,i,a,s)}n(a)},Zn=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(S(t,"string")&&!e.required)return n();y.required(e,t,i,a,s),S(t,"string")||y.pattern(e,t,i,a,s)}n(a)},Hn=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(S(t,"date")&&!e.required)return n();if(y.required(e,t,i,a,s),!S(t,"date")){var u;t instanceof Date?u=t:u=new Date(t),y.type(e,u,i,a,s),u&&y.range(e,u.getTime(),i,a,s)}}n(a)},kn=function(e,t,n,i,s){var a=[],o=Array.isArray(t)?"array":typeof t;y.required(e,t,i,a,s,o),n(a)},pe=function(e,t,n,i,s){var a=e.type,o=[],u=e.required||!e.required&&i.hasOwnProperty(e.field);if(u){if(S(t,a)&&!e.required)return n();y.required(e,t,i,o,s,a),S(t,a)||y.type(e,t,i,o,s)}n(o)},Qn=function(e,t,n,i,s){var a=[],o=e.required||!e.required&&i.hasOwnProperty(e.field);if(o){if(S(t)&&!e.required)return n();y.required(e,t,i,a,s)}n(a)},Y={string:Bn,method:Vn,number:Wn,boolean:Cn,regexp:Dn,integer:Un,float:zn,array:Gn,object:Kn,enum:Yn,pattern:Zn,date:Hn,url:pe,hex:pe,email:pe,required:kn,any:Qn};function xe(){return{default:"Validation error on field %s",required:"%s is required",enum:"%s must be one of %s",whitespace:"%s cannot be empty",date:{format:"%s date %s is invalid for format %s",parse:"%s date could not be parsed, %s is invalid ",invalid:"%s date %s is invalid"},types:{string:"%s is not a %s",method:"%s is not a %s (function)",array:"%s is not an %s",object:"%s is not an %s",number:"%s is not a %s",date:"%s is not a %s",boolean:"%s is not a %s",integer:"%s is not an %s",float:"%s is not a %s",regexp:"%s is not a valid %s",email:"%s is not a valid %s",url:"%s is not a valid %s",hex:"%s is not a valid %s"},string:{len:"%s must be exactly %s characters",min:"%s must be at least %s characters",max:"%s cannot be longer than %s characters",range:"%s must be between %s and %s characters"},number:{len:"%s must equal %s",min:"%s cannot be less than %s",max:"%s cannot be greater than %s",range:"%s must be between %s and %s"},array:{len:"%s must be exactly %s in length",min:"%s cannot be less than %s in length",max:"%s cannot be greater than %s in length",range:"%s must be between %s and %s in length"},pattern:{mismatch:"%s value %s does not match pattern %s"},clone:function(){var e=JSON.parse(JSON.stringify(this));return e.clone=this.clone,e}}}var Ae=xe(),k=function(){function r(t){this.rules=null,this._messages=Ae,this.define(t)}var e=r.prototype;return e.define=function(n){var i=this;if(!n)throw new Error("Cannot configure a schema with no rules");if(typeof n!="object"||Array.isArray(n))throw new Error("Rules must be an object");this.rules={},Object.keys(n).forEach(function(s){var a=n[s];i.rules[s]=Array.isArray(a)?a:[a]})},e.messages=function(n){return n&&(this._messages=Ke(xe(),n)),this._messages},e.validate=function(n,i,s){var a=this;i===void 0&&(i={}),s===void 0&&(s=function(){});var o=n,u=i,w=s;if(typeof u=="function"&&(w=u,u={}),!this.rules||Object.keys(this.rules).length===0)return w&&w(null,o),Promise.resolve(o);function c(d){var v=[],f={};function m(h){if(Array.isArray(h)){var x;v=(x=v).concat.apply(x,h)}else v.push(h)}for(var l=0;l<d.length;l++)m(d[l]);v.length?(f=Fe(v),w(v,f)):w(null,o)}if(u.messages){var g=this.messages();g===Ae&&(g=xe()),Ke(g,u.messages),u.messages=g}else u.messages=this.messages();var b={},j=u.keys||Object.keys(this.rules);j.forEach(function(d){var v=a.rules[d],f=o[d];v.forEach(function(m){var l=m;typeof l.transform=="function"&&(o===n&&(o=W({},o)),f=o[d]=l.transform(f)),typeof l=="function"?l={validator:l}:l=W({},l),l.validator=a.getValidationMethod(l),l.validator&&(l.field=d,l.fullField=l.fullField||d,l.type=a.getType(l),b[d]=b[d]||[],b[d].push({rule:l,value:f,source:o,field:d}))})});var q={};return Sn(b,u,function(d,v){var f=d.rule,m=(f.type==="object"||f.type==="array")&&(typeof f.fields=="object"||typeof f.defaultField=="object");m=m&&(f.required||!f.required&&d.value),f.field=d.field;function l(O,R){return W({},R,{fullField:f.fullField+"."+O,fullFields:f.fullFields?[].concat(f.fullFields,[O]):[O]})}function h(O){O===void 0&&(O=[]);var R=Array.isArray(O)?O:[O];!u.suppressWarning&&R.length&&r.warning("async-validator:",R),R.length&&f.message!==void 0&&(R=[].concat(f.message));var $=R.map(Ge(f,o));if(u.first&&$.length)return q[f.field]=1,v($);if(!m)v($);else{if(f.required&&!d.value)return f.message!==void 0?$=[].concat(f.message).map(Ge(f,o)):u.error&&($=[u.error(f,I(u.messages.required,f.field))]),v($);var B={};f.defaultField&&Object.keys(d.value).map(function(N){B[N]=f.defaultField}),B=W({},B,d.rule.fields);var G={};Object.keys(B).forEach(function(N){var M=B[N],fe=Array.isArray(M)?M:[M];G[N]=fe.map(l.bind(null,N))});var C=new r(G);C.messages(u.messages),d.rule.options&&(d.rule.options.messages=u.messages,d.rule.options.error=u.error),C.validate(d.value,d.rule.options||u,function(N){var M=[];$&&$.length&&M.push.apply(M,$),N&&N.length&&M.push.apply(M,N),v(M.length?M:null)})}}var x;if(f.asyncValidator)x=f.asyncValidator(f,d.value,h,d.source,u);else if(f.validator){try{x=f.validator(f,d.value,h,d.source,u)}catch(O){console.error==null||console.error(O),u.suppressValidatorError||setTimeout(function(){throw O},0),h(O.message)}x===!0?h():x===!1?h(typeof f.message=="function"?f.message(f.fullField||f.field):f.message||(f.fullField||f.field)+" fails"):x instanceof Array?h(x):x instanceof Error&&h(x.message)}x&&x.then&&x.then(function(){return h()},function(O){return h(O)})},function(d){c(d)},o)},e.getType=function(n){if(n.type===void 0&&n.pattern instanceof RegExp&&(n.type="pattern"),typeof n.validator!="function"&&n.type&&!Y.hasOwnProperty(n.type))throw new Error(I("Unknown rule type %s",n.type));return n.type||"string"},e.getValidationMethod=function(n){if(typeof n.validator=="function")return n.validator;var i=Object.keys(n),s=i.indexOf("message");return s!==-1&&i.splice(s,1),i.length===1&&i[0]==="required"?Y.required:Y[this.getType(n)]||void 0},r}();k.register=function(e,t){if(typeof t!="function")throw new Error("Cannot register a validator by type, validator is not a function");Y[e]=t};k.warning=jn;k.messages=Ae;k.validators=Y;const Xn=["","error","validating","success"],ei=je({label:String,labelWidth:{type:[String,Number],default:""},prop:{type:ve([String,Array])},required:{type:Boolean,default:void 0},rules:{type:ve([Object,Array])},error:String,validateStatus:{type:String,values:Xn},for:String,inlineMessage:{type:[String,Boolean],default:""},showMessage:{type:Boolean,default:!0},size:{type:String,values:Qe}}),Ye="ElLabelWrap";var ti=H({name:Ye,props:{isAutoWidth:Boolean,updateAll:Boolean},setup(r,{slots:e}){const t=ie(Oe,void 0),n=ie(ge);n||zt(Ye,"usage: <el-form-item><label-wrap /></el-form-item>");const i=qe("form"),s=V(),a=V(0),o=()=>{var c;if((c=s.value)!=null&&c.firstElementChild){const g=window.getComputedStyle(s.value.firstElementChild).width;return Math.ceil(Number.parseFloat(g))}else return 0},u=(c="update")=>{ot(()=>{e.default&&r.isAutoWidth&&(c==="update"?a.value=o():c==="remove"&&(t==null||t.deregisterLabelWidth(a.value)))})},w=()=>u("update");return at(()=>{w()}),st(()=>{u("remove")}),_t(()=>w()),ne(a,(c,g)=>{r.updateAll&&(t==null||t.registerLabelWidth(c,g))}),Gt(T(()=>{var c,g;return(g=(c=s.value)==null?void 0:c.firstElementChild)!=null?g:null}),w),()=>{var c,g;if(!e)return null;const{isAutoWidth:b}=r;if(b){const j=t==null?void 0:t.autoLabelWidth,q=n==null?void 0:n.hasLabel,d={};if(q&&j&&j!=="auto"){const v=Math.max(0,Number.parseInt(j,10)-a.value),f=t.labelPosition==="left"?"marginRight":"marginLeft";v&&(d[f]=`${v}px`)}return ae("div",{ref:s,class:[i.be("item","label-wrap")],style:d},[(c=e.default)==null?void 0:c.call(e)])}else return ae(Pt,{ref:s},[(g=e.default)==null?void 0:g.call(e)])}}});const ri=["role","aria-labelledby"],ni=H({name:"ElFormItem"}),ii=H({...ni,props:ei,setup(r,{expose:e}){const t=r,n=$t(),i=ie(Oe,void 0),s=ie(ge,void 0),a=Ze(void 0,{formItem:!1}),o=qe("form-item"),u=Ot().value,w=V([]),c=V(""),g=jt(c,100),b=V(""),j=V();let q,d=!1;const v=T(()=>{if((i==null?void 0:i.labelPosition)==="top")return{};const p=$e(t.labelWidth||(i==null?void 0:i.labelWidth)||"");return p?{width:p}:{}}),f=T(()=>{if((i==null?void 0:i.labelPosition)==="top"||i!=null&&i.inline)return{};if(!t.label&&!t.labelWidth&&B)return{};const p=$e(t.labelWidth||(i==null?void 0:i.labelWidth)||"");return!t.label&&!n.label?{marginLeft:p}:{}}),m=T(()=>[o.b(),o.m(a.value),o.is("error",c.value==="error"),o.is("validating",c.value==="validating"),o.is("success",c.value==="success"),o.is("required",fe.value||t.required),o.is("no-asterisk",i==null?void 0:i.hideRequiredAsterisk),(i==null?void 0:i.requireAsteriskPosition)==="right"?"asterisk-right":"asterisk-left",{[o.m("feedback")]:i==null?void 0:i.statusIcon}]),l=T(()=>Xe(t.inlineMessage)?t.inlineMessage:(i==null?void 0:i.inlineMessage)||!1),h=T(()=>[o.e("error"),{[o.em("error","inline")]:l.value}]),x=T(()=>t.prop?me(t.prop)?t.prop:t.prop.join("."):""),O=T(()=>!!(t.label||n.label)),R=T(()=>t.for||w.value.length===1?w.value[0]:void 0),$=T(()=>!R.value&&O.value),B=!!s,G=T(()=>{const p=i==null?void 0:i.model;if(!(!p||!t.prop))return de(p,t.prop).value}),C=T(()=>{const{required:p}=t,F=[];t.rules&&F.push(...he(t.rules));const P=i==null?void 0:i.rules;if(P&&t.prop){const _=de(P,t.prop).value;_&&F.push(...he(_))}if(p!==void 0){const _=F.map((L,U)=>[L,U]).filter(([L])=>Object.keys(L).includes("required"));if(_.length>0)for(const[L,U]of _)L.required!==p&&(F[U]={...L,required:p});else F.push({required:p})}return F}),N=T(()=>C.value.length>0),M=p=>C.value.filter(P=>!P.trigger||!p?!0:Array.isArray(P.trigger)?P.trigger.includes(p):P.trigger===p).map(({trigger:P,..._})=>_),fe=T(()=>C.value.some(p=>p.required)),ht=T(()=>{var p;return g.value==="error"&&t.showMessage&&((p=i==null?void 0:i.showMessage)!=null?p:!0)}),Se=T(()=>`${t.label||""}${(i==null?void 0:i.labelSuffix)||""}`),D=p=>{c.value=p},bt=p=>{var F,P;const{errors:_,fields:L}=p;(!_||!L)&&console.error(p),D("error"),b.value=_?(P=(F=_==null?void 0:_[0])==null?void 0:F.message)!=null?P:`${t.prop} is required`:"",i==null||i.emit("validate",t.prop,!1,b.value)},wt=()=>{D("success"),i==null||i.emit("validate",t.prop,!0,"")},Ft=async p=>{const F=x.value;return new k({[F]:p}).validate({[F]:G.value},{firstFields:!0}).then(()=>(wt(),!0)).catch(_=>(bt(_),Promise.reject(_)))},_e=async(p,F)=>{if(d||!t.prop)return!1;const P=it(F);if(!N.value)return F==null||F(!1),!1;const _=M(p);return _.length===0?(F==null||F(!0),!0):(D("validating"),Ft(_).then(()=>(F==null||F(!0),!0)).catch(L=>{const{fields:U}=L;return F==null||F(!1,U),P?!1:Promise.reject(U)}))},le=()=>{D(""),b.value="",d=!1},Pe=async()=>{const p=i==null?void 0:i.model;if(!p||!t.prop)return;const F=de(p,t.prop);d=!0,F.value=De(q),await ot(),le(),d=!1},xt=p=>{w.value.includes(p)||w.value.push(p)},At=p=>{w.value=w.value.filter(F=>F!==p)};ne(()=>t.error,p=>{b.value=p||"",D(p?"error":"")},{immediate:!0}),ne(()=>t.validateStatus,p=>D(p||""));const ue=tt({...rt(t),$el:j,size:a,validateState:c,labelId:u,inputIds:w,isGroup:$,hasLabel:O,addInputId:xt,removeInputId:At,resetField:Pe,clearValidate:le,validate:_e});return et(ge,ue),at(()=>{t.prop&&(i==null||i.addField(ue),q=De(G.value))}),st(()=>{i==null||i.removeField(ue)}),e({size:a,validateMessage:b,validateState:c,validate:_e,clearValidate:le,resetField:Pe}),(p,F)=>{var P;return ye(),nt("div",{ref_key:"formItemRef",ref:j,class:K(E(m)),role:E($)?"group":void 0,"aria-labelledby":E($)?E(u):void 0},[ae(E(ti),{"is-auto-width":E(v).width==="auto","update-all":((P=E(i))==null?void 0:P.labelWidth)==="auto"},{default:ce(()=>[E(O)?(ye(),It(Mt(E(R)?"label":"div"),{key:0,id:E(u),for:E(R),class:K(E(o).e("label")),style:Me(E(v))},{default:ce(()=>[ee(p.$slots,"label",{label:E(Se)},()=>[Rt(Re(E(Se)),1)])]),_:3},8,["id","for","class","style"])):Ne("v-if",!0)]),_:3},8,["is-auto-width","update-all"]),Le("div",{class:K(E(o).e("content")),style:Me(E(f))},[ee(p.$slots,"default"),ae(Nt,{name:`${E(o).namespace.value}-zoom-in-top`},{default:ce(()=>[E(ht)?ee(p.$slots,"error",{key:0,error:b.value},()=>[Le("div",{class:K(E(h))},Re(b.value),3)]):Ne("v-if",!0)]),_:3},8,["name"])],6)],10,ri)}}});var yt=He(ii,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/form/src/form-item.vue"]]);const di=Lt(wn,{FormItem:yt}),ci=Bt(yt);export{di as E,ci as a,te as b};
