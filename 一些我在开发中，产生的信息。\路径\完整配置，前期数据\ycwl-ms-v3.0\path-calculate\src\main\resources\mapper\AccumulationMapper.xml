<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.pathcalculate.mapper.AccumulationMapper">

    <select id="selectCoordinates" resultType="com.ict.ycwl.pathcalculate.pojo.LngAndLat">
        SELECT longitude, latitude
        FROM accumulation
        where is_delete = 0;
    </select>

    <update id="updateAccumulationIdByLonAndLat">
        UPDATE accumulation
        SET path_accumulation_id=#{accumulationId}
        where longitude = #{longitude}
          and latitude = #{latitude}
          and is_delete = 0;
    </update>


</mapper>