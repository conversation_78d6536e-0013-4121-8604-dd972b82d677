package com.ict.ycwl.pathcalculate.service;

import com.ict.ycwl.pathcalculate.pojo.DoublePoint;
import com.ict.ycwl.pathcalculate.pojo.LngAndLat;
import com.ict.ycwl.pathcalculate.pojo.ResultRoute;
import com.ict.ycwl.pathcalculate.pojo.Route;
import org.apache.commons.math3.ml.clustering.CentroidCluster;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface CalculateService {

//    /**
//     * 将所有聚集区点进行聚类
//     *
//     * @param storeNumber     每个簇所包含的最大点数
//     * @param minNumOfCluster 最小簇数，用于计算sse获取最佳簇数
//     * @param maxNumOfCluster 最大簇数，用于计算sse获取最佳簇数
//     */
//    void clustering(List<LngAndLat> lngAndLats, int storeNumber, int minNumOfCluster, int maxNumOfCluster);

    /**
     * 将所有点分成一个簇，主要是为了获取簇心
     *
     * @param lngAndLats 数据集
     * @return 分簇结果
     */
    List<CentroidCluster<DoublePoint>> assignNumberOfCluster(int assignNumber, List<LngAndLat> lngAndLats);

//    Map<List<String>, Double> calculate(int assignNumber, List<LngAndLat> lngAndLats, Long transitDepotId, double[] transitDepot, String apiKey);

    List<ResultRoute> calculateAll(String apiKey) throws Exception;

    List<ResultRoute> calculateOne(String areaName, String apiKey, int assignNumber) throws Exception;

    List<ResultRoute> calculateRangedRoute(String routeName1, String routeName2, String apiKey);
}
