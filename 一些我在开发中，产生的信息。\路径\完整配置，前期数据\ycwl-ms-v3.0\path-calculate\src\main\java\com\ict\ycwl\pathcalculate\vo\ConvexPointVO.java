package com.ict.ycwl.pathcalculate.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;

/**
 * 凸包打卡点返回参数
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ConvexPointVO {
    private Double longitude;

    private Double latitude;

    private Long accumulationId;

    private String routeName;
}
