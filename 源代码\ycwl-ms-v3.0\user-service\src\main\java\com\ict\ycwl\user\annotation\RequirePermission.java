package com.ict.ycwl.user.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
//需要特定权限才能进行操作的注解
public @interface RequirePermission {
    String value(); // 权限点名称
    boolean required() default true;
}
