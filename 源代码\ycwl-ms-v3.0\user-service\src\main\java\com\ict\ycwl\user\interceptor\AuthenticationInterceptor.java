package com.ict.ycwl.user.interceptor;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.auth0.jwt.exceptions.JWTVerificationException;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ict.ycwl.user.annotation.PassToken;
import com.ict.ycwl.user.annotation.RequirePermission;
import com.ict.ycwl.user.annotation.UserLoginToken;
import com.ict.ycwl.user.dao.OperationDao;
import com.ict.ycwl.user.dao.RoleOperationDao;
import com.ict.ycwl.user.dao.UserDao;
import com.ict.ycwl.user.pojo.Operation;
import com.ict.ycwl.user.pojo.RoleOperation;
import com.ict.ycwl.user.pojo.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.util.List;

public class AuthenticationInterceptor implements HandlerInterceptor {
    @Autowired
    private UserDao userDao;

    @Autowired
    OperationDao operationDao;

    @Autowired
    RoleOperationDao roleOperationDao;

    @Override
    public boolean preHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object object) throws Exception {

        // 从 http 请求头中取出 token
        String token = httpServletRequest.getHeader("token");

        // 如果不是映射到方法直接通过
        if(!(object instanceof HandlerMethod)){
            return true;
        }

        HandlerMethod handlerMethod=(HandlerMethod)object;
        Method method=handlerMethod.getMethod();
        //检查是否有passtoken注释，有则跳过认证

        if (method.isAnnotationPresent(PassToken.class)) {
            PassToken passToken = method.getAnnotation(PassToken.class);
            if (passToken.required()) {
                return true;
            }
        }
        //检查有没有需要用户权限的注解
        if (method.isAnnotationPresent(UserLoginToken.class)) {
            UserLoginToken userLoginToken = method.getAnnotation(UserLoginToken.class);
            if (userLoginToken.required()) {
                // 执行认证
                if (token == null) {
                    throw new RuntimeException("无token，请重新登录");
                }
                // 获取 token 中的 user id
                String userId;
                try {
                    userId = JWT.decode(token).getClaim("userId").asString();
                } catch (JWTDecodeException j) {
                    throw new RuntimeException("401");
                }
                QueryWrapper<User> wrapper = new QueryWrapper<>();
                wrapper.eq("userId",userId);
                User user = userDao.selectOne(wrapper);

                if (user == null) {
                    throw new RuntimeException("用户不存在，请重新登录");
                }
                // 验证 token
                JWTVerifier jwtVerifier = JWT.require(Algorithm.HMAC256(user.getPassword())).build();
                try {
                    jwtVerifier.verify(token);
                } catch (JWTVerificationException e) {
                    throw new RuntimeException("401");
                }

                // 检查是否需要特定权限
                if (method.isAnnotationPresent(RequirePermission.class)) {
                    RequirePermission requirePermission = method.getAnnotation(RequirePermission.class);
                    if (requirePermission.required()) {
                        String requiredPermission = requirePermission.value();

                        // 检查用户是否有该权限
                        if (!hasPermission(user, requiredPermission)) {
                            throw new RuntimeException("权限不足，无法执行此操作");
                        }
                    }
                }

                return true;
            }
        }
        return true;
    }

    @Override
    public void postHandle(HttpServletRequest httpServletRequest,
                           HttpServletResponse httpServletResponse,
                           Object o, ModelAndView modelAndView) throws Exception {

    }
    @Override
    public void afterCompletion(HttpServletRequest httpServletRequest,
                                HttpServletResponse httpServletResponse,
                                Object o, Exception e) throws Exception {

    }

    /**
     * 检查用户是否有指定权限
     * @param user 用户对象
     * @param permissionName 权限名称
     * @return 是否有权限
     */
    private boolean hasPermission(User user, String permissionName) {
        try {
            // 特殊处理：如果是系统管理员，直接返回true
            if ("ycwlAdmin".equals(user.getLoginName()) || "系统管理员".equals(user.getPosition())) {
                return true;
            }

            // 获取用户角色ID
            Long roleId = user.getRoleId();
            if (roleId == null) {
                return false;
            }

            // 查询权限点
            QueryWrapper<Operation> operationWrapper = new QueryWrapper<>();
            operationWrapper.eq("operation_name", permissionName);
            Operation operation = operationDao.selectOne(operationWrapper);

            if (operation == null) {
                return false;
            }

            // 查询角色是否有该权限
            QueryWrapper<RoleOperation> roleOperationWrapper = new QueryWrapper<>();
            roleOperationWrapper.eq("role_id", roleId)
                               .eq("operation_id", operation.getOperationId())
                               .eq("status", 1);
            RoleOperation roleOperation = roleOperationDao.selectOne(roleOperationWrapper);

            return roleOperation != null;
        } catch (Exception e) {
            // 出现异常时，为了安全起见，返回false
            return false;
        }
    }
    }