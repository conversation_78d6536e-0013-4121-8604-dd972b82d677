package com.ict.ycwl.guestbook.service.impl;

import com.ict.ycwl.guestbook.domain.Area;
import com.ict.ycwl.guestbook.mapper.AreaMapper;
import com.ict.ycwl.guestbook.service.AreaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class AreaServiceImpl implements AreaService {

    @Autowired
    private AreaMapper areaMapper;

    @Override
    public List<Area> getAllArea() {
        return areaMapper.selectAll();
    }


}
