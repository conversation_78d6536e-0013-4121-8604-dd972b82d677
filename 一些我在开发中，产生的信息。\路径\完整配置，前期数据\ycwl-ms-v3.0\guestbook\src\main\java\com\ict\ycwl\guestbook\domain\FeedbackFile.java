package com.ict.ycwl.guestbook.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class FeedbackFile {

    @TableId(type = IdType.ASSIGN_ID)
    private Long feedbackFileId;

    private String feedbackFilePath;

    private String feedbackFileRealPath;

    private Long feedbackId;
}
