<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.datamanagement.mapper.StoreMapper">
    <resultMap id="BaseResultMap" type="com.ict.datamanagement.domain.entity.Store">
        <id property="storeId" column="store_id" jdbcType="BIGINT"/>
        <result property="customerCode" column="customer_code" jdbcType="VARCHAR"/>
        <result property="storeName" column="store_name" jdbcType="VARCHAR"/>
        <result property="storeAddress" column="store_address" jdbcType="VARCHAR"/>
        <result property="longitude" column="longitude" jdbcType="DOUBLE"/>
        <result property="latitude" column="latitude" jdbcType="DOUBLE"/>
        <result property="type" column="type" jdbcType="VARCHAR"/>
        <result property="orderCycle" column="order_cycle" jdbcType="VARCHAR"/>
        <result property="district" column="district" jdbcType="VARCHAR"/>
        <result property="areaName" column="area_name" jdbcType="VARCHAR"/>
        <result property="contactName" column="contact_name" jdbcType="VARCHAR"/>
        <result property="contactPhone" column="contact_phone" jdbcType="VARCHAR"/>
        <result property="head" column="head" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="CHAR"/>
        <result property="customerManagerId" column="customer_manager_id" jdbcType="BIGINT"/>
        <result property="accumulationId" column="accumulation_id" jdbcType="BIGINT"/>
        <result property="areaId" column="area_id" jdbcType="BIGINT"/>
        <result property="routeId" column="route_id" jdbcType="BIGINT"/>
        <result property="routeName" column="route_name" jdbcType="VARCHAR"/>
        <result property="customerManagerName" column="customer_manager_name" jdbcType="VARCHAR"/>
        <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="gear" column="gear" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="BIGINT"/>
        <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
        <result property="groupId" column="group_id" jdbcType="BIGINT"/>
        <result property="accumulationArea" column="accumulation_area" jdbcType="VARCHAR"/>
        <result property="accumulationAddress" column="accumulation_address" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        store_id
        ,customer_code,store_name,
        store_address,longitude,latitude,
        type,order_cycle,district,
        area_name,contact_name,contact_phone,head,
        status,customer_manager_id,accumulation_id,
        area_id,route_id,route_name,
        customer_manager_name,is_delete,create_time,
        update_time,gear,create_by,update_by,group_id
    </sql>


    <!--处理配送域不等于韶关-->
    <update id="insertAreaNameOne">
        UPDATE store
        SET area_name = CASE
                            WHEN LEFT (delivery_area, 2) = '韶关' THEN area_name
            WHEN LEFT (delivery_area, 2) = '坪石' THEN '乐昌市'
            WHEN LEFT (delivery_area, 2) = '乐昌' THEN '乐昌市'
            WHEN LEFT (delivery_area, 2) = '曲江' THEN '曲江区'
            WHEN LEFT (delivery_area, 2) = '乳源' THEN '乳源瑶族自治县'
            WHEN LEFT (delivery_area, 2) = '始兴' THEN '始兴县'
            WHEN LEFT (delivery_area, 2) = '翁源' THEN '翁源县'
            WHEN LEFT (delivery_area, 2) = '仁化' THEN '仁化县'
            WHEN LEFT (delivery_area, 2) = '新丰' THEN '新丰县'
            WHEN LEFT (delivery_area, 2) = '南雄' THEN '南雄市'
            ELSE LEFT (delivery_area, 2)
        END
        WHERE (area_name IS NULL OR area_name = '') AND LEFT(delivery_area, 2) != '韶关' ;
    </update>

    <!--设置分组id-->
    <update id="setGroupId">
        UPDATE store JOIN group_areas
        ON store.area_id=group_areas.area_id SET store.group_id=group_areas.group_id;
    </update>

    <!--根据商铺地址填充大区-->
    <update id="insertAreaNameTwo">
        UPDATE store
        SET area_name = CASE
                            WHEN store_address LIKE '%浈江%' THEN '浈江区'
                            WHEN store_address LIKE '%武江%' THEN '武江区'
                            WHEN store_address LIKE '%南雄%' THEN '南雄市'
                            WHEN store_address LIKE '%曲江%' THEN '曲江区'
                            WHEN store_address LIKE '%乐昌%' THEN '乐昌市'
                            WHEN store_address LIKE '%乳源%' THEN '乳源瑶族自治县'
                            WHEN store_address LIKE '%始兴%' THEN '始兴县'
                            WHEN store_address LIKE '%翁源%' THEN '翁源县'
                            WHEN store_address LIKE '%仁化%' THEN '仁化县'
                            WHEN store_address LIKE '%新丰%' THEN '新丰县'
                            WHEN store_address LIKE '%大桥镇%' THEN '乳源瑶族自治县'
                            WHEN store_address LIKE '%必背%' THEN '乳源瑶族自治县'
                            ELSE area_name
            END
        WHERE (area_name IS NULL OR area_name = '');
    </update>

    <!--根据大区名称填充大区id-->
    <update id="setAreaId">
        update store INNER JOIN area
        ON store.area_name=area.area_name SET store.area_id =area.area_id
    </update>

    <!--填充大区名称-->
    <update id="setLocationType" statementType="CALLABLE">
        { CALL setLocationType()}
    </update>
    <!--<update id="deleteAll">
        update store set is_delete=1
    </update>-->
    <delete id="deleteAll">
        delete
        from store
    </delete>


    <select id="selectCountByAddress" resultType="java.lang.Integer">
        select COUNT(*)
        from store
        where store_address = #{storeAddress1}
          and is_delete = 0
    </select>
    <select id="selectByGIS" resultType="java.lang.String">
        select store_address
        from store
        where longitude = #{longitude}
          and latitude = #{latitude}
          and is_delete = 0
    </select>
    <select id="selectAccByGIS" resultType="com.ict.datamanagement.domain.dto.AccumulationDto">
        select accumulation_id, accumulation_address, longitude, latitude
        from accumulation
        where is_delete = 0
          and longitude = #{longitude}
          and latitude = #{latitude}
    </select>
    <select id="selectStoreByGIS" resultType="java.lang.String">
        select store_address
        from store
        where is_delete = 0
          and longitude = #{longitude}
          and latitude = #{latitude}
    </select>
    <select id="selectCityCount" resultType="java.lang.Integer">
        select COUNT(*)
        from store
        where is_delete = 0
          and location_type = 0;
    </select>

    <select id="selectTownshipCount" resultType="java.lang.Integer">
        select COUNT(*)
        from store
        where is_delete = 0
          and location_type = 1;
    </select>
    <select id="selectAccByRouteId" resultType="com.ict.datamanagement.domain.dto.AccumulationDto">
        select accumulation_id, accumulation_address, longitude, latitude
        from accumulation
        where is_delete = 0
          and route_id = #{routeId}
    </select>
    <select id="selectStoreByAcc" resultType="com.ict.datamanagement.domain.dto.route.StoreVo">
        select store_id, store_name, store_address, customer_code, contact_name,create_time as storeCreateTime
                       ,update_time as storeUpdateTime,status as storeStatus
        from store
        where is_delete = 0
          and accumulation_id = #{accumulationId}
    </select>

    <select id="selectCountByCustomerCode" resultType="int">
        select COUNT(*)
        from store
        where customer_code = #{customerCode}
          and is_delete = 0
    </select>

    <select id="selectCreateTimeByid" resultType="java.sql.Date">
        select create_time
        from store
        where store_id = #{storeId}
          and is_delete = 0
    </select>

    <select id="selectUpdateTimeByid" resultType="java.sql.Date">
        select update_time
        from store
        where store_id = #{storeId}
          and is_delete = 0
    </select>

    <select id="getAcc" resultType="java.lang.String">
        select a.accumulation_name
        from store s
                 join accumulation a on s.accumulation_id = a.accumulation_id
        where store_id = #{storeId}
    </select>

    <select id="selectCount" resultMap="BaseResultMap">
        SELECT
        s.store_id,s.customer_code,s.customer_manager_name,s.contact_phone,s.store_address,s.area_name,s.route_name,
        st.head AS contactName,s.status,s.create_time,s.is_special,s.remark,acc.accumulation_name as accumulation_area,acc.accumulation_address
        FROM
        store s
        LEFT JOIN
        store_two st ON st.store_id = s.store_id LEFT JOIN area a on s.area_name=a.area_name LEFT JOIN accumulation acc on acc.accumulation_id=s.accumulation_id
        <where>
            <if test="customerCode != null and customerCode != ''">
                AND s.customer_code =#{customerCode}
            </if>
            <if test="storeAddress != null and storeAddress != ''">
                AND s.store_address LIKE CONCAT('%', #{storeAddress}, '%')
            </if>
            <if test="contactName != null and contactName != ''">
                AND st.head = #{contactName}
            </if>
            <if test="areaId != null">
                AND a.area_id = #{areaId}
            </if>
            <if test="routeId != null">
                AND s.route_id = #{routeId}
            </if>
            <if test="groupId != null">
                AND s.group_id = #{groupId}
            </if>
            <if test="accumulationId != 0">
                AND s.accumulation_id = #{accumulationId}
            </if>
            and s.is_delete=0
        </where>
        ORDER BY s.is_special DESC,s.create_time DESC
    </select>

    <update id="addSpecialPoint" >
             update store set is_special=#{isSpecialPoint},remark=#{remark},special_type=#{specialType} where store_id=#{storeId}
    </update>

    <insert id="addErrorPointInfo">
        insert into error_point (current_store_longitude,current_store_latitude,pairing_store_longitude,pairing_store_latitude) values (#{longitude},#{latitude},#{longitude1},#{latitude1})
    </insert>
</mapper>
