<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.datamanagement.mapper.TeamMapper">

    <update id="updateTeamById">
        update team set team_name=#{teamName},delivery_area_name=#{deliveryAreaName},transit_depot_name=#{transitDepotName},car_sum=#{carSum},route_sum=#{routeSum},is_delete=#{isDelete} where team_id=#{teamId}
    </update>
    <update id="deleteTeamById">
        delete from team where team_id=#{teamId}
    </update>
    <update id="updateTeamtransitDepot">
        update team set transit_depot_name=#{transitDepotName} where team_id=#{id} and is_delete=0
    </update>
    <update id="deleteTeamInfoById">
        update team set delivery_area_name =null,transit_depot_name=null,car_sum=0,route_sum=0 where team_id=#{teamId}
    </update>

    <select id="selectById" resultType="com.ict.datamanagement.domain.entity.Team">
        select * from team where team_id =#{id} and is_delete=0
    </select>
    <select id="selectByTeamName" resultType="com.ict.datamanagement.domain.entity.Team">
        select * from team where team_name=#{teamName} and is_delete=0
    </select>
    <select id="getTeamInfo" resultType="com.ict.datamanagement.domain.vo.transitDepotV0.TeamInfoVO">
        select team_id,team_name,delivery_area_name AS deliveryName
        FROM team AS t1 JOIN `group` AS t2 ON t1.team_name=t2.group_name WHERE is_delete=0
    </select>
    <select id="selectNames" resultType="java.lang.String">
        select DISTINCT team_name from team where is_delete=0
    </select>
    <select id="selectNameByteamName" resultType="java.lang.String">
        select transit_depot_name from team where team_name=#{teamName} and is_delete=0
    </select>
    <select id="selectTeamAndTransitDepot"
            resultType="com.ict.datamanagement.domain.dto.delivery.SelectTeamAndTransitDepotRequest">
        SELECT t2.team_name, GROUP_CONCAT(t1.transit_depot_name SEPARATOR ',') AS transit_depots
        FROM team AS t2
                 LEFT JOIN transit_depot AS t1 ON t1.group_id = t2.team_id where t2.is_delete=0 and t1.is_delete=0
        GROUP BY t2.team_id;
    </select>
</mapper>