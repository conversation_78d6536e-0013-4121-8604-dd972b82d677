import{v as ye,w as _e,x as Xe,n as Ze,E as ke,h as He,y as Je,A as Ye,o as el,r as ll,B as tl,C as ol,D as al,F as nl}from"./base-kpSIrADU.js";/* empty css                */import{a as ce,E as me,b as Ce}from"./table-column-DZpqkK6R.js";import{E as we}from"./input-DqmydyK4.js";import{a as he,E as Ve}from"./select-BOcQ2ynX.js";import{b as Ne}from"./scrollbar-BNeK4Yi-.js";import{E as H}from"./button-IGKrEYb9.js";import"./checkbox-DWZ5xHlw.js";/* empty css             */import{a as xe,E as De}from"./form-item-Bd-FvCZ5.js";import{_ as ee}from"./空心问号-DWucuajp.js";import{p as sl}from"./pickMap-CANvhVXt.js";import{E as se}from"./overlay-D06mCCGK.js";import{d as te,r as p,m as rl,c as Z,b as l,w as t,u as o,Q as J,o as g,a,R as u,S as O,p as w,N as Ue,K as W,P as le,z as il,X as j,n as ul,G as dl,W as pl,T as cl}from"./index-C0QCllTd.js";import{_ as re}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{g as ml,E as fl,a as gl}from"./progress-BWKU0l_-.js";import{u as fe}from"./pick-BIEvBaQG.js";import{a as D}from"./index-m25zEilF.js";import{v as vl}from"./directive-BBeDU6Ak.js";import"./_commonjsHelpers-BbMlrU8H.js";import"./merge-B3fxVp56.js";import"./_initCloneObject-BmTtMqsv.js";import"./flatten-BP0fiJV-.js";import"./castArray-CSO3s-vM.js";import"./getMapKey-C0z490Cj.js";import"./modifyUserAgent-Ct74EQNt.js";import"./index-Bp4b1Vvq.js";import"./index-DOdSMika.js";const bl={class:"changeMilk"},yl={class:"flex-center"},_l={class:"ellipsis-2-lines"},kl={key:1},Cl={class:"flex-center"},wl={class:"btns"},hl=te({__name:"changeMilk",props:{data:{type:Object,default:()=>({})}},emits:["confirmChange"],setup(M,{expose:U,emit:F}){const V=p(!1);U({MilkOpen:V});const N=M,_=p({locks:"",deliveryDistance:"",pickupContainers:"",type:""});rl(()=>N.data,h=>{_.value={locks:h.locks,deliveryDistance:Number(h.deliveryDistance),pickupContainers:h.pickupContainers,type:h.type}},{deep:!0,immediate:!0});const C=p(),R=F;function x(){V.value=!1,console.log(_.value),C.value.validate(h=>{h&&(R("confirmChange",_.value),V.value=!1)})}function v(){C.value.resetFields(),V.value=!1}return(h,c)=>{const E=xe,A=we,b=he,k=Ve,L=Ne,f=De,T=H,B=se;return g(),Z("div",bl,[l(B,{modelValue:o(V),"onUpdate:modelValue":c[4]||(c[4]=d=>J(V)?V.value=d:null),title:"修改定点取货数据",width:"60%",height:"70%","close-on-click-modal":!1},{footer:t(()=>[a("div",wl,[l(T,{type:"primary",onClick:v},{default:t(()=>c[8]||(c[8]=[u("取消")])),_:1}),l(T,{type:"primary",style:{"margin-left":"100px"},onClick:x},{default:t(()=>c[9]||(c[9]=[u("确定")])),_:1})])]),default:t(()=>[a("div",yl,[l(f,{"label-width":"auto",width:"100%",class:"areaForm",model:o(_),ref_key:"formRef",ref:C},{default:t(()=>[l(E,{label:"名称"},{default:t(()=>[a("p",null,O(M.data.storeName),1)]),_:1}),l(E,{label:"档位"},{default:t(()=>[a("p",null,O(M.data.gear),1)]),_:1}),l(E,{label:"编码"},{default:t(()=>[a("p",null,O(M.data.customerCode),1)]),_:1}),l(E,{label:"配送距离",prop:"deliveryDistance"},{default:t(()=>[l(A,{placeholder:"请输入",modelValue:o(_).deliveryDistance,"onUpdate:modelValue":c[0]||(c[0]=d=>o(_).deliveryDistance=d)},null,8,["modelValue"])]),_:1}),l(E,{label:"线路"},{default:t(()=>[a("p",null,O(M.data.customerCode),1)]),_:1}),l(E,{label:"取货柜类型",prop:"type"},{default:t(()=>[l(k,{placeholder:"请选择",modelValue:o(_).type,"onUpdate:modelValue":c[1]||(c[1]=d=>o(_).type=d)},{default:t(()=>[l(b,{label:"01",value:"01"}),l(b,{label:"02",value:"02"}),l(b,{label:"03",value:"03"}),l(b,{label:"04",value:"04"}),l(b,{label:"无",value:""})]),_:1},8,["modelValue"])]),_:1}),l(E,{label:"地址"},{default:t(()=>[a("div",_l,O(M.data.storeAddress),1)]),_:1}),l(E,{label:"取货柜地址",prop:"pickupContainers"},{default:t(()=>[M.data.locks==0?(g(),w(A,{key:0,placeholder:"请输入",modelValue:o(_).pickupContainers,"onUpdate:modelValue":c[2]||(c[2]=d=>o(_).pickupContainers=d)},null,8,["modelValue"])):(g(),Z("p",kl,O(M.data.pickupContainers?M.data.pickupContainers:""),1))]),_:1}),l(E,{prop:"locks"},{label:t(()=>[a("div",Cl,[l(L,{placement:"top",effect:"dark"},{content:t(()=>c[5]||(c[5]=[u(" 加锁的商户不受系统计算的影响，选址配置不会更变。如需更改配置则需要更改为解锁状态。 ")])),default:t(()=>[c[6]||(c[6]=a("img",{src:ee,alt:"",width:"16px",height:"16px",style:{"padding-right":"4px"}},null,-1))]),_:1}),c[7]||(c[7]=a("span",null,"是否加锁",-1))])]),default:t(()=>[l(k,{placeholder:"请选择",modelValue:o(_).locks,"onUpdate:modelValue":c[3]||(c[3]=d=>o(_).locks=d)},{default:t(()=>[l(b,{label:"解锁",value:0}),l(b,{label:"加锁",value:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])])]),_:1},8,["modelValue"])])}}}),Vl=re(hl,[["__scopeId","data-v-ae4723b9"]]),Nl={class:"download"},xl={class:"dialog-footer"},Dl=te({__name:"uploadTable",setup(M,{expose:U}){const F=p([]),V=p(["csv","xls","xlsx"]),N=p([{fileName:"",fileSize:""}]),_=fe(),C=p(!1),R=p(!1),x=p(),v=p(0),h=new AbortController;U({uploadVisible:C});function c(){x.value.clearFiles(),C.value=!1}function E(){var d;(d=x.value)==null||d.submit(),C.value=!1}async function A(){_.downloadNullForm({code:0}).then(d=>{let i=document.createElement("a");i.download="取货户空白表格模版.xlsx",i.style.display="none";let P=URL.createObjectURL(d);i.href=P,document.body.appendChild(i),i.click(),URL.revokeObjectURL(P),document.body.removeChild(i)})}function b(){x.value.clearFiles()}function k(){N.value=[{}],v.value<100&&h.abort()}function L(){var d;v.value==100?D.error("已上传成功,请勿重复上传"):(d=x.value)==null||d.submit()}function f(d){if(d.type!=""||d.type!=null||d.type!=null){const i=d.name.replace(/.+\./,"").toLowerCase();return d.size/1024/1024<20?V.value.includes(i)?(F.value[0]=d,N.value=[{fileName:F.value[0].name,fileSize:(F.value[0].size/1024).toFixed(2)+"kb"}],!0):(D.error("上传文件格式不正确!"),!1):(D.error("上传文件大小不能超过 20MB!"),!1)}}function T(d){x.value.clearFiles();const i=d[0];i.uid=ml(),x.value.handleStart(i),F.value[0]=i}function B(d){const i={signal:h.signal,onUploadProgress:z=>{v.value=Number((z.loaded/z.total*100).toFixed(1))-1}};let P=new FormData;P.append("File",d.file),P.append("Authorization",localStorage.getItem("token")),_.importUser(P,i).then(z=>{if(z.message==="系统异常"){D.error("系统异常"),x.value.clearFiles();return}if(z.includes("导入失败")){D.error("导入失败"),x.value.clearFiles();return}z.includes("导入成功")&&(v.value=100,D({message:"上传成功",type:"success"}),R.value=!0,C.value=!1)})}return(d,i)=>{const P=ke,z=H,m=fl,oe=se,q=ce,ie=gl,ue=me;return g(),Z(Ue,null,[l(oe,{modelValue:o(C),"onUpdate:modelValue":i[0]||(i[0]=Y=>J(C)?C.value=Y:null),title:"导入表格",class:"transform",width:"50%","align-center":"","close-on-click-modal":!1,onClose:c},{footer:t(()=>[a("div",xl,[l(z,{onClick:c,type:"primary"},{default:t(()=>i[5]||(i[5]=[u("取消")])),_:1}),l(z,{type:"primary",onClick:E},{default:t(()=>i[6]||(i[6]=[u(" 确定 ")])),_:1})])]),default:t(()=>[l(m,{ref_key:"uploadRef",ref:x,class:"upload-demo",drag:"",action:"",limit:1,"file-list":o(F),"http-request":B,multiple:!1,"on-exceed":T,"before-upload":f,"auto-upload":!1},{tip:t(()=>[a("div",Nl,[l(z,{type:"primary",icon:o(ye),class:"button",onClick:A},{default:t(()=>i[2]||(i[2]=[u("点击此处下载空白表格")])),_:1},8,["icon"])])]),default:t(()=>[l(P,{class:"el-icon--upload"},{default:t(()=>[l(o(_e))]),_:1}),i[3]||(i[3]=a("div",{class:"el-upload__text"},[u("拖拽文件到此处"),a("em",null,"点击上传")],-1)),i[4]||(i[4]=a("div",{class:"el-upload__text",style:{width:"100%"}},[a("span",{style:{color:"red"}},"* 一次只能导入一个文件,仅支持xls, xlsx和csv格式")],-1))]),_:1},8,["file-list"])]),_:1},8,["modelValue"]),l(oe,{modelValue:o(R),"onUpdate:modelValue":i[1]||(i[1]=Y=>J(R)?R.value=Y:null),title:"上传文件",class:"transform",width:"50%","align-center":"","close-on-click-modal":!1,onClose:b},{default:t(()=>[l(ue,{ref:"tableRef",data:o(N),"cell-style":{textAlign:"center"},"header-cell-style":{height:"1vh","text-align":"center"},size:"small","row-style":{height:"4vh"},style:{"font-size":"1vw"}},{default:t(()=>[l(q,{label:"文件名","min-width":"1%",prop:"fileName"}),l(q,{label:"大小","min-width":"1%",prop:"fileSize"}),o(N)[0].fileName?(g(),w(q,{key:0,label:"状态","min-width":"1%"},{default:t(()=>[l(ie,{"text-inside":!0,"stroke-width":26,percentage:o(v)},null,8,["percentage"])]),_:1})):W("",!0),o(N)[0].fileName?(g(),w(q,{key:1,label:"操作","min-width":"1%"},{default:t(()=>[l(z,{size:"small",type:"primary",onClick:k,icon:o(Xe)},{default:t(()=>i[7]||(i[7]=[u(" 删除 ")])),_:1},8,["icon"]),l(z,{size:"small",type:"primary",onClick:L,icon:o(Ze)},{default:t(()=>i[8]||(i[8]=[u(" 重传 ")])),_:1},8,["icon"])]),_:1})):W("",!0)]),_:1},8,["data"])]),_:1},8,["modelValue"])],64)}}}),Ul=re(Dl,[["__scopeId","data-v-43ff4802"]]),Rl={class:"pageDivide"},El=te({__name:"uploadNote",setup(M,{expose:U}){const F=fe(),V=p(!1),N=p(),_=p(),C=p(null),R=le({pageNum:1,pageSize:6});U({noteVisible:V,onOpenDialog:x});function x(){R.pageNum=1,A()}function v(){C.value=null}function h(b,k){F.downloadUserLog({fileName:b,importTime:k}).then(L=>{let f=document.createElement("a");f.download=b,f.style.display="none";let T=URL.createObjectURL(L);f.href=T,document.body.appendChild(f),f.click(),URL.revokeObjectURL(T),document.body.removeChild(f)})}function c(b){F.deleteCarLog({logsId:b}).then(()=>{D.success("删除成功"),A()})}function E(b=1){R.pageNum=b,A()}function A(){F.importUserLog({...R,type:"2"}).then(b=>{C.value=b,R.pageNum=b.current,_.value=b.records})}return(b,k)=>{const L=ce,f=H,T=me,B=Ce,d=se;return g(),w(d,{modelValue:o(V),"onUpdate:modelValue":k[0]||(k[0]=i=>J(V)?V.value=i:null),title:"导入日志",class:"transform",width:"50%","align-center":"","close-on-click-modal":!1,onClose:v},{default:t(()=>[k[1]||(k[1]=a("div",{style:{"font-size":"20px"}},"最近6个月的导入记录",-1)),o(_)?(g(),w(T,{key:0,ref_key:"tableRef",ref:N,data:o(_),"cell-style":{textAlign:"center"},"header-cell-style":{height:"2vh","text-align":"center"},size:"small","row-style":{height:"4.3vh"},style:{"font-size":"1vw"}},{default:t(()=>[l(L,{label:"文件名","min-width":"1%",prop:"fileName"}),l(L,{label:"大小","min-width":"1%",prop:"fileSize"}),l(L,{label:"导入时间","min-width":"1%",prop:"importTime"}),l(L,{label:"用户","min-width":"1%",prop:"userName"}),l(L,{label:"状态","min-width":"1%",prop:"status"}),l(L,{label:"操作","min-width":"2%"},{default:t(i=>[i.row.status.includes("全部导入成功")?W("",!0):(g(),w(f,{key:0,size:"small",type:"primary",onClick:P=>h(i.row.fileName,i.row.importTime),icon:o(ye)},null,8,["onClick","icon"])),l(f,{size:"small",type:"primary",onClick:P=>c(Number(i.row.fileId)),icon:o(He)},null,8,["onClick","icon"])]),_:1})]),_:1},8,["data"])):W("",!0),a("div",Rl,[o(C)?(g(),w(B,{key:0,layout:"prev, pager, next","current-page":o(R).pageNum,"page-size":o(R).pageSize,total:Number(o(C).total),onCurrentChange:E},null,8,["current-page","page-size","total"])):W("",!0)])]),_:1},8,["modelValue"])}}}),zl=re(El,[["__scopeId","data-v-bdd86f6c"]]),Sl={class:"dialog-footer"},Fl=te({__name:"confirm",props:{list:{}},emits:["wait"],setup(M,{expose:U,emit:F}){const V=F,N=p(!1);U({confirmDialogVis:N});const _=M;function C(){V("wait",_.list),N.value=!1}function R(){N.value=!1}return(x,v)=>{const h=H,c=se;return g(),w(c,{modelValue:o(N),"onUpdate:modelValue":v[0]||(v[0]=E=>J(N)?N.value=E:null),title:"确认变更",class:"transform",width:"40%","align-center":"","close-on-click-modal":!1},{footer:t(()=>[a("div",Sl,[l(h,{onClick:R,type:"primary"},{default:t(()=>v[1]||(v[1]=[u("取消")])),_:1}),l(h,{type:"primary",onClick:C},{default:t(()=>v[2]||(v[2]=[u("确定")])),_:1})])]),default:t(()=>[v[3]||(v[3]=a("div",{class:"content"},"是否确认进入分配商户",-1))]),_:1},8,["modelValue"])}}}),Ll={class:"pickup"},$l={class:"dataSection"},Ml={class:"calculate"},Pl={class:"calData"},Tl={class:"calBtn"},Il={class:"section"},Al={class:"searchContent"},Bl={class:"range"},Gl={class:"search"},Ol={key:0,class:"searchView"},jl={class:"content"},Wl={class:"group"},ql={class:"flex-center"},Kl={class:"btns"},Ql={class:"butContent"},Xl={class:"table"},Zl={class:"flex-center"},Hl={class:"mapSectionContent"},Jl={class:"map"},Yl={class:"mapButton"},et={class:"pageDivide"},lt=te({__name:"pickup",setup(M){const U=fe(),F=p(),V=p(),N=p(),_=p(),C=p(),R=p(),x=p([]),v=p(),h=p(null),c=p(),E=p(),A=p([]),b=p(!0),k=le({pageNum:1,pageSize:12}),L=p([]),f=le({gear:"",avgDistance:"",roadGrade:"",levelParam:""}),T=p(!0),B=p(""),d=p(!1),i=p(),P=p({}),z=p(!1),m=le({color:"",gear:"",customerCode:"",deliveryDistance:"",type:"",pickupContainers:"",storeName:"",storeAddress:"",pageNum:1,pageSize:12,accumulationName:""}),oe={storeName:"商店名称",customerCode:"客户编码",deliveryDistance:"配送距离",gear:"档位",type:"取货柜类型",pickupContainers:"取货柜地址",storeAddress:"地址",color:"颜色"},q=(n,e,s)=>{var y;/^-?\d+\.?\d*$/.test(e)?e.split(".").length>2?s(new Error("小数格式错误")):((y=e.split(".")[1])==null?void 0:y.length)>2?s(new Error("最多保留2位小数")):s():s(new Error("必须为数字且可含小数点"))},ie=le({levelParam:[{required:!0,message:"",trigger:"blur"}],avgDistance:[{required:!0,message:"",trigger:"blur"},{validator:q,trigger:"blur"}],roadGrade:[{required:!0,message:"",trigger:"blur"},{validator:q,trigger:"blur"}],gear:[{required:!0,message:"",trigger:"blur"},{validator:q,trigger:"blur"}]});function ue(n){E.value=n[0]}function Y(n){if(m.pageNum=1,k.pageNum=1,z.value=!0,ne(),n===1){v.value.style="background-color: transparent";return}else if(n===2){v.value.style="background-color: rgb(118, 70, 156)";return}else if(n===3){v.value.style="background-color: rgb(139, 132, 58)";return}else if(n===4){v.value.style="background-color: rgb(72, 144, 76)";return}else if(n===5){v.value.style="background-color: rgb(150, 147, 152)";return}else{v.value.style="background-color: transparent";return}}const Re=n=>{_.value.getMap()&&(n.color==0||n.color==1||n.color==5||_.value.getMap().setZoomAndCenter(16,[n.longitude,n.latitude]))},Ee=({row:n})=>{if(n.color===5)return"gray";if(n.color===2)return"purple";if(n.color===3)return"yellow";if(n.color===4)return"green"},ae=n=>{f[n]=f[n].replace(/[^\d.]/g,"").replace(/\.{2,}/g,".").replace(/(\..*)\./g,"$1")};function ze(n=1){if(z.value){m.pageNum=n,ne();return}k.pageNum=n,X()}function ge(){b.value=!b.value}async function X(){Se(),U.getUserList({...k}).then(n=>{h.value=n,k.pageNum=n.current,x.value=n.records})}function Se(){try{T.value=!0,U.getMap().then(n=>{L.value=n.pickupUsers.map(e=>{const s={lnglat:[e.longitude,e.latitude],info:{name:e.storeName,address:e.pickupContainers?e.pickupContainers:"无",distance:e.deliveryDistance}};return e.color===3?s.type="B":e.color===4?s.type="C":e.color===2&&(s.type="D"),e.pickupContainers&&(s.info.dian=n.pickupLocationVos.filter(y=>y.pickupName===e.pickupContainers).map(y=>[y.longitude,y.latitude])),s}),L.value=[...L.value,...n.pickupLocationVos.map(e=>e.status==1?{lnglat:[e.longitude,e.latitude],type:"E",info:{name:e.pickupName}}:e.status==3?{lnglat:[e.longitude,e.latitude],type:"F",info:{name:e.pickupName,stores:e.stores.length>0?e.stores.map(s=>s.storeName).join(","):"无",posList:n.pickupUsers.filter(s=>s.pickupContainers==e.pickupName).map(s=>[s.longitude,s.latitude])}}:{lnglat:[e.longitude,e.latitude],type:"A",info:{name:e.pickupName,stores:e.stores.length>0?e.stores.map(s=>s.storeName).join(","):"无",posList:n.pickupUsers.filter(s=>s.pickupContainers==e.pickupName).map(s=>[s.longitude,s.latitude])}})],T.value=!1})}catch{}}function ve(){const n=Object.entries(m).filter(([e,s])=>s!==""&&s!==null&&s!==void 0&&e!=="pageNum"&&e!=="pageSize").map(([e,s])=>`${oe[e]||e}:${s}`).join("; ");B.value=n,U.getSelectList().then(e=>{P.value=e}),d.value=!d.value}function Fe(){z.value=!1,v.value.style="",Le(),m.color="",m.pageNum=1,k.pageNum=1,X()}const Le=()=>{Object.keys(m).forEach(n=>{n!=="pageNum"&&n!=="pageSize"&&(m[n]="")}),B.value=""};function ne(){m.color||(m.color=0),U.getUserList({...m}).then(n=>{h.value=n,h.value.records.length==0&&D.error("没有查询到数据"),z.value=!0,k.pageNum=n.current,x.value=n.records})}function $e(){var n,e;if(((n=c.value)==null?void 0:n.getSelectionRows().length)>1){D.error("只能选择一条");return}else if(((e=c.value)==null?void 0:e.getSelectionRows().length)==0){D.error("没有选择");return}N.value.MilkOpen=!0}function Me(n){const{locks:e,deliveryDistance:s,pickupContainers:y,type:S}=n;U.updateUser({locks:Number(e),deliveryDistance:Number(s),pickupContainers:y,type:S,id:c.value.getSelectionRows()[0].id}).then(Q=>{if(Q.status==200||Q.code==200){if(D.success(Q.msg),z.value){ne();return}X()}else D.error("修改失败")})}function Pe(n){U.beAssigned({ids:n}).then(e=>{console.log(e),e.code===200?(D.success(e.msg),X()):D.error("分配异常")})}function Te(){dl(()=>{F.value.onOpenDialog(),F.value.noteVisible=!0})}function Ie(){V.value.uploadVisible=!0}function Ae(){if(c.value.getSelectionRows().length<=0){D.error("没有选择");return}A.value=c.value.getSelectionRows().map(n=>n.id),R.value.confirmDialogVis=!0}function Be(){window.location.reload(),D.success("刷新成功")}function Ge(){U.exportUser().then(n=>{const e=window.URL.createObjectURL(n),s=document.createElement("a");s.style.display="none",s.href=e,s.setAttribute("download","商户信息表.xlsx"),document.body.appendChild(s),s.click(),document.body.removeChild(s)})}function Oe(){const{gear:n,avgDistance:e,roadGrade:s,levelParam:y}=f;if(Number(n)+Number(e)+Number(s)!=1){de(),D.error("前3个相加不为1");return}C.value.validate((S,Q)=>{S?U.calculateUser({gear:Number(n),avgDistance:Number(e),roadGrade:Number(s),levelParam:Number(y)}).then(I=>{k.pageNum=1,X(),D.success(I.msg)}):(de(),D.error("表单验证未通过"))})}function de(){C.value.resetFields()}il(()=>{U.getParams().then(n=>{const{gear:e,avgDistance:s,roadGrade:y,levelParam:S}=n;f.gear=`${e}`,f.avgDistance=`${s}`,f.roadGrade=`${y}`,f.levelParam=S}),X()});function je(n){const e=k.pageNum,s=k.pageSize;return n+1+(e-1)*s}return(n,e)=>{const s=Ne,y=we,S=xe,Q=De,I=H,G=he,pe=Ve,$=ce,We=ke,qe=me,be=H,Ke=Ce,K=pl("op"),Qe=vl;return g(),Z("div",Ll,[a("div",$l,[a("div",Ml,[a("div",Pl,[l(Q,{model:o(f),class:"calForm",ref_key:"balanceRef",ref:C,rules:o(ie)},{default:t(()=>[l(S,{prop:"gear"},{label:t(()=>[l(s,{placement:"top",effect:"dark"},{content:t(()=>e[19]||(e[19]=[u("客户档位越高,"),a("br",null,null,-1),u("要求配送到店,"),a("br",null,null,-1),u("权重设置越低.")])),default:t(()=>[e[20]||(e[20]=a("img",{src:ee,alt:"",width:"16px",height:"16px",style:{"padding-right":"4px"}},null,-1))]),_:1}),e[21]||(e[21]=a("span",{class:"calFont"},"客户档位",-1))]),default:t(()=>[l(y,{modelValue:o(f).gear,"onUpdate:modelValue":e[0]||(e[0]=r=>o(f).gear=r),onInput:e[1]||(e[1]=r=>ae("gear")),class:"calInput"},null,8,["modelValue"])]),_:1}),l(S,{prop:"avgDistance"},{label:t(()=>[l(s,{placement:"top",effect:"dark"},{content:t(()=>e[22]||(e[22]=[u("是指商铺距离它所属的打"),a("br",null,null,-1),u("卡点的距离. 这个距离越"),a("br",null,null,-1),u("远, 说明越应该定点取货"),a("br",null,null,-1)])),default:t(()=>[e[23]||(e[23]=a("img",{src:ee,alt:"",width:"16px",height:"16px",style:{"padding-right":"4px","margin-left":"1vw"}},null,-1))]),_:1}),e[24]||(e[24]=a("span",{class:"calFont"},"平均送货距离",-1))]),default:t(()=>[l(y,{class:"calInput",onInput:e[2]||(e[2]=r=>ae("avgDistance")),modelValue:o(f).avgDistance,"onUpdate:modelValue":e[3]||(e[3]=r=>o(f).avgDistance=r)},null,8,["modelValue"])]),_:1}),l(S,{prop:"roadGrade"},{label:t(()=>[l(s,{placement:"top",effect:"dark"},{content:t(()=>e[25]||(e[25]=[u("客户所在地理位置道路"),a("br",null,null,-1),u("等级越低, 权重越高")])),default:t(()=>[e[26]||(e[26]=a("img",{src:ee,alt:"",width:"16px",height:"16px",style:{"padding-right":"4px"}},null,-1))]),_:1}),e[27]||(e[27]=a("span",{class:"calFont"},"道路等级",-1))]),default:t(()=>[l(y,{class:"calInput",onInput:e[4]||(e[4]=r=>ae("roadGrade")),modelValue:o(f).roadGrade,"onUpdate:modelValue":e[5]||(e[5]=r=>o(f).roadGrade=r)},null,8,["modelValue"])]),_:1}),l(S,{prop:"levelParam"},{label:t(()=>[l(s,{placement:"top",effect:"dark"},{content:t(()=>e[28]||(e[28]=[u("进行权重计算后,若"),a("br",null,null,-1),u("权值 ≥ 输入的定级参数值,"),a("br",null,null,-1),u("则该商户变为定点取货户")])),default:t(()=>[e[29]||(e[29]=a("img",{src:ee,alt:"",width:"16px",height:"16px",style:{"padding-right":"4px","margin-left":"3.6vw"}},null,-1))]),_:1}),e[30]||(e[30]=a("span",{class:"calFont"},"定级参数",-1))]),default:t(()=>[l(y,{class:"calInput",onInput:e[6]||(e[6]=r=>ae("levelParam")),modelValue:o(f).levelParam,"onUpdate:modelValue":e[7]||(e[7]=r=>o(f).levelParam=r)},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),a("div",Tl,[j((g(),w(I,{type:"primary",style:{color:"#003766",height:"25px",width:"80px"},onClick:e[8]||(e[8]=r=>de())},{default:t(()=>e[31]||(e[31]=[u("重置权重")])),_:1})),[[K,"pickup:user:setWeights"]]),j((g(),w(I,{type:"primary",style:{color:"#003766","margin-top":"5px","margin-left":"0",height:"25px",width:"80px"},onClick:Oe},{default:t(()=>e[32]||(e[32]=[u("重新计算")])),_:1})),[[K,"pickup:user:setWeights"]])])]),a("div",Il,[a("div",Al,[a("div",{class:"circle",ref_key:"circle",ref:v},null,512),a("div",Bl,[l(pe,{modelValue:o(m).color,"onUpdate:modelValue":e[9]||(e[9]=r=>o(m).color=r),placeholder:"全部",style:{width:"140px"},onChange:Y},{default:t(()=>[l(G,{label:"定点取货户(已分配)",value:4},{default:t(()=>e[33]||(e[33]=[a("div",{class:"shopType"},[a("div",{class:"ci",style:{"background-color":"#4e8a38"}}),a("span",null,"定点取货户(已分配)")],-1)])),_:1}),l(G,{label:"定点取货户(未分配)",value:3},{default:t(()=>e[34]||(e[34]=[a("div",{class:"shopType"},[a("div",{class:"ci",style:{"background-color":"#8b843a"}}),a("span",null,"定点取货户(未分配)")],-1)])),_:1}),l(G,{label:"普通商户(已分配)",value:2},{default:t(()=>e[35]||(e[35]=[a("div",{class:"shopType"},[a("div",{class:"ci",style:{"background-color":"#76469c"}}),a("span",null,"普通商户(已分配)")],-1)])),_:1}),l(G,{label:"普通商户(未分配)",value:1},{default:t(()=>e[36]||(e[36]=[a("div",{class:"shopType"},[a("div",{class:"ci",style:{"background-color":"transparent",border:"white 1.6px solid"}}),a("span",null,"普通商户(未分配)")],-1)])),_:1}),l(G,{label:"普通商户(待分配)",value:5},{default:t(()=>e[37]||(e[37]=[a("div",{class:"shopType"},[a("div",{class:"ci",style:{"background-color":"#969398"}}),a("span",null,"普通商户(待分配)")],-1)])),_:1}),l(G,{label:"全部",value:0},{default:t(()=>e[38]||(e[38]=[a("div",{class:"shopType"},[a("div",{class:"ci",style:{"background-color":"transparent"}}),a("span",null,"全部")],-1)])),_:1})]),_:1},8,["modelValue"])]),a("div",Gl,[l(y,{placeholder:"请点击搜索",modelValue:o(B),"onUpdate:modelValue":e[10]||(e[10]=r=>J(B)?B.value=r:null),onClick:ve},null,8,["modelValue"]),o(d)?(g(),Z("div",Ol,[a("div",jl,[a("div",Wl,[a("div",{class:"closeBold",onClick:ve},"x"),a("div",ql,[l(Q,{"label-width":"auto",model:o(m),ref_key:"searchModal",ref:i,class:"searchForm"},{default:t(()=>[l(S,{label:"商店名称",prop:"storeName"},{default:t(()=>[l(y,{modelValue:o(m).storeName,"onUpdate:modelValue":e[11]||(e[11]=r=>o(m).storeName=r),placeholder:"请输入"},null,8,["modelValue"])]),_:1}),l(S,{label:"档位",prop:"gear"},{default:t(()=>[l(pe,{placeholder:"请选择",modelValue:o(m).gear,"onUpdate:modelValue":e[12]||(e[12]=r=>o(m).gear=r)},{default:t(()=>[(g(!0),Z(Ue,null,cl(o(P).gears,r=>(g(),w(G,{label:r,value:r},null,8,["label","value"]))),256))]),_:1},8,["modelValue"])]),_:1}),l(S,{label:"客户编码",prop:"customerCode"},{default:t(()=>[l(y,{modelValue:o(m).customerCode,"onUpdate:modelValue":e[13]||(e[13]=r=>o(m).customerCode=r),placeholder:"请输入"},null,8,["modelValue"])]),_:1}),l(S,{label:"配送距离",prop:"deliveryDistance"},{default:t(()=>[l(y,{modelValue:o(m).deliveryDistance,"onUpdate:modelValue":e[14]||(e[14]=r=>o(m).deliveryDistance=r),placeholder:"请输入"},null,8,["modelValue"])]),_:1}),l(S,{label:"取货柜类型",prop:"type"},{default:t(()=>[l(pe,{placeholder:"请选择",modelValue:o(m).type,"onUpdate:modelValue":e[15]||(e[15]=r=>o(m).type=r)},{default:t(()=>[l(G,{label:"01",value:"01"}),l(G,{label:"02",value:"02"}),l(G,{label:"03",value:"03"}),l(G,{label:"04",value:"04"})]),_:1},8,["modelValue"])]),_:1}),l(S,{label:"地址",prop:"storeAddress"},{default:t(()=>[l(y,{modelValue:o(m).storeAddress,"onUpdate:modelValue":e[16]||(e[16]=r=>o(m).storeAddress=r),placeholder:"请输入"},null,8,["modelValue"])]),_:1}),l(S,{label:"取货柜地址",prop:"pickupContainers"},{default:t(()=>[l(y,{modelValue:o(m).pickupContainers,"onUpdate:modelValue":e[17]||(e[17]=r=>o(m).pickupContainers=r),placeholder:"请输入"},null,8,["modelValue"])]),_:1}),l(S,{label:"打卡点",prop:" accumulationName"},{default:t(()=>[l(y,{modelValue:o(m).accumulationName,"onUpdate:modelValue":e[18]||(e[18]=r=>o(m).accumulationName=r),placeholder:"请输入"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),a("div",Kl,[l(I,{onClick:Fe,type:"primary"},{default:t(()=>e[39]||(e[39]=[u("清空")])),_:1}),l(I,{onClick:ne,type:"primary"},{default:t(()=>e[40]||(e[40]=[u("搜索")])),_:1})])])])])):W("",!0)])]),a("div",Ql,[j((g(),w(I,{icon:o(_e),type:"primary",class:"elb",onClick:Ie},{default:t(()=>e[41]||(e[41]=[u("导入表格")])),_:1},8,["icon"])),[[K,"pickup:user:importForm"]]),j((g(),w(I,{icon:o(Je),type:"primary",class:"elb",onClick:Te},{default:t(()=>e[42]||(e[42]=[u("导入日志")])),_:1},8,["icon"])),[[K,"pickup:user:importLogs"]]),j((g(),w(I,{icon:o(Ye),type:"primary",class:"elb",onClick:Ge},{default:t(()=>e[43]||(e[43]=[u("导出表格")])),_:1},8,["icon"])),[[K,"pickup:user:exportForm"]]),j((g(),w(I,{icon:o(el),type:"primary",onClick:$e,class:"elb"},{default:t(()=>e[44]||(e[44]=[u("修改信息")])),_:1},8,["icon"])),[[K,"pickup:user:update"]]),j((g(),w(I,{icon:o(ll),type:"primary",class:"elb",onClick:Be},{default:t(()=>e[45]||(e[45]=[u("全局刷新")])),_:1},8,["icon"])),[[K,"pickup:user:exportForm"]]),j((g(),w(I,{icon:o(tl),type:"primary",class:"elb",onClick:Ae},{default:t(()=>e[46]||(e[46]=[u("待分配")])),_:1},8,["icon"])),[[K,"pickup:user:toBeAssigned"]])])]),a("div",Xl,[o(x)?j((g(),w(qe,{key:0,data:o(x),onRowClick:Re,ref_key:"tableRef",ref:c,"cell-style":{textAlign:"center"},"header-cell-style":{height:"4vh","text-align":"center"},"row-class-name":Ee,size:"small","row-style":{height:"3.9vh"},style:{"font-size":"0.8vw",width:"100%"},onSelectionChange:ue},{default:t(()=>[l($,{type:"selection"}),l($,{"show-overflow-tooltip":!0,label:"序号",type:"index",index:je}),l($,{prop:"customerCode",label:"客户编码"},{default:t(r=>[a("div",Zl,[r.row.locks==1?(g(),w(We,{key:0,size:16},{default:t(()=>[l(o(ol))]),_:1})):W("",!0),a("p",null,O(r.row.customerCode?r.row.customerCode:"无"),1)])]),_:1}),l($,{prop:"contactName",label:"客户名称"}),l($,{prop:"storeName",label:"商店名称"}),l($,{prop:"customerManagerName",label:"负责人"}),l($,{prop:"contactPhone",label:"订货电话"},{default:t(r=>[u(O(r.row.contactPhone?r.row.contactPhone:"无"),1)]),_:1}),l($,{"show-overflow-tooltip":!0,prop:"storeAddress","min-width":"100",label:"地址"}),l($,{"show-overflow-tooltip":!0,prop:"accumulationName",label:"打卡点"}),l($,{prop:"roadGrade",label:"道路等级"},{default:t(r=>[u(O(r.row.roadGrade==="0"?"城区":"乡镇"),1)]),_:1}),l($,{prop:"gear",label:"档位"}),l($,{prop:"deliveryDistance",label:"配送距离(km)"}),l($,{"show-overflow-tooltip":!0,prop:"pickupContainers",label:"取货柜地址"},{default:t(r=>[u(O(r.row.pickupContainers?r.row.pickupContainers:"无"),1)]),_:1}),l($,{prop:"type",label:"取货柜类型"},{default:t(r=>[u(O(r.row.type?r.row.type:"无"),1)]),_:1}),l($,{prop:"weights",label:"权值"})]),_:1},8,["data"])),[[Qe,o(U).loading]]):W("",!0)])]),a("div",{class:"mapSection",style:ul({height:o(b)?"22vh":"70vh"})},[a("div",Hl,[a("div",Jl,[l(sl,{ref_key:"pickUpMapRef",ref:_,list:o(L),loading:o(T)},null,8,["list","loading"])]),a("div",Yl,[o(b)?(g(),w(be,{key:0,type:"primary",icon:o(al),onClick:ge},null,8,["icon"])):(g(),w(be,{key:1,type:"primary",icon:o(nl),onClick:ge},null,8,["icon"]))])])],4),a("div",et,[o(h)?(g(),w(Ke,{key:0,layout:"prev, pager, next","current-page":o(k).pageNum,"page-size":o(k).pageSize,total:Number(o(h).total),onCurrentChange:ze},null,8,["current-page","page-size","total"])):W("",!0)]),l(Vl,{ref_key:"changeModalRef",ref:N,data:o(E),onConfirmChange:Me},null,8,["data"]),l(zl,{ref_key:"uploadNoteRef",ref:F},null,512),l(Ul,{ref_key:"uploadTableRef",ref:V},null,512),l(Fl,{ref_key:"confirmRef",ref:R,list:o(A),onWait:Pe},null,8,["list"])])}}}),Et=re(lt,[["__scopeId","data-v-0e63358a"]]);export{Et as default};
