<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.guestbook.mapper.AreaMapper">
    
    <select id="selectAll" resultType="com.ict.ycwl.guestbook.domain.Area">
        select area_id,area_name from area
    </select>


    <select id="selectAllForCondition" resultType="com.ict.ycwl.guestbook.api.vo.ConditionAreaVo">
        SELECT area_id, area_name
        FROM area
    </select>

    <insert id="insertTest">
        INSERT INTO area VALUES(NULL,'qq')
    </insert>
</mapper>