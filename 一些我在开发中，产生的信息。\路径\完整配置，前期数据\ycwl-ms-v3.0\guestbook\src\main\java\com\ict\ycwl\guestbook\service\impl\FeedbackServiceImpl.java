package com.ict.ycwl.guestbook.service.impl;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ict.ycwl.common.utils.FileUtils;
import com.ict.ycwl.common.web.Paging;
import com.ict.ycwl.guestbook.api.form.FeedbackAddForm;
import com.ict.ycwl.guestbook.api.form.FeedbackListForm;
import com.ict.ycwl.guestbook.api.vo.*;
import com.ict.ycwl.guestbook.domain.Feedback;
import com.ict.ycwl.guestbook.domain.FeedbackFile;
import com.ict.ycwl.guestbook.mapper.*;
import com.ict.ycwl.guestbook.service.FeedbackService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;


@Service
public class FeedbackServiceImpl implements FeedbackService {

    @Value("${file.savePath}")
    private String savePath;

    @Value("${file.accessPathPrefix}")
    private String accessPathPrefix;

    @Autowired
    private FeedbackMapper feedbackMapper;

    @Autowired
    private FeedbackFileMapper fileMapper;

    @Autowired
    private FeedbackReplyMapper replyMapper;

    @Autowired
    private FeedbackReplyFileMapper replyFileMapper;

    @Autowired
    private AreaMapper areaMapper;

    @Autowired
    private RouteMapper routeMapper;

    @Autowired
    private StoreMapper storeMapper;

    @Autowired
    private UserMapper userMapper;

    @Override
    public Paging<FeedbackListVo> getFeedbackList(FeedbackListForm form) {
        Page<FeedbackListVo> feedbackPage = PageHelper.startPage(form.getPageNum(), form.getPageSize()).doSelectPage(() -> feedbackMapper.selectFeedbackList(form));
        return new Paging<>(feedbackPage);
    }

    @Override
    public void addFeedback(FeedbackAddForm form, Long userId) throws Exception {

        //保存异常反馈信息数据
        Feedback feedback = Feedback.builder().feedbackStatus(0).createTime(LocalDateTime.now(ZoneId.of("Asia/Shanghai"))).updateTime(LocalDateTime.now(ZoneId.of("Asia/Shanghai"))).completeTime(null)
                .createBy(userId).updateBy(userId).build();
        BeanUtils.copyProperties(form, feedback);
        feedbackMapper.insert(feedback);
        Long feedbackId = feedback.getFeedbackId();

        //文件上传
        if (form.getFileList() != null && !form.getFileList().isEmpty()) {
            List<FeedbackFile> fileList = new ArrayList<>();
            for (MultipartFile multipartFile : form.getFileList()) {
                String filePath = FileUtils.upload(multipartFile, savePath, multipartFile.getOriginalFilename());
                FeedbackFile feedbackFile = FeedbackFile.builder().feedbackFilePath(File.separator + accessPathPrefix + File.separator + filePath)
                        .feedbackFileRealPath(savePath + filePath).feedbackId(feedbackId).build();
                fileList.add(feedbackFile);

                //保存文件信息
                fileMapper.insert(feedbackFile);
            }
        }

    }

    @Override
    public void removeFeedbacks(List<Long> feedbackIdList) {

        //删除反馈信息附带文件数据
        List<String> fileRealPathList = fileMapper.selectFileRealPath(feedbackIdList);
        if (!fileRealPathList.isEmpty()) {
            fileMapper.deleteFeedbackFile(feedbackIdList);
            FileUtils.deleteFiles(fileRealPathList);
        }

        //获取反馈信息所属的处理信息id集合
        List<Long> replyIdList = replyMapper.selectReplyByFeedbackIds(feedbackIdList);
        if (!replyIdList.isEmpty()) {
            //删除处理信息附带文件数据
            List<String> replyFileRealPathList = replyFileMapper.selectReplyFileRealPath(replyIdList);
            if (!replyFileRealPathList.isEmpty()) {
                replyFileMapper.deleteReplyFileByReplyIds(replyIdList);
                FileUtils.deleteFiles(replyFileRealPathList);
            }

            //删除处理信息
            replyMapper.deleteReplyByFeedbackIds(feedbackIdList);
        }

        //删除反馈信息
        feedbackMapper.deleteFeedbackByIds(feedbackIdList);
    }

    @Override
    public ConditionsDataVo getConditionsData() {
        List<ConditionAreaVo> conditionAreaList = areaMapper.selectAllForCondition();
        //List<ConditionRouteVo> conditionRouteList = routeMapper.selectAllForCondition();
        //List<ConditionStoreVo> conditionStoreList = storeMapper.selectAllForCondition();
        List<ConditionUserVo> deliveryUserList = userMapper.selectUserForCondition("送货员");
        List<ConditionUserVo> customerManagerList = userMapper.selectUserForCondition("客户专员");

        return ConditionsDataVo.builder().areaList(conditionAreaList)
                .customerManagerList(customerManagerList).deliveryUserList(deliveryUserList).build();

    }

    @Override
    public UnhandledFeedbackVo getUnhandledMount() {
        Integer logisticsMount = feedbackMapper.selectCountByType("1");
        Integer marketingMount = feedbackMapper.selectCountByType("2");
        return new UnhandledFeedbackVo(logisticsMount,marketingMount);
    }
}
