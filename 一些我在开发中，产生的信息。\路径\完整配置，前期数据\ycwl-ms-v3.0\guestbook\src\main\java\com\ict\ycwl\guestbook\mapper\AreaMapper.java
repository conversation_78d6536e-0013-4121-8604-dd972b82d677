package com.ict.ycwl.guestbook.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ict.ycwl.guestbook.api.vo.ConditionAreaVo;
import com.ict.ycwl.guestbook.domain.Area;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface AreaMapper extends BaseMapper<Area> {

    List<Area> selectAll();

    List<ConditionAreaVo> selectAllForCondition();

    void insertTest();
}
