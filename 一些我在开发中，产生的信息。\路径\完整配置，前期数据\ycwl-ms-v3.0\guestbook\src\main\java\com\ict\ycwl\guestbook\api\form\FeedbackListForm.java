package com.ict.ycwl.guestbook.api.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@ApiModel("异常反馈信息查询表单")
@Data
@Builder
@Validated
@AllArgsConstructor
@NoArgsConstructor
public class FeedbackListForm {

    @ApiModelProperty(value = "当前页码", dataType = "Integer", required = true, example = "1")
    @Min(value = -1, message = "页码数值错误")
    @NotNull(message = "当前页码不能为空")
    private Integer pageNum;

    @ApiModelProperty(value = "页记录数", dataType = "Integer", required = true, example = "5")
    @Min(value = -1, message = "页记录数数值错误")
    @NotNull(message = "页记录数不能为空")
    private Integer pageSize;

    @ApiModelProperty(value = "异常信息类型", dataType = "String", required = true, example = "1：物流反馈；2：营销反馈")
    @NotBlank(message = "信息类型不可为空！")
    private String feedbackType;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "订单起始日期", dataType = "LocalDateTime", example = "2023-10-23 11:21:59")
    private LocalDateTime orderStartDate;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "订单终止日期", dataType = "LocalDateTime", example = "2023-10-23 11:21:59")
    private LocalDateTime orderEndDate;

    @ApiModelProperty(value = "异常信息状态", dataType = "Integer", example = "0：未处理；1：处理中；2：已处理；3：无需处理")
    private Integer feedbackStatus;

    @ApiModelProperty(value = "客户编码", dataType = "String")
    private String customerCode;

    @ApiModelProperty(value = "客户名称", dataType = "String")
    private String contactName;

    @ApiModelProperty(value = "路线id", dataType = "Long")
    private Long routeId;

    @ApiModelProperty(value = "路线名称", dataType = "String")
    private String routeName;

    @ApiModelProperty(value = "大区名称", dataType = "String")
    private String areaName;

    @ApiModelProperty(value = "送货员工号", dataType = "String")
    private String deliveryWorkNumber;

    @ApiModelProperty(value = "客户专员工号", dataType = "String")
    private String managerWorkNumber;
}
