import{d as m,k as p,af as v,c as a,a as r,N as f,T as g,u as c,b as h,U as k,V as C,D as M,o as n,t as w,S as x}from"./index-C0QCllTd.js";import{a as u}from"./config-BZPPto1F.js";import{_ as B}from"./_plugin-vue_export-helper-DlAUqK2U.js";const L={class:"Management"},N={class:"List"},R=["onClick"],V={class:"content"},y=m({__name:"Management",setup(D){const i=k(),t=C(),o=p(()=>t.path.includes("/management")?t.path.includes("/work")?"message":u.find(e=>t.path==e.router).value:"");function l(e){i.push(e.router),console.log(t),o.value=e.value}return v(()=>{}),(e,I)=>{const _=M("router-view");return n(),a("div",L,[r("div",N,[(n(!0),a(f,null,g(c(u),(s,d)=>(n(),a("div",{class:w(["listItem",{active:c(o)===s.value}]),key:d,onClick:S=>l(s)},x(s.name),11,R))),128))]),r("div",V,[h(_)])])}}}),E=B(y,[["__scopeId","data-v-0e5a8044"]]);export{E as default};
