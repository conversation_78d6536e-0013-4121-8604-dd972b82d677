package com.ict.datamanagement.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName area
 */
@TableName(value ="area")
@Data
public class Area implements Serializable {
    /**
     * 大区id
     */
    @TableId(type = IdType.AUTO)
    private Long areaId;

    /**
     * 大区名称
     */
    private String areaName;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}