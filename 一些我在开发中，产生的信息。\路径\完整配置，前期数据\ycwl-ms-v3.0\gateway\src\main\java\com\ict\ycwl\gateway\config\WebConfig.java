package com.ict.ycwl.gateway.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.config.ResourceHandlerRegistry;
import org.springframework.web.reactive.config.WebFluxConfigurer;

import java.io.File;

@Slf4j
@Configuration
public class WebConfig implements WebFluxConfigurer {

    @Value("${file.savePath}")
    private String savePath;

    @Value("${file.accessPathPrefix}")
    private String accessPathPrefix;

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        log.info("访问路径前缀：{}",accessPathPrefix);
        log.info("保存路径：{}",savePath);
        registry.addResourceHandler("/" + accessPathPrefix + "/**")
                .addResourceLocations("file:" + savePath+File.separator);
    }
}
