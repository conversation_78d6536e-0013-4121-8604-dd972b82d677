import{N as B,Q as Ee,R as Le,E as $,S as xe,l as Ne,m as Se,U as Te,z as $e,V as Oe,n as Re,_ as me,W as Ve}from"./base-kpSIrADU.js";import{E as pe}from"./overlay-D06mCCGK.js";import{E as De}from"./button-IGKrEYb9.js";import{t as ie,a as Ae,E as Be}from"./carousel-item-sFYM8ch0.js";import{m as ve,E as A,u as M}from"./index-m25zEilF.js";import{f as ge,h as ue,i as te,d as X,ap as fe,H as _e,j as we,aq as Me,r as y,ar as Ye,as as Xe,k as w,m as ce,G as ye,z as he,o as _,p as ae,b as r,w as m,a as f,t as g,u as e,n as de,M as He,K as N,c as x,N as Y,q as Pe,T as ke,X as Fe,a5 as je,s as ee,ad as Ke,at as We,au as qe,v as be,av as Ue,J as Ze,ao as Ge,l as Qe,S as Ie,Q as ze,R as Je}from"./index-C0QCllTd.js";import{u as et}from"./input-DqmydyK4.js";import{g as tt}from"./scrollbar-BNeK4Yi-.js";import{_ as Ce}from"./_plugin-vue_export-helper-DlAUqK2U.js";const at=(u,p)=>{if(!B||!u||!p)return!1;const n=u.getBoundingClientRect();let s;return p instanceof Element?s=p.getBoundingClientRect():s={top:0,right:window.innerWidth,bottom:window.innerHeight,left:0},n.top<s.bottom&&n.bottom>s.top&&n.right>s.left&&n.left<s.right},nt=ge({urlList:{type:ue(Array),default:()=>ve([])},zIndex:{type:Number},initialIndex:{type:Number,default:0},infinite:{type:Boolean,default:!0},hideOnClickModal:Boolean,teleported:Boolean,closeOnPressEscape:{type:Boolean,default:!0},zoomRate:{type:Number,default:1.2}}),ot={close:()=>!0,switch:u=>te(u),rotate:u=>te(u)},st=["src"],lt=X({name:"ElImageViewer"}),rt=X({...lt,props:nt,emits:ot,setup(u,{expose:p,emit:n}){const s=u,I={CONTAIN:{name:"contain",icon:fe(Ee)},ORIGINAL:{name:"original",icon:fe(Le)}},{t:h}=_e(),l=we("image-viewer"),{nextZIndex:R}=Me(),d=y(),v=y([]),E=Ye(),z=y(!0),k=y(s.initialIndex),b=Xe(I.CONTAIN),c=y({scale:1,deg:0,offsetX:0,offsetY:0,enableTransition:!1}),H=w(()=>{const{urlList:t}=s;return t.length<=1}),V=w(()=>k.value===0),K=w(()=>k.value===s.urlList.length-1),ne=w(()=>s.urlList[k.value]),oe=w(()=>[l.e("btn"),l.e("prev"),l.is("disabled",!s.infinite&&V.value)]),P=w(()=>[l.e("btn"),l.e("next"),l.is("disabled",!s.infinite&&K.value)]),se=w(()=>{const{scale:t,deg:o,offsetX:i,offsetY:C,enableTransition:S}=c.value;let L=i/t,T=C/t;switch(o%360){case 90:case-270:[L,T]=[T,-L];break;case 180:case-180:[L,T]=[-L,-T];break;case 270:case-90:[L,T]=[-T,L];break}const D={transform:`scale(${t}) rotate(${o}deg) translate(${L}px, ${T}px)`,transition:S?"transform .3s":""};return b.value.name===I.CONTAIN.name&&(D.maxWidth=D.maxHeight="100%"),D}),W=w(()=>te(s.zIndex)?s.zIndex:R());function O(){re(),n("close")}function le(){const t=ie(i=>{switch(i.code){case A.esc:s.closeOnPressEscape&&O();break;case A.space:G();break;case A.left:Q();break;case A.up:a("zoomIn");break;case A.right:J();break;case A.down:a("zoomOut");break}}),o=ie(i=>{const C=i.deltaY||i.deltaX;a(C<0?"zoomIn":"zoomOut",{zoomRate:s.zoomRate,enableTransition:!1})});E.run(()=>{M(document,"keydown",t),M(document,"wheel",o)})}function re(){E.stop()}function q(){z.value=!1}function U(t){z.value=!1,t.target.alt=h("el.image.error")}function Z(t){if(z.value||t.button!==0||!d.value)return;c.value.enableTransition=!1;const{offsetX:o,offsetY:i}=c.value,C=t.pageX,S=t.pageY,L=ie(D=>{c.value={...c.value,offsetX:o+D.pageX-C,offsetY:i+D.pageY-S}}),T=M(document,"mousemove",L);M(document,"mouseup",()=>{T()}),t.preventDefault()}function F(){c.value={scale:1,deg:0,offsetX:0,offsetY:0,enableTransition:!1}}function G(){if(z.value)return;const t=qe(I),o=Object.values(I),i=b.value.name,S=(o.findIndex(L=>L.name===i)+1)%t.length;b.value=I[t[S]],F()}function j(t){const o=s.urlList.length;k.value=(t+o)%o}function Q(){V.value&&!s.infinite||j(k.value-1)}function J(){K.value&&!s.infinite||j(k.value+1)}function a(t,o={}){if(z.value)return;const{zoomRate:i,rotateDeg:C,enableTransition:S}={zoomRate:s.zoomRate,rotateDeg:90,enableTransition:!0,...o};switch(t){case"zoomOut":c.value.scale>.2&&(c.value.scale=Number.parseFloat((c.value.scale/i).toFixed(3)));break;case"zoomIn":c.value.scale<7&&(c.value.scale=Number.parseFloat((c.value.scale*i).toFixed(3)));break;case"clockwise":c.value.deg+=C,n("rotate",c.value.deg);break;case"anticlockwise":c.value.deg-=C,n("rotate",c.value.deg);break}c.value.enableTransition=S}return ce(ne,()=>{ye(()=>{const t=v.value[0];t!=null&&t.complete||(z.value=!0)})}),ce(k,t=>{F(),n("switch",t)}),he(()=>{var t,o;le(),(o=(t=d.value)==null?void 0:t.focus)==null||o.call(t)}),p({setActiveItem:j}),(t,o)=>(_(),ae(We,{to:"body",disabled:!t.teleported},[r(Ke,{name:"viewer-fade",appear:""},{default:m(()=>[f("div",{ref_key:"wrapper",ref:d,tabindex:-1,class:g(e(l).e("wrapper")),style:de({zIndex:e(W)})},[f("div",{class:g(e(l).e("mask")),onClick:o[0]||(o[0]=He(i=>t.hideOnClickModal&&O(),["self"]))},null,2),N(" CLOSE "),f("span",{class:g([e(l).e("btn"),e(l).e("close")]),onClick:O},[r(e($),null,{default:m(()=>[r(e(xe))]),_:1})],2),N(" ARROW "),e(H)?N("v-if",!0):(_(),x(Y,{key:0},[f("span",{class:g(e(oe)),onClick:Q},[r(e($),null,{default:m(()=>[r(e(Ne))]),_:1})],2),f("span",{class:g(e(P)),onClick:J},[r(e($),null,{default:m(()=>[r(e(Se))]),_:1})],2)],64)),N(" ACTIONS "),f("div",{class:g([e(l).e("btn"),e(l).e("actions")])},[f("div",{class:g(e(l).e("actions__inner"))},[r(e($),{onClick:o[1]||(o[1]=i=>a("zoomOut"))},{default:m(()=>[r(e(Te))]),_:1}),r(e($),{onClick:o[2]||(o[2]=i=>a("zoomIn"))},{default:m(()=>[r(e($e))]),_:1}),f("i",{class:g(e(l).e("actions__divider"))},null,2),r(e($),{onClick:G},{default:m(()=>[(_(),ae(Pe(e(b).icon)))]),_:1}),f("i",{class:g(e(l).e("actions__divider"))},null,2),r(e($),{onClick:o[3]||(o[3]=i=>a("anticlockwise"))},{default:m(()=>[r(e(Oe))]),_:1}),r(e($),{onClick:o[4]||(o[4]=i=>a("clockwise"))},{default:m(()=>[r(e(Re))]),_:1})],2)],2),N(" CANVAS "),f("div",{class:g(e(l).e("canvas"))},[(_(!0),x(Y,null,ke(t.urlList,(i,C)=>Fe((_(),x("img",{ref_for:!0,ref:S=>v.value[C]=S,key:i,src:i,style:de(e(se)),class:g(e(l).e("img")),onLoad:q,onError:U,onMousedown:Z},null,46,st)),[[je,C===k.value]])),128))],2),ee(t.$slots,"default")],6)]),_:3})],8,["disabled"]))}});var it=me(rt,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/image-viewer/src/image-viewer.vue"]]);const ut=be(it),ct=ge({hideOnClickModal:Boolean,src:{type:String,default:""},fit:{type:String,values:["","contain","cover","fill","none","scale-down"],default:""},loading:{type:String,values:["eager","lazy"]},lazy:Boolean,scrollContainer:{type:ue([String,Object])},previewSrcList:{type:ue(Array),default:()=>ve([])},previewTeleported:Boolean,zIndex:{type:Number},initialIndex:{type:Number,default:0},infinite:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},zoomRate:{type:Number,default:1.2}}),dt={load:u=>u instanceof Event,error:u=>u instanceof Event,switch:u=>te(u),close:()=>!0,show:()=>!0},ft=["src","loading"],mt={key:0},pt=X({name:"ElImage",inheritAttrs:!1}),vt=X({...pt,props:ct,emits:dt,setup(u,{emit:p}){const n=u;let s="";const{t:I}=_e(),h=we("image"),l=Ue(),R=et(),d=y(),v=y(!1),E=y(!0),z=y(!1),k=y(),b=y(),c=B&&"loading"in HTMLImageElement.prototype;let H,V;const K=w(()=>[h.e("inner"),P.value&&h.e("preview"),E.value&&h.is("loading")]),ne=w(()=>l.style),oe=w(()=>{const{fit:a}=n;return B&&a?{objectFit:a}:{}}),P=w(()=>{const{previewSrcList:a}=n;return Array.isArray(a)&&a.length>0}),se=w(()=>{const{previewSrcList:a,initialIndex:t}=n;let o=t;return t>a.length-1&&(o=0),o}),W=w(()=>n.loading==="eager"?!1:!c&&n.loading==="lazy"||n.lazy),O=()=>{B&&(E.value=!0,v.value=!1,d.value=n.src)};function le(a){E.value=!1,v.value=!1,p("load",a)}function re(a){E.value=!1,v.value=!0,p("error",a)}function q(){at(k.value,b.value)&&(O(),F())}const U=Ve(q,200,!0);async function Z(){var a;if(!B)return;await ye();const{scrollContainer:t}=n;Ge(t)?b.value=t:Qe(t)&&t!==""?b.value=(a=document.querySelector(t))!=null?a:void 0:k.value&&(b.value=tt(k.value)),b.value&&(H=M(b,"scroll",U),setTimeout(()=>q(),100))}function F(){!B||!b.value||!U||(H==null||H(),b.value=void 0)}function G(a){if(a.ctrlKey){if(a.deltaY<0)return a.preventDefault(),!1;if(a.deltaY>0)return a.preventDefault(),!1}}function j(){P.value&&(V=M("wheel",G,{passive:!1}),s=document.body.style.overflow,document.body.style.overflow="hidden",z.value=!0,p("show"))}function Q(){V==null||V(),document.body.style.overflow=s,z.value=!1,p("close")}function J(a){p("switch",a)}return ce(()=>n.src,()=>{W.value?(E.value=!0,v.value=!1,F(),Z()):O()}),he(()=>{W.value?Z():O()}),(a,t)=>(_(),x("div",{ref_key:"container",ref:k,class:g([e(h).b(),a.$attrs.class]),style:de(e(ne))},[v.value?ee(a.$slots,"error",{key:0},()=>[f("div",{class:g(e(h).e("error"))},Ie(e(I)("el.image.error")),3)]):(_(),x(Y,{key:1},[d.value!==void 0?(_(),x("img",Ze({key:0},e(R),{src:d.value,loading:a.loading,style:e(oe),class:e(K),onClick:j,onLoad:le,onError:re}),null,16,ft)):N("v-if",!0),E.value?(_(),x("div",{key:1,class:g(e(h).e("wrapper"))},[ee(a.$slots,"placeholder",{},()=>[f("div",{class:g(e(h).e("placeholder"))},null,2)])],2)):N("v-if",!0)],64)),e(P)?(_(),x(Y,{key:2},[z.value?(_(),ae(e(ut),{key:0,"z-index":a.zIndex,"initial-index":e(se),infinite:a.infinite,"zoom-rate":a.zoomRate,"url-list":a.previewSrcList,"hide-on-click-modal":a.hideOnClickModal,teleported:a.previewTeleported,"close-on-press-escape":a.closeOnPressEscape,onClose:Q,onSwitch:J},{default:m(()=>[a.$slots.viewer?(_(),x("div",mt,[ee(a.$slots,"viewer")])):N("v-if",!0)]),_:3},8,["z-index","initial-index","infinite","zoom-rate","url-list","hide-on-click-modal","teleported","close-on-press-escape"])):N("v-if",!0)],64)):N("v-if",!0)],6))}});var gt=me(vt,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/image/src/image.vue"]]);const _t=be(gt),wt=X({__name:"pathDirection",setup(u,{expose:p}){const n=y(!1);return p({vis:n}),(s,I)=>{const h=_t,l=Ae,R=Be,d=pe;return _(),ae(d,{modelValue:e(n),"onUpdate:modelValue":I[0]||(I[0]=v=>ze(n)?n.value=v:null),title:"路径指引",class:"transform","align-center":"","close-on-click-modal":!1},{default:m(()=>[r(R,{width:"400px",height:"600px",arrow:"always"},{default:m(()=>[(_(),x(Y,null,ke(4,v=>r(l,{key:v+"6"},{default:m(()=>[r(h,{style:{height:"600px"},src:`public/0${v}.jpg`,fit:"contain"},null,8,["src"])]),_:2},1024)),64))]),_:1})]),_:1},8,["modelValue"])}}}),yt=Ce(wt,[["__scopeId","data-v-2a7e5f52"]]),ht={class:"content"},kt={class:"dialog-footer"},bt=X({__name:"pathDialog",props:["dType"],setup(u,{expose:p}){const n=y(!1),s=y(),I=u;p({vis:n});function h(){n.value=!1}function l(){n.value=!1,s.value.vis=!0}return(R,d)=>{const v=De,E=pe;return _(),x(Y,null,[r(E,{modelValue:e(n),"onUpdate:modelValue":d[0]||(d[0]=z=>ze(n)?n.value=z:null),title:"提醒",class:"transform",width:"30%","align-center":"","close-on-click-modal":!1},{footer:m(()=>[f("div",kt,[r(v,{type:"primary",onClick:h},{default:m(()=>d[1]||(d[1]=[Je(" 确定 ")])),_:1})])]),default:m(()=>[f("div",ht,Ie(I.dType)+"信息已变更",1),d[2]||(d[2]=f("p",{class:"content"},"请到路径计算页面重新计算",-1)),f("div",{class:"content",style:{color:"red",cursor:"pointer"},onClick:l},"如何重新计算?")]),_:1},8,["modelValue"]),r(yt,{ref_key:"pathDiRef",ref:s},null,512)],64)}}}),$t=Ce(bt,[["__scopeId","data-v-f5d6c379"]]);export{$t as p};
