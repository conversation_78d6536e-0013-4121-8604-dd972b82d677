import{E as te,G as ae,H as ne}from"./base-kpSIrADU.js";/* empty css                */import{E as le}from"./overlay-D06mCCGK.js";import"./input-DqmydyK4.js";import{a as ie,E as se}from"./select-BOcQ2ynX.js";import{E as re}from"./scrollbar-BNeK4Yi-.js";import{E as ue}from"./button-IGKrEYb9.js";import{E as de,a as ce}from"./collapse-item-BW_6Kq-g.js";import{E as pe}from"./empty-DmGQucfw.js";/* empty css             */import{S as me,M as fe}from"./getMapKey-C0z490Cj.js";import{R as ge}from"./index-DUXS04g8.js";import{d as ve,r as c,z as Ae,c as z,a as g,b as m,w as p,R as L,X as F,u as s,p as N,Q as j,U as we,o as A,K as _e,N as B,T as W,t as he,S as O}from"./index-C0QCllTd.js";import{u as Me}from"./cluster-BxttejUl.js";import{m as R}from"./mapBluePoint-B1PT_daA.js";import{A as ke}from"./index-Bp4b1Vvq.js";import{a as h}from"./index-m25zEilF.js";import{v as ye}from"./directive-BBeDU6Ak.js";import{_ as Se}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./index-DOdSMika.js";import"./_initCloneObject-BmTtMqsv.js";import"./_commonjsHelpers-BbMlrU8H.js";import"./castArray-CSO3s-vM.js";import"./merge-B3fxVp56.js";const Ce={class:"AreaAdjust"},Ee={class:"back"},be={id:"container","element-loading-text":"地图数据加载中...","element-loading-background":"rgba(0,23,49,0.8)"},Ie={class:"adjustCollapse"},ze=["id","onClick"],Le={class:"flex"},Pe={class:"adjustDialogContent"},xe={class:"adjustDialogChange"},De={style:{display:"flex","justify-content":"center"}},Ne={class:"dialog-footer"},Ve=ve({__name:"AreaAdjust",setup(je){window._AMapSecurityConfig={securityJsCode:me};const H=we();function J(){H.replace("/home/<USER>/area")}const r=Me(),P=c(!0),T=c(!1),y=c([]),S=c({accumulationAddress:""}),w=c(),K=c(),C=c(!1);Ae(async()=>{M.value=!0;try{const[e,o]=await Promise.all([r.getCheckErrorPointsAction(),r.getErrorDepotAllAction()]);r.ErrorPoints==0&&(T.value=!0,h({message:"当前没有特殊点",type:"error"})),P.value=!1,M.value=!1,ke.load({key:fe,version:"2.0",plugins:["AMap.DistrictSearch"]}).then(a=>{new a.DistrictSearch({subdistrict:1,extensions:"all",level:"province"}).search("韶关市",function(t,u){const f=u.districtList[0].boundaries,d=[];for(let l=0;l<f.length;l++)d.push([f[l]]);n=new a.Map("container",{mask:d,zoom:9,expandZoomRange:!0,zooms:[9,20],center:[113.767587,24.718014],viewMode:"3D",zoomEnable:!0,resizeEnable:!0});for(let l=0;l<f.length;l++)new a.Polyline({path:f[l],strokeColor:"#3078AC",strokeWeight:2}).setMap(n);G()})}).catch(a=>{console.log(a),M.value=!1})}catch(e){console.error("接口请求失败:",e),M.value=!1,h.error("数据加载失败")}});const x=c({longitude:0,latitude:0}),Y=async(e,o)=>{C.value=!0,w.value=o,console.log(w.value),K.value=e,x.value={longitude:w.value.longitude,latitude:w.value.latitude},E.value=w.value.longitude,b.value=w.value.latitude,n.setZoomAndCenter(18,[E.value,b.value]),setTimeout(()=>{const a=document.getElementById("shop-"+w.value.longitude+"-"+w.value.latitude);a&&a.scrollIntoView({behavior:"smooth",block:"center"})},300),await r.getClosestPointsAction(x.value)},U=()=>{C.value=!1,S.value={accumulationAddress:""}},E=c(),b=c(),Z=()=>{const e=c({longitude:0,latitude:0,accumulationId:""});e.value={accumulationId:S.value.accumulationId,latitude:x.value.latitude,longitude:x.value.longitude},console.log(e.value),r.postUpdateStoreAccumulationIdAction(e.value).then(()=>{setTimeout(()=>{r.UpdateStoreAccumulationIdCode==200&&location.reload()},1500)})},M=c(!0);let n=null,v=[],D=[];function G(){if(v&&n&&(v.forEach(e=>n.remove(e)),v=[],console.log(r.errorDepotAll)),!r.errorDepotAll){h.error("地图数据为空，请先在聚集区计算页面加载地图数据");return}r.errorDepotAll.forEach(e=>{const o=new AMap.Marker({position:new AMap.LngLat(e.longitude,e.latitude),offset:new AMap.Pixel(-16,-16),icon:new AMap.Icon({size:new AMap.Size(32,32),image:R.red,imageSize:new AMap.Size(32,32)}),zIndex:200,title:e.name}),a=`
      <div class="info-card">
        <h3>${e.name}</h3>
        <p>备注：${e.remark||""}</p>
        <p>类型：${e.specialType||""}</p>
      </div>
    `,_=new AMap.InfoWindow({content:a,offset:new AMap.Pixel(0,-30),isCustom:!0});o.on("mouseover",()=>{_.open(n,o.getPosition())}),o.on("mouseout",()=>{_.close()}),o.on("click",function(){var t;y.value=[e.name],E.value=e.longitude,b.value=e.latitude,(t=document.getElementById(e.name))==null||t.scrollIntoView()}),n.add(o),v.push(o),e.son&&Array.isArray(e.son)&&e.son.forEach(t=>{const u=new AMap.Marker({position:new AMap.LngLat(t.longitude,t.latitude),offset:new AMap.Pixel(-16,-16),icon:new AMap.Icon({size:new AMap.Size(32,32),image:"/icon_shop.png",imageSize:new AMap.Size(32,32)}),zIndex:100,title:t.name}),f=`
          <div class="info-card">
            <h3>${t.name}</h3>
            <p>备注：${t.remark||""}</p>
            <p>类型：${t.specialType||""}</p>
          </div>
        `,d=new AMap.InfoWindow({content:f,offset:new AMap.Pixel(0,-30),isCustom:!0});u.on("mouseover",()=>{d.open(n,u.getPosition())}),u.on("mouseout",()=>{d.close()}),u.on("click",function(){var l;y.value=[e.name],E.value=t.longitude,b.value=t.latitude,(l=document.getElementById(e.name))==null||l.scrollIntoView(),setTimeout(()=>{const I=document.getElementById("shop-"+t.longitude+"-"+t.latitude);I&&I.scrollIntoView({behavior:"smooth",block:"center"})},300)}),n.add(u),v.push(u)})})}const V=c(!1),Q=async()=>{if(P.value){h.error("未加载完毕");return}V.value?location.reload():(M.value=!0,await q(),M.value=!1,V.value=!0)},X=async()=>{if(P.value){h.error("未加载完毕");return}try{const e=await r.getAdjustAllData();e&&e.code===200?(h.success(e.msg),location.reload()):h.error((e==null?void 0:e.msg)||"一键调整失败")}catch{h.error("一键调整异常")}},q=async()=>{v.forEach(a=>n.remove(a)),v=[],D.forEach(a=>n.remove(a)),D=[],((await r.getAdjustPreviewData()).data||[]).forEach(a=>{const _=new AMap.Marker({position:new AMap.LngLat(a.accLongitude,a.accLatitude),offset:new AMap.Pixel(-16,-32),icon:new AMap.Icon({size:new AMap.Size(32,32),image:R.red,imageSize:new AMap.Size(32,32)}),zIndex:200,title:a.accName});n.add(_),v.push(_),a.perviewPoints.forEach(t=>{const u=new AMap.Marker({position:new AMap.LngLat(t.oldStorePointLongitude,t.oldStorePointLatitude),offset:new AMap.Pixel(-16,-16),icon:new AMap.Icon({size:new AMap.Size(32,32),image:"/icon_shop.png",imageSize:new AMap.Size(32,32)}),zIndex:100,title:t.oldStorePointName});n.add(u),v.push(u);const f=new AMap.Polyline({path:[new AMap.LngLat(Number(a.accLongitude),Number(a.accLatitude)),new AMap.LngLat(Number(t.oldStorePointLongitude),Number(t.oldStorePointLatitude))],strokeColor:"#3078AC",strokeWeight:3,isOutline:!0,outlineColor:"#fff",borderWeight:1,lineJoin:"round",lineCap:"round",zIndex:120,strokeStyle:"solid"});n.add(f),D.push(f);const d=new AMap.Marker({position:new AMap.LngLat(t.newAccPointLongitude,t.newAccPointLatitude),offset:new AMap.Pixel(-16,-16),icon:new AMap.Icon({size:new AMap.Size(32,32),image:R.orange,imageSize:new AMap.Size(32,32)}),zIndex:200,title:t.newAccPointName});n.add(d),v.push(d);const l=new AMap.Polyline({path:[new AMap.LngLat(Number(t.oldStorePointLongitude),Number(t.oldStorePointLatitude)),new AMap.LngLat(Number(t.newAccPointLongitude),Number(t.newAccPointLatitude))],strokeColor:"#FF9900",strokeWeight:3,isOutline:!0,outlineColor:"#fff",borderWeight:1,lineJoin:"round",lineCap:"round",zIndex:120,strokeStyle:"dashed",strokeDasharray:[10,10]});n.add(l),D.push(l)})})};return(e,o)=>{const a=te,_=pe,t=de,u=ce,f=re,d=ue,l=ie,I=se,ee=le,$=ye;return A(),z("div",Ce,[g("div",Ee,[m(a,{size:"25",class:"backBtn",onClick:J},{default:p(()=>[m(s(ae))]),_:1}),o[3]||(o[3]=L(" 聚集区微调 ")),m(a,{size:"25"},{default:p(()=>[m(s(ne))]),_:1})]),F(g("div",be,null,512),[[$,s(M)]]),F((A(),N(s(ge),{backgroundColor:"#001731","element-loading-text":"加载中...","element-loading-background":"rgba(0,23,49,0.8)"},{default:p(()=>[s(T)?(A(),N(_,{key:0,class:"empty",description:"暂无数据"})):_e("",!0),m(f,{height:"75vh"},{default:p(()=>[g("div",Ie,[o[4]||(o[4]=g("div",{class:"title"},"特殊点详细：",-1)),m(u,{modelValue:s(y),"onUpdate:modelValue":o[0]||(o[0]=i=>j(y)?y.value=i:null),accordion:""},{default:p(()=>[(A(!0),z(B,null,W(s(r).errorDepotAll,i=>(A(),N(t,{key:i.name,title:i.name,name:i.name},{default:p(()=>[g("ul",null,[(A(!0),z(B,null,W(i.son,(k,oe)=>(A(),z("li",{key:oe,id:"shop-"+k.longitude+"-"+k.latitude,class:he(s(E)==k.longitude&&s(b)==k.latitude?"active":""),onClick:Be=>Y(i.name,k)},O(k.name),11,ze))),128))])]),_:2},1032,["title","name"]))),128))]),_:1},8,["modelValue"])])]),_:1}),g("div",Le,[m(d,{type:"primary",onClick:Q},{default:p(()=>[L(O(s(V)?"取消预览":"调整预览"),1)]),_:1}),m(d,{type:"primary",onClick:X},{default:p(()=>o[5]||(o[5]=[L("一键调整")])),_:1})])]),_:1})),[[$,s(P)]]),m(ee,{style:{transform:"translate(36.5vw, 0)"},modelValue:s(C),"onUpdate:modelValue":o[2]||(o[2]=i=>j(C)?C.value=i:null),width:"25%",modal:!1,"before-close":U,"append-to-body":!0},{footer:p(()=>[g("span",Ne,[m(d,{onClick:U},{default:p(()=>o[7]||(o[7]=[L("取消")])),_:1}),m(d,{onClick:Z},{default:p(()=>o[8]||(o[8]=[L("确定")])),_:1})])]),default:p(()=>[g("div",Pe,[g("div",xe,[o[6]||(o[6]=g("div",{style:{"font-size":"2.2vh","font-weight":"bold","margin-bottom":"1vh"}}," 商铺所属聚集区调整：把该商铺调整到正确聚集区以内 ",-1)),g("div",De,[m(I,{modelValue:s(S),"onUpdate:modelValue":o[1]||(o[1]=i=>j(S)?S.value=i:null),"value-key":"accumulationAddress",style:{width:"9vw"}},{default:p(()=>[(A(!0),z(B,null,W(s(r).AccumlationInfo,i=>(A(),N(l,{key:i.accumulationAddress,label:i.accumulationAddress,value:i},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])])])]),_:1},8,["modelValue"])])}}}),ro=Se(Ve,[["__scopeId","data-v-9dfdd690"]]);export{ro as default};
