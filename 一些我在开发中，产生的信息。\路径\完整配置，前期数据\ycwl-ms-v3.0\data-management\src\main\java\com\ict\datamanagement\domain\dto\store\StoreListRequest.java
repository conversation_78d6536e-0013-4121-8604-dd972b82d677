package com.ict.datamanagement.domain.dto.store;

import com.ict.datamanagement.domain.dto.page.PageRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@ApiModel("商铺列表表单")
@AllArgsConstructor
@NoArgsConstructor
public class StoreListRequest extends PageRequest implements Serializable {

    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码",dataType = "String")
    private String customerCode;

    /**
     * 店铺联系人名称
     */
    @ApiModelProperty(value = "店铺联系人名称",dataType = "String")
    private String contactName;

    /**
     * 商铺地址
     */
    @ApiModelProperty(value = "商铺地址",dataType = "String")
    private String storeAddress;

    /**
     * 大区Id
     */
    @ApiModelProperty(value = "大区Id",dataType = "Long")
    private Long areaId;

    /**
     * 路线Id
     */
    @ApiModelProperty(value = "路线Id",dataType = "Long")
    private Long routeId;


    /**
     * 班组Id
     */
    @ApiModelProperty(value = "班组Id",dataType = "Long")
    private Long groupId;
}
