package com.ict.ycwl.user.controller;

import com.ict.ycwl.common.web.AjaxResult;
import com.ict.ycwl.user.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "用户管理API")
@RestController
@RequestMapping("user")
public class UserSearchController {

    @Autowired
    private UserService userService;

    @ApiOperation("查询用户接口")
    @GetMapping("/search")
    public AjaxResult UserSearch(@RequestParam(required = false)@ApiParam(name = "department",value = "部门") String department,
                                 @RequestParam(required = false)@ApiParam(name = "userName",value = "姓名")String userName,
                                 @RequestParam(required = false)@ApiParam(name = "workNumber",value = "工号") String workNumber){
        return userService.userSearch(department,userName,workNumber);
    }


}
