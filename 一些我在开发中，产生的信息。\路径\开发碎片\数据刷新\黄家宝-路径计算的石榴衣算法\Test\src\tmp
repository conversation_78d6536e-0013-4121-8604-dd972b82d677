/*
// 创建新的列表来存储更新后的Voronoi单元
        List<Polygon> newMergedVoronoiCells = new ArrayList<>(mergedVoronoiCells);
        List<Polygon> toRemove = new ArrayList<>();
        List<Polygon> toAdd = new ArrayList<>();

// 检查并处理合并后的Voronoi单元与其他多边形的重叠

        for (int i = 0; i < mergedVoronoiCells.size(); i++) {
            // 获取当前的Voronoi单元
            Polygon cell1 = mergedVoronoiCells.get(i);
            // 遍历所有原始多边形
            for (Polygon polygon : polygons) {
                // 跳过与其原本归属的多边形的重叠检查
                if (polygon == null || polygon == VoronoiToPolygon.get(cell1)) continue;

                // 如果当前Voronoi单元与当前原始多边形相交
                if (cell1.intersects(polygon)) {
                    // 计算交集
                    Geometry intersection = cell1.intersection(polygon);

                    // 如果交集非空
                    if (!intersection.isEmpty()) {
                        // 获取另一个多边形所属的Voronoi
                        Polygon cell2 = PolygonToVoronoi.get(polygon);
                        if(cell2 == null) continue;
                        // 对cell1只保留它对polygon的补集
                        Geometry cell1Complement = cell1.difference(intersection);
                        cell1Complement = GeometryFixer.fix(cell1Complement);
                        cell1Complement = TopologyPreservingSimplifier.simplify(cell1Complement, 0.001);
                        GeometryPrecisionReducer precisionReducer = new GeometryPrecisionReducer(new PrecisionModel(PrecisionModel.FLOATING_SINGLE));
                        Polygon finalCell1Complement = (Polygon) precisionReducer.reduce(cell1Complement);

                        List<Polygon> cell1Polygons = fixSimplifyReduceGeometry(cell1Complement);

                        // 对cell2额外添加polygon对它的补集
                        Geometry cell2Union = cell2.union(intersection);
                        List<Polygon> cell2Polygons = fixSimplifyReduceGeometry(cell2Union);


                        // 临时存储需要删除的多边形
                        toRemove.add(cell1);
                        toRemove.add(cell2);

                        cell1Polygons = mergeOverlappingPolygons(cell1Polygons);
                        cell2Polygons = mergeOverlappingPolygons(cell2Polygons);


                        System.out.println("\n\n\n\nStep3."+ (i + 1) + "获取补集修正后的泰森多边形");
                        for(Polygon p : cell1Polygons) System.out.println(p);
                        System.out.println(" ");
                        for(Polygon p : cell2Polygons) System.out.println(p);

                        // 临时存储需要添加的多边形
                        toAdd.addAll(cell1Polygons);
                        toAdd.addAll(cell2Polygons);

                       // System.out.println(intersection);

                        //System.out.println("\n\n\n\n");

                        //for(Polygon p : cell1Polygons) System.out.println(p);

                       // System.out.println("\n\n\n\n");

                        // 更新PolygonToVoronoi映射
                        for (Polygon cell : cell1Polygons) {
                            VoronoiToPolygon.put(cell, polygon);
                        }
                        for (Polygon cell : cell2Polygons) {
                            VoronoiToPolygon.put(cell, polygon);
                        }
                    }
                }
            }
        }

// 删除需要删除的多边形
        newMergedVoronoiCells.removeAll(toRemove);
// 添加需要添加的多边形
        newMergedVoronoiCells.addAll(toAdd);

// 将更新后的单元列表赋值回原始列表
        mergedVoronoiCells = new ArrayList<>(newMergedVoronoiCells);
*/
