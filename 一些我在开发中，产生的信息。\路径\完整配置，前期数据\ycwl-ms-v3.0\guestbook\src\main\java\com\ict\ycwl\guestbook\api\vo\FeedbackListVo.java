package com.ict.ycwl.guestbook.api.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class FeedbackListVo {

    //反馈信息id
    private Long feedbackId;

    //路线名称
    private String routeName;

    //派送员
    private String deliveryName;

    //客户专员
    private String customerManagerName;

    //异常反馈信息内容
    private String feedbackInformation;

    //客户编码
    private String customerCode;

    //商铺名称
    private String storeName;

    //客户名称
    private String contactName;

    //客户地址
    private String storeAddress;

    //配送域
    private String areaName;

    //订单日期
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private LocalDateTime orderDate;

    //反馈信息文件路径集合
    private List<String> feedbackFileList;

    //处理状态码
    private Integer feedbackStatus;

    //最新更新时间

    private LocalDateTime updateTime;

    //创建时间
    private LocalDateTime createTime;


}
