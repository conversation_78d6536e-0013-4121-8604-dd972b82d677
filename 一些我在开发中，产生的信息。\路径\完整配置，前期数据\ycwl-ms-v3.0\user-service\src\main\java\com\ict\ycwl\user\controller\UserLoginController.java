package com.ict.ycwl.user.controller;

import com.ict.ycwl.common.web.AjaxResult;
import com.ict.ycwl.user.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@Api(tags = "登录API")
@RestController
@RequestMapping("/user")
public class UserLoginController {

    @Resource
    private UserService userService;


    @ApiOperation("用户登录接口")
    @PostMapping("/login")
    public AjaxResult login(@RequestParam("loginName") String loginName,
                            @RequestParam("password") String password,
                            @RequestParam("captcha") String captcha,
                            HttpServletRequest httpServletRequest){
        return userService.loginByName(loginName,password,captcha,httpServletRequest);
    }


}
