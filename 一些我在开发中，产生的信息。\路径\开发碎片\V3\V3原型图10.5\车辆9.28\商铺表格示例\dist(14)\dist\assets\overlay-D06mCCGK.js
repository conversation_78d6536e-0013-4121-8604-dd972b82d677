import{a1 as le,f as X,d as O,H as se,y as j,k as I,o as S,c as _,a as R,s as E,t as y,u as e,S as ae,b as N,w as k,p as q,q as te,K,n as Y,h as ne,ah as re,aq as ie,r as D,aT as ue,m as G,G as de,z as ce,L as fe,aU as me,a2 as pe,j as ve,x as ge,X as ye,J as Ce,I as be,a5 as he,ad as ke,at as Ee,v as De}from"./index-C0QCllTd.js";import{u as Ie,a as Fe,E as Se,b as Ae}from"./index-DOdSMika.js";import{i as Be,E as Te,_ as Q,af as we,u as J,a as $e,ag as Z,N as Le}from"./base-kpSIrADU.js";import{F as Pe,d as Re}from"./scrollbar-BNeK4Yi-.js";import{U as W}from"./input-DqmydyK4.js";import{u as H}from"./button-IGKrEYb9.js";const Ne=(...o)=>s=>{o.forEach(i=>{le(i)?i(s):i.value=s})},x=Symbol("dialogInjectionKey"),ee=X({center:Boolean,alignCenter:Boolean,closeIcon:{type:Be},customClass:{type:String,default:""},draggable:Boolean,fullscreen:Boolean,showClose:{type:Boolean,default:!0},title:{type:String,default:""},ariaLevel:{type:String,default:"2"}}),Oe={close:()=>!0},Me=["aria-level"],ze=["aria-label"],Ue=["id"],Ve=O({name:"ElDialogContent"}),_e=O({...Ve,props:ee,emits:Oe,setup(o){const s=o,{t:i}=se(),{Close:u}=we,{dialogRef:d,headerRef:p,bodyId:A,ns:t,style:n}=j(x),{focusTrapRef:v}=j(Pe),c=I(()=>[t.b(),t.is("fullscreen",s.fullscreen),t.is("draggable",s.draggable),t.is("align-center",s.alignCenter),{[t.m("center")]:s.center},s.customClass]),C=Ne(v,d),f=I(()=>s.draggable);return Ie(d,p,f),(a,F)=>(S(),_("div",{ref:e(C),class:y(e(c)),style:Y(e(n)),tabindex:"-1"},[R("header",{ref_key:"headerRef",ref:p,class:y(e(t).e("header"))},[E(a.$slots,"header",{},()=>[R("span",{role:"heading","aria-level":a.ariaLevel,class:y(e(t).e("title"))},ae(a.title),11,Me)]),a.showClose?(S(),_("button",{key:0,"aria-label":e(i)("el.dialog.close"),class:y(e(t).e("headerbtn")),type:"button",onClick:F[0]||(F[0]=$=>a.$emit("close"))},[N(e(Te),{class:y(e(t).e("close"))},{default:k(()=>[(S(),q(te(a.closeIcon||e(u))))]),_:1},8,["class"])],10,ze)):K("v-if",!0)],2),R("div",{id:e(A),class:y(e(t).e("body"))},[E(a.$slots,"default")],10,Ue),a.$slots.footer?(S(),_("footer",{key:0,class:y(e(t).e("footer"))},[E(a.$slots,"footer")],2)):K("v-if",!0)],6))}});var qe=Q(_e,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/dialog/src/dialog-content.vue"]]);const Ke=X({...ee,appendToBody:Boolean,beforeClose:{type:ne(Function)},destroyOnClose:Boolean,closeOnClickModal:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},lockScroll:{type:Boolean,default:!0},modal:{type:Boolean,default:!0},openDelay:{type:Number,default:0},closeDelay:{type:Number,default:0},top:{type:String},modelValue:Boolean,modalClass:String,width:{type:[String,Number]},zIndex:{type:Number},trapFocus:{type:Boolean,default:!1},headerAriaLevel:{type:String,default:"2"}}),je={open:()=>!0,opened:()=>!0,close:()=>!0,closed:()=>!0,[W]:o=>re(o),openAutoFocus:()=>!0,closeAutoFocus:()=>!0},Ge=(o,s)=>{const u=fe().emit,{nextZIndex:d}=ie();let p="";const A=J(),t=J(),n=D(!1),v=D(!1),c=D(!1),C=D(o.zIndex||d());let f,a;const F=ue("namespace",me),$=I(()=>{const r={},h=`--${F.value}-dialog`;return o.fullscreen||(o.top&&(r[`${h}-margin-top`]=o.top),o.width&&(r[`${h}-width`]=$e(o.width))),r}),M=I(()=>o.alignCenter?{display:"flex"}:{});function z(){u("opened")}function L(){u("closed"),u(W,!1),o.destroyOnClose&&(c.value=!1)}function U(){u("close")}function P(){a==null||a(),f==null||f(),o.openDelay&&o.openDelay>0?{stop:f}=Z(()=>m(),o.openDelay):m()}function B(){f==null||f(),a==null||a(),o.closeDelay&&o.closeDelay>0?{stop:a}=Z(()=>w(),o.closeDelay):w()}function T(){function r(h){h||(v.value=!0,n.value=!1)}o.beforeClose?o.beforeClose(r):B()}function V(){o.closeOnClickModal&&T()}function m(){Le&&(n.value=!0)}function w(){n.value=!1}function l(){u("openAutoFocus")}function g(){u("closeAutoFocus")}function b(r){var h;((h=r.detail)==null?void 0:h.focusReason)==="pointer"&&r.preventDefault()}o.lockScroll&&Fe(n);function oe(){o.closeOnPressEscape&&T()}return G(()=>o.modelValue,r=>{r?(v.value=!1,P(),c.value=!0,C.value=o.zIndex?C.value++:d(),de(()=>{u("open"),s.value&&(s.value.scrollTop=0)})):n.value&&B()}),G(()=>o.fullscreen,r=>{s.value&&(r?(p=s.value.style.transform,s.value.style.transform=""):s.value.style.transform=p)}),ce(()=>{o.modelValue&&(n.value=!0,c.value=!0,P())}),{afterEnter:z,afterLeave:L,beforeLeave:U,handleClose:T,onModalClick:V,close:B,doClose:w,onOpenAutoFocus:l,onCloseAutoFocus:g,onCloseRequested:oe,onFocusoutPrevented:b,titleId:A,bodyId:t,closed:v,style:$,overlayDialogStyle:M,rendered:c,visible:n,zIndex:C}},Je=["aria-label","aria-labelledby","aria-describedby"],Ze=O({name:"ElDialog",inheritAttrs:!1}),He=O({...Ze,props:Ke,emits:je,setup(o,{expose:s}){const i=o,u=pe();H({scope:"el-dialog",from:"the title slot",replacement:"the header slot",version:"3.0.0",ref:"https://element-plus.org/en-US/component/dialog.html#slots"},I(()=>!!u.title)),H({scope:"el-dialog",from:"custom-class",replacement:"class",version:"2.3.0",ref:"https://element-plus.org/en-US/component/dialog.html#attributes",type:"Attribute"},I(()=>!!i.customClass));const d=ve("dialog"),p=D(),A=D(),t=D(),{visible:n,titleId:v,bodyId:c,style:C,overlayDialogStyle:f,rendered:a,zIndex:F,afterEnter:$,afterLeave:M,beforeLeave:z,handleClose:L,onModalClick:U,onOpenAutoFocus:P,onCloseAutoFocus:B,onCloseRequested:T,onFocusoutPrevented:V}=Ge(i,p);ge(x,{dialogRef:p,headerRef:A,bodyId:c,ns:d,rendered:a,style:C});const m=Ae(U),w=I(()=>i.draggable&&!i.fullscreen);return s({visible:n,dialogContentRef:t}),(l,g)=>(S(),q(Ee,{to:"body",disabled:!l.appendToBody},[N(ke,{name:"dialog-fade",onAfterEnter:e($),onAfterLeave:e(M),onBeforeLeave:e(z),persisted:""},{default:k(()=>[ye(N(e(Se),{"custom-mask-event":"",mask:l.modal,"overlay-class":l.modalClass,"z-index":e(F)},{default:k(()=>[R("div",{role:"dialog","aria-modal":"true","aria-label":l.title||void 0,"aria-labelledby":l.title?void 0:e(v),"aria-describedby":e(c),class:y(`${e(d).namespace.value}-overlay-dialog`),style:Y(e(f)),onClick:g[0]||(g[0]=(...b)=>e(m).onClick&&e(m).onClick(...b)),onMousedown:g[1]||(g[1]=(...b)=>e(m).onMousedown&&e(m).onMousedown(...b)),onMouseup:g[2]||(g[2]=(...b)=>e(m).onMouseup&&e(m).onMouseup(...b))},[N(e(Re),{loop:"",trapped:e(n),"focus-start-el":"container",onFocusAfterTrapped:e(P),onFocusAfterReleased:e(B),onFocusoutPrevented:e(V),onReleaseRequested:e(T)},{default:k(()=>[e(a)?(S(),q(qe,Ce({key:0,ref_key:"dialogContentRef",ref:t},l.$attrs,{"custom-class":l.customClass,center:l.center,"align-center":l.alignCenter,"close-icon":l.closeIcon,draggable:e(w),fullscreen:l.fullscreen,"show-close":l.showClose,title:l.title,"aria-level":l.headerAriaLevel,onClose:e(L)}),be({header:k(()=>[l.$slots.title?E(l.$slots,"title",{key:1}):E(l.$slots,"header",{key:0,close:e(L),titleId:e(v),titleClass:e(d).e("title")})]),default:k(()=>[E(l.$slots,"default")]),_:2},[l.$slots.footer?{name:"footer",fn:k(()=>[E(l.$slots,"footer")])}:void 0]),1040,["custom-class","center","align-center","close-icon","draggable","fullscreen","show-close","title","aria-level","onClose"])):K("v-if",!0)]),_:3},8,["trapped","onFocusAfterTrapped","onFocusAfterReleased","onFocusoutPrevented","onReleaseRequested"])],46,Je)]),_:3},8,["mask","overlay-class","z-index"]),[[he,e(n)]])]),_:3},8,["onAfterEnter","onAfterLeave","onBeforeLeave"])],8,["disabled"]))}});var Xe=Q(He,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/dialog/src/dialog.vue"]]);const lo=De(Xe);export{lo as E,Ne as c};
