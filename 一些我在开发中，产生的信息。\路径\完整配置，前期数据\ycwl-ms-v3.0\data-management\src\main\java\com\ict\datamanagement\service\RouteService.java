package com.ict.datamanagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ict.datamanagement.domain.entity.Route;
import com.ict.datamanagement.domain.vo.RouteVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【route】的数据库操作Service
* @createDate 2024-04-22 13:45:36
*/
public interface RouteService extends IService<Route> {

    List<RouteVO> getOptionalData();

    RouteVO getRouteVO(Route route);
}
