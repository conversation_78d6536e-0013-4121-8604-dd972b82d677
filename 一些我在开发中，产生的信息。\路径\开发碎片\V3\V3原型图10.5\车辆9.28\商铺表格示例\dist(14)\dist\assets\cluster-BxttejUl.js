import{r as n,a as u}from"./index-m25zEilF.js";import{Y as vt,r as o}from"./index-C0QCllTd.js";function ht(){return n.get({timeout:36e5,url:"/clustercalculate/cluster/getListResultPoints"})}function yt(){return n.post({timeout:36e5,url:"/clustercalculate/cluster/calculateAll"})}function wt(){return n.get({timeout:1e3*80*8,url:"/clustercalculate/cluster/checkErrorPoints"})}function Dt(){return n.delete({url:"/clustercalculate/cluster/clearInformationList"})}function Ct(e){return n.get({url:`/clustercalculate/cluster/getClosestPoints?latitude=${e.latitude}&longitude=${e.longitude}`})}function Pt(){return n.get({timeout:1e3*60,url:"/clustercalculate/cluster/getErrorPoints"})}function Rt(){return n.get({url:"/clustercalculate/cluster/getInformationList"})}function St(){return n.get({timeout:1e3*60*7,url:"/clustercalculate/cluster/getMapResultPoints"})}function It(e){return n.post({headers:{"Content-Type":"application/x-www-form-urlencoded"},url:"/clustercalculate/cluster/updateStoreAccumulationId",data:e})}function xt(e){return n.get({timeout:1e3*60*5,url:`/pathcalculate/path/calculateOne?apiKey=${e.apiKey}&areaName=${e.areaName}&assignNumber=${e.assignNumber}`})}function Nt(e){return n.get({timeout:1e3*60*20,url:`/pathcalculate/path/calculateAll?apiKey=${e.apiKey}`})}function $t(){return n.get({url:"/pathcalculate/path/getMapData"})}function Lt(){return n.get({timeout:1e3*60*20,url:"/pathcalculate/path/getRouteDetails"})}function jt(e){return n.get({url:`/pathcalculate/path/getAccumulationDetails/${e}`})}function Et(){return n.get({url:"/pathcalculate/path/getTransitDepotRouteData"})}function Ot(e){return n.post({timeout:1e3*2400,url:"/pathcalculate/path/addRoute",data:e})}function Tt(e,s){return n.get({url:`/pathcalculate/path/getRouteData/${s}?transitDepotId=${e.transitDepotId}&routeName=${e.routeName}`})}function Mt(e){return n.get({url:`/pathcalculate/path/getRouteVersion?transitDepotId=${e.transitDepotId}&date=${e.date}`})}function Kt(e){return n.get({timeout:1e3*60,url:`/pathcalculate/path/getSplitLines?groupOrder=${e}`})}function Jt(e,s){return n.get({timeout:1e3*60,url:`/pathcalculate/path/compareBaseGroup/${e}/${s}`})}function Gt(e){return n.post({url:"/pathcalculate/path/adjustPoint",data:e})}function bt(){return n.get({timeout:1e3*60*3,url:"/pathcalculate/path/getConvexPoint"})}function Vt(e){return n.get({timeout:1e3*60,url:`/pathcalculate/path/calculateRangedRoute?apiKey=${e.apiKey}&routeName1=${e.routeName1}&routeName2=${e.routeName2}`})}function kt(e){return n.get({timeout:1e3*2400,url:`/pathcalculate/path/getConvex?apiKey=${e}`})}function Bt(){return n.get({timeout:1e3*60*5,url:"/clustercalculate/cluster/getAllAcc"})}function Ut(){return n.get({timeout:1e3*60*5,url:"/clustercalculate/cluster/getErrorPointsPlus"})}function qt(){return n.get({timeout:1e3*60*5,url:"/pathcalculate/path/getTransitDepotName"})}function zt(){return n.get({timeout:1e3*60*5,url:"/pathcalculate/path/getAllColourConvex"})}function Yt(){return n.post({timeout:1e3*60*5,url:"/datamanagement/routeExport",headers:{"Content-Type":"application/x-download"},responseType:"blob"})}function Ft(){return n.get({url:"/clustercalculate/cluster/preview"})}function Ht(){return n.get({timeout:1e3*60*3,url:"/clustercalculate/cluster/adjustment"})}function Qt(e){return n.get({timeout:60*1e3*3,params:e,url:"/pathcalculate/path/InTeamAveTime"})}function Wt(e){return n.get({timeout:60*1e3*3,params:e,url:"/pathcalculate/path/optimizingOneGroupRoute"})}function Xt(){return n.get({timeout:60*1e3*3,url:"/pathcalculate/path/queryGroupAllId"})}const te=vt("cluster",()=>{const e=o([]),s=o([]);async function M(){const t=await ht();s.value=t.data,console.log(t);const a=s.value.reduce((c,r)=>{const dt=r.accumulation,T=c.find(At=>At.accumulation===dt);return T?T.son.push({shopName:r.name,lnglat:[r.longitude,r.latitude]}):c.push({accumulation:r.accumulation,son:[{shopName:r.name,lnglat:[r.longitude,r.latitude]}]}),c},e.value);e.value=a,console.log(e.value)}async function K(){const t=await yt();t.code===200?u.success(t.msg):u.error("失败")}const p=o();async function J(){const t=await Bt();p.value=t.data}const m=o();async function G(){const t=await Ut();m.value=t.data}async function b(){return await Ft()}async function V(){return await Ht()}async function k(t){return await Wt(t)}async function B(t){return await Qt(t)}async function U(){await Dt()}const f=o();async function q(){const t=await Rt();f.value=t.data}const d=o();async function z(){const t=await wt();d.value=t.data,console.log(t)}const A=o();async function Y(){const t=await Pt();A.value=t.data}const v=o();async function F(t){const a=await Ct(t);v.value=a.data}const h=o();async function H(t){const a=await It(t);h.value=a.code,a.code===200?u.success(a.msg):u.error("失败")}const l=o(localStorage.getItem("Points")!="undefined"?JSON.parse(localStorage.getItem("Points")):void 0);async function Q(){const t=await St();l.value=t.data.point,localStorage.setItem("Points",JSON.stringify(l.value))}const y=o();async function W(t){const a=await xt(t);y.value=a.data,a.msg=="计算成功"?u.success(a.msg):u.error(a.msg)}const w=o();async function X(t){const a=await Nt(t);w.value=a.data,a.msg=="计算成功"?u.success(a.msg):u.error(a.msg)}const D=o();async function Z(){const t=await $t();D.value=t.data}const C=o();async function _(){const t=await Lt();C.value=t.data}const P=o();async function tt(t){const a=await jt(t);P.value=a.data}const R=o();async function et(){const t=await Et();R.value=t.data}const S=o(!1);async function at(t){const a=await Ot(t);return a.code===200?(u.success("保存成功"),S.value=!0):u.error(a.msg),a}const I=o();async function nt(t,a){const c=await Tt(t,a);c.code===200?u.success(c.msg):c.code!==500&&u.error("失败"),I.value=c.data}const x=o();async function ot(t){const a=await Mt(t);console.log(a),x.value=a.data}const N=o();async function ut(){const t=await Kt("二四五一三六");N.value=t.data}const $=o();async function ct(t,a){const c=await Jt(t,a);console.log(c),$.value=c.data}async function rt(t){const a=await Gt(t);a.code===200?u.success(a.msg):u.error("调整失败"),console.log(a)}const L=o();async function st(){const t=await bt();L.value=t.data}const j=o();async function lt(t){const a=await Vt(t);j.value=a.data}const i=o(localStorage.getItem("convex")!="undefined"?JSON.parse(localStorage.getItem("convex")):void 0);async function it(t){const a=await kt(t);i.value=a.data,localStorage.setItem("convex",JSON.stringify(i.value))}const E=o();async function gt(){const t=await qt();E.value=t.data}const g=o(localStorage.getItem("convexColor")!="undefined"?JSON.parse(localStorage.getItem("convexColor")):void 0);async function pt(){const t=await zt();g.value=t.data,localStorage.setItem("convexColor",JSON.stringify(g.value))}async function mt(){return await Yt()}const O=o();async function ft(){const t=await Xt();O.value=t.data}return{clusterDepotAll:p,getClusterDepotAllAction:J,errorDepotAll:m,getErrorDepotAllAction:G,groupIdList:O,getGroupIdAction:ft,getClusterAdjustmentBetweenData:k,getClusterAdjustmentData:B,getAdjustPreviewData:b,getAdjustAllData:V,getAllResultPointsAction:M,clusterAndShopList:e,resultPoints:s,postCalculateAllAction:K,getInformationListAction:q,InformationList:f,getCheckErrorPointsAction:z,ErrorPoints:d,deleteClearInformationListAction:U,getErrorPointsAction:Y,errorResult:A,getClosestPointsAction:F,AccumlationInfo:v,postUpdateStoreAccumulationIdAction:H,UpdateStoreAccumulationIdCode:h,getMapResultPointsAction:Q,MapResultPoints:l,pathCalculateOneAction:W,calculateAllAction:X,newPathResultAll:w,getMapDataAction:Z,oldPathResult:D,getRouteDetailsAction:_,routeDetails:C,newPathResult:y,getStoreDetailsAction:tt,storeResult:P,getTransitDepotRouteDataAction:et,historicalPath:R,postAddRouteAction:at,getRouteDataAction:nt,analysisRouteData:I,getRouteVersionAction:ot,routeVersion:x,getSplitLinesAction:ut,SplitLines:N,compareAreaAction:ct,compareAreaData:$,adjustPointAction:rt,convexPoint:L,getConvexPointAction:st,SingleRoute:j,calculateSingleRouteAction:lt,convex:i,getConvexAction:it,saveState:S,getTransitDepotNameAction:gt,areas:E,colorConvex:g,getColorConvexAction:pt,exportRouteMessage:mt}});export{te as u};
