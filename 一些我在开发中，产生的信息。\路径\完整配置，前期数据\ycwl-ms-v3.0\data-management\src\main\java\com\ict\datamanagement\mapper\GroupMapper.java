package com.ict.datamanagement.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ict.datamanagement.domain.entity.Group;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【group】的数据库操作Mapper
* @createDate 2024-04-22 13:45:36
* @Entity generator.domain.Group
*/
@Mapper
public interface GroupMapper extends BaseMapper<Group> {

    List<Group> selectGroupList();

}




