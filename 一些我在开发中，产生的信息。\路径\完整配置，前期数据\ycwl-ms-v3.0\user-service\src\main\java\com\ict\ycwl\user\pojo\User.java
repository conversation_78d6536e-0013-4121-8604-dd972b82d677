package com.ict.ycwl.user.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("`user`")
public class User {

    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize
    @TableField(value = "`user_id`")
    private Long userId;
    @TableField(value = "`login_name`")
    private String loginName;
    @TableField(value = "`user_name`")
    private String userName;
    @TableField(value = "`work_number`")
    private String workNumber;
    @TableField(value = "`sex`")
    private String sex;
    @TableField(value = "`position`")
    private String position;
    @TableField(value = "`password`")
    private String password;
    @TableField(value = "`department`")
    private String department;
    @TableField(value = "`phone`")
    private String phone;
    @TableField(value = "`email`")
    private String email;
    @TableField(value = "`status`")
    private String status;
    @TableField(value = "`avatar_path`")
    private String avatarPath;
    @TableField(value = "`create_by`")
    private Long createBy;
    @TableField(value = "`create_time`")
    private Timestamp createTime;
    @TableField(value = "`update_by`")
    private Long updateBy;
    @TableField(value = "`update_time`")
    private Timestamp updateTime;
    @TableField(value = "`sign_time`")
    private Date signTime;
    @TableField(value = "`role_id`")
    private Long roleId;
    @TableField(value = "`rank`")
    private String rank;


}
