package com.ict.datamanagement.domain.dto.store;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel("商铺添加表单")
@Data
public class StoreAddRequest implements Serializable {

    /**
     * 客户编码
     */
    @ApiModelProperty(value = "客户编码",dataType = "String")
    private String customerCode;

    /**
     * 店铺名称
     */
    @ApiModelProperty(value = "店铺名称",dataType = "String")
    private String storeName;

    /**
     * 店铺经营地址
     */
    @ApiModelProperty(value = "店铺经营地址",dataType = "String")
    private String storeAddress;

    /**
     * 店铺经度
     */
    @ApiModelProperty(value = "店铺经度",dataType = "Double")
    private Double longitude;

    /**
     * 店铺纬度
     */
    @ApiModelProperty(value = "店铺纬度",dataType = "Double")
    private Double latitude;

    /**
     * 商圈类型
     */
    @ApiModelProperty(value = "商圈类型",dataType = "String")
    private String type;

    /**
     * 订货周期
     */
    @ApiModelProperty(value = "订货周期",dataType = "String")
    private String orderCycle;

    /**
     * 店铺所属行政区
     */
    @ApiModelProperty(value = "店铺所属行政区",dataType = "String")
    private String district;

    /**
     * 店铺所属大区
     */
    @ApiModelProperty(value = "店铺所属大区",dataType = "String")
    private String areaName;

    /**
     * 店铺联系人名称
     */
    @ApiModelProperty(value = "店铺联系人名称",dataType = "String")
    private String contactName;

    /**
     * 店铺联系人电话号码
     */
    @ApiModelProperty(value = "店铺联系人电话号码",dataType = "String")
    private String contactPhone;

    /**
     * 客户专员id
     */
    @ApiModelProperty(value = "客户专员id",dataType = "Long")
    private Long customerManagerId;

    /**
     * 聚集区id
     */
    @ApiModelProperty(value = "聚集区id",dataType = "Long")
    private Long accumulationId;

    /**
     * 大区id
     */
    @ApiModelProperty(value = "大区id",dataType = "Long")
    private Long areaId;

    /**
     * 路线id
     */
    @ApiModelProperty(value = "路线id",dataType = "Long")
    private Long routeId;

    /**
     * 路线名称
     */
    @ApiModelProperty(value = "路线名称",dataType = "String")
    private String routeName;

    /**
     * 客户专员名称
     */
    @ApiModelProperty(value = "客户专员名称",dataType = "String")
    private String customerManagerName;


    /**
     * 客户档位
     */
    @ApiModelProperty(value = "客户档位",dataType = "String")
    private String gear;

    /**
     * 商铺类型归属
     */
    @ApiModelProperty(value = "商铺类型归属",dataType = "String")
    private String locationType;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;



}
