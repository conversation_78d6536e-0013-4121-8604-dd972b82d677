<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.pathcalculate.mapper.StoreMapper">

    <select id="selectCoordinatesByAreaName" resultType="com.ict.ycwl.pathcalculate.pojo.LngAndLat">
        SELECT longitude, latitude
        FROM store
        WHERE area_name LIKE CONCAT('%', #{keyword}, '%')
          and longitude is not null
          and latitude is not null;
    </select>

    <select id="selectAllAreaName" resultType="java.lang.String">
        SELECT DISTINCT area_name
        FROM store
        where area_name is not null;
    </select>

    <update id="updateAccumulationIdByLonAndLat">
        UPDATE store
        SET accumulation_id=#{accumulationId}
        where longitude = #{longitude}
          and latitude = #{latitude};
    </update>

    <select id="selectByLongAndLat" resultType="com.ict.ycwl.pathcalculate.pojo.LngAndLat">
        SELECT s.longitude, s.latitude
        FROM store s
                 LEFT JOIN accumulation a ON s.accumulation_id = a.accumulation_id
        WHERE a.longitude = #{longitude}
          and a.latitude = #{latitude}
          and a.is_delete = 0;
    </select>

    <select id="getOrdinaryPoint" resultType="com.ict.ycwl.pathcalculate.pojo.Store">
        SELECT *
        FROM store AS s
        WHERE NOT EXISTS(
                SELECT 1
                FROM accumulation AS a
                WHERE a.longitude = s.longitude
                  AND a.latitude = s.latitude
                  AND a.is_delete = 0
            )
          AND s.longitude IS NOT NULL
          AND s.latitude IS NOT NULL
          AND s.accumulation_id is not null;
    </select>

    <select id="getOrdinaryPointByAccumulationId" resultType="com.ict.ycwl.pathcalculate.pojo.Store">
        SELECT *
        FROM store as s
        where accumulation_id = #{accumulationId}
          and not EXISTS(SELECT 1
                         from accumulation as a
                         where a.longitude = s.longitude and a.latitude = s.latitude and a.is_delete = 0);
    </select>

    <!--    <select id="getOrdinaryPoint" resultType="com.ict.ycwl.clustercalculate.pojo.LngAndLat">-->
    <!--        SELECT longitude, latitude-->
    <!--        FROM store AS s-->
    <!--        WHERE NOT EXISTS(-->
    <!--                SELECT 1-->
    <!--                FROM accumulation AS a-->
    <!--                WHERE a.longitude = s.longitude-->
    <!--                  AND a.latitude = s.latitude-->
    <!--                  AND a.is_delete = 0-->
    <!--            )-->
    <!--          AND s.longitude IS NOT NULL-->
    <!--          AND s.latitude IS NOT NULL;-->
    <!--    </select>-->

</mapper>