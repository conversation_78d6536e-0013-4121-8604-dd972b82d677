<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.guestbook.mapper.FeedbackFileMapper">

    <select id="selectFilePath" parameterType="Long" resultType="String">
        SELECT feedback_file_path FROM feedback_file WHERE feedback_id = #{feedbackId}
    </select>

    <insert id="insertFeedbackFile">
        INSERT INTO feedback_file VALUES
        <foreach collection="fileList" item="file" separator=",">
            (null,#{file.feedbackFilePath},#{file.feedbackFileRealPath},#{file.feedbackId})
        </foreach>
    </insert>

    <delete id="deleteFeedbackFile">
        DELETE FROM feedback_file WHERE feedback_id IN
        <foreach collection="feedbackIdList" separator="," item="feedbackId" open="(" close=")">
            #{feedbackId}
        </foreach>
    </delete>

    <select id="selectFileRealPath" parameterType="List" resultType="String">
        SELECT feedback_file_real_path FROM feedback_file WHERE feedback_id IN
        <foreach collection="feedbackIdList" separator="," item="feedbackId" open="(" close=")">
            #{feedbackId}
        </foreach>
    </select>

</mapper>