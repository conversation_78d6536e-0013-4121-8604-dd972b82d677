package com.ict.ycwl.user.utils;

import com.auth0.jwt.JWT;
import com.auth0.jwt.exceptions.JWTDecodeException;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ict.ycwl.user.dao.OperationDao;
import com.ict.ycwl.user.dao.RoleDao;
import com.ict.ycwl.user.dao.RoleOperationDao;
import com.ict.ycwl.user.dao.UserDao;
import com.ict.ycwl.user.pojo.Operation;
import com.ict.ycwl.user.pojo.Role;
import com.ict.ycwl.user.pojo.RoleOperation;
import com.ict.ycwl.user.pojo.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;



@Component
public class PermissionUtils {

    @Autowired
    private UserDao userDao;

    @Autowired
    private RoleDao roleDao;

    @Autowired
    private RoleOperationDao roleOperationDao;

    @Autowired
    private OperationDao operationDao;

    /**
     * 检查用户是否有指定权限
     * @param authorization token
     * @param permissionName 权限名称
     * @return true表示有权限，false表示无权限
     */
    public boolean hasPermission(String authorization, String permissionName) {
        try {
            // 从token中获取用户ID
            String userId = JWT.decode(authorization).getClaim("userId").asString();
            
            // 查询用户信息
            LambdaQueryWrapper<User> userWrapper = new LambdaQueryWrapper<>();
            userWrapper.eq(User::getUserId, Long.valueOf(userId));
            User user = userDao.selectOne(userWrapper);
            
            if (user == null) {
                return false;
            }

            // 查询用户角色
            LambdaQueryWrapper<Role> roleWrapper = new LambdaQueryWrapper<>();
            roleWrapper.eq(Role::getRoleId, user.getRoleId());
            Role role = roleDao.selectOne(roleWrapper);

            // 特殊处理：系统管理员拥有所有权限
            if (role != null && "系统管理员".equals(role.getRoleName())) {
                return true;
            }

            // 特殊处理：ycwlAdmin用户拥有所有权限
            if ("ycwlAdmin".equals(user.getLoginName())) {
                return true;
            }

            // 查询权限点
            LambdaQueryWrapper<Operation> operationWrapper = new LambdaQueryWrapper<>();
            operationWrapper.eq(Operation::getOperationName, permissionName);
            Operation operation = operationDao.selectOne(operationWrapper);
            
            if (operation == null) {
                return false;
            }

            // 查询角色是否有该权限
            LambdaQueryWrapper<RoleOperation> roleOperationWrapper = new LambdaQueryWrapper<>();
            roleOperationWrapper.eq(RoleOperation::getRoleId, user.getRoleId())
                               .eq(RoleOperation::getOperationId, operation.getOperationId())
                               .eq(RoleOperation::getStatus, "1");
            RoleOperation roleOperation = roleOperationDao.selectOne(roleOperationWrapper);

            return roleOperation != null;

        } catch (JWTDecodeException e) {
            return false;
        } catch (Exception e) {
            return false;
        }
    }
}
