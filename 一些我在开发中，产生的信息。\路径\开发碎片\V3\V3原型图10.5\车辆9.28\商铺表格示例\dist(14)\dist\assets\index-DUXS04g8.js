import{aQ as He,d as v,z as Fe,aa as Ye,b as i,J as Ge,r as We,P as qe,B as Qe}from"./index-C0QCllTd.js";import{i as Xe,m as Ie}from"./merge-B3fxVp56.js";import{t as Ue,i as Ke,d as Je,g as Ze}from"./_commonjsHelpers-BbMlrU8H.js";var ne=1/0,et=17976931348623157e292;function oe(e){if(!e)return e===0?e:0;if(e=Ue(e),e===ne||e===-ne){var t=e<0?-1:1;return t*et}return e===e?e:0}function tt(e,t){for(var r,l=-1,d=e.length;++l<d;){var s=t(e[l]);s!==void 0&&(r=r===void 0?s:r+s)}return r}var rt=Math.floor,nt=Math.random;function ot(e,t){return e+rt(nt()*(t-e+1))}var it=parseFloat,at=Math.min,lt=Math.random;function ie(e,t,r){if(r&&typeof r!="boolean"&&Xe(e,t,r)&&(t=r=void 0),r===void 0&&(typeof t=="boolean"?(r=t,t=void 0):typeof e=="boolean"&&(r=e,e=void 0)),e===void 0&&t===void 0?(e=0,t=1):(e=oe(e),t===void 0?(t=e,e=0):t=oe(t)),e>t){var l=e;e=t,t=l}if(r||e%1||t%1){var d=lt();return at(e+d*(t-e+it("1e-"+((d+"").length-1))),t)}return ot(e,t)}function ae(e){return e&&e.length?tt(e,Ke):0}var st=0;function ut(e){var t=++st;return He(e)+t}let ft="dv-";function X(e,t=!0){return`${t?".":""}${ft}${e}`}function x(e){return X(e,!1)}function dt(e,t){const r=X(t);return`.__STYLED__ {${e.toString()}}`.replaceAll(".__STYLED__",r)}function g(e){return t=>{const r=document.createElement("style"),l=s=>{r.innerHTML=dt(t,s),document.querySelector("head").appendChild(r)},d=()=>document.querySelector("head").removeChild(r);return s=>{const p=e,n=X(s,!1);return v({setup(a,{slots:o}){return Fe(()=>{l(s)}),Ye(()=>{d()}),()=>i(p,Ge(a,{class:n}),{default:()=>{var u;return[(u=o==null?void 0:o.default)==null?void 0:u.call(o)]}})}})}}}g.span=g((e,{slots:t})=>i("span",e,[t==null?void 0:t.default()]));g.div=g((e,{slots:t})=>i("div",e,[t==null?void 0:t.default()]));g.img=g(e=>i("img",e,null));g.svg=g((e,{slots:t})=>i("svg",e,[t==null?void 0:t.default()]));function pt(e,t){const r=new MutationObserver(t);return r.observe(e,{attributes:!0,attributeFilter:["class","style"],attributeOldValue:!0}),r}function ct(e,t){const{clientWidth:r=0,clientHeight:l=0}=e||{};e?(!r||!l)&&console.warn("DataV: Component width or height is 0px, rendering abnormality may occur!"):console.warn("DataV: Failed to get dom node, component rendering may be abnormal!"),t.width=r,t.height=l}function k(){const e=We(),t=[],r=qe({width:0,height:0}),l=()=>{ct(e.value,r)},d=Je(l,100);return Fe(()=>{l();const s=pt(e.value,d);window.addEventListener("resize",d),t.push(()=>{s.disconnect()},()=>{window.removeEventListener("resize",d)})}),Ye(()=>{t.forEach(s=>s())}),{domRef:e,domSize:r}}function _(e){const t=e;return t.install=function(r){r.component(t.displayName||t.name,e)},e}const Ne=e=>e,$t=(e,t)=>{const r=Math.abs(e[0]-t[0]),l=Math.abs(e[1]-t[1]);return Math.sqrt(Math.pow(r,2)+Math.pow(l,2))};function D(e,t=[]){return Ie(e,t)}function S(){return{color:{type:Ne(Array),default:()=>[]},backgroundColor:{type:String,default:"transparent"}}}function O(e,t=[]){return Ie(e,t)}const C=g.div`
  position: relative;
  width: 100%;
  height: 100%;
`("border-box"),L=g.div`
  position: relative;
  width: 100%;
  height: 100%;
`("border-box-content");g.svg`
  position: absolute;
  display: block;
}
.__STYLED__.right-top {
  right: 0px;
  transform: rotateY(180deg);
}
.__STYLED__.left-bottom {
  bottom: 0px;
  transform: rotateX(180deg);
}
.__STYLED__.right-bottom {
  right: 0px;
  bottom: 0px;
  transform: rotateX(180deg) rotateY(180deg);
`("border");g.svg`
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
}
.__STYLED__ > polyline {
  fill: none;
  stroke-width: 1;
`("border-svg-container");g.svg`
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
}
.__STYLED__ > polyline {
  fill: none;
}
.__STYLED__ .stroke-width-1 {
  stroke-width: 1;
}
.__STYLED__ .stroke-width-3 {
  stroke-width: 3;
`("border-svg-container");var F={exports:{}};/*!
	Copyright (c) 2018 Jed Watson.
	Licensed under the MIT License (MIT), see
	http://jedwatson.github.io/classnames
*/var le;function ht(){return le||(le=1,function(e){(function(){var t={}.hasOwnProperty;function r(){for(var s="",p=0;p<arguments.length;p++){var n=arguments[p];n&&(s=d(s,l(n)))}return s}function l(s){if(typeof s=="string"||typeof s=="number")return s;if(typeof s!="object")return"";if(Array.isArray(s))return r.apply(null,s);if(s.toString!==Object.prototype.toString&&!s.toString.toString().includes("[native code]"))return s.toString();var p="";for(var n in s)t.call(s,n)&&s[n]&&(p=d(p,n));return p}function d(s,p){return p?s?s+" "+p:s+p:s}e.exports?(r.default=r,e.exports=r):window.classNames=r})()}(F)),F.exports}var gt=ht();const U=Ze(gt);var bt=Object.defineProperty,yt=Object.defineProperties,mt=Object.getOwnPropertyDescriptors,se=Object.getOwnPropertySymbols,vt=Object.prototype.hasOwnProperty,wt=Object.prototype.propertyIsEnumerable,ue=(e,t,r)=>t in e?bt(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,kt=(e,t)=>{for(var r in t)vt.call(t,r)&&ue(e,r,t[r]);if(se)for(var r of se(t))wt.call(t,r)&&ue(e,r,t[r]);return e},_t=(e,t)=>yt(e,mt(t));const xt=["red","rgba(0,0,255,0.8)"],St=g.svg`
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
}
.__STYLED__ > polyline {
  fill: none;
}
.__STYLED__.reverse {
  transform: rotate(180deg);
}
.__STYLED__ .stroke-width1 {
  stroke-width: 1;
}
.__STYLED__ .stroke-width3 {
  stroke-width: 3px;
  stroke-linecap: round;
`("border-svg-container"),Ot=()=>_t(kt({},S()),{reverse:{type:Boolean,default:!1}});v({name:"BorderBox4",props:Ot(),setup(e,{slots:t}){const{domRef:r,domSize:l}=k();return()=>{const{color:d,backgroundColor:s,reverse:p}=e,{width:n,height:a}=l,o=O(xt,d);return i(C,{class:x("border-box-4"),ref:u=>r.value=u.$el},{default:()=>[i(St,{class:U({reverse:p}),width:n,height:a},{default:()=>[i("polygon",{fill:s,points:`${n-15}, 22 170, 22 150, 7 40, 7 28, 21 32, 24
                16, 42 16, ${a-32} 41, ${a-7} ${n-15}, ${a-7}`},null),i("polyline",{class:"stroke-width1",stroke:o[0],points:`145, ${a-5} 40, ${a-5} 10, ${a-35} 10, 40 40, 5 150, 5 170, 20 ${n-15}, 20`},null),i("polyline",{stroke:o[1],class:"stroke-width1",points:`245, ${a-1} 36, ${a-1} 14, ${a-23} 14, ${a-100}`},null),i("polyline",{class:"stroke-width3",stroke:o[0],points:`7, ${a-40} 7, ${a-75}`},null),i("polyline",{class:"stroke-width3",stroke:o[0],points:"28, 24 13, 41 13, 64"},null),i("polyline",{class:"stroke-width1",stroke:o[0],points:"5, 45 5, 140"},null),i("polyline",{class:"stroke-width1",stroke:o[1],points:"14, 75 14, 180"},null),i("polyline",{class:"stroke-width1",stroke:o[1],points:"55, 11 147, 11 167, 26 250, 26"},null),i("polyline",{class:"stroke-width3",stroke:o[1],points:"158, 5 173, 16"},null),i("polyline",{class:"stroke-width3",style:{strokeDasharray:"100 250"},stroke:o[0],points:`200, 17 ${n-10}, 17`},null),i("polyline",{class:"stroke-width1",style:{strokeDasharray:"80 270"},stroke:o[1],points:`385, 17 ${n-10}, 17`},null)]}),i(L,null,{default:()=>{var u;return[i("slot",null,[(u=t.default)==null?void 0:u.call(t)])]}})]})}}});var Ct=Object.defineProperty,Lt=Object.defineProperties,Mt=Object.getOwnPropertyDescriptors,fe=Object.getOwnPropertySymbols,Rt=Object.prototype.hasOwnProperty,Tt=Object.prototype.propertyIsEnumerable,de=(e,t,r)=>t in e?Ct(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,jt=(e,t)=>{for(var r in t)Rt.call(t,r)&&de(e,r,t[r]);if(fe)for(var r of fe(t))Tt.call(t,r)&&de(e,r,t[r]);return e},Dt=(e,t)=>Lt(e,Mt(t));const At=["rgba(255, 255, 255, 0.35)","rgba(255, 255, 255, 0.20)"],Et=g.svg`
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
}
.__STYLED__ > polyline {
  fill: none;
}
.__STYLED__.reverse {
  transform: rotate(180deg);
}
.__STYLED__ .stroke-width1 {
  stroke-width: 1;
}
.__STYLED__ .stroke-width2 {
  stroke-width: 2px;
}
.__STYLED__ .stroke-width5 {
  stroke-width: 5px;
`("border-svg-container"),Pt=()=>Dt(jt({},S()),{reverse:{type:Boolean,default:!1}});v({name:"BorderBox5",props:Pt(),setup(e,{slots:t}){const{domRef:r,domSize:l}=k();return()=>{const{color:d,backgroundColor:s,reverse:p}=e,{width:n,height:a}=l,o=O(At,d);return i(C,{class:x("border-box-5"),ref:u=>r.value=u.$el},{default:()=>[i(Et,{class:U({reverse:p}),width:n,height:a},{default:()=>[i("polygon",{fill:s,points:`
                  10, 22 ${n-22}, 22 ${n-22}, ${a-86} ${n-84}, ${a-24} 10, ${a-24}`},null),i("polyline",{class:"stroke-width1",stroke:o[0],points:`8, 5 ${n-5}, 5 ${n-5}, ${a-100}
                  ${n-100}, ${a-5} 8, ${a-5} 8, 5`},null),i("polyline",{class:"stroke-width1",stroke:o[1],points:`3, 5 ${n-20}, 5 ${n-20}, ${a-60}
                  ${n-74}, ${a-5} 3, ${a-5} 3, 5`},null),i("polyline",{class:"stroke-width5",stroke:o[1],points:`50, 13 ${n-35}, 13`},null),i("polyline",{class:"stroke-width2",stroke:o[1],points:`15, 20 ${n-35}, 20`},null),i("polyline",{class:"stroke-width2",stroke:o[1],points:`15, ${a-20} ${n-110}, ${a-20}`},null),i("polyline",{class:"stroke-width5",stroke:o[1],points:`15, ${a-13} ${n-110}, ${a-13}`},null)]}),i(L,null,{default:()=>{var u;return[i("slot",null,[(u=t.default)==null?void 0:u.call(t)])]}})]})}}});g.svg`
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
}
.__STYLED__ > polyline {
  fill: none;
  stroke-width: 1;
`("border-svg-container");g.svg`
  position: absolute;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
}
.__STYLED__ > polyline {
  fill: none;
  stroke-linecap: round;
}
.__STYLED__ .stroke-width2 {
  stroke-width: 2px;
}
.__STYLED__ .stroke-width5 {
  stroke-width: 5px;
`("border-svg-container");function A(){return Qe(qe({id:ut("datav_uuid")}))}var Ft=Object.defineProperty,Yt=Object.defineProperties,qt=Object.getOwnPropertyDescriptors,pe=Object.getOwnPropertySymbols,It=Object.prototype.hasOwnProperty,Nt=Object.prototype.propertyIsEnumerable,ce=(e,t,r)=>t in e?Ft(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,Vt=(e,t)=>{for(var r in t)It.call(t,r)&&ce(e,r,t[r]);if(pe)for(var r of pe(t))Nt.call(t,r)&&ce(e,r,t[r]);return e},Bt=(e,t)=>Yt(e,qt(t));const zt=["#235fa7","#4fd2dd"],Ht=g.svg`
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
`("border-svg-container"),Gt=()=>Bt(Vt({},S()),{dur:{type:Number,default:3},reverse:{type:Boolean,default:!1}});v({name:"BorderBox8",props:Gt(),setup(e,{slots:t}){const{domRef:r,domSize:l}=k(),d=A();return()=>{const{color:s,backgroundColor:p,dur:n,reverse:a}=e,{width:o,height:u}=l,c=O(zt,s),$=`border-box-8-path-${d.id}`,m=`border-box-8-gradient-${d.id}`,M=`border-box-8-mask-${d.id}`,R=a?`M 2.5, 2.5 L 2.5, ${u-2.5} L ${o-2.5}, ${u-2.5} L ${o-2.5}, 2.5 L 2.5, 2.5`:`M2.5, 2.5 L${o-2.5}, 2.5 L${o-2.5}, ${u-2.5} L2.5, ${u-2.5} L2.5, 2.5`;return i(C,{class:x("border-box-8"),ref:w=>r.value=w.$el},{default:()=>[i(Ht,{width:o,height:u},{default:()=>[i("defs",null,[i("path",{id:$,d:R,fill:"transparent"},null),i("radialGradient",{id:m,cx:"50%",cy:"50%",r:"50%"},[i("stop",{offset:"0%","stop-color":"#fff","stop-opacity":"1"},null),i("stop",{offset:"100%","stop-color":"#fff","stop-opacity":"0"},null)]),i("mask",{id:M},[i("circle",{cx:"0",cy:"0",r:"150",fill:`url(#${m})`},[i("animateMotion",{dur:`${n}s`,path:R,rotate:"auto",repeatCount:"indefinite"},null)])])]),i("polygon",{fill:p,points:`5, 5 ${o-5}, 5 ${o-5} ${u-5} 5, ${u-5}`},null),i("use",{stroke:c[0],"stroke-width":"1","xlink:href":`#${$}`},null),i("use",{stroke:c[1],"stroke-width":"3","xlink:href":`#${$}`,mask:`url(#${M})`},[i("animate",{attributeName:"stroke-dasharray",from:`0, ${length}`,to:`${length}, 0`,dur:`${n}s`,repeatCount:"indefinite"},null)])]}),i(L,null,{default:()=>{var w;return[i("slot",null,[(w=t.default)==null?void 0:w.call(t)])]}})]})}}});const Wt=["#11eefd","#0078d2"],Qt=g.svg`
  position: absolute;
  width: 100%;
  height: 100%;
  left: 0px;
  top: 0px;
`("border-svg-container"),nn=_(v({name:"BorderBox9",props:S(),setup(e,{slots:t}){const{domRef:r,domSize:l}=k(),d=A();return()=>{const{color:s,backgroundColor:p}=e,{width:n,height:a}=l,o=O(Wt,s),u=`border-box-9-gradient-${d.id}`,c=`border-box-9-mask-${d.id}`;return i(C,{class:x("border-box-9"),ref:$=>r.value=$.$el},{default:()=>[i(Qt,{width:n,height:a},{default:()=>[i("defs",null,[i("linearGradient",{id:u,x1:"0%",y1:"0%",x2:"100%",y2:"100%"},[i("animate",{attributeName:"x1",values:"0%;100%;0%",dur:"10s",begin:"0s",repeatCount:"indefinite"},null),i("animate",{attributeName:"x2",values:"100%;0%;100%",dur:"10s",begin:"0s",repeatCount:"indefinite"},null),i("stop",{offset:"0%","stop-color":o[0]},[i("animate",{attributeName:"stop-color",values:`${o[0]};${o[1]};${o[0]}`,dur:"10s",begin:"0s",repeatCount:"indefinite"},null)]),i("stop",{offset:"100%","stop-color":o[1]},[i("animate",{attributeName:"stop-color",values:`${o[1]};${o[0]};${o[1]}`,dur:"10s",begin:"0s",repeatCount:"indefinite"},null)])]),i("mask",{id:c},[i("polyline",{stroke:"#fff","stroke-width":"3",fill:"transparent",points:`8, ${a*.4} 8, 3, ${n*.4+7}, 3`},null),i("polyline",{fill:"#fff",points:`
                      8, ${a*.15} 8, 3, ${n*.1+7}, 3
                      ${n*.1}, 8 14, 8 14, ${a*.15-7}
                    `},null),i("polyline",{stroke:"#fff","stroke-width":"3",fill:"transparent",points:`${n*.5}, 3 ${n-3}, 3, ${n-3}, ${a*.25}`},null),i("polyline",{fill:"#fff",points:`
                      ${n*.52}, 3 ${n*.58}, 3
                      ${n*.58-7}, 9 ${n*.52+7}, 9
                    `},null),i("polyline",{fill:"#fff",points:`
                      ${n*.9}, 3 ${n-3}, 3 ${n-3}, ${a*.1}
                      ${n-9}, ${a*.1-7} ${n-9}, 9 ${n*.9+7}, 9
                    `},null),i("polyline",{stroke:"#fff","stroke-width":"3",fill:"transparent",points:`8, ${a*.5} 8, ${a-3} ${n*.3+7}, ${a-3}`},null),i("polyline",{fill:"#fff",points:`
                      8, ${a*.55} 8, ${a*.7}
                      2, ${a*.7-7} 2, ${a*.55+7}
                    `},null),i("polyline",{stroke:"#fff","stroke-width":"3",fill:"transparent",points:`${n*.35}, ${a-3} ${n-3}, ${a-3} ${n-3}, ${a*.35}`},null),i("polyline",{fill:"#fff",points:`
                      ${n*.92}, ${a-3} ${n-3}, ${a-3} ${n-3}, ${a*.8} ${n-9}, ${a*.8+7} ${n-9}, ${a-9} ${n*.92+7}, ${a-9}`},null)])]),i("polygon",{fill:p,points:`
                  15, 9 ${n*.1+1}, 9 ${n*.1+4}, 6 ${n*.52+2}, 6
                  ${n*.52+6}, 10 ${n*.58-7}, 10 ${n*.58-2}, 6
                  ${n*.9+2}, 6 ${n*.9+6}, 10 ${n-10}, 10 ${n-10}, ${a*.1-6}
                  ${n-6}, ${a*.1-1} ${n-6}, ${a*.8+1} ${n-10}, ${a*.8+6}
                  ${n-10}, ${a-10} ${n*.92+7}, ${a-10}  ${n*.92+2}, ${a-6}
                  11, ${a-6} 11, ${a*.15-2} 15, ${a*.15-7}
                `},null),i("rect",{x:"0",y:"0",width:n,height:a,fill:`url(#${u})`,mask:`url(#${c})`},null)]}),i(L,null,{default:()=>{var $;return[i("slot",null,[($=t.default)==null?void 0:$.call(t)])]}})]})}}}));g.svg`
  position: absolute;
  display: block;
}
.__STYLED__.right-top {
  right: 0px;
  transform: rotateY(180deg);
}
.__STYLED__.left-bottom {
  bottom: 0px;
  transform: rotateX(180deg);
}
.__STYLED__.right-bottom {
  right: 0px;
  bottom: 0px;
  transform: rotateX(180deg) rotateY(180deg);
`("border-svg-container");var Y={},q={exports:{}},$e;function Xt(){return $e||($e=1,function(e){function t(r){return r&&r.__esModule?r:{default:r}}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}(q)),q.exports}var I={exports:{}},N={exports:{}},V={exports:{}},he;function Ve(){return he||(he=1,function(e){function t(r,l){(l==null||l>r.length)&&(l=r.length);for(var d=0,s=Array(l);d<l;d++)s[d]=r[d];return s}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}(V)),V.exports}var ge;function Ut(){return ge||(ge=1,function(e){var t=Ve();function r(l){if(Array.isArray(l))return t(l)}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports}(N)),N.exports}var B={exports:{}},be;function Kt(){return be||(be=1,function(e){function t(r){if(typeof Symbol<"u"&&r[Symbol.iterator]!=null||r["@@iterator"]!=null)return Array.from(r)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}(B)),B.exports}var z={exports:{}},ye;function Jt(){return ye||(ye=1,function(e){var t=Ve();function r(l,d){if(l){if(typeof l=="string")return t(l,d);var s={}.toString.call(l).slice(8,-1);return s==="Object"&&l.constructor&&(s=l.constructor.name),s==="Map"||s==="Set"?Array.from(l):s==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(s)?t(l,d):void 0}}e.exports=r,e.exports.__esModule=!0,e.exports.default=e.exports}(z)),z.exports}var H={exports:{}},me;function Zt(){return me||(me=1,function(e){function t(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}e.exports=t,e.exports.__esModule=!0,e.exports.default=e.exports}(H)),H.exports}var ve;function er(){return ve||(ve=1,function(e){var t=Ut(),r=Kt(),l=Jt(),d=Zt();function s(p){return t(p)||r(p)||l(p)||d()}e.exports=s,e.exports.__esModule=!0,e.exports.default=e.exports}(I)),I.exports}var G={},we;function tr(){return we||(we=1,function(e){Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var t=new Map([["transparent","rgba(0,0,0,0)"],["black","#000000"],["silver","#C0C0C0"],["gray","#808080"],["white","#FFFFFF"],["maroon","#800000"],["red","#FF0000"],["purple","#800080"],["fuchsia","#FF00FF"],["green","#008000"],["lime","#00FF00"],["olive","#808000"],["yellow","#FFFF00"],["navy","#000080"],["blue","#0000FF"],["teal","#008080"],["aqua","#00FFFF"],["aliceblue","#f0f8ff"],["antiquewhite","#faebd7"],["aquamarine","#7fffd4"],["azure","#f0ffff"],["beige","#f5f5dc"],["bisque","#ffe4c4"],["blanchedalmond","#ffebcd"],["blueviolet","#8a2be2"],["brown","#a52a2a"],["burlywood","#deb887"],["cadetblue","#5f9ea0"],["chartreuse","#7fff00"],["chocolate","#d2691e"],["coral","#ff7f50"],["cornflowerblue","#6495ed"],["cornsilk","#fff8dc"],["crimson","#dc143c"],["cyan","#00ffff"],["darkblue","#00008b"],["darkcyan","#008b8b"],["darkgoldenrod","#b8860b"],["darkgray","#a9a9a9"],["darkgreen","#006400"],["darkgrey","#a9a9a9"],["darkkhaki","#bdb76b"],["darkmagenta","#8b008b"],["darkolivegreen","#556b2f"],["darkorange","#ff8c00"],["darkorchid","#9932cc"],["darkred","#8b0000"],["darksalmon","#e9967a"],["darkseagreen","#8fbc8f"],["darkslateblue","#483d8b"],["darkslategray","#2f4f4f"],["darkslategrey","#2f4f4f"],["darkturquoise","#00ced1"],["darkviolet","#9400d3"],["deeppink","#ff1493"],["deepskyblue","#00bfff"],["dimgray","#696969"],["dimgrey","#696969"],["dodgerblue","#1e90ff"],["firebrick","#b22222"],["floralwhite","#fffaf0"],["forestgreen","#228b22"],["gainsboro","#dcdcdc"],["ghostwhite","#f8f8ff"],["gold","#ffd700"],["goldenrod","#daa520"],["greenyellow","#adff2f"],["grey","#808080"],["honeydew","#f0fff0"],["hotpink","#ff69b4"],["indianred","#cd5c5c"],["indigo","#4b0082"],["ivory","#fffff0"],["khaki","#f0e68c"],["lavender","#e6e6fa"],["lavenderblush","#fff0f5"],["lawngreen","#7cfc00"],["lemonchiffon","#fffacd"],["lightblue","#add8e6"],["lightcoral","#f08080"],["lightcyan","#e0ffff"],["lightgoldenrodyellow","#fafad2"],["lightgray","#d3d3d3"],["lightgreen","#90ee90"],["lightgrey","#d3d3d3"],["lightpink","#ffb6c1"],["lightsalmon","#ffa07a"],["lightseagreen","#20b2aa"],["lightskyblue","#87cefa"],["lightslategray","#778899"],["lightslategrey","#778899"],["lightsteelblue","#b0c4de"],["lightyellow","#ffffe0"],["limegreen","#32cd32"],["linen","#faf0e6"],["magenta","#ff00ff"],["mediumaquamarine","#66cdaa"],["mediumblue","#0000cd"],["mediumorchid","#ba55d3"],["mediumpurple","#9370db"],["mediumseagreen","#3cb371"],["mediumslateblue","#7b68ee"],["mediumspringgreen","#00fa9a"],["mediumturquoise","#48d1cc"],["mediumvioletred","#c71585"],["midnightblue","#191970"],["mintcream","#f5fffa"],["mistyrose","#ffe4e1"],["moccasin","#ffe4b5"],["navajowhite","#ffdead"],["oldlace","#fdf5e6"],["olivedrab","#6b8e23"],["orange","#ffa500"],["orangered","#ff4500"],["orchid","#da70d6"],["palegoldenrod","#eee8aa"],["palegreen","#98fb98"],["paleturquoise","#afeeee"],["palevioletred","#db7093"],["papayawhip","#ffefd5"],["peachpuff","#ffdab9"],["peru","#cd853f"],["pink","#ffc0cb"],["plum","#dda0dd"],["powderblue","#b0e0e6"],["rosybrown","#bc8f8f"],["royalblue","#4169e1"],["saddlebrown","#8b4513"],["salmon","#fa8072"],["sandybrown","#f4a460"],["seagreen","#2e8b57"],["seashell","#fff5ee"],["sienna","#a0522d"],["skyblue","#87ceeb"],["slateblue","#6a5acd"],["slategray","#708090"],["slategrey","#708090"],["snow","#fffafa"],["springgreen","#00ff7f"],["steelblue","#4682b4"],["tan","#d2b48c"],["thistle","#d8bfd8"],["tomato","#ff6347"],["turquoise","#40e0d0"],["violet","#ee82ee"],["wheat","#f5deb3"],["whitesmoke","#f5f5f5"],["yellowgreen","#9acd32"]]);e.default=t}(G)),G}var ke;function rr(){return ke||(ke=1,function(e){var t=Xt();Object.defineProperty(e,"__esModule",{value:!0}),e.getRgbValue=o,e.getRgbaValue=$,e.getOpacity=m,e.toRgb=M,e.toHex=R,e.getColorFromRgbValue=w,e.darken=ee,e.lighten=te,e.fade=re,e.default=void 0;var r=t(er()),l=t(tr()),d=/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/,s=/^(rgb|rgba|RGB|RGBA)/,p=/^(rgba|RGBA)/;function n(f){var h=d.test(f),b=s.test(f);return h||b?f:(f=a(f),f||(console.error("Color: Invalid color!"),!1))}function a(f){return f?l.default.has(f)?l.default.get(f):!1:(console.error("getColorByKeywords: Missing parameters!"),!1)}function o(f){if(!f)return console.error("getRgbValue: Missing parameters!"),!1;if(f=n(f),!f)return!1;var h=d.test(f),b=s.test(f),y=f.toLowerCase();if(h)return u(y);if(b)return c(y)}function u(f){return f=f.replace("#",""),f.length===3&&(f=Array.from(f).map(function(h){return h+h}).join("")),f=f.split(""),new Array(3).fill(0).map(function(h,b){return parseInt("0x".concat(f[b*2]).concat(f[b*2+1]))})}function c(f){return f.replace(/rgb\(|rgba\(|\)/g,"").split(",").slice(0,3).map(function(h){return parseInt(h)})}function $(f){if(!f)return console.error("getRgbaValue: Missing parameters!"),!1;var h=o(f);return h?(h.push(m(f)),h):!1}function m(f){if(!f)return console.error("getOpacity: Missing parameters!"),!1;if(f=n(f),!f)return!1;var h=p.test(f);return h?(f=f.toLowerCase(),Number(f.split(",").slice(-1)[0].replace(/[)|\s]/g,""))):1}function M(f,h){if(!f)return console.error("toRgb: Missing parameters!"),!1;var b=o(f);if(!b)return!1;var y=typeof h=="number";return y?"rgba("+b.join(",")+",".concat(h,")"):"rgb("+b.join(",")+")"}function R(f){return f?d.test(f)?f:(f=o(f),f?"#"+f.map(function(h){return Number(h).toString(16)}).map(function(h){return h==="0"?"00":h}).join(""):!1):(console.error("toHex: Missing parameters!"),!1)}function w(f){if(!f)return console.error("getColorFromRgbValue: Missing parameters!"),!1;var h=f.length;if(h!==3&&h!==4)return console.error("getColorFromRgbValue: Value is illegal!"),!1;var b=h===3?"rgb(":"rgba(";return b+=f.join(",")+")",b}function ee(f){var h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;if(!f)return console.error("darken: Missing parameters!"),!1;var b=$(f);return b?(b=b.map(function(y,P){return P===3?y:y-Math.ceil(2.55*h)}).map(function(y){return y<0?0:y}),w(b)):!1}function te(f){var h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0;if(!f)return console.error("lighten: Missing parameters!"),!1;var b=$(f);return b?(b=b.map(function(y,P){return P===3?y:y+Math.ceil(2.55*h)}).map(function(y){return y>255?255:y}),w(b)):!1}function re(f){var h=arguments.length>1&&arguments[1]!==void 0?arguments[1]:100;if(!f)return console.error("fade: Missing parameters!"),!1;var b=o(f);if(!b)return!1;var y=[].concat((0,r.default)(b),[h/100]);return w(y)}var ze={fade:re,toHex:R,toRgb:M,darken:ee,lighten:te,getOpacity:m,getRgbValue:o,getRgbaValue:$,getColorFromRgbValue:w};e.default=ze}(Y)),Y}var j=rr(),nr=Object.defineProperty,or=Object.defineProperties,ir=Object.getOwnPropertyDescriptors,_e=Object.getOwnPropertySymbols,ar=Object.prototype.hasOwnProperty,lr=Object.prototype.propertyIsEnumerable,xe=(e,t,r)=>t in e?nr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,sr=(e,t)=>{for(var r in t)ar.call(t,r)&&xe(e,r,t[r]);if(_e)for(var r of _e(t))lr.call(t,r)&&xe(e,r,t[r]);return e},ur=(e,t)=>or(e,ir(t));const Se=["#8aaafb","#1f33a2"],fr=g.svg`
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
}
.__STYLED__ > polyline {
  fill: none;
  stroke-width: 1;
`("border-svg-container"),dr=()=>ur(sr({},S()),{titleWidth:{type:Number,default:250},title:{type:String,default:""}}),on=_(v({name:"BorderBox11",props:dr(),setup(e,{slots:t}){const{domRef:r,domSize:l}=k(),d=A();return()=>{const{color:s,backgroundColor:p,titleWidth:n,title:a}=e,{width:o,height:u}=l,c=O(Se,s),$=`border-box-11-filterId-${d}`;return i(C,{class:x("border-box-11"),ref:m=>r.value=m.$el},{default:()=>[i(fr,{width:o,height:u},{default:()=>[i("defs",null,[i("filter",{id:$,height:"150%",width:"150%",x:"-25%",y:"-25%"},[i("feMorphology",{operator:"dilate",radius:"2",in:"SourceAlpha",result:"thicken"},null),i("feGaussianBlur",{in:"thicken",stdDeviation:"3",result:"blurred"},null),i("feFlood",{"flood-color":c[1],result:"glowColor"},null),i("feComposite",{in:"glowColor",in2:"blurred",operator:"in",result:"softGlowColored"},null),i("feMerge",null,[i("feMergeNode",{in:"softGlowColored"},null),i("feMergeNode",{in:"SourceGraphic"},null)])])]),i("polygon",{fill:p,points:`
                  20, 32 ${o*.5-n/2}, 32 ${o*.5-n/2+20}, 53
                  ${o*.5+n/2-20}, 53 ${o*.5+n/2}, 32
                  ${o-20}, 32 ${o-8}, 48 ${o-8}, ${u-25} ${o-20}, ${u-8}
                  20, ${u-8} 8, ${u-25} 8, 50
                `},null),i("polyline",{stroke:c[0],filter:`url(#${$})`,points:`
                  ${(o-n)/2}, 30
                  20, 30 7, 50 7, ${50+(u-167)/2}
                  13, ${55+(u-167)/2} 13, ${135+(u-167)/2}
                  7, ${140+(u-167)/2} 7, ${u-27}
                  20, ${u-7} ${o-20}, ${u-7} ${o-7}, ${u-27}
                  ${o-7}, ${140+(u-167)/2} ${o-13}, ${135+(u-167)/2}
                  ${o-13}, ${55+(u-167)/2} ${o-7}, ${50+(u-167)/2}
                  ${o-7}, 50 ${o-20}, 30 ${(o+n)/2}, 30
                  ${(o+n)/2-20}, 7 ${(o-n)/2+20}, 7
                  ${(o-n)/2}, 30 ${(o-n)/2+20}, 52
                  ${(o+n)/2-20}, 52 ${(o+n)/2}, 30
                `},null),i("polygon",{stroke:c[0],fill:"transparent",points:`
                  ${(o+n)/2-5}, 30 ${(o+n)/2-21}, 11
                  ${(o+n)/2-27}, 11 ${(o+n)/2-8}, 34
                `},null),i("polygon",{stroke:c[0],fill:"transparent",points:`
                  ${(o-n)/2+5}, 30 ${(o-n)/2+22}, 49
                  ${(o-n)/2+28}, 49 ${(o-n)/2+8}, 26
                `},null),i("polygon",{stroke:c[0],fill:j.fade(c[1]||Se[1],30),filter:`url(#${$})`,points:`
                  ${(o+n)/2-11}, 37 ${(o+n)/2-32}, 11
                  ${(o-n)/2+23}, 11 ${(o-n)/2+11}, 23
                  ${(o-n)/2+33}, 49 ${(o+n)/2-22}, 49
                `},null),i("polygon",{filter:`url(#${$})`,fill:c[0],opacity:"1",points:`
                  ${(o-n)/2-10}, 37 ${(o-n)/2-31}, 37
                  ${(o-n)/2-25}, 46 ${(o-n)/2-4}, 46
                `},[i("animate",{attributeName:"opacity",values:"1;0.7;1",dur:"2s",begin:"0s",repeatCount:"indefinite"},null)]),i("polygon",{filter:`url(#${$})`,fill:c[0],opacity:"0.7",points:`
                  ${(o-n)/2-40}, 37 ${(o-n)/2-61}, 37
                  ${(o-n)/2-55}, 46 ${(o-n)/2-34}, 46
                `},[i("animate",{attributeName:"opacity",values:"0.7;0.4;0.7",dur:"2s",begin:"0s",repeatCount:"indefinite"},null)]),i("polygon",{filter:`url(#${$})`,fill:c[0],opacity:"0.5",points:`
                  ${(o-n)/2-70}, 37 ${(o-n)/2-91}, 37
                  ${(o-n)/2-85}, 46 ${(o-n)/2-64}, 46
                `},[i("animate",{attributeName:"opacity",values:"0.5;0.2;0.5",dur:"2s",begin:"0s",repeatCount:"indefinite"},null)]),i("polygon",{filter:`url(#${$})`,fill:c[0],opacity:"1",points:`
                  ${(o+n)/2+30}, 37 ${(o+n)/2+9}, 37
                  ${(o+n)/2+3}, 46 ${(o+n)/2+24}, 46
                `},[i("animate",{attributeName:"opacity",values:"1;0.7;1",dur:"2s",begin:"0s",repeatCount:"indefinite"},null)]),i("polygon",{filter:`url(#${$})`,fill:c[0],opacity:"0.7",points:`
                  ${(o+n)/2+60}, 37 ${(o+n)/2+39}, 37
                  ${(o+n)/2+33}, 46 ${(o+n)/2+54}, 46
                `},[i("animate",{attributeName:"opacity",values:"0.7;0.4;0.7",dur:"2s",begin:"0s",repeatCount:"indefinite"},null)]),i("polygon",{filter:`url(#${$})`,fill:c[0],opacity:"0.5",points:`
                  ${(o+n)/2+90}, 37 ${(o+n)/2+69}, 37
                  ${(o+n)/2+63}, 46 ${(o+n)/2+84}, 46
                `},[i("animate",{attributeName:"opacity",values:"0.5;0.2;0.5",dur:"2s",begin:"0s",repeatCount:"indefinite"},null)]),i("text",{class:"dv-border-box-11-title",x:`${o/2}`,y:"32",fill:"#fff","font-size":"18","text-anchor":"middle","dominant-baseline":"middle"},[a]),i("polygon",{fill:c[0],filter:`url(#${$})`,points:`
                  7, ${53+(u-167)/2} 11, ${57+(u-167)/2}
                  11, ${133+(u-167)/2} 7, ${137+(u-167)/2}
                `},null),i("polygon",{fill:c[0],filter:`url(#${$})`,points:`
                  ${o-7}, ${53+(u-167)/2} ${o-11}, ${57+(u-167)/2}
                  ${o-11}, ${133+(u-167)/2} ${o-7}, ${137+(u-167)/2}
                `},null)]}),i(L,null,{default:()=>{var m;return[i("slot",null,[(m=t.default)==null?void 0:m.call(t)])]}})]})}}})),T=["#2e6099","#7ce7fd"],pr=g.svg`
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
`("border-svg-container"),an=_(v({name:"BorderBox12",props:S(),setup(e,{slots:t}){const{domRef:r,domSize:l}=k(),d=A();return()=>{const{color:s,backgroundColor:p}=e,{width:n,height:a}=l,o=O(T,s),u=`border-box-12-filterId-${d}`;return i(C,{class:x("border-box-12"),ref:c=>r.value=c.$el},{default:()=>[i(pr,{width:n,height:a},{default:()=>[i("defs",null,[i("filter",{id:u,height:"150%",width:"150%",x:"-25%",y:"-25%"},[i("feMorphology",{operator:"dilate",radius:"1",in:"SourceAlpha",result:"thicken"},null),i("feGaussianBlur",{in:"thicken",stdDeviation:"2",result:"blurred"},null),i("feFlood",{"flood-color":j.fade(o[1]||T[1],70),result:"glowColor"},[i("animate",{attributeName:"flood-color",values:`
                        ${j.fade(o[1]||T[1],70)};
                        ${j.fade(o[1]||T[1],30)};
                        ${j.fade(o[1]||T[1],70)};
                      `,dur:"3s",begin:"0s",repeatCount:"indefinite"},null)]),i("feComposite",{in:"glowColor",in2:"blurred",operator:"in",result:"softGlowColored"},null),i("feMerge",null,[i("feMergeNode",{in:"softGlowColored"},null),i("feMergeNode",{in:"SourceGraphic"},null)])])]),n&&a&&i("path",{fill:p,"stroke-width":"2",stroke:o[0],d:`
                    M15 5 L ${n-15} 5 Q ${n-5} 5, ${n-5} 15
                    L ${n-5} ${a-15} Q ${n-5} ${a-5}, ${n-15} ${a-5}
                    L 15, ${a-5} Q 5 ${a-5} 5 ${a-15} L 5 15
                    Q 5 5 15 5
                  `},null),i("path",{"stroke-width":"2",fill:"transparent","stroke-linecap":"round",filter:`url(#${u})`,stroke:o[1],d:"M 20 5 L 15 5 Q 5 5 5 15 L 5 20"},null),i("path",{"stroke-width":"2",fill:"transparent","stroke-linecap":"round",filter:`url(#${u})`,stroke:o[1],d:`M ${n-20} 5 L ${n-15} 5 Q ${n-5} 5 ${n-5} 15 L ${n-5} 20`},null),i("path",{"stroke-width":"2",fill:"transparent","stroke-linecap":"round",filter:`url(#${u})`,stroke:o[1],d:`
                  M ${n-20} ${a-5} L ${n-15} ${a-5}
                  Q ${n-5} ${a-5} ${n-5} ${a-15}
                  L ${n-5} ${a-20}
                `},null),i("path",{"stroke-width":"2",fill:"transparent","stroke-linecap":"round",filter:`url(#${u})`,stroke:o[1],d:`
                  M 20 ${a-5} L 15 ${a-5}
                  Q 5 ${a-5} 5 ${a-15}
                  L 5 ${a-20}
                `},null)]}),i(L,null,{default:()=>{var c;return[i("slot",null,[(c=t.default)==null?void 0:c.call(t)])]}})]})}}}));g.svg`
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0px;
  left: 0px;
`("border-svg-container");function E(){return{color:{type:Ne(Array),default:()=>[]}}}function K(){return{reverse:{type:Boolean,default:!1}}}function J(e){return{duration:{type:Number,default:e}}}function cr({width:e,height:t,rowPoints:r,rowCount:l}){const d=e/(r+1),s=t/(l+1);return new Array(l).fill(0).map((p,n)=>new Array(r).fill(0).map((a,o)=>[d*(o+1),s*(n+1)])).reduce((p,n)=>[...p,...n],[])}const $r=200,hr=50,Z=20,gr=4,Be=cr({width:$r,height:hr,rowPoints:Z,rowCount:gr});Be[Z*2-1];Be[Z*2-3];g.div`
  width: 100%;
  height: 100%;
}
.__STYLED__ svg {
  transform-origin: left top;
`("decoration-1");var br=Object.defineProperty,Oe=Object.getOwnPropertySymbols,yr=Object.prototype.hasOwnProperty,mr=Object.prototype.propertyIsEnumerable,Ce=(e,t,r)=>t in e?br(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,W=(e,t)=>{for(var r in t||(t={}))yr.call(t,r)&&Ce(e,r,t[r]);if(Oe)for(var r of Oe(t))mr.call(t,r)&&Ce(e,r,t[r]);return e};const vr=["#3faacb","#fff"];function wr(){return W(W(W({},E()),K()),J(6))}function kr(e,t,r){return e?{width:1,height:r,x:t/2,y:0}:{width:t,height:1,x:0,y:r/2}}const _r=g.div`
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
`("decoration-2");v({name:"Decoration2",props:wr(),setup(e){const{domRef:t,domSize:r}=k();return()=>{const{width:l,height:d}=r,{color:s,reverse:p,duration:n}=e,a=D(vr,s),{x:o,y:u,width:c,height:$}=kr(p,l,d);return i(_r,{ref:m=>t.value=m.$el},{default:()=>[i("svg",{width:l,height:d},[i("rect",{x:o,y:u,width:c,height:$,fill:a[0]},[i("animate",{attributeName:p?"height":"width",from:"0",to:p?d:l,dur:`${n}s`,calcMode:"spline",keyTimes:"0;1",keySplines:".42,0,.58,1",repeatCount:"indefinite"},null)]),i("rect",{x:o,y:u,width:"1",height:"1",fill:a[1]},[i("animate",{attributeName:p?"y":"x",from:"0",to:p?d:l,dur:`${n}s`,calcMode:"spline",keyTimes:"0;1",keySplines:"0.42,0,0.58,1",repeatCount:"indefinite"},null)])])]})}}});function xr({width:e,height:t,rowPoints:r,rowCount:l}){const d=e/(r+1),s=t/(l+1);return new Array(l).fill(0).map((p,n)=>new Array(r).fill(0).map((a,o)=>[d*(o+1),s*(n+1)])).reduce((p,n)=>[...p,...n],[])}const Sr=300,Or=35,Cr=25,Lr=2;xr({width:Sr,height:Or,rowPoints:Cr,rowCount:Lr});g.div`
  width: 100%;
  height: 100%;
}
.__STYLED__ svg {
  transform-origin: left top;
`("decoration-3");var Mr=Object.defineProperty,Le=Object.getOwnPropertySymbols,Rr=Object.prototype.hasOwnProperty,Tr=Object.prototype.propertyIsEnumerable,Me=(e,t,r)=>t in e?Mr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,Q=(e,t)=>{for(var r in t||(t={}))Rr.call(t,r)&&Me(e,r,t[r]);if(Le)for(var r of Le(t))Tr.call(t,r)&&Me(e,r,t[r]);return e};function jr(){return Q(Q(Q({},E()),K()),J(3))}const Dr=["rgba(255, 255, 255, 0.3)","rgba(255, 255, 255, 0.3)"],Ar=g.div`
  position: relative;
  width: 100%;
  height: 100%;
`("decoration-4"),Er=g.div`
  display: flex;
  overflow: hidden;
  position: absolute;
  flex: 1;
}
.__STYLED__.normal {
  animation: ani-height ease-in-out infinite;
  left: 50%;
  margin-left: -2px;
}
.__STYLED__.reverse {
  animation: ani-width ease-in-out infinite;
  top: 50%;
  margin-top: -2px;
}
@keyframes ani-height {
  0% {
    height: 0%;
  }
  70% {
    height: 100%;
  }
  100% {
    height: 100%;
  }
}
@keyframes ani-width {
  0% {
    width: 0%;
  }
  70% {
    width: 100%;
  }
  100% {
    width: 100%;
  }
`("decoration-content");v({name:"Decoration4",props:jr(),setup(e){const{domRef:t,domSize:r}=k();return()=>{const{width:l,height:d}=r,{color:s,reverse:p,duration:n}=e,a=D(Dr,s),o=p?l:5,u=p?5:d,c={width:`${o}px`,height:`${u}px`,animationDuration:`${n}s`},$=p?`0, 2.5 ${l}, 2.5`:`2.5, 0 2.5, ${d}`;return i(Ar,{ref:m=>t.value=m.$el},{default:()=>[i(Er,{class:U(p?"reverse":"normal"),style:c},{default:()=>[i("svg",{width:o,height:u},[i("polyline",{stroke:a[0],points:$},null),i("polyline",{class:"bold-line",stroke:a[1],"stroke-width":"3","stroke-dasharray":"20, 80","stroke-dashoffset":"-30",points:$},null)])]})]})}}});var Pr=Object.defineProperty,Re=Object.getOwnPropertySymbols,Fr=Object.prototype.hasOwnProperty,Yr=Object.prototype.propertyIsEnumerable,Te=(e,t,r)=>t in e?Pr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,je=(e,t)=>{for(var r in t||(t={}))Fr.call(t,r)&&Te(e,r,t[r]);if(Re)for(var r of Re(t))Yr.call(t,r)&&Te(e,r,t[r]);return e};const qr=["#3f96a5","#3f96a5"];function Ir(){return je(je({},E()),J(1.2))}function De(e){return new Array(e.length-1).fill(0).map((t,r)=>$t(e[r],e[r+1]))}function Nr(e,t){const r=[[0,t*.2],[e*.18,t*.2],[e*.2,t*.4],[e*.25,t*.4],[e*.27,t*.6],[e*.72,t*.6],[e*.75,t*.4],[e*.8,t*.4],[e*.82,t*.2],[e,t*.2]],l=[[e*.3,t*.8],[e*.7,t*.8]];return{line1Sum:ae(De(r)),line2Sum:ae(De(l)),line1Point:r.map(d=>d.join(",")).join(" "),line2Point:l.map(d=>d.join(",")).join(" ")}}const Vr=g.div`
  width: 100%;
  height: 100%;
`("decoration-5");v({name:"Decoration5",props:Ir(),setup(e){const{domRef:t,domSize:r}=k();return()=>{const{width:l,height:d}=r,{color:s,duration:p}=e,n=D(qr,s),{line1Sum:a,line2Sum:o,line1Point:u,line2Point:c}=Nr(l,d);return i(Vr,{ref:$=>t.value=$.$el},{default:()=>[i("svg",{width:l,height:d},[i("polyline",{fill:"transparent",stroke:n[0],"stroke-width":"3",points:u},[i("animate",{attributeName:"stroke-dasharray",attributeType:"XML",from:`0, ${a/2}, 0, ${a/2}`,to:`0, 0, ${a}, 0`,dur:`${p}s`,begin:"0s",calcMode:"spline",keyTimes:"0;1",keySplines:"0.4,1,0.49,0.98",repeatCount:"indefinite"},null)]),i("polyline",{fill:"transparent",stroke:n[1],"stroke-width":"2",points:c},[i("animate",{attributeName:"stroke-dasharray",attributeType:"XML",from:`0, ${o/2}, 0, ${o/2}`,to:`0, 0, ${o}, 0`,dur:`${p}s`,begin:"0s",calcMode:"spline",keyTimes:"0;1",keySplines:".4,1,.49,.98",repeatCount:"indefinite"},null)])])]})}}});const Br=300,zr=35,Hr=1,Gr=40;g.div`
  width: 100%;
  height: 100%;
}
.__STYLED__ .svg-origin {
  transform-origin: left top;
`("decoration-6");function Wr({width:e,height:t,rowPoints:r,rowCount:l}){const d=e/(r+1),s=t/(l+1),p=new Array(l).fill(0).map((u,c)=>new Array(r).fill(0).map(($,m)=>[d*(m+1),s*(c+1)])).reduce((u,c)=>[...u,...c],[]),n=new Array(l*r).fill(0).map(()=>Math.random()>.8?ie(.7*t,t):ie(.2*t,.5*t)),a=new Array(l*r).fill(0).map((u,c)=>n[c]*Math.random()),o=new Array(l*r).fill(0).map(()=>Math.random()+1.5);return{points:p,heights:n,minHeights:a,randoms:o}}Wr({width:Br,height:zr,rowPoints:Gr,rowCount:Hr});g.div`
  display: flex;
  width: 100%;
  height: 100%;
  justify-content: center;
  align-items: center;
`("decoration-7");var Qr=Object.defineProperty,Ae=Object.getOwnPropertySymbols,Xr=Object.prototype.hasOwnProperty,Ur=Object.prototype.propertyIsEnumerable,Ee=(e,t,r)=>t in e?Qr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,Pe=(e,t)=>{for(var r in t||(t={}))Xr.call(t,r)&&Ee(e,r,t[r]);if(Ae)for(var r of Ae(t))Ur.call(t,r)&&Ee(e,r,t[r]);return e};const Kr=["#3f96a5","#3f96a5"];function Jr(){return Pe(Pe({},E()),K())}const Zr=g.div`
  display: flex;
  width: 100%;
  height: 100%;
`("decoration-8");v({name:"Decoration8",props:Jr(),setup(e){const{domRef:t,domSize:r}=k();return()=>{const{color:l,reverse:d}=e,{width:s,height:p}=r,n=o=>d?s-o:o,a=D(Kr,l);return i(Zr,{ref:o=>t.value=o.$el},{default:()=>[i("svg",{width:s,height:p},[i("polyline",{stroke:a[0],"stroke-width":"2",fill:"transparent",points:`${n(0)}, 0 ${n(30)}, ${p/2}`},null),i("polyline",{stroke:a[0],"stroke-width":"2",fill:"transparent",points:`${n(20)}, 0 ${n(50)}, ${p/2} ${n(s)}, ${p/2}`},null),i("polyline",{stroke:a[1],fill:"transparent","stroke-width":"3",points:`${n(0)}, ${p-3}, ${n(200)}, ${p-3}`},null)])]})}}});g.div`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.__STYLED__ .loading-tip {
  font-size: 15px;
`("loading");export{on as J,an as R,nn as U};
