import{f as C}from"./base-kpSIrADU.js";/* empty css              */import{E as k}from"./button-IGKrEYb9.js";import{d as R,r as V,z as w,c as d,u as s,b as o,w as a,K as x,a as B,U as b,V as E,D as N,o as _,t as m,R as c}from"./index-C0QCllTd.js";import{_ as $}from"./_plugin-vue_export-helper-DlAUqK2U.js";const g={class:"Computer"},y={key:0,class:"btn-content"},M={class:"content"},z=R({__name:"Computer",setup(A){const f=b(),n=E(),t=V(n.name);w(()=>{n.name=="AreaAdjust"&&(t.value="area")});function r(u){f.push(`/home/<USER>/${u}`),t.value=u}return(u,e)=>{const l=k,i=C,v=N("RouterView");return _(),d("div",g,[s(n).meta.isShow?x("",!0):(_(),d("div",y,[o(i,{class:"item"},{default:a(()=>[o(l,{class:m({vertical:!0,active:s(t)==="route"}),onClick:e[0]||(e[0]=p=>r("route"))},{default:a(()=>e[3]||(e[3]=[c("路径计算")])),_:1},8,["class"])]),_:1}),o(i,{class:"item"},{default:a(()=>[o(l,{class:m({vertical:!0,active:s(t)==="area"}),onClick:e[1]||(e[1]=p=>r("area"))},{default:a(()=>e[4]||(e[4]=[c("聚集区计算")])),_:1},8,["class"])]),_:1}),o(i,{class:"item"},{default:a(()=>[o(l,{type:"primary",class:m({vertical:!0,active:s(t)==="pos"||s(t)==="pickup"||s(t)==="MilkRun"}),onClick:e[2]||(e[2]=p=>r("MilkRun"))},{default:a(()=>e[5]||(e[5]=[c("定点取货")])),_:1},8,["class"])]),_:1})])),B("div",M,[o(v)])])}}}),T=$(z,[["__scopeId","data-v-0166d30e"]]);export{T as default};
