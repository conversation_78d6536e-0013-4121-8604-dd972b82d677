<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.datamanagement.mapper.DeliveryMapper">
    <update id="deleteTeamByTeamId">
        update delivery_area set team_id=0 where team_id=#{teamId}
    </update>
    <update id="delete">
        update delivery_area set is_delete=1 where delivery_area_id=#{id}
    </update>
    <select id="getTeamGroupList" resultType="com.ict.datamanagement.domain.vo.teamVO.TeamVO">
        SELECT team_id,SUM(car_number) AS carSum,SUM(route_number) AS routeSum, GROUP_CONCAT(delivery_area_name) AS deliveryNameList,GROUP_CONCAT(DISTINCT transit_depot_id) AS transitDepotList FROM delivery_area GROUP BY team_id
    </select>
    <select id="queryDeliveryByTeamId" resultType="com.ict.datamanagement.domain.entity.Delivery">
        select * from delivery_area where team_id =#{teamId}
    </select>
    <select id="getType" resultType="java.lang.String">
        select delivery_name FROM delivery_area AS t1 JOIN delivery_type AS t2 ON t1.delivery_type_id =t2.delivery_type_id WHERE delivery_area_name=#{s}
    </select>
    <select id="selectByName" resultType="com.ict.datamanagement.domain.entity.Delivery">
        select * from delivery_area where delivery_area_name=#{name}
    </select>
    <select id="selectNameList" resultType="java.lang.String">
        select delivery_area_name from delivery_area where is_delete=0
    </select>
    <select id="mySelectList" resultType="com.ict.datamanagement.domain.entity.Delivery">
        select * from delivery_area where is_delete=0
    </select>
    <select id="mySelectCount" resultType="java.lang.Long">
        select COUNT(*) from delivery_area where transit_depot_id=#{oldTransitDepotId}
    </select>
    <select id="selectTeamAndTransitDepot"
            resultType="com.ict.datamanagement.domain.dto.delivery.SelectTeamAndTransitDepotRequest">
        SELECT team_name, GROUP_CONCAT(t1.transit_depot_name SEPARATOR ', ') AS transit_depots
        FROM transit_depot as t1 left JOIN team as t2 on t2.team_id=t1.group_id
        WHERE t1.is_delete = 0
        GROUP BY group_id;
    </select>
    <select id="selectCountByTransitDepotId" resultType="java.lang.Integer">
        select COUNT(*) from delivery_area where transit_depot_id=#{transitDepotId} and is_delete=0 and delivery_area_id !=#{deliveryAreaId}
    </select>
    <update id="updateTeamIdById">
        update delivery_area set team_id=#{teamId} where delivery_area_id=#{deliveryId}
    </update>
    <update id="updateTransitDepotIdById">
        update delivery_area set transit_depot_id=#{transitDepotId} where delivery_area_id=#{deliveryAreaId}
    </update>
    <update id="updatetransitDepotIdToZero">
        update delivery_area set transit_depot_id=0 where transit_depot_id=#{transitDepotId}
    </update>
    <update id="updatetransitDepotIdToZeroWhereTeamId">
        update delivery_area
        set transit_depot_id=0
        where transit_depot_id = #{transitDepotId}
          and team_id !=#{teamId}
    </update>
</mapper>