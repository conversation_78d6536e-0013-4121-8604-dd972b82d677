package com.ict.ycwl.guestbook.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ict.ycwl.guestbook.domain.FeedbackReplyFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface FeedbackReplyFileMapper extends BaseMapper<FeedbackReplyFile> {

    List<String> selectReplyFile(Long replyId);

    Long insertReplyFile(@Param("fileList") List<FeedbackReplyFile> fileList);

    int deleteReplyFileByReplyIds(@Param("replyIdList") List<Long> replyIdList);

    List<String> selectReplyFileRealPath(@Param("replyIdList") List<Long> replyIdList);
}
