package com.ict.ycwl.pathcalculate.vo;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 路线返回类
 */
@Data
public class RouteVO {

    /**
     * 路线id
     */
    Long routeId;

    /**
     * 路线名称
     */
    String routeName;

    /**
     * 路线距离
     */
    String distance;

    /**
     * 中转站id
     */
    Long transitDepotId;

    /**
     * 大区id
     */
    Long areaId;

    /**
     * 路线坐标点串
     */
    List<Map<String, Double>> polyline;

    /**
     * 凸包坐标点串
     */
    List<Map<String, Double>> convex;

}
