import org.locationtech.jts.geom.*;
import org.locationtech.jts.geom.util.GeometryFixer;
import org.locationtech.jts.operation.overlay.snap.GeometrySnapper;
import org.locationtech.jts.precision.GeometryPrecisionReducer;
import org.locationtech.jts.simplify.TopologyPreservingSimplifier;
import org.locationtech.jts.triangulate.DelaunayTriangulationBuilder;
import org.locationtech.jts.triangulate.VoronoiDiagramBuilder;
import org.locationtech.jts.triangulate.quadedge.QuadEdgeSubdivision;

import java.util.*;

public class VoronoiSplit {
    // 全局定义几何工厂和精度模型
    private static final GeometryFactory geometryFactory = new GeometryFactory();
    private static final PrecisionModel precisionModel = new PrecisionModel(PrecisionModel.FLOATING_SINGLE);
    private static final GeometryPrecisionReducer precisionReducer = new GeometryPrecisionReducer(precisionModel);
    // 修复并简化几何体
    // 修复并简化几何体，同时处理MultiPolygon类型



    public Geometry fixSimplifyReduceGeometryToG(Geometry geometry) {
        // 修复几何体
        Geometry fixedGeometry = GeometryFixer.fix(geometry);

        // 简化几何体
        Geometry simplifiedGeometry = TopologyPreservingSimplifier.simplify(fixedGeometry, 0.0001);

        // 使用 GeometrySnapper 进行 snap rounding
        Geometry snappedGeometry = GeometrySnapper.snapToSelf(simplifiedGeometry, 0.0001, true);

        // 减少精度
        return precisionReducer.reduce(snappedGeometry);
    }

    private List<Polygon> fixSimplifyReduceGeometry(Geometry geometry) {
        List<Polygon> polygons = new ArrayList<>();

        // 修复几何体
        Geometry fixedGeometry = GeometryFixer.fix(geometry);

        // 简化几何体
        Geometry simplifiedGeometry = TopologyPreservingSimplifier.simplify(fixedGeometry, 0.001);

        // 处理 GeometryCollection
        if (simplifiedGeometry instanceof GeometryCollection) {
            for (int i = 0; i < simplifiedGeometry.getNumGeometries(); i++) {
                Geometry geom = simplifiedGeometry.getGeometryN(i);
                polygons.addAll(fixSimplifyReduceGeometry(geom));
            }
        } else {
            // 减少精度
            Geometry reducedGeometry;
            try {
                reducedGeometry = precisionReducer.reduce(simplifiedGeometry);
            } catch (IllegalArgumentException e) {
                System.err.println("Reduction failed, possible invalid input: " + e.getMessage());
                return polygons;  // 返回空列表或根据具体需求处理
            }

            // 处理Polygon和MultiPolygon类型
            if (reducedGeometry instanceof Polygon) {
                polygons.add((Polygon) reducedGeometry);
            } else if (reducedGeometry instanceof MultiPolygon) {
                for (int i = 0; i < reducedGeometry.getNumGeometries(); i++) {
                    polygons.add((Polygon) reducedGeometry.getGeometryN(i));
                }
            } else if (reducedGeometry instanceof LineString) {
                // 将 LineString 转换为 Polygon
                Polygon polygon = lineStringToPolygon((LineString) reducedGeometry);
                if (polygon != null) {
                    polygons.add(polygon);
                }
            } else {
                throw new IllegalArgumentException("Unexpected geometry type: " + reducedGeometry.getGeometryType());
            }
        }

        return polygons;
    }

    private Polygon lineStringToPolygon(LineString lineString) {
        Coordinate[] coordinates = lineString.getCoordinates();
        if (coordinates.length > 3 && coordinates[0].equals2D(coordinates[coordinates.length - 1])) {
            return geometryFactory.createPolygon(coordinates);
        }
        else{

        }
        return null;  // 如果 LineString 不能形成一个闭合多边形，则返回 null
    }

    public List<Polygon> mergeOverlappingPolygons(List<Polygon> polygons) {
        List<Polygon> mergedPolygons = new ArrayList<>();
        List<Polygon> toMerge = new ArrayList<>(polygons);

        for (Polygon polygon : toMerge) {
            boolean hasMerged = false;

            // 检查当前多边形与列表中其他多边形是否重叠
            for (int i = 0; i < mergedPolygons.size(); i++) {
                Polygon existingPolygon = mergedPolygons.get(i);

                if (polygon.intersects(existingPolygon)) {
                    // 如果重叠，则合并两个多边形
                    Geometry union = polygon.union(existingPolygon);

                    // 将合并后的多边形更新到列表中
                    if (union instanceof Polygon) {
                        mergedPolygons.set(i, (Polygon) union);
                    } else if (union instanceof MultiPolygon) {
                        mergedPolygons.remove(i);
                        for (int j = 0; j < union.getNumGeometries(); j++) {
                            mergedPolygons.add((Polygon) union.getGeometryN(j));
                        }
                    }

                    hasMerged = true;
                    break;
                }
            }

            if (!hasMerged) {
                mergedPolygons.add(polygon);
            }
        }

        return mergedPolygons;
    }

    // 去除polygon1中与polygon2重叠的点
    public Polygon removePointsInsidePolygon(Polygon polygon1, Polygon polygon2) {
        Coordinate[] coordinates1 = polygon1.getCoordinates();
        List<Coordinate> newCoords = new ArrayList<>();

        // 遍历 polygon1 的所有点
        for (Coordinate coord : coordinates1) {
            // 如果该点不在 polygon2 内，则添加到新的坐标列表中
            if (!polygon2.contains(geometryFactory.createPoint(coord))) {
                newCoords.add(coord);
            }
        }

        // 确保新的坐标列表形成一个闭合的多边形
        if (!newCoords.isEmpty() && !newCoords.get(0).equals2D(newCoords.get(newCoords.size() - 1))) {
            newCoords.add(newCoords.get(0));
        }

        // 构建新的多边形
        return geometryFactory.createPolygon(newCoords.toArray(new Coordinate[0]));
    }

    // 生成三角剖分模型
    public static Geometry buildTriangulatedNetwork(List<Polygon> polygons) {
        List<Coordinate> coordinates = new ArrayList<>();

        // 提取所有多边形的顶点
        for (Polygon polygon : polygons) {
            for (Coordinate coord : polygon.getCoordinates()) {
                coordinates.add(coord);
            }
        }

        // 生成Delaunay三角剖分
        DelaunayTriangulationBuilder builder = new DelaunayTriangulationBuilder();
        builder.setSites(coordinates);
        QuadEdgeSubdivision subdivision = builder.getSubdivision();

        return subdivision.getTriangles(geometryFactory);
    }


    public List<Polygon> splitPolygonsWithVoronoi(List<Polygon> polygons) {
        GeometryFactory geometryFactory = new GeometryFactory();

        // 创建多边形列表

        /*polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.560588, 25.088858),
                new Coordinate(113.560588, 25.088858),
                new Coordinate(113.624317, 24.965148),
                new Coordinate(113.644577, 25.052965),
                new Coordinate(113.6549, 25.109814),
                new Coordinate(113.638226, 25.1129),
                new Coordinate(113.627673, 25.111031),
                new Coordinate(113.560588, 25.088858)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.658321, 25.080264),
                new Coordinate(113.706815, 25.066009),
                new Coordinate(113.717595, 25.088101),
                new Coordinate(113.68599, 25.091865),
                new Coordinate(113.658321, 25.080264)
        }));*/

        // 获取多边形的所有顶点
        List<Coordinate> vertices = new ArrayList<>();
        Map<Coordinate, Polygon> vertexToPolygonMap = new HashMap<>();
        for (Polygon polygon : polygons) {
            if(polygon == null) continue;
            //System.out.println(polygon);
            Coordinate[] coords = polygon.getCoordinates();
            for (Coordinate coord : coords) {
                vertices.add(coord);
                vertexToPolygonMap.put(coord, polygon);
            }
        }
        //System.out.println("\n\n\n\n");
        // 计算Voronoi图
        VoronoiDiagramBuilder voronoiBuilder = new VoronoiDiagramBuilder();
        voronoiBuilder.setSites(vertices);
        voronoiBuilder.setClipEnvelope(new Envelope(113.0, 115.0, 23.0, 26.0));
        Geometry voronoiDiagram = voronoiBuilder.getDiagram(geometryFactory);
        System.out.println("\n\n获得全体泰森多边形");
        System.out.println(voronoiDiagram);

        // 创建用于存储每个多边形的Voronoi单元的Map
        Map<Polygon, List<Polygon>> polygonVoronoiCells = new HashMap<>();

        // 初始化Map
        for (Polygon polygon : polygons) {
            polygonVoronoiCells.put(polygon, new ArrayList<>());
        }

        // 分配Voronoi单元到对应的多边形
        for (int i = 0; i < vertices.size(); i++) {
            Coordinate vertex = vertices.get(i);
            Polygon voronoiCell = getCellContainingPoint(voronoiDiagram, vertex);
            if (voronoiCell != null) {
                Polygon originalPolygon = vertexToPolygonMap.get(vertex);
                polygonVoronoiCells.get(originalPolygon).add(voronoiCell);
            }
        }

        System.out.println("\n\n\n\nStep1: 将所有点的泰森多边形找到");
        for (Polygon polygon : polygons) {
            if (polygon == null) continue;
            List<Polygon> cells = polygonVoronoiCells.get(polygon);
            for(Polygon p: cells) System.out.println(p);
        }

        Geometry triangular = buildTriangulatedNetwork(voronoiDiagram, polygons);
        System.out.println("\n\n获取泰森多边形的三角剖分网格");
        System.out.println(triangular);


        // 创建用于将Voronoi单元映射到其原属的多边形的Map, 以及反向映射
        Map<Polygon, Polygon> VoronoiToPolygon = new HashMap<>();
        Map<Polygon, Polygon> PolygonToVoronoi = new HashMap<>();

        // 合并Voronoi单元并处理重叠
        List<Polygon> mergedVoronoiCells = new ArrayList<>();
        for (Polygon polygon : polygons) {
            if(polygon == null) continue;
            List<Polygon> cells = polygonVoronoiCells.get(polygon);
            GeometryCollection geometryCollection = new GeometryCollection(cells.toArray(new Geometry[0]), geometryFactory);
            Geometry union = geometryCollection.union();
            if (union instanceof Polygon) {
                mergedVoronoiCells.add((Polygon) union);
            } else if (union instanceof MultiPolygon) {
                // 处理MultiPolygon情况
                for (int j = 0; j < union.getNumGeometries(); j++) {
                    mergedVoronoiCells.add((Polygon) union.getGeometryN(j));
                }
            }
            if (union instanceof Polygon) {
                VoronoiToPolygon.put((Polygon) union, polygon);
                PolygonToVoronoi.put(polygon, (Polygon) union);
            }
            //System.out.println(union);
        }

        System.out.println("\n\n\n\nStep2:获得合并泰森多边形");
        for(Polygon p: mergedVoronoiCells) System.out.println(p);

        // 创建新的列表来存储更新后的Voronoi单元
        List<Polygon> newMergedVoronoiCells = new ArrayList<>(mergedVoronoiCells);
        List<Polygon> toRemove = new ArrayList<>();
        List<Polygon> toAdd = new ArrayList<>();

        // 检查并处理合并后的Voronoi单元与其他多边形的重叠
        for(Polygon polygon : polygons)
        {
            if(polygon == null) continue;
            // 获得当前遍历到的polygon的Voronoi单元
            Polygon cell2 = PolygonToVoronoi.get(polygon);
            if(cell2 == null) continue;
            for (int i = 0; i < mergedVoronoiCells.size(); i++) {
                // 获取当前重叠的Voronoi单元
                Polygon cell1 = mergedVoronoiCells.get(i);
                if(cell1 == null) continue;
                if (cell1 == cell2) continue;
                // 如果当前Voronoi单元与当前原始多边形相交
                if (polygon.intersects(cell1)) {
                    // 计算交集
                    Geometry intersection = polygon.intersection(cell1);

                    Geometry AllCell = cell1.union(cell2);
                    // 如果交集非空
                    if (!intersection.isEmpty()) {
                        if(cell2 == null) continue;
                        Geometry cell1Complement = cell1;
                        List<Polygon> cell_section = fixSimplifyReduceGeometry(intersection);
                        for(Polygon p : cell_section) {
                            cell1Complement = cell1Complement.difference(p);
                        }
                        /*cell1Complement = GeometryFixer.fix(cell1Complement);
                        cell1Complement = TopologyPreservingSimplifier.simplify(cell1Complement, 0.001);
                        GeometryPrecisionReducer precisionReducer = new GeometryPrecisionReducer(new PrecisionModel(PrecisionModel.FLOATING_SINGLE));*/
                        //Polygon finalCell1Complement = (Polygon) precisionReducer.reduce(cell1Complement);

                        cell1Complement = cell1Complement.difference(polygon);

                        List<Polygon> cell1Polygons = fixSimplifyReduceGeometry(cell1Complement);
                        List<Polygon> newcellPolygons = new ArrayList<>();
                        /*for(Polygon p : cell1Polygons){
                            Polygon new_p = removePointsInsidePolygon(p, polygon);
                            newcellPolygons.add(new_p);
                        }*/

                        // 对cell2额外添加polygon对它的补集
                        Geometry cell2Union = cell2;
                        AllCell = GeometryFixer.fix(AllCell);
                        AllCell = fixSimplifyReduceGeometryToG(AllCell);
                        Geometry Newcell1Complement = cell1Complement.intersection(AllCell);
                        if(Newcell1Complement instanceof GeometryCollection){
                            GeometryCollection gc = (GeometryCollection) Newcell1Complement;
                            for (int j = 0; j < gc.getNumGeometries(); j++) {
                                Geometry g = gc.getGeometryN(j);
                                cell2Union = AllCell.difference(g);
                            }
                        }
                        else {
                            Newcell1Complement = fixGeometry(Newcell1Complement);
                            cell2Union = AllCell.difference(Newcell1Complement);
                        }
                        /*for(Polygon p : cell_section){
                            cell2Union = cell2Union.union(p);
                        }
*/
                        List<Polygon> cell2Polygons = fixSimplifyReduceGeometry(cell2Union);

                        // 临时存储需要删除的多边形
                        toRemove.add(cell1);
                        toRemove.add(cell2);

                        cell1Polygons = mergeOverlappingPolygons(cell1Polygons);
                        cell2Polygons = mergeOverlappingPolygons(cell2Polygons);


                        System.out.println("\n\n\n\nStep3."+ (i + 1) + "获取补集修正后的泰森多边形");
                        for(Polygon p : cell1Polygons) System.out.println(p);
                        System.out.println(" ");
                        for(Polygon p : cell2Polygons) System.out.println(p);

                        // 临时存储需要添加的多边形
                        toAdd.addAll(cell1Polygons);
                        toAdd.addAll(cell2Polygons);

                        // 更新PolygonToVoronoi映射
                        // 将当前遍历到的Polygon映射给新的cell2
                        for (Polygon cell : cell2Polygons) {
                            PolygonToVoronoi.put(polygon, cell);
                        }

                        // 获取当前重叠cell1对应的另一个Otherpolygon
                        Polygon Otherpolygon = VoronoiToPolygon.get(cell1);
                        // 将另一个Otherpolygon映射给新的cell1
                        for (Polygon cell : cell1Polygons) {
                            PolygonToVoronoi.put(Otherpolygon, cell);
                        }
                    }
                }
            }
            // 删除需要删除的多边形
            newMergedVoronoiCells.removeAll(toRemove);
            // 添加需要添加的多边形
            newMergedVoronoiCells.addAll(toAdd);
            // 将更新后的单元列表赋值回原始列表
            mergedVoronoiCells = new ArrayList<>(newMergedVoronoiCells);
            System.out.println("\n\n\n\n");
            System.out.println("Step3.out 更新当前一轮修正结果\n");
            for(Polygon p : mergedVoronoiCells){
                System.out.println(p);
            }
            // 清空数组
            toAdd = new ArrayList<>();
            toRemove = new ArrayList<>();
        }





        // 打印合并后的Voronoi单元
        System.out.println("\n\n\n\nStep5: 获得修正泰森多边形");
        for (Polygon cell : mergedVoronoiCells) {
            System.out.println(cell);
        }

        return mergedVoronoiCells;
    }

    public static Geometry buildVoronoi(List<Polygon> polygons){
        GeometryFactory geometryFactory = new GeometryFactory();

        // 获取多边形的所有顶点
        List<Coordinate> vertices = new ArrayList<>();
        for (Polygon polygon : polygons) {
            if(polygon == null) continue;
            Coordinate[] coords = polygon.getCoordinates();
            for (Coordinate coord : coords) {
                vertices.add(coord);
            }
        }
        // 计算Voronoi图
        VoronoiDiagramBuilder voronoiBuilder = new VoronoiDiagramBuilder();
        voronoiBuilder.setSites(vertices);
        voronoiBuilder.setClipEnvelope(new Envelope(112.8, 115.0, 23.8, 26.0));
        Geometry voronoiDiagram = voronoiBuilder.getDiagram(geometryFactory);
        return  voronoiDiagram;
    }
    
    public static Geometry buildTriangulatedNetwork(Geometry voronoi, List<Polygon> polygons) {
        List<Coordinate> coordinates = new ArrayList<>();

        // 提取所有多边形的顶点
        for (int i = 0; i < voronoi.getNumGeometries(); i++) {
            Polygon polygon = (Polygon) voronoi.getGeometryN(i);
            for (Coordinate coord : polygon.getCoordinates()) {
                coordinates.add(coord);
            }
        }
        for (Polygon polygon : polygons) {
            for (Coordinate coord : polygon.getCoordinates()) {
                coordinates.add(coord);
            }
        }

        VoronoiDiagramBuilder voronoiBuilder = new VoronoiDiagramBuilder();
        voronoiBuilder.setSites(coordinates);
        voronoiBuilder.setClipEnvelope(new Envelope(112.8, 115.0, 23.8, 26.0));
        Geometry triangleNetwork = voronoiBuilder.getDiagram(geometryFactory);

        for (int i = 0; i < triangleNetwork.getNumGeometries(); i++) {
            Polygon polygon = (Polygon) triangleNetwork.getGeometryN(i);
            for (Coordinate coord : polygon.getCoordinates()) {
                coordinates.add(coord);
            }
        }

        // 生成Delaunay三角剖分
        /*DelaunayTriangulationBuilder builder = new DelaunayTriangulationBuilder();
        builder.setSites(coordinates);
        QuadEdgeSubdivision subdivision = builder.getSubdivision();
        triangleNetwork = subdivision.getTriangles(geometryFactory);

*/
        HashMap<Geometry, Geometry> hacker = new HashMap<>();
        HashMap<Geometry, Geometry> repair = new HashMap<>();
        for(int i = 0; i < triangleNetwork.getNumGeometries(); i++){
            Geometry triangle = triangleNetwork.getGeometryN(i);
            hacker.put(triangle, null);
            repair.put(triangle, null);
        }
        System.out.println("\n\n\nHACKER:\n");
        for(int i = 0; i < triangleNetwork.getNumGeometries(); i++){
            Geometry triangle = triangleNetwork.getGeometryN(i);
            for(Geometry p : polygons){
                if(triangle.intersection(p) instanceof Polygon && !triangle.intersection(p).isEmpty()){
                    if(hacker.get(triangle) != null){
                        System.out.println(triangle);
                        Geometry inter1 = triangle.intersection(p);
                        Geometry inter2 = triangle.intersection(hacker.get(triangle));
                        HashSet<Coordinate> set = new HashSet<>();
                        set.addAll(List.of(inter1.getCoordinates()));
                        set.addAll(List.of(inter2.getCoordinates()));
                        set.addAll(List.of(triangle.getCoordinates()));
                        List<Coordinate> coordinateList = new ArrayList<>(set);
                        DelaunayTriangulationBuilder Triangular_builder = new DelaunayTriangulationBuilder();
                        Triangular_builder.setSites(coordinateList);
                        QuadEdgeSubdivision Triangular_subdivision = Triangular_builder.getSubdivision();
                        Geometry triangle_repair = Triangular_subdivision.getTriangles(geometryFactory);
                        repair.put(triangle, triangle_repair);
                    }
                    hacker.put(triangle, p);
                }
            }
        }
        List<Geometry> new_triangleNetwork = new ArrayList<>();
        for(int i = 0; i < triangleNetwork.getNumGeometries(); i++) {
            Geometry triangle = triangleNetwork.getGeometryN(i);
            if (repair.get(triangle) != null) {
                Geometry triangle_repair = repair.get(triangle);
                for (int j = 0; j < triangle_repair.getNumGeometries(); j++) {
                    new_triangleNetwork.add(triangle_repair.getGeometryN(j));
                }
            } else {
                new_triangleNetwork.add(triangle);
            }
        }

        HashSet<Geometry> set = new HashSet<>(new_triangleNetwork);
        new_triangleNetwork.clear();
        new_triangleNetwork.addAll(set);

        triangleNetwork = geometryFactory.createGeometryCollection(new_triangleNetwork.toArray(new Geometry[0]));
        return triangleNetwork;
    }

    // 泰森多边形暨三角剖分网格
    public static Geometry VT_gird(List<Polygon> polygons){
        Geometry voronoiDiagram = buildVoronoi(polygons);
        Geometry vt_network = buildTriangulatedNetwork(voronoiDiagram, polygons);
        //System.out.println("\n\n\n\n --------------------------------------------------------------------" + vt_network);
        return vt_network;
    }


    // 获取包含给定点的Voronoi单元
    private static Polygon getCellContainingPoint(Geometry voronoiDiagram, Coordinate point) {
        for (int i = 0; i < voronoiDiagram.getNumGeometries(); i++) {
            Polygon cell = (Polygon) voronoiDiagram.getGeometryN(i);
            if (cell.contains(new GeometryFactory().createPoint(point))) {
                return cell;
            }
        }
        return null;
    }

    // 修复几何对象，如果是MultiPolygon则转换为Polygon
    private Polygon fixGeometry(Geometry geometry) {
        if (geometry instanceof Polygon) {
            return (Polygon) geometry;
        } else if (geometry instanceof MultiPolygon) {
            // 合并MultiPolygon为一个Polygon
            return (Polygon) geometry.union();
        }
        throw new IllegalArgumentException("Unexpected geometry type: " + geometry.getGeometryType());
    }

    public static void main(String[] args) {
        List<Polygon> polygons = new ArrayList<>();
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.727166, 25.304861),
                new Coordinate(113.733395, 25.25601),
                new Coordinate(113.865906, 25.201329),
                new Coordinate(113.937188, 25.303314),
                new Coordinate(113.955875, 25.330793),
                new Coordinate(113.836659, 25.831624),
                new Coordinate(113.727166, 25.304861)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.793132, 25.152474),
                new Coordinate(113.838499, 25.139183),
                new Coordinate(113.943643, 25.133792),
                new Coordinate(113.799577, 25.162533),
                new Coordinate(113.793132, 25.152474)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.774136, 24.915802),
                new Coordinate(113.900526, 24.960427),
                new Coordinate(113.814666, 24.976133),
                new Coordinate(113.774136, 24.915802)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.744233, 25.042364),
                new Coordinate(113.745712, 25.03193),
                new Coordinate(113.780645, 25.027158),
                new Coordinate(113.830642, 25.056242),
                new Coordinate(113.830316, 25.064812),
                new Coordinate(113.825693, 25.071896),
                new Coordinate(113.757961, 25.059644),
                new Coordinate(113.744233, 25.042364)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.658321, 25.080264),
                new Coordinate(113.706815, 25.066009),
                new Coordinate(113.717595, 25.088101),
                new Coordinate(113.68599, 25.091865),
                new Coordinate(113.658321, 25.080264)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.726711, 25.088577),
                new Coordinate(113.726711, 25.088577),
                new Coordinate(113.739468, 25.081346),
                new Coordinate(113.744636, 25.083087),
                new Coordinate(113.745773, 25.086166),
                new Coordinate(113.745773, 25.086166),
                new Coordinate(113.745322, 25.089879),
                new Coordinate(113.733613, 25.089755),
                new Coordinate(113.726711, 25.088577)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.746279, 25.088949),
                new Coordinate(113.754657, 25.074007),
                new Coordinate(113.765271, 25.101996),
                new Coordinate(113.746753, 25.094568),
                new Coordinate(113.746279, 25.088949)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.560588, 25.088858),
                new Coordinate(113.560588, 25.088858),
                new Coordinate(113.624317, 24.965148),
                new Coordinate(113.644577, 25.052965),
                new Coordinate(113.6549, 25.109814),
                new Coordinate(113.638226, 25.1129),
                new Coordinate(113.627673, 25.111031),
                new Coordinate(113.560588, 25.088858)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.438228, 25.222742),
                new Coordinate(113.599192, 25.254163),
                new Coordinate(113.598679, 25.258883),
                new Coordinate(113.438228, 25.222742)
        }));
        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(114.097266, 24.010266),
                new Coordinate(114.097266, 24.010266),
                new Coordinate(114.117483, 23.961694),
                new Coordinate(114.14478, 23.974555),
                new Coordinate(114.17876, 24.028903),
                new Coordinate(114.16795, 24.044339),
                new Coordinate(114.133139, 24.05045),
                new Coordinate(114.097266, 24.010266)
        }));

       /* polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.726711, 25.088577),
                new Coordinate(113.726711, 25.088577),
                new Coordinate(113.739468, 25.081346),
                new Coordinate(113.744636, 25.083087),
                new Coordinate(113.745773, 25.086166),
                new Coordinate(113.745773, 25.086166),
                new Coordinate(113.745322, 25.089879),
                new Coordinate(113.733613, 25.089755),
                new Coordinate(113.726711, 25.088577)
        }));

        polygons.add(geometryFactory.createPolygon(new Coordinate[]{
                new Coordinate(113.746279, 25.088949),
                new Coordinate(113.754657, 25.074007),
                new Coordinate(113.765271, 25.101996),
                new Coordinate(113.746753, 25.094568),
                new Coordinate(113.746279, 25.088949)
        }));*/

        VoronoiSplit split = new VoronoiSplit();
        System.out.println("\n\n\n\nStep0: 获得所有凸包");
        for(Polygon p : polygons) System.out.println(p);
        Geometry network = split.VT_gird(polygons);
        System.out.println("\n\n\n\nStep0x7fffff: 输出网格图");
        System.out.println(network);
        /*List<Polygon> splits = split.splitPolygonsWithVoronoi(polygons);
        System.out.println("\n\n\n\nStep6: 输出正式结果");
        for(Polygon p : splits) System.out.println(p);*/
    }
}
