import{S as x,M as b}from"./getMapKey-C0z490Cj.js";import{m as D}from"./modifyUserAgent-Ct74EQNt.js";import{A as I}from"./index-Bp4b1Vvq.js";import{a as B}from"./index-m25zEilF.js";import{d as L,r as z,m as $,z as P,aa as N,c as M,a as o,N as W,T,K as U,o as v,S as F}from"./index-C0QCllTd.js";import{_ as K}from"./_plugin-vue_export-helper-DlAUqK2U.js";const O={class:"map-container"},R={class:"legend-box"},V=["src"],Y={style:{color:"black"}},j={key:0,class:"map-loading-mask"},q=L({__name:"pickMap",props:{list:{type:Array,required:!0,validator:l=>Array.isArray(l)},mapType:{type:Number,default:1},loading:{type:Boolean,default:!1}},setup(l,{expose:_}){window._AMapSecurityConfig={securityJsCode:x},D();const w=z();let n=null;_({getMap:C});let y=[],i=null,p=[];const A=l,d=z(null),k=()=>{y.forEach(e=>{n.remove(e),e.off("mouseover  click mouseout")}),y=[],A.list.forEach(e=>{if(e.type==="A"||typeof e.type>"u")return;const c=new AMap.Icon({image:u[e.type],size:new AMap.Size(12,12),imageSize:new AMap.Size(12,12)}),a=new AMap.Marker({position:e.lnglat,icon:c,map:n,extData:u[e.type]});a.on("mouseover",t=>{if(e.type==="E"||typeof e.type>"u"){i.setContent(`
      <div class="info-card">
        <h3>${e.info.name}</h3>
      </div>
      `),i.open(n,t.target.getPosition());return}i.setContent(`
      <div class="info-card">
        <h3>${e.info.name}</h3>
        ${e.info.address?`<p>最佳取货地：${e.info.address}</p>
        <p>距离：${e.info.distance}公里</p>`:`<p>关联商户: ${e.info.stores} </p>`}

      </div>
      `),i.open(n,t.target.getPosition())}),a.on("click",t=>{var g;if(e.type==="E"||typeof e.type>"u")return;let r=new AMap.Icon({image:u[e.type],size:new AMap.Size(12,12),imageSize:new AMap.Size(12,12)});d.value&&(r=new AMap.Icon({image:d.value.getExtData(),size:new AMap.Size(12,12),imageSize:new AMap.Size(12,12)}),d.value.setIcon(r),p.forEach(s=>{s&&s.clear()}),p=[]);const f=new AMap.Icon({image:u[e.type].replace("-1","-2"),size:new AMap.Size(12,12),imageSize:new AMap.Size(12,12)});if(d.value=t.target,t.target.setIcon(f),e.info&&e.info.dian){const s=new AMap.Driving({policy:0,map:n,hideMarkers:!0}),h=[e.lnglat[0],e.lnglat[1]],m=e.info.dian[0];s.search(h,m),p.push(s)}if(e.info.posList&&((g=e.info.posList)==null?void 0:g.length)>0){const s=[e.lnglat[0],e.lnglat[1]];e.info.posList.forEach(h=>{const m=new AMap.Driving({policy:0,map:n,hideMarkers:!0}),E=h;m.search(s,E),p.push(m)})}}),a.on("mouseout",()=>i.close()),y.push(a)})};$(()=>A.list,()=>{n&&k()},{deep:!0});const u={A:"/1-1.png",B:"/2-1.png",C:"/3-1.png",D:"/4-1.png",E:"/jinyong.png",F:"/8-1.png"},S={取货地:"/6-1.png",商户:"/5-1.png"};P(()=>{I.load({key:b,version:"2.0",plugins:["AMap.DistrictSearch","AMap.MarkerCluster","AMap.Driving"]}).then(async e=>{new e.DistrictSearch({subdistrict:1,extensions:"all",level:"province"}).search("韶关市",(a,t)=>{const r=t.districtList[0].boundaries,f=[];for(let s=0;s<r.length;s++)f.push([r[s]]);n=new e.Map(w.value,{mask:f,viewMode:"3D",zoom:9,center:[113.58,24.81],expandZoomRange:!0,zooms:[9,18],zoomEnable:!0,resizeEnable:!0});const g=new e.Polygon({path:r,strokeColor:"#788fbe",strokeWeight:6,fillOpacity:.1,strokeStyle:"solid"});n.add(g),i=new e.InfoWindow({isCustom:!0,closeWhenClickMap:!0,offset:new e.Pixel(15,-40)}),k()})}).catch(e=>{B.error("地图加载失败"),console.error(" 地图加载异常:",e)})});function C(){return n}return N(()=>{n&&(n.destroy(),n=null)}),(e,c)=>(v(),M("div",O,[o("div",{ref_key:"mapContainer",ref:w,class:"map-box"},null,512),o("div",R,[(v(),M(W,null,T(S,(a,t)=>o("div",{key:t,class:"legend-item"},[o("img",{src:a,style:{width:"22px",height:"22px"}},null,8,V),o("span",Y,F(t),1)])),64))]),l.loading?(v(),M("div",j,c[0]||(c[0]=[o("div",{class:"loading-spinner"},null,-1),o("div",{class:"loading-text"},"地图加载中...",-1)]))):U("",!0)]))}}),ee=K(q,[["__scopeId","data-v-bb6633d5"]]);export{ee as p};
