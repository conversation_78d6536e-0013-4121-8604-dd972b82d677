import{f}from"./base-kpSIrADU.js";/* empty css              */import{E as v}from"./button-IGKrEYb9.js";import{U as C}from"./index-DUXS04g8.js";import{d as g,r as h,c as k,a as l,b as t,w as s,u as r,U as B,V as b,D as w,o as x,t as m,R as u}from"./index-C0QCllTd.js";import{_ as y}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./merge-B3fxVp56.js";import"./_initCloneObject-BmTtMqsv.js";import"./_commonjsHelpers-BbMlrU8H.js";const E={class:"Board"},V={class:"board-right"},N={class:"btn-content"},R=g({__name:"Car",setup($){const c=B();b();const o=h(1);function n(a){c.push({path:`/home/<USER>/car/${a}`}),o.value==2?o.value=1:o.value=2}return(a,e)=>{const p=w("router-view"),i=v,d=f;return x(),k("div",E,[l("div",V,[t(r(C),{color:["#90ade8","#90ade8"]},{default:s(()=>[t(p)]),_:1})]),l("div",N,[t(d,{class:"item",hidden:!a.logisticsMount},{default:s(()=>[t(i,{type:"primary",class:m({vertical:!0,active:r(o)===1}),onClick:e[0]||(e[0]=_=>n("message"))},{default:s(()=>e[2]||(e[2]=[u("基本信息")])),_:1},8,["class"])]),_:1},8,["hidden"]),t(d,{class:"item",hidden:!a.marketingMount},{default:s(()=>[t(i,{type:"primary",class:m({vertical:!0,active:r(o)===2}),onClick:e[1]||(e[1]=_=>n("work"))},{default:s(()=>e[3]||(e[3]=[u("工作实情")])),_:1},8,["class"])]),_:1},8,["hidden"])])])}}}),F=y(R,[["__scopeId","data-v-d36ff8f4"]]);export{F as default};
