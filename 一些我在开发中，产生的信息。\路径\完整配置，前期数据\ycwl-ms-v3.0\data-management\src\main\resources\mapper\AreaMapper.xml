<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.datamanagement.mapper.AreaMapper">

    <resultMap id="BaseResultMap" type="com.ict.datamanagement.domain.entity.Area">
            <id property="area_id" column="area_id" jdbcType="BIGINT"/>
            <result property="area_name" column="area_name" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        area_id,area_name
    </sql>
</mapper>
