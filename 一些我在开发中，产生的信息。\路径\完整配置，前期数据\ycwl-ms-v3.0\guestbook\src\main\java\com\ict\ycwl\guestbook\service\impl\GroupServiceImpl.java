package com.ict.ycwl.guestbook.service.impl;

import com.ict.ycwl.guestbook.api.vo.GroupVo;
import com.ict.ycwl.guestbook.mapper.GroupMapper;
import com.ict.ycwl.guestbook.service.GroupService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;



@Service
public class GroupServiceImpl implements GroupService {

    @Autowired
    private GroupMapper groupMapper;

    @Override
    public GroupVo getGroupAndUser(Long areaId) {
        return groupMapper.selectGroupByAreaId(areaId);
    }

}
