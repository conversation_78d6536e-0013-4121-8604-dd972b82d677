import"./base-kpSIrADU.js";/* empty css                */import{E as X}from"./scrollbar-BNeK4Yi-.js";import{E as q,a as G}from"./collapse-item-BW_6Kq-g.js";import{E as H}from"./button-IGKrEYb9.js";import"./input-DqmydyK4.js";import{E as Q,a as ee}from"./select-BOcQ2ynX.js";import{S as oe,M as te}from"./getMapKey-C0z490Cj.js";import{u as ne}from"./cluster-BxttejUl.js";import{d as le,r as _,m as D,P as se,z as ae,A as ie,c as E,a as I,b as d,w as c,X as $,u as F,U as re,o as k,R as x,p as V,N as A,T as R,t as ce,S as ue}from"./index-C0QCllTd.js";import{U as de}from"./index-DUXS04g8.js";import{m as me}from"./modifyUserAgent-Ct74EQNt.js";import{A as fe}from"./index-Bp4b1Vvq.js";import{m as U}from"./mapBluePoint-B1PT_daA.js";import{a as z}from"./index-m25zEilF.js";import{E as pe}from"./el-overlay-CJCFAIWq.js";import{E as O}from"./index-DJDAALh9.js";import{v as ge}from"./directive-BBeDU6Ak.js";import{_ as ve}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./castArray-CSO3s-vM.js";import"./_initCloneObject-BmTtMqsv.js";import"./_commonjsHelpers-BbMlrU8H.js";import"./merge-B3fxVp56.js";import"./index-DOdSMika.js";const _e={class:"area"},ke={class:"map"},we={class:"btn-box"},Ce={id:"container","element-loading-text":"地图数据加载中...","element-loading-background":"rgba(0,23,49,0.8)"},be={class:"region"},he=["id","onClick"],Ee=le({__name:"Area",setup(Ie){me(),window._AMapSecurityConfig={securityJsCode:oe};const y=_("0"),N=re();function W(){N.replace("/home/<USER>/AreaAdjust")}const l=ne();l.getInformationListAction(),l.getCheckErrorPointsAction();const L=_(!1);function M(){if(l.MapResultPoints){const e=i.getAllOverlays("marker");i.remove(e),L.value=!0,B()}}D(y,e=>{if(console.log(e),l.MapResultPoints){const t=i.getAllOverlays("marker");i.remove(t),L.value=!0,B()}});function j(){if(l.ErrorPoints==null)z.warning("数据处理中请稍后再试。");else{const e=O.service({lock:!0,text:"保存中...",background:"rgba(0, 0, 0, 0.7)"});l.ErrorPoints===0?(l.deleteClearInformationListAction().then(()=>{l.getInformationListAction()}),e.close(),z.success("保存成功")):pe({title:"注意!",message:"还有未保存的聚集区错误点，是否忽略并继续保存?",showCancelButton:!0}).then(t=>{console.log(t),t=="confirm"&&(l.deleteClearInformationListAction().then(()=>{l.getInformationListAction()}),e.close(),z.success("保存成功"))}).catch(()=>{e.close()})}}const Y=_(),T=_(!0);l.getClusterDepotAllAction().then(()=>{T.value=!1});const Z=()=>{const e=O.service({lock:!0,text:"计算中...",background:"rgba(0, 0, 0, 0.7)"});l.postCalculateAllAction().then(()=>{e.close()})},P=_([]),h=se({}),f=_(),p=_();function J(e,t){const m=l.clusterDepotAll;for(const C in m){const r=m[C];for(const g in r){const w=r[g];for(const o of w)if(o.longitude===e&&o.latitude===t)return{district:C,town:g,shop:o}}}return null}function K(e){f.value!=e[0]&&p.value!=e[1]&&(f.value=e[0],p.value=e[1]),l.MapResultPoints.findIndex(m=>{if(m.lnglat[0]==e[0]&&m.lnglat[1]==e[1])return!0})!=-1?i.setZoomAndCenter(12,e):z.error("该点不在地图范围内")}const S=_(!0);let i=null;ae(()=>{B()});function B(){fe.load({key:te,version:"2.0",plugins:["AMap.DistrictSearch"]}).then(e=>{new e.DistrictSearch({subdistrict:1,extensions:"all",level:"province"}).search("韶关市",function(m,C){const r=C.districtList[0].boundaries,g=[];for(let o=0;o<r.length;o++)g.push([r[o]]);i=new e.Map("container",{mask:g,zoom:9,expandZoomRange:!0,zooms:[9,18],center:[113.767587,24.718014],viewMode:"3D",zoomEnable:!0,resizeEnable:!0});for(let o=0;o<r.length;o++)new e.Polyline({path:r[o],strokeColor:"#3078AC",strokeWeight:2}).setMap(i);l.MapResultPoints&&!L.value?w():(S.value=!0,l.getMapResultPointsAction().then(()=>{S.value=!1,w()}));function w(){S.value=!1,l.MapResultPoints.forEach(o=>{const b=new e.Icon({size:new e.Size(25,25),image:U.orange,imageSize:new e.Size(25,25)}),v=new e.Icon({size:new e.Size(30,30),image:U.blue,imageSize:new e.Size(30,30)});if(o.state=="center"){if(y.value=="1")return;const a=new e.Marker({position:new e.LngLat(o.lnglat[0],o.lnglat[1]),offset:new e.Pixel(-7,-17),icon:b,title:"中心点",zooms:[9,18]});i.add(a),a.on("click",function(){f.value=o.lnglat[0],p.value=o.lnglat[1];const n=J(o.lnglat[0],o.lnglat[1]);n&&(P.value=[n.district],h[n.district]||(h[n.district]=[]),h[n.district]=[n.town],setTimeout(()=>{const u=document.getElementById("shop-"+n.shop.accumulation);u&&u.scrollIntoView({behavior:"smooth",block:"center"})},300))}),D([f,p],n=>{n[0]==o.lnglat[0]&&n[1]==o.lnglat[1]?a.setIcon(v):a.setIcon(b)})}else if(o.state=="error"){const a=new e.Icon({size:new e.Size(30,30),image:U.red,imageSize:new e.Size(30,30)}),n=new e.Marker({position:new e.LngLat(o.lnglat[0],o.lnglat[1]),offset:new e.Pixel(-7,-17),zIndex:100,icon:a,title:"特殊点",zooms:[9,18]});n.on("click",function(){var s;Y.value=o.accumulation,(s=document.getElementById(o.accumulation))==null||s.scrollIntoView(),f.value!=o.lnglat[0]&&p.value!=o.lnglat[1]&&(f.value=o.lnglat[0],p.value=o.lnglat[1])});const u=new e.InfoWindow({isCustom:!0,closeWhenClickMap:!0,content:`<div style="padding:8px;color:black">
                        <b style="color:#FFD700;">${o.name}</b><br/>
                        备注: ${o.remark}<br/>
                        类型: ${o.specialType}<br/>
                        </div>`,offset:new e.Pixel(0,-30)});u.setContent(`
      <div class="info-card">
        <h3>${o.name}</h3>
        <p>备注：${o.remark}</p>
        <p>类型：${o.specialType}</p>
      </div>
      `),n.on("mouseover",function(s){u.open(i,n.getPosition())}),n.on("mouseout",function(s){u.close()}),D([f,p],s=>{s[0]==o.lnglat[0]&&s[1]==o.lnglat[1]?n.setIcon(v):n.setIcon(a)}),i.add(n)}})}})}).catch(e=>{console.log(e)})}return ie(()=>{i.destroy(),i=null,document.getElementById("container").remove()}),(e,t)=>{const m=ee,C=Q,r=H,g=q,w=G,o=X,b=ge;return k(),E("div",_e,[I("div",ke,[I("div",we,[t[6]||(t[6]=I("p",null,"显示:",-1)),d(C,{class:"select",modelValue:y.value,"onUpdate:modelValue":t[0]||(t[0]=v=>y.value=v)},{default:c(()=>[d(m,{label:"全部",value:"0"}),d(m,{label:"仅特殊点",value:"1"})]),_:1},8,["modelValue"]),d(r,{onClick:Z},{default:c(()=>t[2]||(t[2]=[x("重新计算")])),_:1}),d(r,{class:"adjust",onClick:W},{default:c(()=>t[3]||(t[3]=[x("聚集区微调")])),_:1}),d(r,{class:"save",onClick:j},{default:c(()=>t[4]||(t[4]=[x("保存结果")])),_:1}),d(r,{class:"save",onClick:M},{default:c(()=>t[5]||(t[5]=[x("更新地图数据")])),_:1})]),$(I("div",Ce,null,512),[[b,S.value]])]),d(F(de),{color:["#90ade8","#90ade8"],backgroundColor:"#001731"},{default:c(()=>[I("div",be,[$((k(),V(o,{"element-loading-text":"加载中...","element-loading-background":"rgba(0,23,49,0.8)",height:"75vh"},{default:c(()=>[d(w,{modelValue:P.value,"onUpdate:modelValue":t[1]||(t[1]=v=>P.value=v),accordion:""},{default:c(()=>[(k(!0),E(A,null,R(F(l).clusterDepotAll,(v,a)=>(k(),V(g,{key:a,title:a,name:a},{default:c(()=>[d(w,{modelValue:h[a],"onUpdate:modelValue":n=>h[a]=n,accordion:""},{default:c(()=>[(k(!0),E(A,null,R(v,(n,u)=>(k(),V(g,{key:u,title:u,name:u},{default:c(()=>[(k(!0),E(A,null,R(n,s=>(k(),E("div",{key:s.accumulation,id:"shop-"+s.accumulation,class:ce(["regionCollapseItemContext",f.value===s.longitude&&p.value===s.latitude?"active":""]),onClick:ye=>K([s.longitude,s.latitude])},ue(s.name),11,he))),128))]),_:2},1032,["title","name"]))),128))]),_:2},1032,["modelValue","onUpdate:modelValue"])]),_:2},1032,["title","name"]))),128))]),_:1},8,["modelValue"])]),_:1})),[[b,T.value]])])]),_:1})])}}}),qe=ve(Ee,[["__scopeId","data-v-af8180b5"]]);export{qe as default};
