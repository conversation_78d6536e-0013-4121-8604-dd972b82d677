package com.ict.ycwl.guestbook.api.group;

import com.ict.ycwl.common.web.AjaxResult;
import com.ict.ycwl.guestbook.api.vo.GroupVo;
import com.ict.ycwl.guestbook.domain.Area;
import com.ict.ycwl.guestbook.service.AreaService   ;
import com.ict.ycwl.guestbook.service.GroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

@Api(tags = "班组信息API")
@Validated
@RestController
@RequestMapping("/group")
public class GroupApi {

    @Autowired
    private AreaService areaService;

    @Autowired
    private GroupService groupService;

    @ApiOperation("获取所有大区数据接口")
    @GetMapping("/getAreaList")
    public AjaxResult getAreaList(@RequestHeader("Authorization") String Authorization){
        List<Area> areaList = areaService.getAllArea();
        return AjaxResult.success(areaList);
    }

    @ApiOperation("获取班组人员信息接口")
    @GetMapping("/getGroup/{areaId}")
    public AjaxResult getGroup(@RequestHeader("Authorization") String Authorization
            ,@NotNull(message = "大区id不能为空") @Min(message = "大区id格式错误",value = 1)
             @ApiParam(value = "大区id",required = true) @PathVariable("areaId")Long areaId){
        GroupVo group = groupService.getGroupAndUser(areaId);
        return AjaxResult.success(group);
    }



}
