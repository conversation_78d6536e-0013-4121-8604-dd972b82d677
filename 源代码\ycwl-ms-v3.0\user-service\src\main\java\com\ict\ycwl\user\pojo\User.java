package com.ict.ycwl.user.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@TableName("user")
public class User {

    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize
    private Long userId;
    private String loginName;
    private String userName;
    private String workNumber;
    private String sex;
    private String position;
    private String password;
    private String department;
    private String phone;
    private String email;
    private String status;
    private String avatarPath;
    private Long createBy;
    private Timestamp createTime;
    private Long updateBy;
    private Timestamp updateTime;
    private Date signTime;
    private Long roleId;
    private String rank;

}
