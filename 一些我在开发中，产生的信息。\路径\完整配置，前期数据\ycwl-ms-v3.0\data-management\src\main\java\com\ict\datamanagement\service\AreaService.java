package com.ict.datamanagement.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ict.datamanagement.domain.entity.Area;
import com.ict.datamanagement.domain.entity.Area;
import com.ict.datamanagement.domain.vo.AreaVO;

import java.util.List;


/**
* <AUTHOR>
* @description 针对表【area】的数据库操作Service
* @createDate 2024-04-22 13:45:36
*/
public interface AreaService extends IService<Area> {

    List<AreaVO> getOptionalData();

    AreaVO getAreaVO(Area area);
}
