package com.ict.ycwl.pathcalculate.pojo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 
 * @TableName group_areas
 */
@TableName(value ="group_areas")
@Data
public class GroupAreas implements Serializable {
    /**
     * 大区Id
     */
    @TableId(value = "area_id")
    private Long areaId;

    /**
     * 班组Id
     */
    @TableField(value = "group_id")
    private Long groupId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}