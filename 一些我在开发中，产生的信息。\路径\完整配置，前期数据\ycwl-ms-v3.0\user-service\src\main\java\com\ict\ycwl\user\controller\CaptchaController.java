package com.ict.ycwl.user.controller;

import com.ict.ycwl.common.web.AjaxResult;
import com.ict.ycwl.user.service.CaptchaService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Api(tags = "登录API")
@RestController
@RequestMapping("captcha")
public class CaptchaController {


    @Autowired
    private CaptchaService captchaService;

    @ApiOperation(value = "验证码接口")
    @GetMapping(value = "/captcha", produces = "image/jpeg")
    public void captcha(HttpServletRequest request, HttpServletResponse response) {
        AjaxResult.success(captchaService.captcha(request,response));
    }

//    @ApiOperation(value = "获取验证码文本")
//    @GetMapping("/get")
//    public AjaxResult getCaptcha(HttpServletRequest request){
//        return captchaService.getCaptcha(request);
//    }

}
