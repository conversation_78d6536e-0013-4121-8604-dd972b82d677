package com.ict.ycwl.user.controller;

import com.ict.ycwl.common.web.AjaxResult;
import com.ict.ycwl.user.service.UserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@Api(tags = "用户管理API")
@RestController
@RequestMapping("/user")
public class UserAvatarController {

    @Autowired
    private UserService userService;

    @ApiOperation("上传头像接口")
    @PostMapping("/avatar")
    public AjaxResult uploadAvatar(@RequestParam("photo") MultipartFile photo) throws Exception {
        return AjaxResult.success(userService.uploadAvatar(photo));
    }
}
