import{f as k}from"./base-kpSIrADU.js";/* empty css              */import{E as R}from"./button-IGKrEYb9.js";import{d as C,r as V,c as l,a as b,b as t,u as a,w as o,K as w,U as x,V as B,D as E,o as m,t as p,R as _}from"./index-C0QCllTd.js";import{_ as M}from"./_plugin-vue_export-helper-DlAUqK2U.js";const N={class:"MilkRun"},g={class:"content"},$={key:0,class:"btn-content"},h=C({__name:"MilkRun",setup(y){const d=x(),r=B(),s=V(r.name);function u(n){d.push(`/home/<USER>/MilkRun/${n}`),s.value=n}return(n,e)=>{const f=E("RouterView"),c=R,i=k;return m(),l("div",N,[b("div",g,[t(f)]),a(r).meta.isShow?w("",!0):(m(),l("div",$,[t(i,{class:"item"},{default:o(()=>[t(c,{class:p({vertical:!0,active:a(s)==="pickup"}),onClick:e[0]||(e[0]=v=>u("pickup"))},{default:o(()=>e[2]||(e[2]=[_("取货户分析")])),_:1},8,["class"])]),_:1}),t(i,{class:"item"},{default:o(()=>[t(c,{class:p({vertical:!0,active:a(s)==="pos"}),onClick:e[1]||(e[1]=v=>u("pos"))},{default:o(()=>e[3]||(e[3]=[_("选址分析")])),_:1},8,["class"])]),_:1})]))])}}}),T=M(h,[["__scopeId","data-v-d93b52e6"]]);export{T as default};
