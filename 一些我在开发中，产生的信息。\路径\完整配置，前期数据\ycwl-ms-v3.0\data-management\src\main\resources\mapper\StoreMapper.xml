<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.datamanagement.mapper.StoreMapper">

    <resultMap id="BaseResultMap" type="com.ict.datamanagement.domain.entity.Store">
            <id property="storeId" column="store_id" jdbcType="BIGINT"/>
            <result property="customerCode" column="customer_code" jdbcType="VARCHAR"/>
            <result property="storeName" column="store_name" jdbcType="VARCHAR"/>
            <result property="storeAddress" column="store_address" jdbcType="VARCHAR"/>
            <result property="longitude" column="longitude" jdbcType="DOUBLE"/>
            <result property="latitude" column="latitude" jdbcType="DOUBLE"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="orderCycle" column="order_cycle" jdbcType="VARCHAR"/>
            <result property="district" column="district" jdbcType="VARCHAR"/>
            <result property="areaName" column="area_name" jdbcType="VARCHAR"/>
            <result property="contactName" column="contact_name" jdbcType="VARCHAR"/>
            <result property="contactPhone" column="contact_phone" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="CHAR"/>
            <result property="customerManagerId" column="customer_manager_id" jdbcType="BIGINT"/>
            <result property="accumulationId" column="accumulation_id" jdbcType="BIGINT"/>
            <result property="areaId" column="area_id" jdbcType="BIGINT"/>
            <result property="routeId" column="route_id" jdbcType="BIGINT"/>
            <result property="routeName" column="route_name" jdbcType="VARCHAR"/>
            <result property="customerManagerName" column="customer_manager_name" jdbcType="VARCHAR"/>
            <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="gear" column="gear" jdbcType="VARCHAR"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
            <result property="groupId" column="group_id" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        store_id,customer_code,store_name,
        store_address,longitude,latitude,
        type,order_cycle,district,
        area_name,contact_name,contact_phone,
        status,customer_manager_id,accumulation_id,
        area_id,route_id,route_name,
        customer_manager_name,is_delete,create_time,
        update_time,gear,create_by,update_by,group_id
    </sql>
</mapper>
