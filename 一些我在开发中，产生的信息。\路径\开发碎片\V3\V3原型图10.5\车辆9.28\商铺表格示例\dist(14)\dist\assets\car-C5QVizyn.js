import{Y as E,r as s}from"./index-C0QCllTd.js";import{r as n}from"./index-m25zEilF.js";function G(t){return n.post({url:"/datamanagement/addCar",params:t})}function H(t){return n.delete({url:"/datamanagement/deleteCar",params:t})}function J(t){return n.post({url:"/datamanagement/updateCar",params:t})}function C(t){return n.get({url:"/datamanagement/carList",params:t})}function K(){return n.post({url:"/datamanagement/selectDownBox"})}function O(){return n.post({url:"/datamanagement/getAddCarDownBox"})}function Q(t){return n.post({url:"/datamanagement/addCarActualList",params:t})}function R(t){return n.delete({url:"/datamanagement/deleteCarActual",params:t})}function V(t){return n.get({url:"/datamanagement/carActualList",params:t})}function W(t){return n.post({url:"/datamanagement/updateCarActual",params:t})}function X(){return n.post({url:"/datamanagement/carActualDownBox"})}function Z(t,c){return n.post({url:"/datamanagement/carImport",data:t,headers:{accept:"*/*","Content-Type":"multipart/form-data"},onUploadProgress:c.onUploadProgress,signal:c.signal})}function w(t){return n.get({url:"/datamanagement/getImportLogs",params:t})}function _(t){return n.delete({url:"/datamanagement/deleteImportLogs",params:t})}function $(t){return n.get({url:"/datamanagement/downloadLogs",headers:{"Content-Type":"application/x-download"},responseType:"blob",params:t})}function S(t){return n.post({url:"/datamanagement/downloadNullFrom",headers:{"Content-Type":"application/x-download"},responseType:"blob",params:t})}const ta=E("car",()=>{const t=s([]),c=s(),f=s(),m=s(),l=s([]),u=s(),g=s(),p=s(),i=s(!1);async function y(a){return await G(a)}async function A(a){return await H(a)}async function h(a){return await J(a)}async function L(a){let e=1,o=!0;for(t.value=[];o;)try{const r=await C({pageNum:e,pageSize:a});t.value=[...t.value,...r.data.records],o=r.data.records.length===a,e++}catch(r){console.error("请求数据时出错:",r),o=!1}}async function v(a){return await C(a)}async function D(){const a=await K();c.value=a.data}async function I(){const a=await O();f.value=a.data}async function T(a){return await Q(a)}async function x(a){return await R(a)}async function b(a){return await W(a)}async function B(a){let e=1,o=!0;for(l.value=[];o;)try{const r=await V({pageNum:e,pageSize:a});l.value=[...l.value,...r.data.records],o=r.data.records.length===a,e++}catch(r){console.error("请求数据时出错:",r),o=!1}}async function N(){const a=await X();m.value=a.data}async function F(a,e){return await Z(a,e)}async function M(a){const e=await w(a);p.value=e.data}async function P(a){let e=1;const o="1";let r=!0;for(u.value=[];r;)try{const d=await w({pageNum:e,pageSize:a,type:o});u.value=[...u.value,...d.data.records],r=d.data.records.length===a,e++}catch(d){console.error("请求数据时出错:",d),r=!1}}async function U(a){return await _(a)}async function q(a){return await $(a)}async function Y(a){return await S(a)}function j(a,e){g.value=u.value.slice(a*e-e,a*e)}function k(){i.value=!i.value}return{addCarData:y,deleteCarData:A,updateCarData:h,getAllCarData:L,getCarData:v,searchCarInfoData:D,addCarInfoData:I,addCarActual:T,deleteCarActual:x,updateCarActual:b,getTotalCarActual:B,getActualDownBox:N,importCarActual:F,deleteCarLog:U,downloadCarLog:q,importCarLog:M,importAllLog:P,updatelogList:j,downloadFrom:Y,updateIsinTable:k,isInTable:i,carSearchInfo:c,carTotalList:t,carAddInfo:f,actualCarTotalList:l,actualInfo:m,noteAllList:u,noteList:g,noteData:p}});export{ta as c};
