package com.ict.ycwl.user.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("user_group")
public class UserGroup {
    @TableId(type = IdType.ASSIGN_ID)
    private Long groupId;
    private Long userId;
    private String isLeader;
}
