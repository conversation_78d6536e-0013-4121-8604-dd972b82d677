package com.ict.ycwl.pathcalculate.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 班组所有路线统计类
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GroupRouteVO {

    /**
     * 班组Id
     */
    private Long groupId;

    /**
     * 班组名称
     */
    private String groupName;

    /**
     * 运营里程
     */
    String averageDistance;

    /**
     * 工作时长
     */
    String averageWorkTime;

    /**
     * 载货量
     */
    String averCargoWeight;

    /**
     * 载货量
     */
    String totalCargoWeight;
}
