import{p as ye,h as Ne,o as be}from"./base-kpSIrADU.js";import{E as xe,b as Ce,a as De}from"./table-column-DZpqkK6R.js";import{E as ee}from"./input-DqmydyK4.js";import{a as ae,E as le}from"./select-BOcQ2ynX.js";import"./scrollbar-BNeK4Yi-.js";import"./checkbox-DWZ5xHlw.js";import{a as te,E as re}from"./form-item-Bd-FvCZ5.js";import{E as J}from"./button-IGKrEYb9.js";import{d as W,r as y,P as oe,c as D,b as a,w as l,u as e,Q as ne,o as r,a as h,N as A,T as S,p as C,R as E,af as Ve,S as me,z as we,K as $,Z as H}from"./index-C0QCllTd.js";import{E as se}from"./overlay-D06mCCGK.js";import{c as X}from"./car-C5QVizyn.js";import{a as V}from"./index-m25zEilF.js";import{_ as Y}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{p as ke}from"./pathDialog-BrGNJEyI.js";import"./_commonjsHelpers-BbMlrU8H.js";import"./merge-B3fxVp56.js";import"./_initCloneObject-BmTtMqsv.js";import"./flatten-BP0fiJV-.js";import"./castArray-CSO3s-vM.js";import"./index-DOdSMika.js";import"./carousel-item-sFYM8ch0.js";const Ae={class:"addCarDialog"},Ie={class:"flex-center"},Se={class:"flex"},Le={class:"flex"},Pe={class:"flex"},Ee={class:"btns"},ze=W({__name:"addCar",emits:["confirmAdd"],setup(F,{expose:v,emit:M}){const p=X(),g=y(!1);v({addCarOpen:g});const b=y(),w=M,m=y({licensePlateNumber:"",carDriverName:"",carDriverPhone:"",deliveryAreaName:"",maxLoad:"",status:""}),z=oe({licensePlateNumber:[{required:!0,message:"请输入",trigger:"blur"}],carDriverName:[{required:!0,message:"请选择",trigger:"change"}],status:[{required:!0,message:"请选择",trigger:"change"}],maxLoad:[{validator:(L,o,n)=>{o===""?n(new Error("请输入吨数")):isNaN(o)||o.trim()===""?n(new Error("请输入数字")):Number(o)>2?n(new Error("不能超过2吨")):n()},trigger:"blur"}]});async function x(){b.value&&b.value.validate(L=>{L?(w("confirmAdd",m.value),b.value.resetFields()):V({message:"添加失败!",type:"warning"})})}const f=()=>{b.value.resetFields(),g.value=!1};function k(){m.value.carDriverName=p.carAddInfo.carDriver.find(L=>L.phone===m.value.carDriverPhone).userName}function t(){m.value.carDriverPhone=p.carAddInfo.carDriver.find(L=>L.userName===m.value.carDriverName).phone}return(L,o)=>{const n=ee,_=te,I=ae,P=le,B=re,R=J,O=se;return r(),D("div",Ae,[a(O,{modelValue:e(g),"onUpdate:modelValue":o[6]||(o[6]=u=>ne(g)?g.value=u:null),title:"添加车辆信息",width:"60%",height:"70%","close-on-click-modal":!1,onClose:f},{default:l(()=>[h("div",Ie,[a(B,{"label-width":"auto",class:"areaForm",model:e(m),ref_key:"formRef",ref:b,rules:e(z)},{default:l(()=>[h("div",Se,[a(_,{label:"车牌号",prop:"licensePlateNumber"},{default:l(()=>[a(n,{style:{width:"250px"},placeholder:"点击输入",modelValue:e(m).licensePlateNumber,"onUpdate:modelValue":o[0]||(o[0]=u=>e(m).licensePlateNumber=u)},null,8,["modelValue"])]),_:1}),a(_,{label:"配送域",prop:"deliveryAreaName"},{default:l(()=>[a(P,{style:{width:"250px"},placeholder:"请选择",modelValue:e(m).deliveryAreaName,"onUpdate:modelValue":o[1]||(o[1]=u=>e(m).deliveryAreaName=u)},{default:l(()=>[(r(!0),D(A,null,S(e(p).carAddInfo.deliveryList,u=>(r(),C(I,{label:u,value:u,key:u+"9"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),h("div",Le,[a(_,{label:"驾驶人",prop:"carDriverName"},{default:l(()=>[a(P,{style:{width:"250px"},placeholder:"请选择",modelValue:e(m).carDriverName,"onUpdate:modelValue":o[2]||(o[2]=u=>e(m).carDriverName=u),onChange:t},{default:l(()=>[(r(!0),D(A,null,S(e(p).carAddInfo.carDriver,u=>(r(),C(I,{label:u.userName,value:u.userName,key:u+"9"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"电话",prop:"carDriverPhone"},{default:l(()=>[a(P,{style:{width:"250px"},placeholder:"请选择",modelValue:e(m).carDriverPhone,"onUpdate:modelValue":o[3]||(o[3]=u=>e(m).carDriverPhone=u),onChange:k},{default:l(()=>[(r(!0),D(A,null,S(e(p).carAddInfo.carDriver,u=>(r(),C(I,{label:u.phone,value:u.phone,key:u+"9"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),h("div",Pe,[a(_,{label:"最大载重",prop:"maxLoad",style:{display:"flex"}},{default:l(()=>[a(n,{style:{width:"200px"},placeholder:"请输入",modelValue:e(m).maxLoad,"onUpdate:modelValue":o[4]||(o[4]=u=>e(m).maxLoad=u)},null,8,["modelValue"]),o[7]||(o[7]=h("div",{class:"tons"},"吨",-1))]),_:1}),a(_,{label:"状态",prop:"status"},{default:l(()=>[a(P,{style:{width:"250px"},placeholder:"请选择",modelValue:e(m).status,"onUpdate:modelValue":o[5]||(o[5]=u=>e(m).status=u)},{default:l(()=>[(r(!0),D(A,null,S(e(p).carAddInfo.status,u=>(r(),C(I,{label:u,value:u,key:u+"9"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})])]),_:1},8,["model","rules"])]),h("div",Ee,[a(R,{type:"primary",onClick:f},{default:l(()=>o[8]||(o[8]=[E("取消")])),_:1}),a(R,{type:"primary",style:{"margin-left":"100px"},onClick:x},{default:l(()=>o[9]||(o[9]=[E("确定")])),_:1})])]),_:1},8,["modelValue"])])}}}),Re=Y(ze,[["__scopeId","data-v-f506645b"]]),Ue={class:"changeCarDialog"},$e={class:"flex-center"},Me={class:"flex"},Te={style:{width:"250px","font-size":"20px"}},Fe={class:"flex"},Oe={class:"flex"},Be={class:"btns"},qe=W({__name:"changeCar",props:["carId","carName","info"],emits:["confirmChange"],setup(F,{expose:v,emit:M}){const p=X(),g=y(!1);v({changeCarOpen:g});const b=F,w=y(),m=M,d=y({carDriverName:"",carDriverPhone:"",deliveryAreaName:"",maxLoad:"",status:""});Ve(()=>{d.value.carDriverName=b.info.userName,d.value.carDriverPhone=b.info.phone,d.value.maxLoad=b.info.maxLoad,d.value.status=b.info.status,d.value.deliveryAreaName=b.info.deliveryAreaName});const x=oe({maxLoad:[{validator:(o,n,_)=>{n===""?_(new Error("请输入吨数")):isNaN(n)||n.trim()===""?_(new Error("请输入数字")):Number(n)>2?_(new Error("不能超过2吨")):_()},trigger:"blur"}]});async function f(){const o={licensePlateNumber:b.carName,carId:Number(b.carId),...d.value};w.value&&w.value.validate(n=>{n?p.updateCarData(o).then(_=>{if(w.value.resetFields(),g.value=!1,_.code===50001||_.message==="系统异常"){V.error("系统异常!");return}m("confirmChange")}):V({message:"修改失败!",type:"warning"})})}const k=()=>{w.value.resetFields(),g.value=!1};function t(){d.value.carDriverName=p.carAddInfo.carDriver.find(o=>o.phone===d.value.carDriverPhone).userName}function L(){d.value.carDriverPhone=p.carAddInfo.carDriver.find(o=>o.userName===d.value.carDriverName).phone}return(o,n)=>{const _=te,I=ae,P=le,B=ee,R=re,O=J,u=se;return r(),D("div",Ue,[a(u,{modelValue:e(g),"onUpdate:modelValue":n[5]||(n[5]=i=>ne(g)?g.value=i:null),title:"修改车辆信息",width:"60%",height:"70%","close-on-click-modal":!1,onClose:k},{default:l(()=>[h("div",$e,[a(R,{"label-width":"auto",class:"areaForm",model:e(d),ref_key:"formRef",ref:w,rules:e(x)},{default:l(()=>[h("div",Me,[a(_,{label:"车牌号"},{default:l(()=>[h("p",Te,me(F.carName),1)]),_:1}),a(_,{label:"配送域",prop:"deliveryAreaName"},{default:l(()=>[a(P,{style:{width:"250px"},placeholder:"请选择",modelValue:e(d).deliveryAreaName,"onUpdate:modelValue":n[0]||(n[0]=i=>e(d).deliveryAreaName=i)},{default:l(()=>[(r(!0),D(A,null,S(e(p).carAddInfo.deliveryList,i=>(r(),C(I,{label:i,value:i,key:i+"9"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),h("div",Fe,[a(_,{label:"驾驶人",prop:"carDriverName"},{default:l(()=>[a(P,{style:{width:"250px"},placeholder:"请选择",modelValue:e(d).carDriverName,"onUpdate:modelValue":n[1]||(n[1]=i=>e(d).carDriverName=i),onChange:L},{default:l(()=>[(r(!0),D(A,null,S(e(p).carAddInfo.carDriver,i=>(r(),C(I,{label:i.userName,value:i.userName,key:i+"9"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(_,{label:"电话",prop:"carDriverPhone"},{default:l(()=>[a(P,{style:{width:"250px"},placeholder:"请选择",modelValue:e(d).carDriverPhone,"onUpdate:modelValue":n[2]||(n[2]=i=>e(d).carDriverPhone=i),onChange:t},{default:l(()=>[(r(!0),D(A,null,S(e(p).carAddInfo.carDriver,i=>(r(),C(I,{label:i.phone,value:i.phone,key:i+"9"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})]),h("div",Oe,[a(_,{label:"最大载重",prop:"maxLoad",style:{display:"flex"}},{default:l(()=>[a(B,{style:{width:"200px"},placeholder:"请输入",modelValue:e(d).maxLoad,"onUpdate:modelValue":n[3]||(n[3]=i=>e(d).maxLoad=i)},null,8,["modelValue"]),n[6]||(n[6]=h("div",{class:"tons"},"吨",-1))]),_:1}),a(_,{label:"状态",prop:"status"},{default:l(()=>[a(P,{style:{width:"250px"},placeholder:"请选择",modelValue:e(d).status,"onUpdate:modelValue":n[4]||(n[4]=i=>e(d).status=i)},{default:l(()=>[(r(!0),D(A,null,S(e(p).carAddInfo.status,i=>(r(),C(I,{label:i,value:i,key:i+"9"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1})])]),_:1},8,["model","rules"])]),h("div",Be,[a(O,{type:"primary",onClick:k},{default:l(()=>n[7]||(n[7]=[E("取消")])),_:1}),a(O,{type:"primary",style:{"margin-left":"100px"},onClick:f},{default:l(()=>n[8]||(n[8]=[E("确定")])),_:1})])]),_:1},8,["modelValue"])])}}}),je=Y(qe,[["__scopeId","data-v-2dd84026"]]),Qe={class:"dialog-footer"},Ke=W({__name:"deleteCar",props:["carId"],emits:["confirmDelete"],setup(F,{expose:v,emit:M}){const p=X(),g=y(!1),b=M,w=F;v({deleteVis:g});function m(){g.value=!1}function d(){const z={carId:Number(w.carId)};p.deleteCarData(z).then(x=>{if(g.value=!1,x.code===50001||x.message==="系统异常"){V.error("系统异常!");return}b("confirmDelete")})}return(z,x)=>{const f=J,k=se;return r(),C(k,{modelValue:e(g),"onUpdate:modelValue":x[0]||(x[0]=t=>ne(g)?g.value=t:null),title:"删除车辆信息",class:"transform",width:"40%","align-center":"","close-on-click-modal":!1},{footer:l(()=>[h("div",Qe,[a(f,{onClick:m,type:"primary"},{default:l(()=>x[1]||(x[1]=[E("取消")])),_:1}),a(f,{type:"primary",onClick:d},{default:l(()=>x[2]||(x[2]=[E(" 确定 ")])),_:1})])]),default:l(()=>[x[3]||(x[3]=h("div",{class:"content"},"确定删除所选车辆",-1))]),_:1},8,["modelValue"])}}}),Ze=Y(Ke,[["__scopeId","data-v-eba7c2d1"]]),Ge={class:"to-messages"},He={style:{display:"flex"}},Je={class:"top"},We={key:3,class:"search"},Xe={class:"btn-content"},Ye={key:0,class:"main"},ea={class:"dialog"},aa={class:"pa"},la=W({__name:"Message",setup(F){we(()=>{f.getAllCarData(t.pageSize).then(()=>{ve(t.pageSize)})});const v=y({searchCarNumber:"",searchTeamName:"",searchDriver:"",searchStatus:"",searchMaxLoad:""}),M=y([]),p=y(!1),g=y(!1),b=y(),w=y(),m=y(),d=y(),z=y(),x=y(-1),f=X(),k=y([]),t=oe({pageNum:1,pageSize:14}),L=y();function o(){f.searchCarInfoData().then(()=>{g.value=!0})}function n(){g.value=!1}function _(){p.value=!1,v.value={searchCarNumber:"",searchTeamName:"",searchDriver:"",searchStatus:"",searchMaxLoad:""},t.pageNum=1,ie({...t})}function I(){p.value=!0,t.pageNum=1,ie({...t,carDriver:v.value.searchDriver,licensePlateNumber:v.value.searchCarNumber,maxLoad:v.value.searchMaxLoad,status:v.value.searchStatus,teamName:v.value.searchTeamName})}function P(){if(p.value){V({message:"还在搜索状态中,禁止打开弹窗",type:"warning"});return}f.addCarInfoData().then(()=>{b.value.addCarOpen=!0})}function B(){if(p.value){V({message:"还在搜索状态中,禁止打开弹窗",type:"warning"});return}if(d.value.getSelectionRows().length!==1){V({message:"请正确选择一个车辆删除!",type:"warning"});return}if(d.value.getSelectionRows()[0].status!=="异常"){V({message:"请选择异常车辆删除!",type:"warning"});return}w.value.deleteVis=!0}const R=y();function O(N){if(b.value.addCarOpen=!1,f.carTotalList.findIndex(s=>s.licensePlateNumber===N.licensePlateNumber)!==-1){V({message:"添加失败!有相同名字",type:"warning"});return}f.addCarData(N).then(s=>{if(s.code===50001||s.message==="系统异常"){V.error("系统异常!");return}R.value.vis=!0,f.getAllCarData(t.pageSize).then(()=>{Q(t.pageNum,t.pageSize),V({message:"添加成功!",type:"success"})})})}function u(){w.value.deleteVis=!1,f.getAllCarData(t.pageSize).then(()=>{Q(t.pageNum,t.pageSize),V({message:"删除成功!",type:"success"})})}function i(){if(d.value.getSelectionRows().length!==1){V({message:"请单选一个班组修改",type:"warning"});return}f.addCarInfoData().then(()=>{m.value.changeCarOpen=!0})}const ue=y(0);function pe(){m.value.changeCarOpen=!1,R.value.vis=!0,ue.value=t.pageNum,f.getAllCarData(t.pageSize).then(()=>{t.pageNum=ue.value,Q(t.pageNum,t.pageSize),V({message:"修改成功!",type:"success"})})}const T=y();function fe(N){N.length>=1&&(x.value=N[0].carId,z.value=N[0].licensePlateNumber,T.value=Object.assign({},N[0]),T.value.deliveryAreaName||(T.value.deliveryAreaName=""),T.value.teamName||(T.value.teamName=""))}function de(N=1){if(t.pageNum=N,p.value){k.value=M.value.slice(t.pageNum*t.pageSize-t.pageSize,t.pageNum*t.pageSize);return}Q(t.pageNum,t.pageSize)}function Q(N,s){if(p.value){I();return}k.value=f.carTotalList.slice(N*s-s,N*s)}function ve(N){k.value=f.carTotalList.slice(0,N)}function ie(N){f.getCarData(N).then(s=>{if(s.code===50001||s.message==="系统异常"){V.error("系统异常!");return}console.log(N),k.value=s.data.records})}function ge(N){const s=t.pageNum,K=t.pageSize;return N+1+(s-1)*K}return(N,s)=>{const K=ee,q=J,Z=ae,G=le,j=te,_e=re,U=De,he=xe,ce=Ce;return r(),D(A,null,[h("div",Ge,[h("div",He,[h("div",Je,[a(K,{class:"input",placeholder:"请点击搜索",onClick:o}),e(H)("data-management:car:add")?(r(),C(q,{key:0,class:"button",icon:e(ye),onClick:P},{default:l(()=>s[5]||(s[5]=[E("添加车辆")])),_:1},8,["icon"])):$("",!0),e(H)("data-management:car:delete")?(r(),C(q,{key:1,class:"button",icon:e(Ne),onClick:B},{default:l(()=>s[6]||(s[6]=[E("删除车辆")])),_:1},8,["icon"])):$("",!0),e(H)("data-management:car:update")?(r(),C(q,{key:2,class:"button",icon:e(be),onClick:i},{default:l(()=>s[7]||(s[7]=[E("修改信息")])),_:1},8,["icon"])):$("",!0),e(g)?(r(),D("div",We,[h("div",{class:"off",onClick:n},"x"),a(_e,{class:"form",model:e(v),ref_key:"searchModal",ref:L},{default:l(()=>[a(j,{label:"车牌号",style:{width:"45%"},prop:"searchCarNumber"},{default:l(()=>[a(G,{style:{width:"250px"},placeholder:"请选择",modelValue:e(v).searchCarNumber,"onUpdate:modelValue":s[0]||(s[0]=c=>e(v).searchCarNumber=c)},{default:l(()=>[(r(!0),D(A,null,S(e(f).carSearchInfo.licensePlateNumberList,c=>(r(),C(Z,{label:c,value:c,key:c+"9"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(j,{label:"班组",style:{width:"47%","margin-left":"20px"},prop:"searchTeamName"},{default:l(()=>[a(G,{style:{width:"250px"},placeholder:"请选择",modelValue:e(v).searchTeamName,"onUpdate:modelValue":s[1]||(s[1]=c=>e(v).searchTeamName=c)},{default:l(()=>[(r(!0),D(A,null,S(e(f).carSearchInfo.teamList,c=>(r(),C(Z,{label:c,value:c,key:c+"9"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(j,{label:"驾驶人",style:{width:"45%"},prop:"searchDriver"},{default:l(()=>[a(G,{style:{width:"250px"},placeholder:"请选择",modelValue:e(v).searchDriver,"onUpdate:modelValue":s[2]||(s[2]=c=>e(v).searchDriver=c)},{default:l(()=>[(r(!0),D(A,null,S(Object.keys(e(f).carSearchInfo.carDriverList),c=>(r(),C(Z,{label:c,value:c,key:c+"9"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(j,{label:"状态",style:{width:"47%","margin-left":"20px"},prop:"searchStatus"},{default:l(()=>[a(G,{style:{width:"100px"},placeholder:"请输入",modelValue:e(v).searchStatus,"onUpdate:modelValue":s[3]||(s[3]=c=>e(v).searchStatus=c)},{default:l(()=>[(r(!0),D(A,null,S(e(f).carSearchInfo.statusList,c=>(r(),C(Z,{label:c,value:c,key:c+"9"},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),a(j,{label:"最大载重",style:{width:"100%"},prop:"searchMaxLoad"},{default:l(()=>[a(K,{style:{width:"100px"},placeholder:"请输入",modelValue:e(v).searchMaxLoad,"onUpdate:modelValue":s[4]||(s[4]=c=>e(v).searchMaxLoad=c)},null,8,["modelValue"])]),_:1}),h("div",Xe,[a(q,{onClick:_},{default:l(()=>s[8]||(s[8]=[E("清空")])),_:1}),a(q,{onClick:I},{default:l(()=>s[9]||(s[9]=[E("搜索")])),_:1})])]),_:1},8,["model"])])):$("",!0)])]),e(H)("data-management:car:view")?(r(),D("div",Ye,[a(he,{ref_key:"tableRef",ref:d,data:e(k),"cell-style":{textAlign:"center"},onSelectionChange:fe,"header-cell-style":{height:"4vh","text-align":"center"},size:"small","row-style":{height:"4.3vh"},style:{"font-size":"0.8vw"}},{default:l(()=>[a(U,{type:"selection","min-width":"1%"}),a(U,{label:"序号","min-width":"1%",type:"index",index:ge}),a(U,{label:"车牌号","min-width":"3%",prop:"licensePlateNumber"}),a(U,{label:"驾驶人名称","min-width":"3%",prop:"userName"}),a(U,{label:"驾驶人电话","min-width":"2%",prop:"phone"}),a(U,{label:"最大载重 (吨)","min-width":"2.5%",prop:"maxLoad"}),a(U,{label:"状态","min-width":"2%",prop:"status"}),a(U,{label:"所属配送域","min-width":"3%",prop:"deliveryAreaName"},{default:l(c=>[h("div",null,me(c.row.deliveryAreaName?c.row.deliveryAreaName:"无"),1)]),_:1})]),_:1},8,["data"])])):$("",!0),h("div",ea,[a(Re,{ref_key:"addCarRef",ref:b,onConfirmAdd:O},null,512),a(Ze,{ref_key:"deleteCarRef",ref:w,onConfirmDelete:u,carId:e(x)},null,8,["carId"]),e(T)?(r(),C(je,{key:0,ref_key:"changeCarRef",ref:m,onConfirmChange:pe,carId:e(x),info:e(T),carName:e(z)},null,8,["carId","info","carName"])):$("",!0),a(ke,{ref_key:"pathRef",ref:R,dType:"车辆"},null,512)])]),h("div",aa,[e(p)?$("",!0):(r(),C(ce,{key:0,layout:"prev, pager, next","current-page":e(t).pageNum,"page-size":e(t).pageSize,total:e(f).carTotalList.length,onCurrentChange:de},null,8,["current-page","page-size","total"])),e(p)?(r(),C(ce,{key:1,layout:"prev, pager, next","current-page":e(t).pageNum,"page-size":e(t).pageSize,total:e(k).length,onCurrentChange:de},null,8,["current-page","page-size","total"])):$("",!0)])],64)}}}),Da=Y(la,[["__scopeId","data-v-69b6e26f"]]);export{Da as default};
