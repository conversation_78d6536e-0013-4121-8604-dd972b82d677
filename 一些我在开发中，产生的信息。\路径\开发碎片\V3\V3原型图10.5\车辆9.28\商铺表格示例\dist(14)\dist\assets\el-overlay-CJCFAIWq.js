import{G as O,d as be,aj as ye,k as w,r as M,P as Ce,m as Y,z as Ee,A as he,a4 as Be,D as T,o as c,p as v,w as p,X as U,b as C,a as m,t as r,n as x,M as z,c as H,q as P,K as S,S as R,e as D,s as we,R as K,a5 as q,ad as Me,l as le,ak as re,al as Te,a7 as Se,am as ie,an as _,a1 as ee,ao as ne}from"./index-C0QCllTd.js";import{E as ke}from"./button-IGKrEYb9.js";import{E as Ie}from"./input-DqmydyK4.js";import{E as Ae,u as Re,a as Oe,b as Le}from"./index-DOdSMika.js";import{_ as Ve,E as $e,T as ze,M as se,u as oe,N as Pe}from"./base-kpSIrADU.js";import{E as De}from"./index-m25zEilF.js";import{d as Ne,i as Fe}from"./scrollbar-BNeK4Yi-.js";const Ue='a[href],button:not([disabled]),button:not([hidden]),:not([tabindex="-1"]),input:not([disabled]),input:not([type="hidden"]),select:not([disabled]),textarea:not([disabled])',He=e=>getComputedStyle(e).position==="fixed"?!1:e.offsetParent!==null,te=e=>Array.from(e.querySelectorAll(Ue)).filter(n=>Ke(n)&&He(n)),Ke=e=>{if(e.tabIndex>0||e.tabIndex===0&&e.getAttribute("tabIndex")!==null)return!0;if(e.disabled)return!1;switch(e.nodeName){case"A":return!!e.href&&e.rel!=="ignore";case"INPUT":return!(e.type==="hidden"||e.type==="file");case"BUTTON":case"SELECT":case"TEXTAREA":return!0;default:return!1}},j="_trap-focus-children",E=[],ae=e=>{if(E.length===0)return;const n=E[E.length-1][j];if(n.length>0&&e.code===De.tab){if(n.length===1){e.preventDefault(),document.activeElement!==n[0]&&n[0].focus();return}const a=e.shiftKey,i=e.target===n[0],l=e.target===n[n.length-1];i&&a&&(e.preventDefault(),n[n.length-1].focus()),l&&!a&&(e.preventDefault(),n[0].focus())}},qe={beforeMount(e){e[j]=te(e),E.push(e),E.length<=1&&document.addEventListener("keydown",ae)},updated(e){O(()=>{e[j]=te(e)})},unmounted(){E.shift(),E.length===0&&document.removeEventListener("keydown",ae)}},je=be({name:"ElMessageBox",directives:{TrapFocus:qe},components:{ElButton:ke,ElFocusTrap:Ne,ElInput:Ie,ElOverlay:Ae,ElIcon:$e,...ze},inheritAttrs:!1,props:{buttonSize:{type:String,validator:Fe},modal:{type:Boolean,default:!0},lockScroll:{type:Boolean,default:!0},showClose:{type:Boolean,default:!0},closeOnClickModal:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},closeOnHashChange:{type:Boolean,default:!0},center:Boolean,draggable:Boolean,roundButton:{default:!1,type:Boolean},container:{type:String,default:"body"},boxType:{type:String,default:""}},emits:["vanish","action"],setup(e,{emit:n}){const{locale:a,zIndex:i,ns:l,size:o}=ye("message-box",w(()=>e.buttonSize)),{t:d}=a,{nextZIndex:f}=i,y=M(!1),s=Ce({autofocus:!0,beforeClose:null,callback:null,cancelButtonText:"",cancelButtonClass:"",confirmButtonText:"",confirmButtonClass:"",customClass:"",customStyle:{},dangerouslyUseHTMLString:!1,distinguishCancelAndClose:!1,icon:"",inputPattern:null,inputPlaceholder:"",inputType:"text",inputValue:null,inputValidator:null,inputErrorMessage:"",message:null,modalFade:!0,modalClass:"",showCancelButton:!1,showConfirmButton:!0,type:"",title:void 0,showInput:!1,action:"",confirmButtonLoading:!1,cancelButtonLoading:!1,confirmButtonDisabled:!1,editorErrorMessage:"",validateError:!1,zIndex:f()}),N=w(()=>{const t=s.type;return{[l.bm("icon",t)]:t&&se[t]}}),F=oe(),u=oe(),ue=w(()=>s.icon||se[s.type]||""),de=w(()=>!!s.message),h=M(),G=M(),I=M(),V=M(),X=M(),ce=w(()=>s.confirmButtonClass);Y(()=>s.inputValue,async t=>{await O(),e.boxType==="prompt"&&t!==null&&Z()},{immediate:!0}),Y(()=>y.value,t=>{var g,B;t&&(e.boxType!=="prompt"&&(s.autofocus?I.value=(B=(g=X.value)==null?void 0:g.$el)!=null?B:h.value:I.value=h.value),s.zIndex=f()),e.boxType==="prompt"&&(t?O().then(()=>{var Q;V.value&&V.value.$el&&(s.autofocus?I.value=(Q=ge())!=null?Q:h.value:I.value=h.value)}):(s.editorErrorMessage="",s.validateError=!1))});const fe=w(()=>e.draggable);Re(h,G,fe),Ee(async()=>{await O(),e.closeOnHashChange&&window.addEventListener("hashchange",A)}),he(()=>{e.closeOnHashChange&&window.removeEventListener("hashchange",A)});function A(){y.value&&(y.value=!1,O(()=>{s.action&&n("action",s.action)}))}const W=()=>{e.closeOnClickModal&&$(s.distinguishCancelAndClose?"close":"cancel")},pe=Le(W),me=t=>{if(s.inputType!=="textarea")return t.preventDefault(),$("confirm")},$=t=>{var g;e.boxType==="prompt"&&t==="confirm"&&!Z()||(s.action=t,s.beforeClose?(g=s.beforeClose)==null||g.call(s,t,s,A):A())},Z=()=>{if(e.boxType==="prompt"){const t=s.inputPattern;if(t&&!t.test(s.inputValue||""))return s.editorErrorMessage=s.inputErrorMessage||d("el.messagebox.error"),s.validateError=!0,!1;const g=s.inputValidator;if(typeof g=="function"){const B=g(s.inputValue);if(B===!1)return s.editorErrorMessage=s.inputErrorMessage||d("el.messagebox.error"),s.validateError=!0,!1;if(typeof B=="string")return s.editorErrorMessage=B,s.validateError=!0,!1}}return s.editorErrorMessage="",s.validateError=!1,!0},ge=()=>{const t=V.value.$refs;return t.input||t.textarea},J=()=>{$("close")},ve=()=>{e.closeOnPressEscape&&J()};return e.lockScroll&&Oe(y),{...Be(s),ns:l,overlayEvent:pe,visible:y,hasMessage:de,typeClass:N,contentId:F,inputId:u,btnSize:o,iconComponent:ue,confirmButtonClasses:ce,rootRef:h,focusStartRef:I,headerRef:G,inputRef:V,confirmRef:X,doClose:A,handleClose:J,onCloseRequested:ve,handleWrapperClick:W,handleInputEnter:me,handleAction:$,t:d}}}),Ge=["aria-label","aria-describedby"],Xe=["aria-label"],We=["id"];function Ze(e,n,a,i,l,o){const d=T("el-icon"),f=T("close"),y=T("el-input"),s=T("el-button"),N=T("el-focus-trap"),F=T("el-overlay");return c(),v(Me,{name:"fade-in-linear",onAfterLeave:n[11]||(n[11]=u=>e.$emit("vanish")),persisted:""},{default:p(()=>[U(C(F,{"z-index":e.zIndex,"overlay-class":[e.ns.is("message-box"),e.modalClass],mask:e.modal},{default:p(()=>[m("div",{role:"dialog","aria-label":e.title,"aria-modal":"true","aria-describedby":e.showInput?void 0:e.contentId,class:r(`${e.ns.namespace.value}-overlay-message-box`),onClick:n[8]||(n[8]=(...u)=>e.overlayEvent.onClick&&e.overlayEvent.onClick(...u)),onMousedown:n[9]||(n[9]=(...u)=>e.overlayEvent.onMousedown&&e.overlayEvent.onMousedown(...u)),onMouseup:n[10]||(n[10]=(...u)=>e.overlayEvent.onMouseup&&e.overlayEvent.onMouseup(...u))},[C(N,{loop:"",trapped:e.visible,"focus-trap-el":e.rootRef,"focus-start-el":e.focusStartRef,onReleaseRequested:e.onCloseRequested},{default:p(()=>[m("div",{ref:"rootRef",class:r([e.ns.b(),e.customClass,e.ns.is("draggable",e.draggable),{[e.ns.m("center")]:e.center}]),style:x(e.customStyle),tabindex:"-1",onClick:n[7]||(n[7]=z(()=>{},["stop"]))},[e.title!==null&&e.title!==void 0?(c(),H("div",{key:0,ref:"headerRef",class:r(e.ns.e("header"))},[m("div",{class:r(e.ns.e("title"))},[e.iconComponent&&e.center?(c(),v(d,{key:0,class:r([e.ns.e("status"),e.typeClass])},{default:p(()=>[(c(),v(P(e.iconComponent)))]),_:1},8,["class"])):S("v-if",!0),m("span",null,R(e.title),1)],2),e.showClose?(c(),H("button",{key:0,type:"button",class:r(e.ns.e("headerbtn")),"aria-label":e.t("el.messagebox.close"),onClick:n[0]||(n[0]=u=>e.handleAction(e.distinguishCancelAndClose?"close":"cancel")),onKeydown:n[1]||(n[1]=D(z(u=>e.handleAction(e.distinguishCancelAndClose?"close":"cancel"),["prevent"]),["enter"]))},[C(d,{class:r(e.ns.e("close"))},{default:p(()=>[C(f)]),_:1},8,["class"])],42,Xe)):S("v-if",!0)],2)):S("v-if",!0),m("div",{id:e.contentId,class:r(e.ns.e("content"))},[m("div",{class:r(e.ns.e("container"))},[e.iconComponent&&!e.center&&e.hasMessage?(c(),v(d,{key:0,class:r([e.ns.e("status"),e.typeClass])},{default:p(()=>[(c(),v(P(e.iconComponent)))]),_:1},8,["class"])):S("v-if",!0),e.hasMessage?(c(),H("div",{key:1,class:r(e.ns.e("message"))},[we(e.$slots,"default",{},()=>[e.dangerouslyUseHTMLString?(c(),v(P(e.showInput?"label":"p"),{key:1,for:e.showInput?e.inputId:void 0,innerHTML:e.message},null,8,["for","innerHTML"])):(c(),v(P(e.showInput?"label":"p"),{key:0,for:e.showInput?e.inputId:void 0},{default:p(()=>[K(R(e.dangerouslyUseHTMLString?"":e.message),1)]),_:1},8,["for"]))])],2)):S("v-if",!0)],2),U(m("div",{class:r(e.ns.e("input"))},[C(y,{id:e.inputId,ref:"inputRef",modelValue:e.inputValue,"onUpdate:modelValue":n[2]||(n[2]=u=>e.inputValue=u),type:e.inputType,placeholder:e.inputPlaceholder,"aria-invalid":e.validateError,class:r({invalid:e.validateError}),onKeydown:D(e.handleInputEnter,["enter"])},null,8,["id","modelValue","type","placeholder","aria-invalid","class","onKeydown"]),m("div",{class:r(e.ns.e("errormsg")),style:x({visibility:e.editorErrorMessage?"visible":"hidden"})},R(e.editorErrorMessage),7)],2),[[q,e.showInput]])],10,We),m("div",{class:r(e.ns.e("btns"))},[e.showCancelButton?(c(),v(s,{key:0,loading:e.cancelButtonLoading,class:r([e.cancelButtonClass]),round:e.roundButton,size:e.btnSize,onClick:n[3]||(n[3]=u=>e.handleAction("cancel")),onKeydown:n[4]||(n[4]=D(z(u=>e.handleAction("cancel"),["prevent"]),["enter"]))},{default:p(()=>[K(R(e.cancelButtonText||e.t("el.messagebox.cancel")),1)]),_:1},8,["loading","class","round","size"])):S("v-if",!0),U(C(s,{ref:"confirmRef",type:"primary",loading:e.confirmButtonLoading,class:r([e.confirmButtonClasses]),round:e.roundButton,disabled:e.confirmButtonDisabled,size:e.btnSize,onClick:n[5]||(n[5]=u=>e.handleAction("confirm")),onKeydown:n[6]||(n[6]=D(z(u=>e.handleAction("confirm"),["prevent"]),["enter"]))},{default:p(()=>[K(R(e.confirmButtonText||e.t("el.messagebox.confirm")),1)]),_:1},8,["loading","class","round","disabled","size"]),[[q,e.showConfirmButton]])],2)],6)]),_:3},8,["trapped","focus-trap-el","focus-start-el","onReleaseRequested"])],42,Ge)]),_:3},8,["z-index","overlay-class","mask"]),[[q,e.visible]])]),_:3})}var Je=Ve(je,[["render",Ze],["__file","/home/<USER>/work/element-plus/element-plus/packages/components/message-box/src/index.vue"]]);const L=new Map,Qe=e=>{let n=document.body;return e.appendTo&&(le(e.appendTo)&&(n=document.querySelector(e.appendTo)),ne(e.appendTo)&&(n=e.appendTo),ne(n)||(n=document.body)),n},Ye=(e,n,a=null)=>{const i=C(Je,e,ee(e.message)||re(e.message)?{default:ee(e.message)?e.message:()=>e.message}:null);return i.appContext=a,ie(i,n),Qe(e).appendChild(n.firstElementChild),i.component},xe=()=>document.createElement("div"),_e=(e,n)=>{const a=xe();e.onVanish=()=>{ie(null,a),L.delete(l)},e.onAction=o=>{const d=L.get(l);let f;e.showInput?f={value:l.inputValue,action:o}:f=o,e.callback?e.callback(f,i.proxy):o==="cancel"||o==="close"?e.distinguishCancelAndClose&&o!=="cancel"?d.reject("close"):d.reject("cancel"):d.resolve(f)};const i=Ye(e,a,n),l=i.proxy;for(const o in e)_(e,o)&&!_(l.$props,o)&&(l[o]=e[o]);return l.visible=!0,l};function k(e,n=null){if(!Pe)return Promise.reject();let a;return le(e)||re(e)?e={message:e}:a=e.callback,new Promise((i,l)=>{const o=_e(e,n??k._context);L.set(o,{options:e,callback:a,resolve:i,reject:l})})}const en=["alert","confirm","prompt"],nn={alert:{closeOnPressEscape:!1,closeOnClickModal:!1},confirm:{showCancelButton:!0},prompt:{showCancelButton:!0,showInput:!0}};en.forEach(e=>{k[e]=sn(e)});function sn(e){return(n,a,i,l)=>{let o="";return Te(a)?(i=a,o=""):Se(a)?o="":o=a,k(Object.assign({title:o,message:n,type:"",...nn[e]},i,{boxType:e}),l)}}k.close=()=>{L.forEach((e,n)=>{n.doClose()}),L.clear()};k._context=null;const b=k;b.install=e=>{b._context=e._context,e.config.globalProperties.$msgbox=b,e.config.globalProperties.$messageBox=b,e.config.globalProperties.$alert=b.alert,e.config.globalProperties.$confirm=b.confirm,e.config.globalProperties.$prompt=b.prompt};const cn=b;export{cn as E};
