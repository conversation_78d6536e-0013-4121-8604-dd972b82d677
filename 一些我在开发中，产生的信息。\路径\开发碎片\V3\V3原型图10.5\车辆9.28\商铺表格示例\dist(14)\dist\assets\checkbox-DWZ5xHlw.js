import{aJ as de,aK as be,aL as ce,ag as J,l as O,i as U,ah as N,y as I,k as h,a7 as z,m as Q,G as W,L as X,r as D,a0 as E,al as ve,aM as A,d as L,a2 as H,j as R,o as C,p as Y,w as Z,a as q,t as x,u as t,X as $,c as y,Q as V,M as w,ab as F,s as T,N as me,R as _,S as ee,K as P,q as ae,n as fe,f as ke,h as pe,x as he,a4 as ge,v as xe,O as le}from"./index-C0QCllTd.js";import{U as G,d as ne}from"./input-DqmydyK4.js";import{K as Ce,k as K,c as j,L as te,_ as M}from"./base-kpSIrADU.js";import{h as ye,i as Se}from"./select-BOcQ2ynX.js";import{f as Le}from"./flatten-BP0fiJV-.js";import{s as Be,o as Ee}from"./_commonjsHelpers-BbMlrU8H.js";function Ie(e){return Be(Ee(e,void 0,Le),e+"")}function $e(e,i,u){for(var a=-1,k=i.length,c={};++a<k;){var v=i[a],o=de(e,v);u(o,v)&&be(c,ce(v,e),o)}return c}function Ve(e,i){return $e(e,i,function(u,a){return ye(e,a)})}var we=Ie(function(e,i){return e==null?{}:Ve(e,i)});const oe={modelValue:{type:[Number,String,Boolean],default:void 0},label:{type:[String,Boolean,Number,Object]},indeterminate:Boolean,disabled:Boolean,checked:Boolean,name:{type:String,default:void 0},trueLabel:{type:[String,Number],default:void 0},falseLabel:{type:[String,Number],default:void 0},id:{type:String,default:void 0},controls:{type:String,default:void 0},border:Boolean,size:J,tabindex:[String,Number],validateEvent:{type:Boolean,default:!0}},se={[G]:e=>O(e)||U(e)||N(e),change:e=>O(e)||U(e)||N(e)},B=Symbol("checkboxGroupContextKey"),Fe=({model:e,isChecked:i})=>{const u=I(B,void 0),a=h(()=>{var c,v;const o=(c=u==null?void 0:u.max)==null?void 0:c.value,m=(v=u==null?void 0:u.min)==null?void 0:v.value;return!z(o)&&e.value.length>=o&&!i.value||!z(m)&&e.value.length<=m&&i.value});return{isDisabled:Ce(h(()=>(u==null?void 0:u.disabled.value)||a.value)),isLimitDisabled:a}},Ge=(e,{model:i,isLimitExceeded:u,hasOwnLabel:a,isDisabled:k,isLabeledByFormItem:c})=>{const v=I(B,void 0),{formItem:o}=K(),{emit:m}=X();function l(n){var d,b;return n===e.trueLabel||n===!0?(d=e.trueLabel)!=null?d:!0:(b=e.falseLabel)!=null?b:!1}function f(n,d){m("change",l(n),d)}function p(n){if(u.value)return;const d=n.target;m("change",l(d.checked),n)}async function S(n){u.value||!a.value&&!k.value&&c.value&&(n.composedPath().some(r=>r.tagName==="LABEL")||(i.value=l([!1,e.falseLabel].includes(i.value)),await W(),f(i.value,n)))}const s=h(()=>(v==null?void 0:v.validateEvent)||e.validateEvent);return Q(()=>e.modelValue,()=>{s.value&&(o==null||o.validate("change").catch(n=>ne()))}),{handleChange:p,onClickRoot:S}},Ne=e=>{const i=D(!1),{emit:u}=X(),a=I(B,void 0),k=h(()=>z(a)===!1),c=D(!1);return{model:h({get(){var o,m;return k.value?(o=a==null?void 0:a.modelValue)==null?void 0:o.value:(m=e.modelValue)!=null?m:i.value},set(o){var m,l;k.value&&E(o)?(c.value=((m=a==null?void 0:a.max)==null?void 0:m.value)!==void 0&&o.length>(a==null?void 0:a.max.value),c.value===!1&&((l=a==null?void 0:a.changeEvent)==null||l.call(a,o))):(u(G,o),i.value=o)}}),isGroup:k,isLimitExceeded:c}},ze=(e,i,{model:u})=>{const a=I(B,void 0),k=D(!1),c=h(()=>{const l=u.value;return N(l)?l:E(l)?ve(e.label)?l.map(A).some(f=>Se(f,e.label)):l.map(A).includes(e.label):l!=null?l===e.trueLabel:!!l}),v=j(h(()=>{var l;return(l=a==null?void 0:a.size)==null?void 0:l.value}),{prop:!0}),o=j(h(()=>{var l;return(l=a==null?void 0:a.size)==null?void 0:l.value})),m=h(()=>!!(i.default||e.label));return{checkboxButtonSize:v,isChecked:c,isFocused:k,checkboxSize:o,hasOwnLabel:m}},De=(e,{model:i})=>{function u(){E(i.value)&&!i.value.includes(e.label)?i.value.push(e.label):i.value=e.trueLabel||!0}e.checked&&u()},ie=(e,i)=>{const{formItem:u}=K(),{model:a,isGroup:k,isLimitExceeded:c}=Ne(e),{isFocused:v,isChecked:o,checkboxButtonSize:m,checkboxSize:l,hasOwnLabel:f}=ze(e,i,{model:a}),{isDisabled:p}=Fe({model:a,isChecked:o}),{inputId:S,isLabeledByFormItem:s}=te(e,{formItemContext:u,disableIdGeneration:f,disableIdManagement:k}),{handleChange:n,onClickRoot:d}=Ge(e,{model:a,isLimitExceeded:c,hasOwnLabel:f,isDisabled:p,isLabeledByFormItem:s});return De(e,{model:a}),{inputId:S,isLabeledByFormItem:s,isChecked:o,isDisabled:p,isFocused:v,checkboxButtonSize:m,checkboxSize:l,hasOwnLabel:f,model:a,handleChange:n,onClickRoot:d}},Pe=["tabindex","role","aria-checked"],Re=["id","aria-hidden","name","tabindex","disabled","true-value","false-value"],Te=["id","aria-hidden","disabled","value","name","tabindex"],Ke=L({name:"ElCheckbox"}),Me=L({...Ke,props:oe,emits:se,setup(e){const i=e,u=H(),{inputId:a,isLabeledByFormItem:k,isChecked:c,isDisabled:v,isFocused:o,checkboxSize:m,hasOwnLabel:l,model:f,handleChange:p,onClickRoot:S}=ie(i,u),s=R("checkbox"),n=h(()=>[s.b(),s.m(m.value),s.is("disabled",v.value),s.is("bordered",i.border),s.is("checked",c.value)]),d=h(()=>[s.e("input"),s.is("disabled",v.value),s.is("checked",c.value),s.is("indeterminate",i.indeterminate),s.is("focus",o.value)]);return(b,r)=>(C(),Y(ae(!t(l)&&t(k)?"span":"label"),{class:x(t(n)),"aria-controls":b.indeterminate?b.controls:null,onClick:t(S)},{default:Z(()=>[q("span",{class:x(t(d)),tabindex:b.indeterminate?0:void 0,role:b.indeterminate?"checkbox":void 0,"aria-checked":b.indeterminate?"mixed":void 0},[b.trueLabel||b.falseLabel?$((C(),y("input",{key:0,id:t(a),"onUpdate:modelValue":r[0]||(r[0]=g=>V(f)?f.value=g:null),class:x(t(s).e("original")),type:"checkbox","aria-hidden":b.indeterminate?"true":"false",name:b.name,tabindex:b.tabindex,disabled:t(v),"true-value":b.trueLabel,"false-value":b.falseLabel,onChange:r[1]||(r[1]=(...g)=>t(p)&&t(p)(...g)),onFocus:r[2]||(r[2]=g=>o.value=!0),onBlur:r[3]||(r[3]=g=>o.value=!1),onClick:r[4]||(r[4]=w(()=>{},["stop"]))},null,42,Re)),[[F,t(f)]]):$((C(),y("input",{key:1,id:t(a),"onUpdate:modelValue":r[5]||(r[5]=g=>V(f)?f.value=g:null),class:x(t(s).e("original")),type:"checkbox","aria-hidden":b.indeterminate?"true":"false",disabled:t(v),value:b.label,name:b.name,tabindex:b.tabindex,onChange:r[6]||(r[6]=(...g)=>t(p)&&t(p)(...g)),onFocus:r[7]||(r[7]=g=>o.value=!0),onBlur:r[8]||(r[8]=g=>o.value=!1),onClick:r[9]||(r[9]=w(()=>{},["stop"]))},null,42,Te)),[[F,t(f)]]),q("span",{class:x(t(s).e("inner"))},null,2)],10,Pe),t(l)?(C(),y("span",{key:0,class:x(t(s).e("label"))},[T(b.$slots,"default"),b.$slots.default?P("v-if",!0):(C(),y(me,{key:0},[_(ee(b.label),1)],64))],2)):P("v-if",!0)]),_:3},8,["class","aria-controls","onClick"]))}});var Oe=M(Me,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/checkbox/src/checkbox.vue"]]);const Ue=["name","tabindex","disabled","true-value","false-value"],Ae=["name","tabindex","disabled","value"],qe=L({name:"ElCheckboxButton"}),je=L({...qe,props:oe,emits:se,setup(e){const i=e,u=H(),{isFocused:a,isChecked:k,isDisabled:c,checkboxButtonSize:v,model:o,handleChange:m}=ie(i,u),l=I(B,void 0),f=R("checkbox"),p=h(()=>{var s,n,d,b;const r=(n=(s=l==null?void 0:l.fill)==null?void 0:s.value)!=null?n:"";return{backgroundColor:r,borderColor:r,color:(b=(d=l==null?void 0:l.textColor)==null?void 0:d.value)!=null?b:"",boxShadow:r?`-1px 0 0 0 ${r}`:void 0}}),S=h(()=>[f.b("button"),f.bm("button",v.value),f.is("disabled",c.value),f.is("checked",k.value),f.is("focus",a.value)]);return(s,n)=>(C(),y("label",{class:x(t(S))},[s.trueLabel||s.falseLabel?$((C(),y("input",{key:0,"onUpdate:modelValue":n[0]||(n[0]=d=>V(o)?o.value=d:null),class:x(t(f).be("button","original")),type:"checkbox",name:s.name,tabindex:s.tabindex,disabled:t(c),"true-value":s.trueLabel,"false-value":s.falseLabel,onChange:n[1]||(n[1]=(...d)=>t(m)&&t(m)(...d)),onFocus:n[2]||(n[2]=d=>a.value=!0),onBlur:n[3]||(n[3]=d=>a.value=!1),onClick:n[4]||(n[4]=w(()=>{},["stop"]))},null,42,Ue)),[[F,t(o)]]):$((C(),y("input",{key:1,"onUpdate:modelValue":n[5]||(n[5]=d=>V(o)?o.value=d:null),class:x(t(f).be("button","original")),type:"checkbox",name:s.name,tabindex:s.tabindex,disabled:t(c),value:s.label,onChange:n[6]||(n[6]=(...d)=>t(m)&&t(m)(...d)),onFocus:n[7]||(n[7]=d=>a.value=!0),onBlur:n[8]||(n[8]=d=>a.value=!1),onClick:n[9]||(n[9]=w(()=>{},["stop"]))},null,42,Ae)),[[F,t(o)]]),s.$slots.default||s.label?(C(),y("span",{key:2,class:x(t(f).be("button","inner")),style:fe(t(k)?t(p):void 0)},[T(s.$slots,"default",{},()=>[_(ee(s.label),1)])],6)):P("v-if",!0)],2))}});var ue=M(je,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/checkbox/src/checkbox-button.vue"]]);const Je=ke({modelValue:{type:pe(Array),default:()=>[]},disabled:Boolean,min:Number,max:Number,size:J,label:String,fill:String,textColor:String,tag:{type:String,default:"div"},validateEvent:{type:Boolean,default:!0}}),Qe={[G]:e=>E(e),change:e=>E(e)},We=L({name:"ElCheckboxGroup"}),Xe=L({...We,props:Je,emits:Qe,setup(e,{emit:i}){const u=e,a=R("checkbox"),{formItem:k}=K(),{inputId:c,isLabeledByFormItem:v}=te(u,{formItemContext:k}),o=async l=>{i(G,l),await W(),i("change",l)},m=h({get(){return u.modelValue},set(l){o(l)}});return he(B,{...we(ge(u),["size","min","max","disabled","validateEvent","fill","textColor"]),modelValue:m,changeEvent:o}),Q(()=>u.modelValue,()=>{u.validateEvent&&(k==null||k.validate("change").catch(l=>ne()))}),(l,f)=>{var p;return C(),Y(ae(l.tag),{id:t(c),class:x(t(a).b("group")),role:"group","aria-label":t(v)?void 0:l.label||"checkbox-group","aria-labelledby":t(v)?(p=t(k))==null?void 0:p.labelId:void 0},{default:Z(()=>[T(l.$slots,"default")]),_:3},8,["id","class","aria-label","aria-labelledby"])}}});var re=M(Xe,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/checkbox/src/checkbox-group.vue"]]);const la=xe(Oe,{CheckboxButton:ue,CheckboxGroup:re});le(ue);const na=le(re);export{la as E,na as a};
