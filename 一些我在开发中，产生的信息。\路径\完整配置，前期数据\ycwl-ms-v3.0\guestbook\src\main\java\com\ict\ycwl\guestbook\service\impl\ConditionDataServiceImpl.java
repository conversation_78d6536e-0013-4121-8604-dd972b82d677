package com.ict.ycwl.guestbook.service.impl;

import com.ict.ycwl.guestbook.api.vo.ConditionSingleDataVo;
import com.ict.ycwl.guestbook.domain.Store;
import com.ict.ycwl.guestbook.mapper.StoreMapper;
import com.ict.ycwl.guestbook.service.ConditionDataService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ConditionDataServiceImpl implements ConditionDataService {

    @Autowired
    private StoreMapper storeMapper;


    @Override
    public ConditionSingleDataVo getDataByStore(String customerCode) {
        Store store = storeMapper.selectSingleForCondition(customerCode);
        ConditionSingleDataVo conditionSingleDataVo = new ConditionSingleDataVo();
        BeanUtils.copyProperties(store,conditionSingleDataVo);
        return conditionSingleDataVo;
    }
}
