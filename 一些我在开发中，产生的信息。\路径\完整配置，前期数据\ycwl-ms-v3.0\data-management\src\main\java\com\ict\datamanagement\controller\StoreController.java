package com.ict.datamanagement.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ict.datamanagement.common.BaseResponse;
import com.ict.datamanagement.common.ResultUtils;
import com.ict.datamanagement.common.StatusCode;
import com.ict.datamanagement.domain.dto.store.StoreAddRequest;
import com.ict.datamanagement.domain.dto.store.StoreUpdateRequest;
import com.ict.datamanagement.domain.dto.store.StoreListRequest;
import com.ict.datamanagement.domain.dto.store.UpdateAreaRequest;
import com.ict.datamanagement.domain.entity.Area;
import com.ict.datamanagement.domain.entity.Store;
import com.ict.datamanagement.domain.vo.StoreVO;
import com.ict.datamanagement.exception.BusinessException;
import com.ict.datamanagement.service.AreaService;
import com.ict.datamanagement.service.StoreService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Api(tags = "商铺管理")
@Slf4j
@RestController("/store")
public class StoreController {

    @Resource
    private StoreService storeService;

    @Resource
    private AreaService areaService;

    @ApiOperation("商铺分页列表")
    @GetMapping("/list")
    public BaseResponse<Page<Store>> list(StoreListRequest storeListRequest) {
        QueryWrapper<Store> queryWrapper = storeService.getQueryWrapper(storeListRequest);
        int pageNum = storeListRequest.getPageNum();
        int pageSize = storeListRequest.getPageSize();
        Page<Store> page = storeService.page(new Page<>(pageNum, pageSize), queryWrapper);
        return ResultUtils.success(page);
    }

    @ApiOperation("添加商铺")
    @PostMapping("/add")
    public BaseResponse<String> add(@RequestBody StoreAddRequest storeAddRequest) {
        //todo 参数校验
        Store store = new Store();
        BeanUtils.copyProperties(storeAddRequest, store);
        if (storeAddRequest.getLocationType().equals("城区")){
            store.setLocationType("0");
        }else if (storeAddRequest.getLocationType().equals("乡镇")){
            store.setLocationType("1");
        }else {
            throw new BusinessException(StatusCode.Parameter_ERROR);
        }

        boolean save = storeService.save(store);
        if (!save) {
            throw new BusinessException(StatusCode.SYSTEM_ERROR);
        }
        return ResultUtils.success();
    }

    @ApiOperation("获取商铺详细信息")
    @GetMapping("/get/{storeId}")
    public BaseResponse<StoreVO> get(@PathVariable("storeId") Long storeId) {
        Store store = storeService.getById(storeId);
        StoreVO storeVO = storeService.getStoreVO(store);
        return ResultUtils.success(storeVO);
    }

    @ApiOperation("更新商铺信息")
    @PostMapping("/update")
    public BaseResponse<String> add(@RequestBody StoreUpdateRequest storeUpdateRequest) {
        //todo 参数校验
        Store store = new Store();
        BeanUtils.copyProperties(storeUpdateRequest, store);
        boolean update = storeService.updateById(store);
        if (!update) {
            throw new BusinessException(StatusCode.SYSTEM_ERROR);
        }

        return ResultUtils.success();
    }

    @ApiOperation("批量删除商铺")
    @DeleteMapping("/delete/{storeIdList}")
    public BaseResponse<String> delete(@PathVariable("storeIdList") List<Long> storeIdList) {
        boolean remove = storeService.removeBatchByIds(storeIdList);
        if (!remove) {
            throw new BusinessException(StatusCode.SYSTEM_ERROR);
        }

        return ResultUtils.success();
    }


    @ApiOperation("修改商铺所属行政区")
    @PostMapping("/updateArea")
    public BaseResponse<String> updateArea(@RequestBody UpdateAreaRequest updateAreaRequest) {
        List<Long> storeIdList = updateAreaRequest.getStoreIdList();
        Long areaId = updateAreaRequest.getAreaId();
        String areaName = areaService.getById(areaId).getAreaName();

        // 参数校验
        Long areaTotalId = 0L;
        for (int i = 0; i    < storeIdList.size(); i++) {
            Store store = storeService.getById(storeIdList.get(i));
            if (store==null){
                throw new BusinessException(StatusCode.Parameter_ERROR);
            }
            Long areaOldId = store.getAreaId();
            if (areaOldId == null) {
                throw new BusinessException(StatusCode.SYSTEM_ERROR);
            }
            if (i == 0) {
                areaTotalId = areaOldId;
            } else {
                if (!areaTotalId.equals(areaOldId)) {
                    throw new BusinessException(StatusCode.Parameter_ERROR);
                }
            }
        }

        boolean update = false;
        for (Long id : storeIdList) {
            Store store = Store.builder().storeId(id).areaName(areaName).areaId(areaId).build();
            update = storeService.updateById(store);
        }

        if (!update) {
            throw new BusinessException(StatusCode.SYSTEM_ERROR);
        }

        return ResultUtils.success();
    }


}
