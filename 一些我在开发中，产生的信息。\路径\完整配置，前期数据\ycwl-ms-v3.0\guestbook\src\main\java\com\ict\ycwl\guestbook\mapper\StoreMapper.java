package com.ict.ycwl.guestbook.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ict.ycwl.guestbook.api.vo.ConditionStoreVo;
import com.ict.ycwl.guestbook.domain.Store;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface StoreMapper extends BaseMapper<Store> {

    List<ConditionStoreVo> selectAllForCondition();

    Store selectSingleForCondition(String customerCode);

}
