package com.ict.ycwl.pathcalculate.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("route")
public class Route {

    @TableId(type = IdType.ASSIGN_ID)
    private Long routeId;

    private String routeName;

    private String distance;

    private Long transitDepotId;

    private Long areaId;

    private String polyline;

    private Timestamp createTime;

    private Timestamp updateTime;

    private Boolean isDelete;

    private String cargoWeight;

    private Integer versionNumber;

    private String convex;

    private String workTime;
}
