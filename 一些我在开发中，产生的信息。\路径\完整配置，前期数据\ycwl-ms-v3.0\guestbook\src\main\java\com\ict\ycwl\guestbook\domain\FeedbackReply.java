package com.ict.ycwl.guestbook.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data

@Builder
@AllArgsConstructor
@NoArgsConstructor
public class FeedbackReply {

    @TableId(type = IdType.ASSIGN_ID)
    private Long replyId;

    private String replyContent;

    private Long createBy;

    private LocalDateTime createTime;

    private String replyType;

    private Long feedbackId;
}
