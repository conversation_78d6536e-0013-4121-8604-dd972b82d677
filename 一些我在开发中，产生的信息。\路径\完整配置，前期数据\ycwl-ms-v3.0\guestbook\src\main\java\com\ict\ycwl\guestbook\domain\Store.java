package com.ict.ycwl.guestbook.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

@Data
public class Store {

    @TableId(type = IdType.ASSIGN_ID)
    private Long storeId;

    private String customerCode;

    private String storeName;

    private String storeAddress;

    private String longitude;

    private String latitude;

    private String type;

    private String orderCycle;

    private String district;

    private String areaName;

    private String contactName;

    private String contactPhone;

    private String status;

    private Long customerManagerId;

    private String customerManagerName;

    private Long areaId;

    private Long routeId;

    private String routeName;
}
