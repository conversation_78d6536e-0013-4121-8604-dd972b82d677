package com.ict.ycwl.pathcalculate.vo;

import com.ict.ycwl.pathcalculate.vo.details.RouteDetailsVO;
import com.ict.ycwl.pathcalculate.vo.details.TransitDepotDetailsVO;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class GroupDataVO {

    /**
     * 中转站Id
     */
    Long groupId;

    /**
     * 中转站名称
     */
    String groupName;


    /**
     * 车辆数
     */
    Integer carCount;

    /**
     * 路线总数
     */
    Integer routeCount;

    /**
     * 大区下路线集合
     */
    List<TransitDepotDetailsVO> list;
}
