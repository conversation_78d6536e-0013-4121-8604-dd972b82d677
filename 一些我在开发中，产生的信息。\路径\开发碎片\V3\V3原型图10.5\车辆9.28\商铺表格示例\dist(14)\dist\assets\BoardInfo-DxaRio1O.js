import{g as ye,r as Ne,p as ae,h as le,j as he,E as oe,z as ce}from"./base-kpSIrADU.js";import{E as Ce,a as Ie,b as Ve}from"./table-column-DZpqkK6R.js";import{E as ne}from"./input-DqmydyK4.js";import{E as me,a as pe}from"./select-BOcQ2ynX.js";import{E as we}from"./scrollbar-BNeK4Yi-.js";import{d as J,z as Se,r as _,c as h,a as m,t as ee,u as e,S as O,b as t,w as l,p as z,K as X,o as f,N as $,T as L,Q as G,R as Y,Z as W,$ as fe,X as De,m as H,P as te,V as Re}from"./index-C0QCllTd.js";import{u as K}from"./board-ZJ5DF_V6.js";import{E as Z}from"./button-IGKrEYb9.js";import{E as re,a as se}from"./form-item-Bd-FvCZ5.js";import{E as _e}from"./date-picker-C-6M_J1A.js";import{_ as Q}from"./_plugin-vue_export-helper-DlAUqK2U.js";/* empty css                */import"./checkbox-DWZ5xHlw.js";/* empty css             */import{v as xe}from"./directive-BBeDU6Ak.js";import{E as ue}from"./overlay-D06mCCGK.js";import{E as be}from"./progress-BWKU0l_-.js";import{a as ie}from"./index-m25zEilF.js";/* empty css                   */import{E as ve}from"./index-DJDAALh9.js";import{E as Te,a as Ue}from"./radio-BnYb1uhH.js";import{E as $e}from"./el-overlay-CJCFAIWq.js";import"./_commonjsHelpers-BbMlrU8H.js";import"./merge-B3fxVp56.js";import"./_initCloneObject-BmTtMqsv.js";import"./flatten-BP0fiJV-.js";import"./castArray-CSO3s-vM.js";import"./index-1tmHbbca.js";import"./index-DOdSMika.js";const Ae={class:"InfoSearch"},Ee={class:"title"},Fe={class:"search-content"},Me={class:"search-btn"},Le={class:"btn-content"},ze=J({__name:"InfoSearch",props:{feedbackType:{}},emits:["itemAdd","itemSearch","itemReset","itemState","itemDelete"],setup(q,{expose:T,emit:P}){const p=K(),I=P,k=q;Se(()=>{p.getCondAction()});const C=[{label:"全部",value:"",class:"sRound round all"},{label:"未处理",value:"0",class:"sRound round notProcessed"},{label:"处理中",value:"1",class:"sRound round dispose"},{label:"已处理",value:"2",class:"sRound round processed"},{label:"无需处理",value:"3",class:"sRound round notDispose"}];function S(D){switch(D){case"0":return"notProcessed";case"1":return"dispose";case"2":return"processed";case"3":return"notDispose";case"":return"all"}}const i=_({contactName:"",areaName:"",customerCodeL:"",feedbackStatus:"",deliveryWorkNumber:"",customerManagerName:"",orderEndDate:"",orderStartDate:"",routeName:""}),y=_();function U(){y.value==null?(i.value.orderStartDate="",i.value.orderEndDate=""):(i.value.orderStartDate=y.value[0],i.value.orderEndDate=y.value[1])}function a(){I("itemState",{...i.value})}function M(){I("itemSearch",{...i.value})}function b(){i.value={contactName:"",areaName:"",customerCodeL:"",feedbackStatus:"",deliveryWorkNumber:"",customerManagerName:"",orderEndDate:"",orderStartDate:"",routeName:""},y.value="",I("itemReset")}function A(){I("itemAdd")}function x(){I("itemDelete")}return T({searchForm:i}),(D,d)=>{const E=pe,v=me,u=_e,o=se,s=ne,N=re,V=Z;return f(),h("div",Ae,[m("div",Ee,[m("div",{class:ee(["round",S(e(i).feedbackStatus)])},null,2),m("p",null,O(k.feedbackType=="1"?"物流反馈":"营销反馈"),1),t(v,{modelValue:e(i).feedbackStatus,"onUpdate:modelValue":d[0]||(d[0]=n=>e(i).feedbackStatus=n),size:"small",onChange:a},{default:l(()=>[(f(),h($,null,L(C,n=>t(E,{key:n.value,label:n.label,value:n.value},{default:l(()=>[m("div",{class:ee(n.class)},null,2),m("span",null,O(n.label),1)]),_:2},1032,["label","value"])),64))]),_:1},8,["modelValue"])]),m("div",Fe,[t(N,{inline:!0,"label-width":"100px",model:e(i)},{default:l(()=>[t(o,{label:"订单时间",class:"block"},{default:l(()=>[t(u,{modelValue:e(y),"onUpdate:modelValue":d[1]||(d[1]=n=>G(y)?y.value=n:null),"value-format":"YYYY-MM-DD hh:mm:ss",type:"datetimerange","start-placeholder":"开始时间","end-placeholder":"结束时间",onChange:U},null,8,["modelValue"])]),_:1}),t(o,{label:"客户编码"},{default:l(()=>[t(s,{modelValue:e(i).customerCode,"onUpdate:modelValue":d[2]||(d[2]=n=>e(i).customerCode=n),placeholder:"点击输入"},null,8,["modelValue"])]),_:1}),t(o,{label:"客户名称"},{default:l(()=>[t(s,{modelValue:e(i).contactName,"onUpdate:modelValue":d[3]||(d[3]=n=>e(i).contactName=n),placeholder:"点击输入"},null,8,["modelValue"])]),_:1}),t(o,{label:"大区"},{default:l(()=>[t(v,{clearable:"",filterable:"",modelValue:e(i).areaName,"onUpdate:modelValue":d[4]||(d[4]=n=>e(i).areaName=n)},{default:l(()=>{var n;return[(f(!0),h($,null,L((n=e(p).cond)==null?void 0:n.areaList,r=>(f(),z(E,{key:r.areaId,value:r.areaName},null,8,["value"]))),128))]}),_:1},8,["modelValue"])]),_:1}),t(o,{label:"路线名称"},{default:l(()=>[t(s,{modelValue:e(i).routeName,"onUpdate:modelValue":d[5]||(d[5]=n=>e(i).routeName=n),placeholder:"点击输入"},null,8,["modelValue"])]),_:1}),t(o,{label:"客户专员"},{default:l(()=>[t(v,{clearable:"",filterable:"",modelValue:e(i).customerManagerName,"onUpdate:modelValue":d[6]||(d[6]=n=>e(i).customerManagerName=n)},{default:l(()=>{var n;return[(f(!0),h($,null,L((n=e(p).cond)==null?void 0:n.customerManagerList,r=>(f(),z(E,{key:r.workNumber,label:r.userName,value:r.userName},null,8,["label","value"]))),128))]}),_:1},8,["modelValue"])]),_:1}),t(o,{label:"送货员"},{default:l(()=>[t(v,{clearable:"",filterable:"",modelValue:e(i).deliveryWorkNumber,"onUpdate:modelValue":d[7]||(d[7]=n=>e(i).deliveryWorkNumber=n)},{default:l(()=>{var n;return[(f(!0),h($,null,L((n=e(p).cond)==null?void 0:n.deliveryUserList,r=>(f(),z(E,{key:r.workNumber,label:r.userName,value:r.workNumber},null,8,["label","value"]))),128))]}),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"]),m("div",Me,[t(V,{icon:e(ye),onClick:M},{default:l(()=>d[8]||(d[8]=[Y("搜索")])),_:1},8,["icon"]),t(V,{icon:e(Ne),onClick:b},{default:l(()=>d[9]||(d[9]=[Y("重置")])),_:1},8,["icon"])])]),m("div",Le,[e(W)("guest-book:logistics:exception:add")&&D.feedbackType==="1"||e(W)("guest-book:marketing:exception:add")&&D.feedbackType==="2"?(f(),z(V,{key:0,icon:e(ae),onClick:A},{default:l(()=>d[10]||(d[10]=[Y("添加异常信息")])),_:1},8,["icon"])):X("",!0),e(W)("guest-book:logistics:exception:delete")&&D.feedbackType==="1"||e(W)("guest-book:marketing:exception:delete")&&D.feedbackType==="2"?(f(),z(V,{key:1,icon:e(le),onClick:x},{default:l(()=>d[11]||(d[11]=[Y("批量删除")])),_:1},8,["icon"])):X("",!0)])])}}}),Pe=Q(ze,[["__scopeId","data-v-195bff41"]]),Be={class:"InfoTable"},Oe=J({__name:"InfoTable",props:{tableData:{default:()=>[]},feedbackType:{}},emits:["itemClick"],setup(q,{expose:T,emit:P}){const p=P,I=K(),{loading:k}=fe(I),C=q,S=({row:a})=>{if(a.feedbackStatus===0)return{background:"rgba(161, 86, 192,0.8)",textAlign:"center"};if(a.feedbackStatus===1)return{background:"rgba(190,174,58,0.8)",textAlign:"center"};if(a.feedbackStatus===3)return{background:"rgba(124,135,148,0.8)",textAlign:"center"};if(a)return{textAlign:"center"}};function i(a){p("itemClick",a)}const y=_();function U(a){y.value=a.map(M=>M.feedbackId.toString()).join(),I.UnhandledAmountAction()}return T({deleteData:y}),(a,M)=>{const b=Ie,A=oe,x=Z,D=Ce,d=xe;return f(),h("div",Be,[De((f(),z(D,{data:C.tableData,"header-cell-style":{height:"4vh","text-align":"center"},size:"small","row-style":{height:"4.3vh"},"cell-style":S,style:{"font-size":"0.8vw"},onSelectionChange:U},{default:l(()=>[e(W)("guest-book:logistics:exception:delete")&&a.feedbackType==="1"||e(W)("guest-book:marketing:exception:delete")&&a.feedbackType==="2"?(f(),z(b,{key:0,type:"selection",fixed:"","min-width":"3%"})):X("",!0),t(b,{type:"index",label:"序号","min-width":"3%"}),t(b,{prop:"areaName",label:"大区","min-width":"4%"}),t(b,{prop:"routeName",label:"线路名称","min-width":"5%"}),t(b,{prop:"deliveryName",label:"送货员","min-width":"4%"}),t(b,{prop:"customerManagerName",label:"客户专员","min-width":"5%"}),t(b,{prop:"customerCode",label:"客户编码","min-width":"5%"}),t(b,{prop:"contactName",label:"客户名称","min-width":"5%"}),t(b,{prop:"storeAddress",label:"客户地址","min-width":"20%"}),t(b,{prop:"feedbackInformation",label:"异常信息反馈","min-width":"8%"}),t(b,{prop:"orderDate",label:"订单时间","min-width":"10%"}),t(b,{prop:"updateTime",label:"最新回复时间","min-width":"10%"}),t(b,{label:"操作","min-width":"4%",fixed:"right"},{default:l(E=>[t(x,{link:"",size:"small",onClick:v=>i(E.row)},{default:l(()=>[t(A,{color:"rgb(204,255,255)",size:"15"},{default:l(()=>[t(e(he))]),_:1})]),_:2},1032,["onClick"])]),_:1})]),_:1},8,["data","cell-style"])),[[d,e(k)]])])}}}),We=Q(Oe,[["__scopeId","data-v-da045651"]]),qe={class:"InfoItem"},Ye={class:"my-header"},je={class:"el-dialog-div-top"},Ge={class:"el-dialog-div"},Je={class:"el-dialog-div-info"},Ke={class:"el-dialog-div-img"},Qe=["src"],Xe={key:0,class:"el-dialog-div-recover"},Ze={class:"reply-text"},He={class:"reply-date"},et={class:"reply-img"},tt=["src"],at=J({__name:"InfoItem",props:{feedbackType:{}},emits:["replyClick"],setup(q,{expose:T,emit:P}){const p=K(),I=P,k=_(!1),C=_(0),S=q,i={contactName:"客户名称",deliveryName:"送货员",orderDate:"订单日期",customerCode:"客户编号",customerManagerName:"客户专员",routeName:"送货路线",storeAddress:"客户地址"},y=_({contactName:"",deliveryName:"",orderDate:"",customerCode:"",customerManagerName:"",routeName:"",storeAddress:""}),U=_();function a(o){switch(o){case 0:return"notProcessed";case 1:return"dispose";case 2:return"processed";case 3:return"notDispose"}}function M(o){switch(o){case 0:return"未处理";case 1:return"处理中";case 2:return"已处理";case 3:return"无需处理"}}function b(o){return o.map(N=>"http://127.0.0.1:8080"+N)}const A=_([]),x=_(!0),D=_([]),d=_(0);function E(o){k.value=!0,C.value=o.feedbackId,d.value=o.feedbackStatus,U.value=o,p.getDetailData(C.value),H(()=>p.detail,s=>{A.value=s;let N;for(N in y.value)y.value[N]=o[N];D.value=b(o.feedbackFileList)}),U.value.feedbackStatus==2||U.value.feedbackStatus==3?x.value=!1:x.value=!0}function v(o,s){d.value=s,p.getDetailData(o),H(()=>p.detail,N=>{A.value=N}),(d.value==2||d.value==3)&&(x.value=!1)}T({handleOpen:E,getReplyData:v});function u(o){I("replyClick",o)}return(o,s)=>{const N=Z,V=we,n=ue;return f(),h("div",qe,[t(n,{modelValue:e(k),"onUpdate:modelValue":s[1]||(s[1]=r=>G(k)?k.value=r:null),width:"70%",top:"4%",title:"nihao"},{header:l(()=>{var r;return[m("div",Ye,[m("div",je,[m("div",{class:ee(["round",a(e(d))])},null,2),m("p",null,O((r=e(U))==null?void 0:r.feedbackInformation)+"  ("+O(M(e(d)))+") ",1)])])]}),default:l(()=>[m("div",Ge,[t(V,{class:"scroll"},{default:l(()=>[m("div",Je,[(f(!0),h($,null,L(e(y),(r,g)=>(f(),h("div",{key:g,class:"el-dialog-div-info-item"},O(i[g])+":  "+O(r),1))),128))]),m("div",Ke,[(f(!0),h($,null,L(e(D),r=>(f(),h("div",{key:r,class:"el-dialog-div-img-item"},[m("img",{src:r,alt:""},null,8,Qe)]))),128))]),e(x)&&(e(W)("guest-book:logistics:exception:reply")&&S.feedbackType==="1"||e(W)("guest-book:marketing:exception:reply")&&S.feedbackType==="2")?(f(),h("div",Xe,[s[3]||(s[3]=m("span",{class:"line"},null,-1)),t(N,{onClick:s[0]||(s[0]=r=>u(e(C)))},{default:l(()=>s[2]||(s[2]=[Y("点击回复")])),_:1}),s[4]||(s[4]=m("span",{class:"line"},null,-1))])):X("",!0),(f(!0),h($,null,L(e(A),r=>(f(),h($,{key:r.replyId},[m("div",Ze,[m("span",null,O(r.replyType=="1"?"送货部回复":"营销部回复")+":  "+O(r.replyContent||"没有回复消息"),1),m("div",He,O(r.createTime),1)]),m("div",et,[(f(!0),h($,null,L(b(r.replyFilePathList),g=>(f(),h("div",{key:g,class:"reply-img-item"},[m("img",{src:g,alt:""},null,8,tt)]))),128))])],64))),128))]),_:1})])]),_:1},8,["modelValue"])])}}}),lt=Q(at,[["__scopeId","data-v-9e539cbd"]]),ot={class:"InfoAdd"},nt={class:"dialog-content"},rt={class:"form-content"},st=["src"],ut={class:"el-upload-list__item-actions"},dt=["onClick"],it=["src"],ct=J({__name:"InfoAdd",emits:["addSuccess"],setup(q,{expose:T,emit:P}){const p=K(),{singleCondData:I}=fe(p),k=_(!1);function C(v){k.value=!0,a.value.feedbackType=v,p.getCondAction()}T({handleOpen:C});const S=P,i=_(),y=_(),U=te({customerCode:[{required:!0,message:"请选择客户编码",trigger:"blur"}],areaName:[{required:!0,message:"请选择大区名称",trigger:"blur"}],routeName:[{required:!0,message:"请选择路线名称",trigger:"blur"}],orderDate:[{required:!0,message:"请选择日期",trigger:"blur"}],deliveryName:[{required:!0,message:"请选择送货员",trigger:"blur"}],customerManagerName:[{required:!0,message:"请选择客户专员",trigger:"blur"}],feedbackInformation:[{required:!0,message:"请填写异常信息",trigger:"blur"}]}),a=_({areaName:"",customerCode:"",deliveryName:"",deliveryWorkNumber:"",feedbackInformation:"",feedbackType:"1",orderDate:"",routeId:0,routeName:"",contactName:"",customerManagerName:"",fileList:[]}),M=_(""),b=_(!1);async function A(v){p.singleCondDataAction(v).then(()=>{a.value.areaName=I.value.areaName,a.value.contactName=I.value.contactName,a.value.customerManagerName=I.value.customerManagerName,a.value.routeId=Number(I.value.routeId),a.value.routeName=I.value.routeName})}const x=async(v,u)=>{v&&u&&v.validate(()=>{}).then(o=>{u.validate((s,N)=>{if(s){if(o){const V=ve.service({lock:!0,text:"正在提交中",background:"rgba(0, 0, 0, 0.7)"});p.getCondAction().then(()=>{var B;const{customerManagerList:n,deliveryUserList:r}=p.cond;for(const w of r)w.userName==a.value.deliveryName&&(a.value.deliveryWorkNumber=w.workNumber);for(const w of n)w.userName==a.value.customerManagerName&&(a.value.managerWorkNumber=w.workNumber);const g=new FormData;g.append("areaName",a.value.areaName),g.append("customerCode",a.value.customerCode),g.append("deliveryName",a.value.deliveryName),g.append("deliveryWorkNumber",a.value.deliveryWorkNumber),g.append("managerWorkNumber",a.value.managerWorkNumber),g.append("feedbackInformation",a.value.feedbackInformation),g.append("feedbackType",a.value.feedbackType),g.append("routeId",a.value.routeId),g.append("routeName",a.value.routeName),g.append("customerManagerName",a.value.customerManagerName),g.append("orderDate",a.value.orderDate),(B=a.value.fileList)==null||B.forEach(w=>{g.append("fileList",w.raw)}),p.addFeedbackAction(g).then(()=>{V.close(),k.value=!1,S("addSuccess"),p.UnhandledAmountAction()})})}}else console.log("error submit!",N)})})},D=(v,u)=>{v&&u&&(v.resetFields(),u.resetFields(),a.value.fileList=[])},d=v=>{console.log(v),a.value.fileList.splice(a.value.fileList.indexOf(v),1)},E=v=>{M.value=v.url,b.value=!0};return(v,u)=>{const o=ne,s=se,N=pe,V=me,n=_e,r=re,g=oe,B=be,w=Z,j=ue;return f(),h($,null,[m("div",ot,[t(j,{modelValue:e(k),"onUpdate:modelValue":u[11]||(u[11]=c=>G(k)?k.value=c:null),width:"90%",onClosed:u[12]||(u[12]=c=>D(e(i),e(y)))},{default:l(()=>[m("div",nt,[m("div",rt,[t(r,{inline:!0,model:e(a),"label-width":"130",rules:e(U),ref_key:"ruleFormRef",ref:i},{default:l(()=>[t(s,{label:"客户编码",prop:"customerCode"},{default:l(()=>[t(o,{type:"text",modelValue:e(a).customerCode,"onUpdate:modelValue":u[0]||(u[0]=c=>e(a).customerCode=c),onBlur:u[1]||(u[1]=c=>A(e(a).customerCode))},null,8,["modelValue"])]),_:1}),t(s,{label:"送货员",prop:"deliveryName"},{default:l(()=>[t(V,{modelValue:e(a).deliveryName,"onUpdate:modelValue":u[2]||(u[2]=c=>e(a).deliveryName=c),placeholder:"请选择送货员名称"},{default:l(()=>{var c;return[(f(!0),h($,null,L((c=e(p).cond)==null?void 0:c.deliveryUserList,F=>(f(),z(N,{key:F.workNumber,value:F.userName},null,8,["value"]))),128))]}),_:1},8,["modelValue"])]),_:1}),t(s,{label:"订单日期",prop:"orderDate"},{default:l(()=>[t(n,{modelValue:e(a).orderDate,"onUpdate:modelValue":u[3]||(u[3]=c=>e(a).orderDate=c),type:"datetime",placeholder:"选择日期时间","value-format":"YYYY-MM-DD hh:mm:ss"},null,8,["modelValue"])]),_:1}),t(s,{label:"客户名称",prop:"customerCode"},{default:l(()=>[t(V,{disabled:"",modelValue:e(a).contactName,"onUpdate:modelValue":u[4]||(u[4]=c=>e(a).contactName=c),placeholder:"请选择客户名称"},{default:l(()=>{var c;return[(f(!0),h($,null,L((c=e(p).cond)==null?void 0:c.customerManagerList,F=>(f(),z(N,{key:F.userName,label:F.userName,value:F.userName},null,8,["label","value"]))),128))]}),_:1},8,["modelValue"])]),_:1}),t(s,{label:"路线名称",prop:"routeName"},{default:l(()=>[t(V,{disabled:"",modelValue:e(a).routeName,"onUpdate:modelValue":u[5]||(u[5]=c=>e(a).routeName=c),placeholder:"请选择路线名称"},{default:l(()=>{var c;return[(f(!0),h($,null,L((c=e(p).cond)==null?void 0:c.areaList,F=>(f(),z(N,{key:F.areaName,value:F.areaName},null,8,["value"]))),128))]}),_:1},8,["modelValue"])]),_:1}),t(s,{label:"客户专员",prop:"customerManagerName"},{default:l(()=>[t(V,{disabled:"",modelValue:e(a).customerManagerName,"onUpdate:modelValue":u[6]||(u[6]=c=>e(a).customerManagerName=c),placeholder:"请选择客户专员名称"},{default:l(()=>{var c;return[(f(!0),h($,null,L((c=e(p).cond)==null?void 0:c.customerManagerList,F=>(f(),z(N,{key:F.workNumber,value:F.userName},null,8,["value"]))),128))]}),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),t(r,{rules:e(U),model:e(a),ref_key:"ruleFormRef2",ref:y},{default:l(()=>[t(s,{class:"item",label:"编辑异常信息",prop:"feedbackInformation"},{default:l(()=>[t(o,{type:"textarea",maxlength:"50","show-word-limit":"",autosize:{minRows:5,maxRows:20},modelValue:e(a).feedbackInformation,"onUpdate:modelValue":u[7]||(u[7]=c=>e(a).feedbackInformation=c)},null,8,["modelValue"])]),_:1}),t(s,{class:"item",label:"上传签收照片"},{default:l(()=>[t(B,{accept:"image/*",multiple:"","list-type":"picture-card","auto-upload":!1,"file-list":e(a).fileList,"onUpdate:fileList":u[8]||(u[8]=c=>e(a).fileList=c)},{file:l(({file:c})=>[m("div",null,[m("img",{class:"el-upload-list__item-thumbnail",src:c.url,style:{width:"100%",height:"100%"},alt:""},null,8,st),m("span",ut,[m("span",{class:"el-upload-list__item-preview",onClick:F=>E(c)},[t(g,null,{default:l(()=>[t(e(ce))]),_:1})],8,dt),m("span",{class:"el-upload-list__item-delete",onClick:d},[t(g,null,{default:l(()=>[t(e(le))]),_:1})])])])]),default:l(()=>[t(g,null,{default:l(()=>[t(e(ae))]),_:1})]),_:1},8,["file-list"])]),_:1}),t(w,{class:"btn",size:"large",onClick:u[9]||(u[9]=c=>x(e(i),e(y)))},{default:l(()=>u[14]||(u[14]=[Y("提交")])),_:1}),t(w,{class:"btn",size:"large",onClick:u[10]||(u[10]=c=>D(e(i),e(y)))},{default:l(()=>u[15]||(u[15]=[Y("清空")])),_:1})]),_:1},8,["rules","model"])])]),_:1},8,["modelValue"])]),t(j,{modelValue:e(b),"onUpdate:modelValue":u[13]||(u[13]=c=>G(b)?b.value=c:null)},{default:l(()=>[m("img",{width:"100%",src:e(M),alt:"Preview Image"},null,8,it)]),_:1},8,["modelValue"])],64)}}}),mt=Q(ct,[["__scopeId","data-v-7807a2cb"]]),pt={class:"infoReply"},ft={class:"el-dialog-div"},_t=["src"],bt={class:"el-upload-list__item-actions"},vt=["onClick"],gt=["src"],kt=J({__name:"infoReply",props:{feedbackType:{}},emits:["renewClick"],setup(q,{expose:T,emit:P}){const p=K(),I=[{index:1,stateText:"处理中"},{index:2,stateText:"已处理"},{index:3,stateText:"无需处理"}],k=te({replyContent:"",feedbackStatus:"",UploadFile:[]}),C=_(!1),S=localStorage.getItem("userInfo"),i=JSON.parse(S).position;function y(){return i=="客户专员"?"2":i=="送货员"?"1":"0"}const U=q,a=_(13);function M(n){C.value=!0,a.value=n}T({handleReply:M});const b=_(),A=_(""),x=_(!1),D=te({replyContent:[{required:!0,message:"请输入自定义内容",trigger:"blur"}],feedbackStatus:[{required:!0,message:"请选择反馈信息处理状态",trigger:"change"}]}),d=_([]),E=_(),v=(n,r)=>{d.value=r,console.log(n)},u=P,o=async n=>{n&&await n.validate((r,g)=>{r?$e.confirm("确认提交回复?").then(()=>{const B=ve.service({lock:!0,text:"正在提交中",background:"rgba(0, 0, 0, 0.7)"});let w=new FormData;w.append("feedbackId",a.value),w.append("replyContent",k.replyContent),w.append("replyType",y()),w.append("feedbackStatus",k.feedbackStatus),d.value.forEach(j=>{w.append("fileList",j.raw)}),p.postInfoAddAction(w).then(j=>{B.close(),j.code==200?(C.value=!1,u("renewClick",a.value),p.UnhandledAmountAction(),ie({type:"success",message:"提交成功"})):ie({message:"提交失败",type:"warning"})}).catch(()=>{})}).catch(()=>{}):console.log("error",g)})},s=n=>{console.log(n),console.log(d.value),d.value.splice(d.value.indexOf(n),1)},N=n=>{A.value=n.url,x.value=!0},V=n=>{n&&(E.value.clearFiles(),n.resetFields())};return(n,r)=>{const g=ne,B=se,w=oe,j=be,c=Te,F=Ue,ge=Z,ke=re,de=ue;return f(),h($,null,[m("div",pt,[t(de,{modelValue:e(C),"onUpdate:modelValue":r[4]||(r[4]=R=>G(C)?C.value=R:null),width:"70%",center:"",top:"4%",onClose:r[5]||(r[5]=R=>V(e(b)))},{default:l(()=>[m("div",ft,[t(ke,{ref_key:"replyFormRef",ref:b,model:e(k),rules:e(D),"label-width":"160px",class:"demo-ruleForm","status-icon":""},{default:l(()=>[t(B,{label:y()=="1"?"物流部反馈 :":"营销部反馈 :",prop:"replyContent"},{default:l(()=>[t(g,{modelValue:e(k).replyContent,"onUpdate:modelValue":r[0]||(r[0]=R=>e(k).replyContent=R),maxlength:"100",placeholder:"点击输入自定义内容......","show-word-limit":"",autosize:{minRows:5,maxRows:10},type:"textarea"},null,8,["modelValue"])]),_:1},8,["label"]),t(B,{label:"上传照片 :"},{default:l(()=>[t(j,{"file-list":e(k).UploadFile,"onUpdate:fileList":r[1]||(r[1]=R=>e(k).UploadFile=R),accept:"image/*",multiple:"",action:"fakeAction","show-file-list":!0,"list-type":"picture-card","auto-upload":!1,"on-change":v,ref_key:"uploadRef",ref:E},{file:l(({file:R})=>[m("div",null,[m("img",{class:"el-upload-list__item-thumbnail",src:R.url,style:{width:"100%",height:"100%"},alt:""},null,8,_t),m("span",bt,[m("span",{class:"el-upload-list__item-preview",onClick:Ct=>N(R)},[t(w,null,{default:l(()=>[t(e(ce))]),_:1})],8,vt),m("span",{class:"el-upload-list__item-delete",onClick:s},[t(w,null,{default:l(()=>[t(e(le))]),_:1})])])])]),default:l(()=>[t(w,null,{default:l(()=>[t(e(ae))]),_:1})]),_:1},8,["file-list"])]),_:1}),e(W)("guest-book:logistics:exception:modify")&&U.feedbackType==="1"||e(W)("guest-book:marketing:exception:modify")&&U.feedbackType==="2"?(f(),z(B,{key:0,label:"状态修改为 :",prop:"feedbackStatus"},{default:l(()=>[t(F,{modelValue:e(k).feedbackStatus,"onUpdate:modelValue":r[2]||(r[2]=R=>e(k).feedbackStatus=R)},{default:l(()=>[(f(),h($,null,L(I,R=>t(c,{key:R.index,label:R.index},{default:l(()=>[Y(O(R.stateText),1)]),_:2},1032,["label"])),64))]),_:1},8,["modelValue"])]),_:1})):X("",!0),t(ge,{class:"btn",onClick:r[3]||(r[3]=R=>o(e(b)))},{default:l(()=>r[7]||(r[7]=[Y("回复")])),_:1})]),_:1},8,["model","rules"])])]),_:1},8,["modelValue"])]),t(de,{modelValue:e(x),"onUpdate:modelValue":r[6]||(r[6]=R=>G(x)?x.value=R:null)},{default:l(()=>[m("img",{width:"100%",src:e(A),alt:"Preview Image"},null,8,gt)]),_:1},8,["modelValue"])],64)}}}),yt=Q(kt,[["__scopeId","data-v-8a19a089"]]),Nt={class:"BoardInfo"},ht=J({__name:"BoardInfo",setup(q){const T=K(),P=Re(),p=_({pageNum:1,pageSize:6}),I=_();function k(o){var s;p.value.pageNum=o,S((s=I.value)==null?void 0:s.searchForm)}const C=_("1");function S(o={}){T.getBoardData({feedbackType:C.value,...p.value,...o}).then(()=>{p.value.pageNum=T.boardData.currentPageNum})}H(()=>P.query.feedbackType,o=>{C.value=o,p.value.pageNum=1,S()},{immediate:!0});const i=_(),y=_();function U(o){var s;(s=i.value)==null||s.handleOpen(o)}const a=_();function M(){var o,s;(o=a.value)!=null&&o.deleteData&&T.removeFeedbackAction((s=a.value)==null?void 0:s.deleteData).then(()=>{S()})}function b(o){var s;(s=y.value)==null||s.handleReply(o)}const A=_();function x(o){S(),H(()=>T.boardData.dataCurrentPage,s=>{var V,n;const N=s.filter(r=>r.feedbackId==o);A.value=(V=N[0])==null?void 0:V.feedbackStatus,(n=i.value)==null||n.getReplyData(o,A.value)})}const D=_();function d(){var o;(o=D.value)==null||o.handleOpen(C.value)}function E(o){p.value.pageNum=1,S(o)}function v(o){p.value.pageNum=1,S(o)}function u(){p.value.pageNum=1,S()}return(o,s)=>{var V;const N=Ve;return f(),h("div",Nt,[t(Pe,{ref_key:"infoSearchRef",ref:I,onItemAdd:d,onItemDelete:M,onItemSearch:v,onItemReset:u,onItemState:E,feedbackType:e(C)},null,8,["feedbackType"]),t(We,{class:"info-table","table-data":(V=e(T).boardData)==null?void 0:V.dataCurrentPage,onItemClick:U,ref_key:"InfoTableRef",ref:a,feedbackType:e(C)},null,8,["table-data","feedbackType"]),t(lt,{ref_key:"InfoItemRef",ref:i,onReplyClick:b,feedbackType:e(C)},null,8,["feedbackType"]),t(yt,{ref_key:"InfoReplyRef",ref:y,feedbackType:e(C),onRenewClick:x},null,8,["feedbackType"]),t(N,{class:"abs",layout:"prev, pager, next","current-page":e(p).pageNum,"page-size":e(p).pageSize,total:e(T).boardData.totalCount,onCurrentChange:k},null,8,["current-page","page-size","total"]),t(mt,{ref_key:"InfoAddRef",ref:D,onAddSuccess:S},null,512)])}}}),Ht=Q(ht,[["__scopeId","data-v-a15df166"]]);export{Ht as default};
