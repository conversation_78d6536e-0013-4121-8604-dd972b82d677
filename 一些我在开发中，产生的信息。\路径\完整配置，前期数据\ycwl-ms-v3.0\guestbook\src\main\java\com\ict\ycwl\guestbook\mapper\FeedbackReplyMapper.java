package com.ict.ycwl.guestbook.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ict.ycwl.guestbook.api.vo.FeedbackReplyVo;
import com.ict.ycwl.guestbook.domain.FeedbackReply;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface FeedbackReplyMapper extends BaseMapper<FeedbackReply> {

    List<FeedbackReplyVo> selectReplyAndPhoto(Long feedbackId);

    Long insertFeedbackReply(FeedbackReply reply);

    List<Long> selectReplyByFeedbackIds(@Param("feedbackIdList") List<Long> feedbackIdList);

    int deleteReplyByFeedbackIds(@Param("feedbackIdList") List<Long> feedbackIdList);
}
