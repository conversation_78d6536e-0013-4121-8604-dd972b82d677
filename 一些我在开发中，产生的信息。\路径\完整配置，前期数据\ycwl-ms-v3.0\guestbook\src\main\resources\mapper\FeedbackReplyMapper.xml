<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.guestbook.mapper.FeedbackReplyMapper">

    <sql id="getFeedbackReplyColumn">
        reply_id,reply_content,create_time,reply_type
    </sql>

    <resultMap id="feedbackReplyMap" type="com.ict.ycwl.guestbook.api.vo.FeedbackReplyVo">
        <id property="replyId" column="reply_id"></id>
        <result property="replyContent" column="reply_content"></result>
        <result property="createTime" column="create_time"></result>
        <result property="replyType" column="reply_type"></result>
        <collection property="replyFilePathList" fetchType="eager"
                    select="com.ict.ycwl.guestbook.mapper.FeedbackReplyFileMapper.selectReplyFile"
                    column="reply_id"></collection>
    </resultMap>

    <select id="selectReplyAndPhoto" resultMap="feedbackReplyMap" parameterType="Long">
        SELECT <include refid="getFeedbackReplyColumn"></include>
            FROM feedback_reply WHERE feedback_id = #{feedbackId} ORDER BY create_time
    </select>

    <insert id="insertFeedbackReply" useGeneratedKeys="true" keyProperty="replyId">
        INSERT INTO feedback_reply VALUES(NULL,#{replyContent},#{createBy},#{createTime},#{replyType},#{feedbackId})
    </insert>

    <select id="selectReplyByFeedbackIds" parameterType="List" resultType="Long">
        SELECT reply_id FROM feedback_reply WHERE feedback_id IN
        <foreach collection="feedbackIdList" separator="," item="feedbackId" open="(" close=")">
            #{feedbackId}
        </foreach>
    </select>

    <delete id="deleteReplyByFeedbackIds">
        DELETE FROM feedback_reply WHERE feedback_id IN
        <foreach collection="feedbackIdList" separator="," item="feedbackId" open="(" close=")">
            #{feedbackId}
        </foreach>
    </delete>
</mapper>