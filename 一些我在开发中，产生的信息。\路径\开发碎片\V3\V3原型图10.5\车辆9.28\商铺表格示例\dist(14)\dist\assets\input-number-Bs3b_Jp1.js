import{f as ue,ag as se,i as d,d as J,H as oe,j as ie,r as ce,P as de,k as V,a7 as E,m as me,z as pe,af as be,o as b,c as z,X as R,u as t,t as D,e as _,b as M,w as W,p as S,K as Y,M as P,l as fe,v as ve}from"./index-C0QCllTd.js";import{C as Q,I as k,i as I,U as h,E as Ne,t as Ve,d as j}from"./input-DqmydyK4.js";import{k as he,c as Ie,K as ye,b as ge,O as we,E as X,P as Ee,p as _e,_ as Se}from"./base-kpSIrADU.js";import{v as q}from"./index-1tmHbbca.js";const Pe=ue({id:{type:String,default:void 0},step:{type:Number,default:1},stepStrictly:Boolean,max:{type:Number,default:Number.POSITIVE_INFINITY},min:{type:Number,default:Number.NEGATIVE_INFINITY},modelValue:Number,readonly:Boolean,disabled:Boolean,size:se,controls:{type:Boolean,default:!0},controlsPosition:{type:String,default:"",values:["","right"]},valueOnClear:{type:[String,Number,null],validator:l=>l===null||d(l)||["min","max"].includes(l),default:null},name:String,label:String,placeholder:String,precision:{type:Number,validator:l=>l>=0&&l===Number.parseInt(`${l}`,10)},validateEvent:{type:Boolean,default:!0}}),ke={[Q]:(l,F)=>F!==l,blur:l=>l instanceof FocusEvent,focus:l=>l instanceof FocusEvent,[k]:l=>d(l)||I(l),[h]:l=>d(l)||I(l)},Fe=["aria-label","onKeydown"],Ae=["aria-label","onKeydown"],Ce=J({name:"ElInputNumber"}),xe=J({...Ce,props:Pe,emits:ke,setup(l,{expose:F,emit:c}){const a=l,{t:O}=oe(),m=ie("input-number"),v=ce(),u=de({currentValue:a.modelValue,userInput:null}),{formItem:f}=he(),U=V(()=>d(a.modelValue)&&a.modelValue<=a.min),$=V(()=>d(a.modelValue)&&a.modelValue>=a.max),Z=V(()=>{const e=H(a.step);return E(a.precision)?Math.max(H(a.modelValue),e):(e>a.precision,a.precision)}),A=V(()=>a.controls&&a.controlsPosition==="right"),G=Ie(),N=ye(),C=V(()=>{if(u.userInput!==null)return u.userInput;let e=u.currentValue;if(I(e))return"";if(d(e)){if(Number.isNaN(e))return"";E(a.precision)||(e=e.toFixed(a.precision))}return e}),x=(e,n)=>{if(E(n)&&(n=Z.value),n===0)return Math.round(e);let r=String(e);const s=r.indexOf(".");if(s===-1||!r.replace(".","").split("")[s+n])return e;const g=r.length;return r.charAt(g-1)==="5"&&(r=`${r.slice(0,Math.max(0,g-1))}6`),Number.parseFloat(Number(r).toFixed(n))},H=e=>{if(I(e))return 0;const n=e.toString(),r=n.indexOf(".");let s=0;return r!==-1&&(s=n.length-r-1),s},L=(e,n=1)=>d(e)?x(e+a.step*n):u.currentValue,B=()=>{if(a.readonly||N.value||$.value)return;const e=Number(C.value)||0,n=L(e);y(n),c(k,u.currentValue)},K=()=>{if(a.readonly||N.value||U.value)return;const e=Number(C.value)||0,n=L(e,-1);y(n),c(k,u.currentValue)},T=(e,n)=>{const{max:r,min:s,step:o,precision:p,stepStrictly:g,valueOnClear:w}=a;r<s&&Ve("InputNumber","min should not be greater than max.");let i=Number(e);if(I(e)||Number.isNaN(i))return null;if(e===""){if(w===null)return null;i=fe(w)?{min:s,max:r}[w]:w}return g&&(i=x(Math.round(i/o)*o,p)),E(p)||(i=x(i,p)),(i>r||i<s)&&(i=i>r?r:s,n&&c(h,i)),i},y=(e,n=!0)=>{var r;const s=u.currentValue,o=T(e);if(!n){c(h,o);return}s!==o&&(u.userInput=null,c(h,o),c(Q,o,s),a.validateEvent&&((r=f==null?void 0:f.validate)==null||r.call(f,"change").catch(p=>j())),u.currentValue=o)},ee=e=>{u.userInput=e;const n=e===""?null:Number(e);c(k,n),y(n,!1)},ne=e=>{const n=e!==""?Number(e):"";(d(n)&&!Number.isNaN(n)||e==="")&&y(n),u.userInput=null},te=()=>{var e,n;(n=(e=v.value)==null?void 0:e.focus)==null||n.call(e)},re=()=>{var e,n;(n=(e=v.value)==null?void 0:e.blur)==null||n.call(e)},ae=e=>{c("focus",e)},le=e=>{var n;c("blur",e),a.validateEvent&&((n=f==null?void 0:f.validate)==null||n.call(f,"blur").catch(r=>j()))};return me(()=>a.modelValue,e=>{const n=T(u.userInput),r=T(e,!0);!d(n)&&(!n||n!==r)&&(u.currentValue=r,u.userInput=null)},{immediate:!0}),pe(()=>{var e;const{min:n,max:r,modelValue:s}=a,o=(e=v.value)==null?void 0:e.input;if(o.setAttribute("role","spinbutton"),Number.isFinite(r)?o.setAttribute("aria-valuemax",String(r)):o.removeAttribute("aria-valuemax"),Number.isFinite(n)?o.setAttribute("aria-valuemin",String(n)):o.removeAttribute("aria-valuemin"),o.setAttribute("aria-valuenow",u.currentValue||u.currentValue===0?String(u.currentValue):""),o.setAttribute("aria-disabled",String(N.value)),!d(s)&&s!=null){let p=Number(s);Number.isNaN(p)&&(p=null),c(h,p)}}),be(()=>{var e,n;const r=(e=v.value)==null?void 0:e.input;r==null||r.setAttribute("aria-valuenow",`${(n=u.currentValue)!=null?n:""}`)}),F({focus:te,blur:re}),(e,n)=>(b(),z("div",{class:D([t(m).b(),t(m).m(t(G)),t(m).is("disabled",t(N)),t(m).is("without-controls",!e.controls),t(m).is("controls-right",t(A))]),onDragstart:n[1]||(n[1]=P(()=>{},["prevent"]))},[e.controls?R((b(),z("span",{key:0,role:"button","aria-label":t(O)("el.inputNumber.decrease"),class:D([t(m).e("decrease"),t(m).is("disabled",t(U))]),onKeydown:_(K,["enter"])},[M(t(X),null,{default:W(()=>[t(A)?(b(),S(t(ge),{key:0})):(b(),S(t(we),{key:1}))]),_:1})],42,Fe)),[[t(q),K]]):Y("v-if",!0),e.controls?R((b(),z("span",{key:1,role:"button","aria-label":t(O)("el.inputNumber.increase"),class:D([t(m).e("increase"),t(m).is("disabled",t($))]),onKeydown:_(B,["enter"])},[M(t(X),null,{default:W(()=>[t(A)?(b(),S(t(Ee),{key:0})):(b(),S(t(_e),{key:1}))]),_:1})],42,Ae)),[[t(q),B]]):Y("v-if",!0),M(t(Ne),{id:e.id,ref_key:"input",ref:v,type:"number",step:e.step,"model-value":t(C),placeholder:e.placeholder,readonly:e.readonly,disabled:t(N),size:t(G),max:e.max,min:e.min,name:e.name,label:e.label,"validate-event":!1,onWheel:n[0]||(n[0]=P(()=>{},["prevent"])),onKeydown:[_(P(B,["prevent"]),["up"]),_(P(K,["prevent"]),["down"])],onBlur:le,onFocus:ae,onInput:ee,onChange:ne},null,8,["id","step","model-value","placeholder","readonly","disabled","size","max","min","name","label","onKeydown"])],34))}});var Be=Se(xe,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/input-number/src/input-number.vue"]]);const Me=ve(Be);export{Me as E};
