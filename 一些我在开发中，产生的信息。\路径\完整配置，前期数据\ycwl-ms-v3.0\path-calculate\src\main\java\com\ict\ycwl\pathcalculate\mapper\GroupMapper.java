package com.ict.ycwl.pathcalculate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ict.ycwl.pathcalculate.pojo.Group;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@Repository
public interface GroupMapper extends BaseMapper<Group> {

    /**
     * 根据模糊查询得到班组信息
     *
     * @return 班组集合
     */
    List<Group> getGroupsByLikeName();

    /**
     * 根据班组名字得到班组信息
     *
     * @param name 班组名字
     * @return 班组集合
     */
    Group getGroupsByName(String name);
}
