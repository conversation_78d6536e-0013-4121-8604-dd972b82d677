import{Y as f,U as h,r as s}from"./index-C0QCllTd.js";import{r as i,a as m}from"./index-m25zEilF.js";function S(){return i.get({url:"/userservice/captcha/captcha",responseType:"blob"})}function v(n,t){return i.post({url:"/userservice/user/login",headers:{"Content-Type":"application/x-www-form-urlencoded",captcha:t},data:n})}const y=f("login",()=>{const n=h(),t=s(""),c=s("");async function p(){S().then(o=>{const a=o.headers;c.value=a.captcha;const r=URL.createObjectURL(o.data);t.value=r})}const e=s({token:localStorage.getItem("token")??"",user:JSON.parse(localStorage.getItem("userInfo"))??"",operations:JSON.parse(localStorage.getItem("operation"))??""}),u=s();async function g(o){var r,l;const a=await v(o,c.value);u.value=a.data.token,localStorage.setItem("token",u.value),e.value=a.data,console.log(e.value),localStorage.setItem("userInfo",JSON.stringify((r=e.value)==null?void 0:r.user)),localStorage.setItem("operation",JSON.stringify((l=e.value)==null?void 0:l.operations)),m.success("登录成功"),n.push("/home")}return{captcha:t,captchaText:c,getCaptchaAction:p,loginAction:g,userInfo:e}});export{y as u};
