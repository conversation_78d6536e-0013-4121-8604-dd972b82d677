import"./base-kpSIrADU.js";import{E as u,a as c}from"./form-item-Bd-FvCZ5.js";import{E as _}from"./input-DqmydyK4.js";import{d as f,r as g,c as V,a,b as l,w as i,u as e,e as h,o as w}from"./index-C0QCllTd.js";import{u as v}from"./login-Bm4pw2eQ.js";import{_ as C}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./castArray-CSO3s-vM.js";import"./index-m25zEilF.js";import"./_initCloneObject-BmTtMqsv.js";const E={class:"login"},N={class:"login-content"},k={class:"login-form"},x=["src"],L=f({__name:"Login",setup(b){const s=v(),t=g({loginName:"",password:"",captcha:""});s.getCaptchaAction();function d(){s.loginAction({...t.value})}return(y,o)=>{const r=_,m=c,p=u;return w(),V("div",E,[a("div",N,[o[4]||(o[4]=a("h1",{class:"title"},"粤北卷烟物流管理平台",-1)),a("div",k,[l(p,{model:e(t),onKeyup:h(d,["enter"])},{default:i(()=>[l(m,null,{default:i(()=>[l(r,{modelValue:e(t).loginName,"onUpdate:modelValue":o[0]||(o[0]=n=>e(t).loginName=n),modelModifiers:{trim:!0},autocomplete:"on",placeholder:"请输入账号"},null,8,["modelValue"])]),_:1}),l(m,null,{default:i(()=>[l(r,{modelValue:e(t).password,"onUpdate:modelValue":o[1]||(o[1]=n=>e(t).password=n),modelModifiers:{trim:!0},autocomplete:"on",placeholder:"请输入密码","show-password":""},null,8,["modelValue"])]),_:1}),l(m,null,{default:i(()=>[l(r,{style:{width:"60% !important"},modelValue:e(t).captcha,"onUpdate:modelValue":o[2]||(o[2]=n=>e(t).captcha=n),modelModifiers:{trim:!0},placeholder:"请输入验证码"},null,8,["modelValue"]),a("img",{onClick:o[3]||(o[3]=n=>e(s).getCaptchaAction()),src:e(s).captcha,alt:""},null,8,x)]),_:1})]),_:1},8,["model"]),a("div",{class:"button",onClick:d},"GO")])])])}}}),O=C(L,[["__scopeId","data-v-1b18d800"]]);export{O as default};
