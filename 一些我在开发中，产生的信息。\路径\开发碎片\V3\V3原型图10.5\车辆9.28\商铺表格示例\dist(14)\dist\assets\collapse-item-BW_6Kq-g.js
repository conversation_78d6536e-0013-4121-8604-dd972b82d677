import{f as P,h as $,i as j,r as g,m as U,x as W,j as x,k as r,d as f,o as H,c as O,s as y,t as u,u as t,p as z,w as T,J as G,ac as J,ad as R,y as X,a as E,b as k,e as q,M as Q,X as Y,a5 as Z,R as ee,S as te,v as se,O as ae}from"./index-C0QCllTd.js";import{m as oe}from"./index-m25zEilF.js";import{U as V,C as S}from"./input-DqmydyK4.js";import{c as K}from"./castArray-CSO3s-vM.js";import{_ as B,m as le,E as ne}from"./base-kpSIrADU.js";const M=()=>Math.floor(Math.random()*1e4),N=a=>typeof j(a),de=P({accordion:Boolean,modelValue:{type:$([Array,String,Number]),default:()=>oe([])}}),ie={[V]:N,[S]:N},A=Symbol("collapseContextKey"),re=(a,l)=>{const s=g(K(a.modelValue)),n=o=>{s.value=o;const d=a.accordion?s.value[0]:s.value;l(V,d),l(S,d)},e=o=>{if(a.accordion)n([s.value[0]===o?"":o]);else{const d=[...s.value],i=d.indexOf(o);i>-1?d.splice(i,1):d.push(o),n(d)}};return U(()=>a.modelValue,()=>s.value=K(a.modelValue),{deep:!0}),W(A,{activeNames:s,handleItemClick:e}),{activeNames:s,setActiveNames:n}},ce=()=>{const a=x("collapse");return{rootKls:r(()=>a.b())}},pe=f({name:"ElCollapse"}),me=f({...pe,props:de,emits:ie,setup(a,{expose:l,emit:s}){const n=a,{activeNames:e,setActiveNames:o}=re(n,s),{rootKls:d}=ce();return l({activeNames:e,setActiveNames:o}),(i,p)=>(H(),O("div",{class:u(t(d))},[y(i.$slots,"default")],2))}});var ue=B(me,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/collapse/src/collapse.vue"]]);const fe=f({name:"ElCollapseTransition"}),ve=f({...fe,setup(a){const l=x("collapse-transition"),s=e=>{e.style.maxHeight="",e.style.overflow=e.dataset.oldOverflow,e.style.paddingTop=e.dataset.oldPaddingTop,e.style.paddingBottom=e.dataset.oldPaddingBottom},n={beforeEnter(e){e.dataset||(e.dataset={}),e.dataset.oldPaddingTop=e.style.paddingTop,e.dataset.oldPaddingBottom=e.style.paddingBottom,e.style.maxHeight=0,e.style.paddingTop=0,e.style.paddingBottom=0},enter(e){e.dataset.oldOverflow=e.style.overflow,e.scrollHeight!==0?e.style.maxHeight=`${e.scrollHeight}px`:e.style.maxHeight=0,e.style.paddingTop=e.dataset.oldPaddingTop,e.style.paddingBottom=e.dataset.oldPaddingBottom,e.style.overflow="hidden"},afterEnter(e){e.style.maxHeight="",e.style.overflow=e.dataset.oldOverflow},enterCancelled(e){s(e)},beforeLeave(e){e.dataset||(e.dataset={}),e.dataset.oldPaddingTop=e.style.paddingTop,e.dataset.oldPaddingBottom=e.style.paddingBottom,e.dataset.oldOverflow=e.style.overflow,e.style.maxHeight=`${e.scrollHeight}px`,e.style.overflow="hidden"},leave(e){e.scrollHeight!==0&&(e.style.maxHeight=0,e.style.paddingTop=0,e.style.paddingBottom=0)},afterLeave(e){s(e)},leaveCancelled(e){s(e)}};return(e,o)=>(H(),z(R,G({name:t(l).b()},J(n)),{default:T(()=>[y(e.$slots,"default")]),_:3},16,["name"]))}});var h=B(ve,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/collapse-transition/src/collapse-transition.vue"]]);h.install=a=>{a.component(h.name,h)};const ge=h,he=P({title:{type:String,default:""},name:{type:$([String,Number]),default:()=>M()},disabled:Boolean}),ye=a=>{const l=X(A),s=g(!1),n=g(!1),e=g(M()),o=r(()=>l==null?void 0:l.activeNames.value.includes(a.name));return{focusing:s,id:e,isActive:o,handleFocus:()=>{setTimeout(()=>{n.value?n.value=!1:s.value=!0},50)},handleHeaderClick:()=>{a.disabled||(l==null||l.handleItemClick(a.name),s.value=!1,n.value=!0)},handleEnterClick:()=>{l==null||l.handleItemClick(a.name)}}},Ce=(a,{focusing:l,isActive:s,id:n})=>{const e=x("collapse"),o=r(()=>[e.b("item"),e.is("active",t(s)),e.is("disabled",a.disabled)]),d=r(()=>[e.be("item","header"),e.is("active",t(s)),{focusing:t(l)&&!a.disabled}]),i=r(()=>[e.be("item","arrow"),e.is("active",t(s))]),p=r(()=>e.be("item","wrap")),C=r(()=>e.be("item","content")),b=r(()=>e.b(`content-${t(n)}`)),_=r(()=>e.b(`head-${t(n)}`));return{arrowKls:i,headKls:d,rootKls:o,itemWrapperKls:p,itemContentKls:C,scopedContentId:b,scopedHeadId:_}},be=["id","aria-expanded","aria-controls","aria-describedby","tabindex"],_e=["id","aria-hidden","aria-labelledby"],we=f({name:"ElCollapseItem"}),Ee=f({...we,props:he,setup(a,{expose:l}){const s=a,{focusing:n,id:e,isActive:o,handleFocus:d,handleHeaderClick:i,handleEnterClick:p}=ye(s),{arrowKls:C,headKls:b,rootKls:_,itemWrapperKls:F,itemContentKls:L,scopedContentId:w,scopedHeadId:I}=Ce(s,{focusing:n,isActive:o,id:e});return l({isActive:o}),(v,c)=>(H(),O("div",{class:u(t(_))},[E("button",{id:t(I),class:u(t(b)),"aria-expanded":t(o),"aria-controls":t(w),"aria-describedby":t(w),tabindex:v.disabled?-1:0,onClick:c[0]||(c[0]=(...m)=>t(i)&&t(i)(...m)),onKeydown:c[1]||(c[1]=q(Q((...m)=>t(p)&&t(p)(...m),["stop","prevent"]),["space","enter"])),onFocus:c[2]||(c[2]=(...m)=>t(d)&&t(d)(...m)),onBlur:c[3]||(c[3]=m=>n.value=!1)},[y(v.$slots,"title",{},()=>[ee(te(v.title),1)]),k(t(ne),{class:u(t(C))},{default:T(()=>[k(t(le))]),_:1},8,["class"])],42,be),k(t(ge),null,{default:T(()=>[Y(E("div",{id:t(w),role:"region",class:u(t(F)),"aria-hidden":!t(o),"aria-labelledby":t(I)},[E("div",{class:u(t(L))},[y(v.$slots,"default")],2)],10,_e),[[Z,t(o)]])]),_:3})],2))}});var D=B(Ee,[["__file","/home/<USER>/work/element-plus/element-plus/packages/components/collapse/src/collapse-item.vue"]]);const Ie=se(ue,{CollapseItem:D}),Ke=ae(D);export{Ke as E,Ie as a};
