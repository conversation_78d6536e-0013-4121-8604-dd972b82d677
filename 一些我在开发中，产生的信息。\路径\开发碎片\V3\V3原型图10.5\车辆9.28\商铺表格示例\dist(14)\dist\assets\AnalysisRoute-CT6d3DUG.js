import{E as ue,G as de}from"./base-kpSIrADU.js";import{E as ce}from"./button-IGKrEYb9.js";import{E as pe}from"./date-picker-C-6M_J1A.js";import"./input-DqmydyK4.js";import"./scrollbar-BNeK4Yi-.js";import{E as me,a as fe,c as ge}from"./select-BOcQ2ynX.js";/* empty css             */import{S as ye,M as be}from"./getMapKey-C0z490Cj.js";import{d as ve,z as he,r as k,c as D,a as m,b as d,w as f,R as B,u as c,Q as z,ae as _e,U as Se,o as g,N as I,T as L,p as N}from"./index-C0QCllTd.js";import{J as U}from"./index-DUXS04g8.js";import{A as xe}from"./index-Bp4b1Vvq.js";import{Z as ae,c as we,a as Ce,e as ke,g as De,b as Re,n as Pe,m as Me,A as Ee,d as Te,M as J,f as Ae,h as Oe,j as Ve,k as Be,l as ze,o as Ie,p as Le,q as Ne,r as je,s as We,t as $e,u as Ue,v as Ge,w as Je,x as Ye,y as Fe,z as te,B as Ze,C as qe,D as He,E as Ke,F as Qe,G as Xe,H as ea,I as aa,J as ta,K as oa,L as sa,N as la,O as na,P as ra,Q as ia,R as ua,S as da,T as ca,U as pa,V as ma,W as fa,X as ga,Y as ya,_ as ba,$ as va,a0 as ha,a1 as _a,a2 as Sa,a3 as xa,a4 as wa,a5 as Ca,a6 as ka,a7 as Da,a8 as Ra,a9 as Pa,aa as Ma,ab as Ea,ac as Ta,ad as Aa,ae as Oa,af as Va,ag as Ba,ah as za,ai as Ia,aj as La,ak as Na,al as ja,am as Wa,an as $a,ao as Ua,ap as Ga,aq as Ja,ar as Ya,as as Fa,at as Za,au as qa,av as Ha,aw as Ka,ax as Qa,ay as Xa,az as et,aA as at,aB as tt,aC as Y,aD as F,aE as Z,aF as q,aG as ot,aH as st,aI as lt,aJ as nt,aK as rt,aL as it,aM as ut,aN as dt,aO as ct,aP as pt,aQ as mt,aR as ft,aS as gt,aT as yt,aU as bt,i as vt,aV as ht,aW as _t,aX as X,aY as St,aZ as xt,a_ as wt,a$ as Ct,b0 as kt,b1 as Dt,b2 as Rt,b3 as Pt,b4 as Mt,b5 as Et,b6 as Tt,b7 as At,b8 as Ot,b9 as Vt,ba as Bt,bb as zt,bc as It,bd as oe,be as Lt,bf as Nt,bg as jt,bh as Wt,bi as $t,bj as Ut,bk as Gt,bl as Jt,bm as Yt,bn as Ft,bo as Zt,bp as qt,bq as Ht,br as Kt}from"./universalTransition-CVj2Okbb.js";import{u as Qt}from"./cluster-BxttejUl.js";import{a as ee}from"./index-m25zEilF.js";import{_ as Xt}from"./_plugin-vue_export-helper-DlAUqK2U.js";import"./_commonjsHelpers-BbMlrU8H.js";import"./flatten-BP0fiJV-.js";import"./_initCloneObject-BmTtMqsv.js";import"./index-1tmHbbca.js";import"./merge-B3fxVp56.js";function eo(o,t,b,r,v,R,P,M){var x=new ae({style:{text:o,font:t,align:b,verticalAlign:r,padding:v,rich:R,overflow:P?"truncate":null,lineHeight:M}});return x.getBoundingRect()}function ao(o){return we(null,o)}var to={isDimensionStacked:Ce,enableDataStack:ke,getStackedDimension:De};function oo(o,t){var b=t;t instanceof J||(b=new J(t));var r=Re(b);return r.setExtent(o[0],o[1]),Pe(r,b),r}function so(o){Me(o,Ee)}function lo(o,t){return t=t||{},Te(o,null,null,t.state!=="normal")}const no=Object.freeze(Object.defineProperty({__proto__:null,createDimensions:Ae,createList:ao,createScale:oo,createSymbol:Oe,createTextStyle:lo,dataStack:to,enableHoverEmphasis:Ve,getECData:Be,getLayoutRect:ze,mixinAxisModelCommonMethods:so},Symbol.toStringTag,{value:"Module"})),ro=Object.freeze(Object.defineProperty({__proto__:null,MAX_SAFE_INTEGER:Ie,asc:Le,getPercentWithPrecision:Ne,getPixelPrecision:je,getPrecision:We,getPrecisionSafe:$e,isNumeric:Ue,isRadianAroundZero:Ge,linearMap:Je,nice:Ye,numericToNumber:Fe,parseDate:te,quantile:Ze,quantity:qe,quantityExponent:He,reformIntervals:Ke,remRadian:Qe,round:Xe},Symbol.toStringTag,{value:"Module"})),io=Object.freeze(Object.defineProperty({__proto__:null,format:ea,parse:te},Symbol.toStringTag,{value:"Module"})),uo=Object.freeze(Object.defineProperty({__proto__:null,Arc:aa,BezierCurve:ta,BoundingRect:oa,Circle:sa,CompoundPath:la,Ellipse:na,Group:ra,Image:ia,IncrementalDisplayable:ua,Line:da,LinearGradient:ca,Polygon:pa,Polyline:ma,RadialGradient:fa,Rect:ga,Ring:ya,Sector:ba,Text:ae,clipPointsByRect:va,clipRectByRect:ha,createIcon:_a,extendPath:Sa,extendShape:xa,getShapeClass:wa,getTransform:Ca,initProps:ka,makeImage:Da,makePath:Ra,mergePath:Pa,registerShape:Ma,resizePath:Ea,updateProps:Ta},Symbol.toStringTag,{value:"Module"})),co=Object.freeze(Object.defineProperty({__proto__:null,addCommas:Aa,capitalFirst:Oa,encodeHTML:Va,formatTime:Ba,formatTpl:za,getTextRect:eo,getTooltipMarker:Ia,normalizeCssArray:La,toCamelCase:Na,truncateText:ja},Symbol.toStringTag,{value:"Module"})),po=Object.freeze(Object.defineProperty({__proto__:null,bind:Wa,clone:$a,curry:Ua,defaults:Ga,each:Ja,extend:Ya,filter:Fa,indexOf:Za,inherits:qa,isArray:Ha,isFunction:Ka,isObject:Qa,isString:Xa,map:et,merge:at,reduce:tt},Symbol.toStringTag,{value:"Module"}));function mo(o){var t=Y.extend(o);return Y.registerClass(t),t}function fo(o){var t=F.extend(o);return F.registerClass(t),t}function go(o){var t=Z.extend(o);return Z.registerClass(t),t}function yo(o){var t=q.extend(o);return q.registerClass(t),t}const G=Object.freeze(Object.defineProperty({__proto__:null,Axis:ot,ChartView:q,ComponentModel:Y,ComponentView:F,List:st,Model:J,PRIORITY:lt,SeriesModel:Z,color:nt,connect:rt,dataTool:it,dependencies:ut,disConnect:dt,disconnect:ct,dispose:pt,env:mt,extendChartView:yo,extendComponentModel:mo,extendComponentView:fo,extendSeriesModel:go,format:co,getCoordinateSystemDimensions:ft,getInstanceByDom:gt,getInstanceById:yt,getMap:bt,graphic:uo,helper:no,init:vt,innerDrawElementOnCanvas:ht,matrix:_t,number:ro,parseGeoJSON:X,parseGeoJson:X,registerAction:St,registerCoordinateSystem:xt,registerLayout:wt,registerLoading:Ct,registerLocale:kt,registerMap:Dt,registerPostInit:Rt,registerPostUpdate:Pt,registerPreprocessor:Mt,registerProcessor:Et,registerTheme:Tt,registerTransform:At,registerUpdateLifecycle:Ot,registerVisual:Vt,setCanvasCreator:Bt,setPlatformAPI:zt,throttle:It,time:io,use:oe,util:po,vector:Lt,version:Nt,zrUtil:jt,zrender:Wt},Symbol.toStringTag,{value:"Module"}));oe([$t,Ut,Gt,Jt,Yt,Ft,Zt,qt,Ht,Kt]);const bo={class:"AnalysisRoute"},vo={class:"back"},ho={class:"AnalysisRouteTop"},_o={class:"selete"},So={class:"AnalysisRouteBottom"},xo={class:"BottomLeft"},wo={class:"BottomMiddle"},Co={class:"BottomRight"},ko=ve({__name:"AnalysisRoute",setup(o){window._AMapSecurityConfig={securityJsCode:ye};const t=Se();function b(){t.back()}let r=null,v=null;xe.load({key:be,version:"2.0",plugins:["AMap.DistrictSearch"]}).then(s=>{new s.DistrictSearch({subdistrict:0,extensions:"all",level:"province"}).search("韶关市",function(C,y){console.log(C);const i=y.districtList[0].boundaries,p=[];for(let l=0;l<i.length;l++)p.push([i[l]]);r=new s.Map("container",{mask:p,zoom:9,expandZoomRange:!0,zooms:[8,18],center:[113.767587,24.718014],viewMode:"3D",zoomEnable:!0,resizeEnable:!0}),v=new s.Map("container2",{mask:p,zoom:9,expandZoomRange:!0,zooms:[8,18],center:[113.767587,24.718014],viewMode:"3D",zoomEnable:!0,resizeEnable:!0});for(let l=0;l<i.length;l++)new s.Polyline({path:i[l],strokeColor:"#3078AC",strokeWeight:2}).setMap(r);for(let l=0;l<i.length;l++)new s.Polyline({path:i[l],strokeColor:"#3078AC",strokeWeight:2}).setMap(v);const h=new s.Bounds([112.785684,23.8],[114.74949,25.56]);r.setLimitBounds(h),v.setLimitBounds(h)})}).catch(s=>{console.log(s)});const R=()=>{G.init(document.getElementById("main")).setOption(x)},P=()=>{G.init(document.getElementById("main2")).setOption(j)},M=()=>{G.init(document.getElementById("main3")).setOption(W)};he(()=>{R(),P(),M()});let x={textStyle:{color:"#ffffff"},xAxis:{type:"category",data:["旧线路","新线路"]},yAxis:{type:"value"},grid:{x:70,x2:40,y:70,y2:40},series:[{data:[{value:0,itemStyle:{color:"#fcfe6d"}},{value:0,itemStyle:{color:"#3baefe"}}],type:"bar",barWidth:"20%",label:{show:!0,position:"top",textStyle:{color:"#ffffff",fontSize:15}}}]},j={textStyle:{color:"#ffffff"},xAxis:{type:"category",data:["旧线路","新线路"]},yAxis:{type:"value"},grid:{x:70,x2:40,y:70,y2:40},series:[{data:[{value:0,itemStyle:{color:"#fcfe6d"}},{value:0,itemStyle:{color:"#3baefe"}}],type:"bar",barWidth:"20%",label:{show:!0,position:"top",textStyle:{color:"#ffffff",fontSize:15}}}]},W={textStyle:{color:"#ffffff"},xAxis:{type:"category",data:["旧线路","新线路"]},yAxis:{type:"value"},grid:{x:75,x2:40,y:70,y2:40},series:[{data:[{value:0,itemStyle:{color:"#fcfe6d"}},{value:0,itemStyle:{color:"#3baefe"}}],type:"bar",barWidth:"20%",label:{show:!0,position:"top",textStyle:{color:"#ffffff",fontSize:15}}}]};const n=Qt();n.getTransitDepotRouteDataAction();const w=k(""),E=k(""),se=(s,e)=>{w.value=s,E.value=e},T=k(""),S=k("");let le=new Array("星期一","星期二","星期三","星期四","星期五");const A=k(""),ne=()=>{E.value==""?ee.warning("请先选择中转站-车牌号"):S.value==""?ee.warning("请先选择日期"):n.getRouteVersionAction({date:S.value.replaceAll(".","-"),transitDepotId:E.value})};let O=[],V=[];const re=()=>{let s=w.value+"-"+T.value;r.remove(O),v.remove(V),O=[],V=[],n.getRouteDataAction({transitDepotId:E.value,routeName:s+"-"+S.value+"-"+A.value},"309bde1e73b984c7d8a87ab19255963c").then(()=>{var y,i,p,h,l,a,_,$;console.log("旧路线"),console.log((y=n.analysisRouteData)==null?void 0:y.cargoWeight),x.series[0].data[0].value=Number((i=n.analysisRouteData)==null?void 0:i.cargoWeight),console.log((p=n.analysisRouteData)==null?void 0:p.workTime),j.series[0].data[0].value=Number((h=n.analysisRouteData)==null?void 0:h.workTime),console.log((l=n.analysisRouteData)==null?void 0:l.distance),W.series[0].data[0].value=Number((a=n.analysisRouteData)==null?void 0:a.distance);let e=[];($=(_=n.analysisRouteData)==null?void 0:_.polyline)==null||$.forEach(u=>{e.push(new AMap.LngLat(u.longitude,u.latitude))});let C=new AMap.Polyline({path:e,strokeWeight:6,showDir:!0,strokeColor:"#001731",lineJoin:"round"});O.push(C),r.add(O),console.log(JSON.parse(window.sessionStorage.getItem("newPath"))),JSON.parse(window.sessionStorage.getItem("newPath")).find(u=>{var H;if(u.routeName.slice(0,-11)===s){let K=[];(H=u.polyline)==null||H.forEach(Q=>{K.push(new AMap.LngLat(Q.longitude,Q.latitude))});let ie=new AMap.Polyline({path:K,strokeWeight:6,showDir:!0,strokeColor:"red",lineJoin:"round"});V.push(ie),v.add(V),console.log("新路线"),console.log(u.cargoWeight),x.series[0].data[1].value=Number(u.cargoWeight),console.log(u.workTime),j.series[0].data[1].value=Number(u.workTime),console.log(u.distance),W.series[0].data[1].value=Number(u.distance)}}),R(),P(),M()})};return(s,e)=>{const C=ue,y=fe,i=ge,p=me,h=pe,l=ce;return g(),D("div",bo,[m("div",vo,[d(C,{size:"25",class:"backBtn",onClick:b},{default:f(()=>[d(c(de))]),_:1}),e[4]||(e[4]=B(" 路径分析  "))]),m("div",ho,[m("div",_o,[e[7]||(e[7]=B(" 请选择您要分析的路径： ")),d(p,{modelValue:c(w),"onUpdate:modelValue":e[0]||(e[0]=a=>z(w)?w.value=a:null),placeholder:"请选择中转站-车牌号",size:"large",style:{width:"13vw","margin-top":"3.5vh"}},{default:f(()=>[(g(!0),D(I,null,L(c(n).historicalPath,a=>(g(),N(i,{key:a.transitDepotId,label:a.transitDepotName},{default:f(()=>[(g(!0),D(I,null,L(a.licensePlateNumberList,_=>(g(),N(y,{key:_,label:_,value:_,onClick:$=>se(a.transitDepotName+"-"+_,a.transitDepotId)},null,8,["label","value","onClick"]))),128))]),_:2},1032,["label"]))),128))]),_:1},8,["modelValue"]),d(p,{modelValue:c(T),"onUpdate:modelValue":e[1]||(e[1]=a=>z(T)?T.value=a:null),placeholder:"请选择星期",size:"large",style:{width:"13vw","margin-top":"3.5vh"}},{default:f(()=>[(g(!0),D(I,null,L(c(le),a=>(g(),N(y,{key:a,label:a,value:a},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),d(h,{style:{width:"13vw","margin-top":"3.5vh"},modelValue:c(S),"onUpdate:modelValue":e[2]||(e[2]=a=>z(S)?S.value=a:null),type:"date",placeholder:"选择日期时间","value-format":"YYYY.MM.DD"},null,8,["modelValue"]),d(p,{modelValue:c(A),"onUpdate:modelValue":e[3]||(e[3]=a=>z(A)?A.value=a:null),placeholder:"请选择版本号",size:"large",style:{width:"13vw","margin-top":"3.5vh"},onClick:ne},{default:f(()=>[(g(!0),D(I,null,L(c(n).routeVersion,a=>(g(),N(y,{key:a,label:a,value:a},null,8,["label","value"]))),128))]),_:1},8,["modelValue"]),d(l,{class:"btn",onClick:re},{default:f(()=>e[5]||(e[5]=[B("进行分析")])),_:1}),d(l,{class:"btn"},{default:f(()=>e[6]||(e[6]=[B("导出分析图")])),_:1})]),e[8]||(e[8]=_e('<div class="oldRoute" data-v-512eb480> 旧路线： <div class="oldRouteBorder" data-v-512eb480><div id="container" data-v-512eb480></div></div></div><div class="newRoute" data-v-512eb480> 新路线： <div class="newRouteBorder" data-v-512eb480><div id="container2" data-v-512eb480></div></div></div>',2))]),m("div",So,[m("div",xo,[d(c(U),{color:["#73e5ff","#73e5ff"],backgroundColor:"#001731",title:"运行里程/公里"},{default:f(()=>e[9]||(e[9]=[m("div",{id:"main"},null,-1)])),_:1})]),m("div",wo,[d(c(U),{color:["#73e5ff","#73e5ff"],backgroundColor:"#001731",title:"工作时长/小时"},{default:f(()=>e[10]||(e[10]=[m("div",{id:"main2"},null,-1)])),_:1})]),m("div",Co,[d(c(U),{color:["#73e5ff","#73e5ff"],backgroundColor:"#001731",title:"载货量/盒"},{default:f(()=>e[11]||(e[11]=[m("div",{id:"main3"},null,-1)])),_:1})])])])}}}),Yo=Xt(ko,[["__scopeId","data-v-512eb480"]]);export{Yo as default};
