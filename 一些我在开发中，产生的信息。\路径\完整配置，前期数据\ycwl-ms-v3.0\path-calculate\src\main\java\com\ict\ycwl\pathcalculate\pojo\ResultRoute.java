package com.ict.ycwl.pathcalculate.pojo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResultRoute {

    @TableId(type = IdType.ASSIGN_ID)
    private Long routeId;

    private String routeName;

    private String distance;

    private Long transitDepotId;

    private Long groupId;

    private Long areaId;

    private List<LngAndLat> polyline;

    private Timestamp createTime;

    private Timestamp updateTime;

    private boolean isDelete;

    private String cargoWeight;

    private Long versionId;

    private BigDecimal workTime;

    private List<LngAndLat> convex;
}
