package com.ict.ycwl.user.exception;

import com.ict.ycwl.common.web.AjaxResult;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理权限不足异常
     */
    @ExceptionHandler(RuntimeException.class)
    public AjaxResult handleRuntimeException(RuntimeException e) {
        String message = e.getMessage();
        
        // 权限不足的异常
        if ("权限不足，无法执行此操作".equals(message)) {
            return AjaxResult.error(403, "权限不足，无法执行此操作");
        }
        
        // 登录相关异常
        if ("无token，请重新登录".equals(message) || "用户不存在，请重新登录".equals(message)) {
            return AjaxResult.error(401, message);
        }
        
        // 401异常
        if ("401".equals(message)) {
            return AjaxResult.error(401, "登录已过期，请重新登录");
        }
        
        // 其他运行时异常
        return AjaxResult.error(500, "系统异常：" + message);
    }
}
