package com.ict.ycwl.pathcalculate.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@ApiModel("获取着色分块表单")
public class GetColourConvexHullFrom {

    /**
     * 班组名称
     */
    @ApiModelProperty(value = "班组名称", dataType = "String", required = true, example = "班组一")
    @NotNull(message = "班组名称不能为空")
    private String groupName;

    /**
     * 该班组的地图分割线
     */
    @ApiModelProperty(value = "该班组的地图分割线的坐标点集合", dataType = "double[]", required = true, example = "[[113.5940036,24.77783257],[113.5940036,24.77783257]]")
    private List<double[]> splitLines;
}
