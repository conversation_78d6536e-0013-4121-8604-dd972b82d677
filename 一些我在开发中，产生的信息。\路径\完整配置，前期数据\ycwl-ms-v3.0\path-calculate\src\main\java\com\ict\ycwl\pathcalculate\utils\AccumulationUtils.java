package com.ict.ycwl.pathcalculate.utils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.ict.ycwl.pathcalculate.mapper.AccumulationMapper;
import com.ict.ycwl.pathcalculate.mapper.TransitDepotMapper;
import com.ict.ycwl.pathcalculate.pojo.Accumulation;
import com.ict.ycwl.pathcalculate.pojo.TransitDepot;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class AccumulationUtils {

    @Autowired
    private AccumulationMapper accumulationMapper;

    @Autowired
    private TransitDepotMapper transitDepotMapper;

    public void updateAccumulationToDepot() {
        QueryWrapper<TransitDepot> q1 = new QueryWrapper<>();
        q1.like("transit_depot_name", "坪石").eq("status", 1);
        TransitDepot t1 = transitDepotMapper.selectOne(q1);
        UpdateWrapper<Accumulation> u1 = new UpdateWrapper<>();
        u1.like("area_name", "乐昌").eq("is_delete", 0).set("transit_depot_id", t1.getTransitDepotId());
        accumulationMapper.update(null, u1);
        UpdateWrapper<Accumulation> u2 = new UpdateWrapper<>();
        u2.like("area_name", "大桥").eq("is_delete", 0).set("transit_depot_id", t1.getTransitDepotId());
        accumulationMapper.update(null, u2);

        QueryWrapper<TransitDepot> q2 = new QueryWrapper<>();
        q2.like("transit_depot_name", "班组一物流配送中心").eq("status", 1);
        TransitDepot t2 = transitDepotMapper.selectOne(q2);
        UpdateWrapper<Accumulation> u4 = new UpdateWrapper<>();
        u4.like("area_name", "曲江").eq("is_delete", 0).set("transit_depot_id", t2.getTransitDepotId());
        accumulationMapper.update(null, u4);
        UpdateWrapper<Accumulation> u5 = new UpdateWrapper<>();
        u5.like("area_name", "铁龙").eq("is_delete", 0).set("transit_depot_id", t2.getTransitDepotId());
        accumulationMapper.update(null, u5);
        UpdateWrapper<Accumulation> u12 = new UpdateWrapper<>();
        u12.like("area_name", "浈江").eq("is_delete", 0).set("transit_depot_id", t2.getTransitDepotId());
        accumulationMapper.update(null, u12);
        UpdateWrapper<Accumulation> u11 = new UpdateWrapper<>();
        u11.like("area_name", "仁化").eq("is_delete", 0).set("transit_depot_id", t2.getTransitDepotId());
        accumulationMapper.update(null, u11);

        QueryWrapper<TransitDepot> q3 = new QueryWrapper<>();
        q3.like("transit_depot_name", "马市").eq("status", 1);
        TransitDepot t3 = transitDepotMapper.selectOne(q3);
        UpdateWrapper<Accumulation> u7 = new UpdateWrapper<>();
        u7.like("area_name", "始兴").eq("is_delete", 0).set("transit_depot_id", t3.getTransitDepotId());
        accumulationMapper.update(null, u7);
        UpdateWrapper<Accumulation> u8 = new UpdateWrapper<>();
        u8.like("area_name", "南雄").eq("is_delete", 0).set("transit_depot_id", t3.getTransitDepotId());
        accumulationMapper.update(null, u8);

        QueryWrapper<TransitDepot> q4 = new QueryWrapper<>();
        q4.like("transit_depot_name", "翁源").eq("status", 1);
        TransitDepot t4 = transitDepotMapper.selectOne(q4);
        UpdateWrapper<Accumulation> u9 = new UpdateWrapper<>();
        u9.like("area_name", "翁源").eq("is_delete", 0).set("transit_depot_id", t4.getTransitDepotId());
        accumulationMapper.update(null, u9);

        QueryWrapper<TransitDepot> q5 = new QueryWrapper<>();
        q5.like("transit_depot_name", "新丰").eq("status", 1);
        TransitDepot t5 = transitDepotMapper.selectOne(q5);
        UpdateWrapper<Accumulation> u10 = new UpdateWrapper<>();
        u10.like("area_name", "新丰").eq("is_delete", 0).set("transit_depot_id", t5.getTransitDepotId());
        accumulationMapper.update(null, u10);

        QueryWrapper<TransitDepot> q6 = new QueryWrapper<>();
        q6.like("transit_depot_name", "班组二物流配送中心").eq("status", 1);
        TransitDepot t6 = transitDepotMapper.selectOne(q6);
        UpdateWrapper<Accumulation> u13 = new UpdateWrapper<>();
        u13.like("area_name", "武江").eq("is_delete", 0).set("transit_depot_id", t6.getTransitDepotId());
        accumulationMapper.update(null, u13);
        UpdateWrapper<Accumulation> u6 = new UpdateWrapper<>();
        u6.like("area_name", "乳源").eq("is_delete", 0).set("transit_depot_id", t6.getTransitDepotId());
        accumulationMapper.update(null, u6);
        UpdateWrapper<Accumulation> u3 = new UpdateWrapper<>();
        u3.like("area_name", "市辖区").eq("is_delete", 0).set("transit_depot_id", t6.getTransitDepotId());
        accumulationMapper.update(null, u3);
        UpdateWrapper<Accumulation> u14 = new UpdateWrapper<>();
        u14.like("area_name", "必背").eq("is_delete", 0).set("transit_depot_id", t6.getTransitDepotId());
        accumulationMapper.update(null, u14);
    }
}
