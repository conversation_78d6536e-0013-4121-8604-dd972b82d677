package com.ict.ycwl.guestbook.api;

import com.ict.ycwl.common.web.AjaxResult;
import com.ict.ycwl.guestbook.domain.Area;
import com.ict.ycwl.guestbook.mapper.AreaMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Api(tags = "测试")
@RestController
@RequestMapping("/test")
public class TestApi {

    @Autowired
    private AreaMapper areaMapper;
    @ApiOperation("测试接口")
    @GetMapping
    public AjaxResult test(){
        List<Area> areas = areaMapper.selectAll();
        return AjaxResult.success(areas);

    }
}
