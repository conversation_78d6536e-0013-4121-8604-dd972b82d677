package com.ict.ycwl.pathcalculate.form;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("打卡点更新表单")
public class AdjustPointForm {

    @ApiModelProperty(value = "聚集区Id",dataType = "Long",required = true)
    private Long accumulationId;

    @ApiModelProperty(value = "路线Id",dataType = "Long",required = false)
    private Long routeId;



}
