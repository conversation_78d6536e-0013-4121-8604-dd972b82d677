<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.datamanagement.mapper.CarDriverMapper">

    <select id="selectName" resultType="java.lang.String">
        select user_name from user where position="物流部经办人员"
    </select>
    <select id="selectByName" resultType="com.ict.datamanagement.domain.entity.CarDriver">
        select user_id,user_name,phone from user where user_name=#{carDriverName} and position="物流部经办人员"
    </select>
</mapper>
