package com.ict.ycwl.pathcalculate.utils.getColorUtils;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.GeometryFactory;
import org.locationtech.jts.geom.Polygon;
import org.locationtech.jts.io.ParseException;

public class Readboundary {
    // 创建GeometryFactory对象，用于生成几何图形
    private GeometryFactory geometryFactory;

    // 构造方法初始化GeometryFactory
    public Readboundary() {
        this.geometryFactory = new GeometryFactory();
    }

    /**
     * 从指定文件中读取多边形数据
     *
     * @param filePath 文件路径
     * @return 多边形
     * @throws IOException    文件读取异常
     * @throws ParseException 坐标解析异常
     */
    public Polygon readPolygonsFromFile(String filePath) throws IOException, ParseException {
        // 读取文件中的所有行
        List<String> lines = Files.readAllLines(Paths.get(filePath));

        // 遍历每一行，解析多边形
        Polygon polygon = null;
        for (String line : lines) {
            //System.out.println(line);
            if (line.contains("districts") || line.contains("citycode") || line.contains("adcode")
                    || line.contains("name") || line.contains("center")
                    || line.contains("level") || line.contains("}]")) {
                continue;
            }
            // 去除首尾引号，并跳过空行和包含"convex"的行
            line = line.trim();
            if (line.contains("polyline")) {
                line = line.substring(13, line.length() - 2);
            }
            //System.out.println(line);
            if (!line.trim().isEmpty() && !line.equals("convex")) {
                // 将该行解析为多边形对象
                //System.out.println(line);
                polygon = createPolygonFromLine(line);
            }
        }
        // 返回多边形列表
        return polygon;
    }

    /**
     * 将一行字符串分割解析为一组Coordinate创建Polygon多边形对象
     *
     * @param line 包含坐标对的字符串
     * @return Polygon多边形对象
     * @throws ParseException 坐标解析异常
     */
    public Polygon createPolygonFromLine(String line) throws ParseException {
        // 按分号分隔坐标对
        String[] coordinatePairs = line.split(";");
        // 创建一个Coordinate数组用于存储解析后的坐标
        int Pairs_len = coordinatePairs.length;
        if(Pairs_len <= 2) {
            return null;
        }
        Coordinate[] coordinates = new Coordinate[Pairs_len];

        // 遍历每个坐标对
        for (int i = 0; i < coordinatePairs.length; i++) {
            // 按逗号分隔x和y坐标
            String[] parts = coordinatePairs[i].split(",");
            double x = Double.parseDouble(parts[0]);
            double y = Double.parseDouble(parts[1]);
            // 创建Coordinate对象并存储在数组中
            coordinates[i] = new Coordinate(x, y);
        }
        //System.out.println("OK");
        // 使用GeometryFactory创建Polygon对象
        return geometryFactory.createPolygon(coordinates);
    }
}
