package com.ict.ycwl.guestbook.service.impl;

import com.ict.ycwl.common.utils.FileUtils;
import com.ict.ycwl.guestbook.api.form.ReplyAddForm;
import com.ict.ycwl.guestbook.api.vo.FeedbackReplyVo;
import com.ict.ycwl.guestbook.domain.FeedbackReply;
import com.ict.ycwl.guestbook.domain.FeedbackReplyFile;
import com.ict.ycwl.guestbook.mapper.FeedbackMapper;
import com.ict.ycwl.guestbook.mapper.FeedbackReplyFileMapper;
import com.ict.ycwl.guestbook.mapper.FeedbackReplyMapper;
import com.ict.ycwl.guestbook.service.FeedbackReplyService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;

@Service
public class FeedbackReplyServiceImpl implements FeedbackReplyService {

    @Value("${file.savePath}")
    private String savePath;

    @Value("${file.accessPathPrefix}")
    private String accessPathPrefix;

    @Autowired
    private FeedbackReplyMapper replyMapper;

    @Autowired
    private FeedbackReplyFileMapper replyFileMapper;

    @Autowired
    private FeedbackMapper feedbackMapper;

    @Override
    public List<FeedbackReplyVo> getReplyAndFile(Long feedbackId) {
        return replyMapper.selectReplyAndPhoto(feedbackId);
    }

    @Override
    public void addReplyAndFile(ReplyAddForm form, Long userId) throws Exception {
        //拷贝
        FeedbackReply feedbackReply = FeedbackReply.builder().createBy(userId).createTime(LocalDateTime.now(ZoneId.of("Asia/Shanghai"))).build();
        BeanUtils.copyProperties(form, feedbackReply);

        //插入处理信息数据
        replyMapper.insert(feedbackReply);
        Long replyId = feedbackReply.getReplyId();

        //判断是否有文件并存储
        if (form.getFileList() != null && !form.getFileList().isEmpty()) {
            List<FeedbackReplyFile> fileList = new ArrayList<>();
            for (MultipartFile multipartFile : form.getFileList()) {
                String filePath = FileUtils.upload(multipartFile, savePath, multipartFile.getOriginalFilename());
                FeedbackReplyFile replyFile = FeedbackReplyFile.builder().replyFilePath(File.separator + accessPathPrefix + File.separator + filePath)
                        .replyFileRealPath(savePath + filePath).replyId(replyId).build();
                fileList.add(replyFile);
                //保存文件信息
                replyFileMapper.insert(replyFile);
            }
        }

        //判断是否需要修改状态（前面还需判定数值是否正确（1 || 2），现直接存储）
        String feedbackType = feedbackMapper.selectTypeById(form.getFeedbackId());
        if (!feedbackType.equals(form.getReplyType())) {
            if (form.getFeedbackStatus()==2||form.getFeedbackStatus()==3) {
                feedbackMapper.updateFeedbackStatus(form.getFeedbackStatus(), feedbackReply.getFeedbackId(), LocalDateTime.now(ZoneId.of("Asia/Shanghai")));
            } else {
                feedbackMapper.updateFeedbackStatus(form.getFeedbackStatus(), feedbackReply.getFeedbackId(), null);
            }
        }

        //更新反馈信息的最近更新时间和更新者
        feedbackMapper.updateUpdateData(LocalDateTime.now(ZoneId.of("Asia/Shanghai")),userId,feedbackReply.getFeedbackId());

    }

}
